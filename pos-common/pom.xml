<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>api-web</artifactId>
        <groupId>com.charge.cloud</groupId>
        <version>2.1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>${api-web-common.version}</version>
    <artifactId>pos-common</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>charge-common</artifactId>
        </dependency>
    </dependencies>
</project>