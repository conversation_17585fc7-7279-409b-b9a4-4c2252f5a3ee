package com.charge.pos.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 创建订单的结果
 *
 * <AUTHOR>
 * @date 2023/2/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateOrderResult {

    /**
     * 流水id
     */
    @NotNull
    private String id;

    /**
     * 订单号
     */
    private String orderNum;

}


