package com.charge.pos.dto;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Author: yjw
 * Date: 2023/2/28 14:03
 */

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor
@Builder
public class AccountInfo implements Serializable{
    private static final long serialVersionUID = 8181720264457428091L;
    private String accountUuid;
    private String account;
    private String mobile;
    private String email;
    private String realName;
    private Byte gender;
    private String birthday;
    private String idCardNum;
    private String landline;
    private String external;
    private String jobType;
    private String jobRank;
    private String jobLevel;
    private Integer establishment;
    private Integer onJob;
    private String TypeId;
    private String classify;
    private String province;
    private String city;
    private String region;
    private BigDecimal longitude;
    private BigDecimal latitude;
    private String orgType;
    private String orgName;
    private String orgUuid;
    private String parentId;
    private String parentName;//父级名称
    private String communityUuid;
    private String communityName;
    private String systemType;
    private String authInfoList;
    private List<CommunityDtoV1> resultList;
    private String user_token;
    private String cropId;


}
