package com.charge.pos.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * [订单规则收费项DTO]
 *
 * <AUTHOR> wu<PERSON>hao
 * @version : [v1.0]
 */
@Data
public class OrderRuleChargeItemDTO implements Serializable {
    private static final long serialVersionUID = 2977246290299850250L;

    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数量
     */
    private Integer pageSize;
    /**
     * 小区
     */
    private Long communityId;

    /**
     * 订单一级品类 1=有偿服务 2=停车服务  3=临时类
     */
    private Integer firstClassificationId;

    /**
     * 订单二级品类 有偿服务={100=通用工单} 停车服务={200=月卡，201=临停}  临时类={300=通用，301=押金，302=临停}
     */
    private Integer secondClassificationId;

    /**
     * 是否启用 0:启用, 1:禁用, 2:未启用
     */
    private List<Integer> status;

    /**
     * 收费项名称
     */
    private String itemName;

    /**
     * 收费项支持的业务类型，支持多选（1：周期型，2：订单型，3：合同，4：临时，5：押金）
     */
    private List<Integer> businessTypes;

    /**
     * 订单规则收费项集合
     */
    private List<Long> communityOrderRuleItemIds;

}
