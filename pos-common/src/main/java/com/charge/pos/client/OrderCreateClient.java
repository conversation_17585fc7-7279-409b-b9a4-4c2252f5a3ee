package com.charge.pos.client;

import com.charge.common.dto.ChargeResponse;
import com.charge.pos.dto.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 订单创建
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
public interface OrderCreateClient {


    /**
     * 创建多个临时收费订单
     *
     * @param deviceInfo 设备信息
     * @return 计费结果响应
     */
    @PostMapping(value = "/")
    ChargeResponse<CreateOrderResult> createMultiTempOrder(@RequestParam String deviceInfo, @RequestParam String mercid,
                                                           @RequestParam String ownerId, @RequestParam String tempChargeData,
                                                           @RequestParam String amount, @RequestParam String paymentMethod,
                                                           @RequestParam String collectorId,@RequestParam String collectorName,
                                                           @RequestParam String payMember, @RequestParam String bankTransactionNo,
                                                           @RequestParam String arrivalDate, @RequestParam String bankAccountUuid,
                                                           @RequestParam String bankAccountNum, @RequestParam String memo,
                                                           @RequestParam String token);




}


