<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.charge.framework</groupId>
        <artifactId>charge-common-parent</artifactId>
        <version>3.0.2-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.charge.cloud</groupId>
    <artifactId>api-web</artifactId>
    <packaging>pom</packaging>
    <version>2.1.0-SNAPSHOT</version>
    <name>api-web</name>

    <modules>
        <module>pos-common</module>
        <module>api-web-app</module>
        <module>joylife-common</module>
    </modules>
    <properties>
        <api-web-common.version>1.0.12</api-web-common.version>
    </properties>
</project>