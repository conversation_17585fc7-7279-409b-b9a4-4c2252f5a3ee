<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.charge.framework</groupId>
        <artifactId>charge-app-parent</artifactId>
        <version>3.0.2-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.charge.cloud</groupId>
    <name>${project.artifactId}</name>
    <artifactId>api-web-app</artifactId>
    <properties>
        <api-web-common.version>1.0.12</api-web-common.version>
        <zxing.version>3.4.1</zxing.version>
        <emoji.version>5.1.1</emoji.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>leaf-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>pay-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>charge-bill-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>general-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.framework</groupId>
            <artifactId>charge-framework-starter-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>charge-cloud-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>pay-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>pay-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>fee-calculate-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>charge-bill-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>maindata-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>config-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>config-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>pos-common</artifactId>
            <version>${api-web-common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>apicloud-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>user-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>order-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.10.1</version>
        </dependency>


        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>finance-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>joylife-common</artifactId>
            <version>${api-web-common.version}</version>
        </dependency>

        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>charge-api-sdk</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>reportcloud-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>invoice-bff</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>invoice-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.charge.cloud</groupId>
            <artifactId>general-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>${emoji.version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
            <version>2.8.0</version>
        </dependency>
    </dependencies>
</project>