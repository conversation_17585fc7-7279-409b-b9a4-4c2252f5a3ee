package com.charge.api.web.support;

import com.charge.api.web.BaseTest;
import com.charge.api.web.config.PrePayLockConfig;
import com.charge.bill.client.LockClient;
import com.charge.bill.dto.LockObjectDTO;
import com.charge.bill.enums.BillPrePaySceneEnum;
import com.charge.bill.enums.PaymentMethodEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.SpringContextUtil;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.HashSet;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2024/01/03
 */
class PrePayLockSupportTest extends BaseTest {


    @Resource
    private PrePayLockSupport prePayLockSupport;
    @Test
    public void checkAndLock() {
        PrePayLockConfig prePayLockConfig = new PrePayLockConfig();
        prePayLockSupport.setPrePayLockConfig(prePayLockConfig);
        prePayLockSupport.setLockClient(new LockClientImpl());

        LockObjectDTO lockObject_1 = new LockObjectDTO();
        lockObject_1.setMark("abc_1");
        lockObject_1.setBusinessScene(BillPrePaySceneEnum.ZHAOXI_LIFE.getCode());
        lockObject_1.setReceiveBillIdList(new HashSet<>(Arrays.asList(1L, 3L)));

        assertDoesNotThrow(() -> prePayLockSupport.lockAndClose(lockObject_1));


        LockObjectDTO lockObject_2 = new LockObjectDTO();
        lockObject_2.setMark("abc_2");
        lockObject_2.setBusinessScene(BillPrePaySceneEnum.ZHAOXI_LIFE.getCode());
        lockObject_2.setPaymentMethod(PaymentMethodEnum.WECHAT.getPaymentMethod());
        lockObject_2.setReceiveBillIdList(new HashSet<>(Arrays.asList(2L, 3L)));
        assertThrows(ChargeBusinessException.class, () -> prePayLockSupport.lockAndClose(lockObject_2));


        prePayLockConfig.setEnableLock(false);
        prePayLockSupport.setPrePayLockConfig(prePayLockConfig);
        assertDoesNotThrow(() -> prePayLockSupport.lockAndClose(lockObject_2));


        prePayLockConfig.setEnableLock(true);
        prePayLockConfig.setForbidLockScenes(new HashSet<>(Arrays.asList(BillPrePaySceneEnum.ZHAOXI_LIFE.getCode())));
        prePayLockSupport.setPrePayLockConfig(prePayLockConfig);
        assertDoesNotThrow(() -> prePayLockSupport.lockAndClose(lockObject_2));

        prePayLockConfig = new PrePayLockConfig();
        prePayLockConfig.setAllowClosePreviousScenes(new HashSet<>(Arrays.asList(BillPrePaySceneEnum.ZHAOXI_LIFE.getCode())));
        prePayLockSupport.setPrePayLockConfig(prePayLockConfig);

        assertThrows(RuntimeException.class, () -> prePayLockSupport.lockAndClose(lockObject_2));

        prePayLockConfig.setAllowClosePreviousAfterSec(10);
        prePayLockSupport.setPrePayLockConfig(prePayLockConfig);
        assertThrows(ChargeBusinessException.class, () -> prePayLockSupport.lockAndClose(lockObject_2));

        //重复检测
        prePayLockConfig.setCheckRepeat(true);
        prePayLockConfig.setAllowClosePreviousAfterSec(0);
        prePayLockSupport.setPrePayLockConfig(prePayLockConfig);
        assertThrows(ChargeBusinessException.class, () -> prePayLockSupport.lockAndClose(lockObject_2));

        lockObject_2.setPaymentMethod(PaymentMethodEnum.ALIPAY.getPaymentMethod());
        assertThrows(RuntimeException.class, () -> prePayLockSupport.lockAndClose(lockObject_2));

        lockObject_2.setPaymentMethod(PaymentMethodEnum.WECHAT.getPaymentMethod());
        assertThrows(ChargeBusinessException.class, () -> prePayLockSupport.lockAndClose(lockObject_2));

        try {
            Thread.sleep(10000L);
        } catch (InterruptedException ex) {

        }
        assertThrows(RuntimeException.class, () -> prePayLockSupport.lockAndClose(lockObject_2));


    }
}