package com.charge.api.web.controller;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.BaseTest;
import com.charge.api.web.controller.pos.ChargeItemConfigController;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.PrestoreItemVO;
import com.charge.api.web.vo.pos.SystemChargeItemVO;
import com.charge.api.web.vo.pos.TemporaryChargeItemVO;
import com.charge.common.dto.ChargeResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * [一句话描述该类的功能]
 *
 * <AUTHOR> wuChao
 * @version : [v1.0]
 */
@Slf4j
public class ChargeItemConfigControllerTest extends BaseTest {
    @Autowired
    private ChargeItemConfigController chargeItemConfigController;

    @Test
    public void getOrderRuleChargeItemPage(){
        ChargePageResponse<List<SystemChargeItemVO>> orderRuleChargeItemPage = chargeItemConfigController.getOrderRuleChargeItemPage("1", 1, 10, "1");
        System.out.println("orderRuleChargeItemPage = " + JSON.toJSONString(orderRuleChargeItemPage));
    }

    @Test
    public void getOrderRuleChargeItemTemporaryStop(){
        ChargeResponse<List<TemporaryChargeItemVO>> orderRuleChargeItemTemporaryStop = chargeItemConfigController.getOrderRuleChargeItemTemporaryStop("1", "1");
        System.out.println("orderRuleChargeItemTemporaryStop = " + JSON.toJSONString(orderRuleChargeItemTemporaryStop));
    }

//    @Test
//    public void getOrderRuleDepositItem(){
//        ChargePageResponse<List<SystemChargeItemVO>> orderRuleDepositItem = chargeItemConfigController.getOrderRuleDepositItem("1", 1, 10, "1", "1");
//        System.out.println("orderRuleDepositItem = " + JSON.toJSONString(orderRuleDepositItem));
//    }

//    @Test
//    public void getPrestoreItem(){
//        ChargeResponse<List<PrestoreItemVO>> prestoreItem = chargeItemConfigController.getPrestoreItem("1", "", 1, 10, "1");
//        System.out.println("prestoreItem = " + JSON.toJSONString(prestoreItem));
//    }
}
