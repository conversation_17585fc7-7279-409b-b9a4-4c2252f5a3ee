package com.charge.api.web.controller;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.BaseTest;
import com.charge.api.web.controller.joylife.point.PointController;
import com.charge.api.web.controller.pos.ChargeItemConfigController;
import com.charge.api.web.dto.joylife.DistributePointsReq;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.SystemChargeItemVO;
import com.charge.cloud.api.core.OpenApiTool;
import com.charge.cloud.api.dto.OpenApiRequest;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.core.util.JacksonConverter;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/4/7 17:03
 */
public class PointControllerTest extends BaseTest {


    @Autowired
    private PointController pointController;
    @Autowired
    private OpenApiTool openApiTool;

    @Value("${pay.ras.chargePublicKey}")
    private String chargePublicKey;

    @Value("${pay.ras.joyLiftPrivateKey}")
    private String joyLiftPrivateKey;




    @Test
    public void getOrderRuleChargeItemPage() throws Exception {

        Map<String, Object> data = new HashMap<>();
        data.put("communityId", "59059885");
        data.put("pid", "10");
        data.put("phone", "***********");
        data.put("points", "10");
        data.put("paymentSource", "yue_home");
        OpenApiRequest openApiRequest = openApiTool.build(data, joyLiftPrivateKey, chargePublicKey);

        DistributePointsReq distributePointsReq = new DistributePointsReq();
        distributePointsReq.setCommunityId("59059885");
        distributePointsReq.setCharset(openApiRequest.getCharset());
        distributePointsReq.setFormat(openApiRequest.getFormat());
        distributePointsReq.setSignType(openApiRequest.getSignType());
        distributePointsReq.setTimestamp(openApiRequest.getTimestamp());
        distributePointsReq.setSign(openApiRequest.getSign());
        distributePointsReq.setRandomKey(openApiRequest.getRandomKey());
        distributePointsReq.setBizContent(openApiRequest.getBizContent());

        System.out.println(JacksonConverter.write(distributePointsReq));

        ChargeResponse response = pointController.distributePoints(distributePointsReq);
        System.out.println(response);
    }

    @Test
    public void getPointsDiscountEnableTest() throws Exception {
        pointController.getPointsDiscountEnable("64206188","7126523caf6c4570896415cc4abbca03","null");
    }
}
