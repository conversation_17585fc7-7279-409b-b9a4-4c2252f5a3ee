package com.charge.api.web.bill.pos;

import com.charge.api.web.BaseTest;
import com.charge.api.web.service.pos.ChargeBillService;
import com.charge.api.web.vo.lakala.OrdersBillCreateRequest;
import com.charge.api.web.vo.lakala.PaymentResponse;
import com.charge.common.exception.ChargeBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * pos小区多房间临时收费下单：订单类
 */
@Slf4j
public class AddNewTempChargeServiceTest extends BaseTest {
    @Resource
    private ChargeBillService chargeBillService;

    @Test
    public void testCreateBill() throws ChargeBusinessException {
        OrdersBillCreateRequest request = new OrdersBillCreateRequest(
                "00000104YP620000514431",
                "822290370110494",
                "2",
                // 构建临时订单类
                BuildCommonUtil.buildOrderItemJson(),
                "100",
                "4",
                "1",
                "justTest",
                "用于测试",
                "440606204040062051",
                // arrivalDate: 无使用场景
                null,
                "440606204040062052",
                "440606204040062053",
                "用于测试",
                null);
        PaymentResponse paymentResponse = chargeBillService.createBill(request);
        log.info("PaymentResponse value = {}", paymentResponse);
        Assert.assertTrue(Objects.nonNull(paymentResponse) && Objects.nonNull(paymentResponse.getId()));
    }
}
