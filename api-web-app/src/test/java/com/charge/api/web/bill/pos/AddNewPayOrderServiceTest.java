package com.charge.api.web.bill.pos;

import com.charge.api.web.BaseTest;
import com.charge.api.web.service.pos.ChargeBillService;
import com.charge.api.web.vo.lakala.PaymentResponse;
import com.charge.api.web.vo.lakala.ReceivableAndOrderBillCreateRequest;
import com.charge.common.exception.ChargeBusinessException;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * POS缴费下单：缴欠费、临时订单
 */
public class AddNewPayOrderServiceTest extends BaseTest {

    @Resource
    private ChargeBillService chargeBillService;

    @Test
    public void addNewPayOrderTest() throws ChargeBusinessException {
        PaymentResponse paymentResponse = chargeBillService.createBill(buildRequest());
        Assert.assertTrue(Objects.nonNull(paymentResponse));
    }

    private ReceivableAndOrderBillCreateRequest buildRequest() {
        ReceivableAndOrderBillCreateRequest request = new ReceivableAndOrderBillCreateRequest();
        request.setHouseUuid("2");
        request.setBankTransactionNo("54462e99d2c211edb64");
        request.setPaymentMethod("4");
        request.setPayMember(request.getPayMember());
        request.setCollectorId("1");
        request.setCollectorName("justTest");
        request.setBankAccountNum("440606204040062051");
        request.setMemo("领域改造测试");
        request.setBankAccountUuid(null);
        request.setTotalPrice("100"); // 缴费总金额        TODO 金额判断？写入实收单#incomeMoney
        request.setArrearsPrice("150"); // 欠费项金额      欠费项金额 == 缴欠费的金额
        // 构建临时订单类
        request.setTempChargeData(BuildCommonUtil.buildOrderItemJson());
        // 构建缴欠费类
        request.setPayItemIds("1727889715698442313"); // 总金额为150:欠收金额100;违约金欠收金额50;
        return request;
    }
}
