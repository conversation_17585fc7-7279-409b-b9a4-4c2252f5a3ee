package com.charge.api.web.bill.pos;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.BaseTest;
import com.charge.api.web.service.pos.ChargeBillService;
import com.charge.api.web.vo.lakala.BatchBillCreateRequest;
import com.charge.api.web.vo.lakala.PaymentResponse;
import com.charge.api.web.vo.pos.ChargeItemPrice;
import com.charge.api.web.vo.pos.HouseReceivableItems;
import com.charge.common.exception.ChargeBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;

/**
 * POS多房间缴费：缴欠费
 */
@Slf4j
public class AddBatchPayOrderServiceTest extends BaseTest {
    @Resource
    private ChargeBillService chargeBillService;

    @Test
    public void testCreateBill() throws ChargeBusinessException {
        BatchBillCreateRequest request = new BatchBillCreateRequest(
                "4",
                "1",
                "justTest",
                buildChargeDataJson(),
                // arrearsPrice 总缴费金额 仅用于校验值无其他实际作用
                "100",
                // totalPrice  实收金额
                "100",
                null,
                null,
                "用于测试",
                null,
                "用于测试",
                "440606204040062051",
                null,
                "justTest",
                "1",
                null,
                null,
                null,
                "00000104YP620000514431",
                "822290370110494");
        PaymentResponse paymentResponse = chargeBillService.createBill(request);
        log.info("PaymentResponse value = {}", paymentResponse);
        Assert.assertTrue(Objects.nonNull(paymentResponse) && Objects.nonNull(paymentResponse.getId()));
    }

    public String buildChargeDataJson() {
        HouseReceivableItems houseReceivableItems = new HouseReceivableItems();
        houseReceivableItems.setHouseId("1");
        houseReceivableItems.setItemList(Arrays.asList(buildChargeItemPrice()));
        return JSON.toJSONString(Arrays.asList(houseReceivableItems));
    }

    private ChargeItemPrice buildChargeItemPrice() {
        ChargeItemPrice chargeItemPrice = new ChargeItemPrice();
        chargeItemPrice.setItemId("1727887536967229512"); // 应收单ID
        chargeItemPrice.setPrice("50"); // 仅用于判断数据是否>0，无其他用处
        return chargeItemPrice;
    }
}
