package com.charge.api.web.bill.pos;

import com.alibaba.fastjson.JSONObject;
import com.charge.api.web.service.pos.impl.TempChargeVO;

import java.util.Arrays;
import java.util.List;

public class BuildCommonUtil {

    /**
     * 构建临时订单类
     * @return
     */
    public static String buildOrderItemJson() {
        // 构建临时订单类
        TempChargeVO tempChargeVO = new TempChargeVO();
        tempChargeVO.setTempItemId("5");
        tempChargeVO.setItemName("物业管理费_自测");
        tempChargeVO.setPrice("50");
        List<TempChargeVO> tempChargeVOList = Arrays.asList(tempChargeVO);
        return JSONObject.toJSONString(tempChargeVOList);
    }
}
