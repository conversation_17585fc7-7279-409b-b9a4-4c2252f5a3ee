package com.charge.api.web.controller;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Collection;
import java.util.Locale;

import com.charge.api.web.dto.ssdp.SsdpRefundPaymentDTO;
import com.charge.core.util.JacksonConverter;
import org.apache.catalina.connector.Response;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.charge.api.web.BaseTest;
import com.charge.api.web.controller.ssdp.SsdpCallbackController;
import com.charge.api.web.dto.ssdp.SsdpRefundAuditConditionDTO;
import com.charge.api.web.dto.ssdp.SsdpRefundAuditDTO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2023/2/28
 */
public class SsdpCallbackControllerTest extends BaseTest {

    @Autowired
    private SsdpCallbackController ssdpCallbackController;

    @Test
    public void auditStatusCallback() {
        SsdpRefundAuditConditionDTO ssdpRefundAuditConditionDTO = new SsdpRefundAuditConditionDTO();
        ssdpRefundAuditConditionDTO.setTotalCount("1");
        ssdpRefundAuditConditionDTO.setRecordList(Arrays.asList(buildSsdpRefundAuditDTO()));
        ssdpCallbackController.auditStatusCallback(ssdpRefundAuditConditionDTO, new Response() );
    }

    @Test
    public void paymentDetailsCallback() {
        String json = "{\"totalCount\":1,\"refundStatus\":true,\"refundRecordId\":\"R335453453\",\n" +
                "\"recordList\":[{\"paymentTime\":\"2023-02-28 14:41:34 843\",\n" +
                "\"paymentAmount\":\"560\",\n" +
                "\"paymentBankName\":\"招商银行\",\n" +
                "\"paymentBankAccount\":\"***************\",\n" +
                "\"accountName\":\"活期-招行南宁分行营业部***************-人民币\",\n" +
                "\"accountOpeningBank\":\"招商银行股份有限公司南宁分行营业部\"}]}";

        SsdpRefundPaymentDTO ssdpRefundPaymentDTO = JacksonConverter.read(json,SsdpRefundPaymentDTO.class);

        ssdpCallbackController.paymentDetailsCallback(ssdpRefundPaymentDTO,new Response());
    }

    private SsdpRefundAuditDTO buildSsdpRefundAuditDTO() {
        SsdpRefundAuditDTO ssdpRefundAuditDTO = new SsdpRefundAuditDTO();
        ssdpRefundAuditDTO.setRefundStatus("1");
        ssdpRefundAuditDTO.setRefundRecordId("*********");
        return ssdpRefundAuditDTO;
    }

}
