package com.charge.api.web.controller;

import java.util.ArrayList;
import java.util.List;

import com.charge.common.exception.ChargeBusinessException;
import org.apache.catalina.connector.Response;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.charge.api.web.BaseTest;
import com.charge.api.web.config.ParkingPlatformConfig;
import com.charge.api.web.controller.parking.ParkingCallbackController;
import com.charge.api.web.dto.parking.GetPlatformBankFlowParam;
import com.charge.api.web.dto.parking.PlatformBankFlowRecord;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/5/24
 */
@Slf4j
public class ParkingCallbackControllerTest extends BaseTest {

    @Autowired
    private ParkingCallbackController parkingCallbackController;

    @Autowired
    private ParkingPlatformConfig parkingPlatformConfig;

    @Test
    public void sync() throws ChargeBusinessException {
        GetPlatformBankFlowParam param = new GetPlatformBankFlowParam();
        param.setTotalNum("1");
        param.setBatchId("sfxt_202357241557_1");
        param.setDone(false);
        param.setCurrentNum(1);
        param.setRecordList(record());
        parkingCallbackController.getPlatformBankBusinessFlow(param,new Response());
    }

    private List<PlatformBankFlowRecord> record() {
        List<PlatformBankFlowRecord> records = new ArrayList<>();
        PlatformBankFlowRecord record = new PlatformBankFlowRecord();
        record.setCommunityId("009871");
        record.setCommunityName("物业深圳公司");
        record.setParkingId("733");
        record.setParkingName("立方测试车场");
        record.setItemId("1fb76ed2-158a-4e64-b180-a4572457502e");
        record.setTransferId("1");
        record.setPaymentChannel("SWIFTPASS");
        record.setPaymentMethod("云平台");
        record.setPaymentSource("停车场云平台");
        records.add(record);
        return records;
    }

    @Test
    public void config() {
        log.info("{}", parkingPlatformConfig);
    }
}
