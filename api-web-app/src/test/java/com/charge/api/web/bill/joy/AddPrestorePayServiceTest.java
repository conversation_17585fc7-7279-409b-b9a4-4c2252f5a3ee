package com.charge.api.web.bill.joy;

import com.charge.api.web.BaseTest;
import com.charge.api.web.service.pay.PrestoreService;
import com.charge.api.web.vo.joylife.PrestoreChargeOrderInfo;
import com.charge.api.web.vo.joylife.request.PrestoreOrderRequest;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ChargeStatusEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.config.constant.CommonChargeItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 朝昔预存下单
 */
@Slf4j
public class AddPrestorePayServiceTest extends BaseTest {

    @Resource
    private PrestoreService prestoreService;

    @Test
    public void testCreateBill() throws ChargeBusinessException {
        PrestoreOrderRequest request = new PrestoreOrderRequest();
        request.setCommunityId("1");
        request.setHouseId("1");
        request.setChargeOrderInfoList(buildChargeOrderInfoList());
        // 设置金额
        BigDecimal allMoney = request.getChargeOrderInfoList().stream().map(item -> new BigDecimal(item.getMoney()).setScale(2,
                RoundingMode.HALF_UP)).reduce(BigDecimal.ZERO, BigDecimal::add);
        request.setTotalPrice(allMoney.toString());
        // 设置积分
        BigDecimal allPoints = request.getChargeOrderInfoList().stream()
                .map(item -> StringUtils.isEmpty(item.getPoints()) ? BigDecimal.ZERO : new BigDecimal(item.getPoints())
//                        .setScale(2, RoundingMode.HALF_UP)
                        )
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        request.setTotalPoints(allPoints.toString());
        // 用于创建Pay#Order
        request.setMchCreateIp("123");
        request.setPaymentSource(PaymentTerminalEnum.POS.getCode());
        request.setSubAppid("123");
        request.setSubOpenid("123456");
        request.setUserName("测试");
        request.setUserId("1");
        request.setBuyerLogonId("支付宝账号");
        // 用于构建实收单信息
        request.setEquityAccount("123"); // 权益账号
        request.setPhone("***********");
        request.setMemo("JustTest");
        request.setPaymentMethod("4");
        ChargeResponse<Object> response = prestoreService.addPrestoreInfo(request);
        Assert.assertTrue(Objects.nonNull(response) && response.getCode().equals(ChargeStatusEnum.SUCCESS.getCode()));
    }

    private List<PrestoreChargeOrderInfo> buildChargeOrderInfoList() {
        // 专项预存
        PrestoreChargeOrderInfo specialInfo = new PrestoreChargeOrderInfo();
        specialInfo.setItemId("2");
        specialInfo.setItemName("物业管理费_自测");
        specialInfo.setMoney("10");
        specialInfo.setPoints("20");
        // 通用预存： 通用预存不能存在积分
        PrestoreChargeOrderInfo commonInfo = new PrestoreChargeOrderInfo();
        commonInfo.setItemId(String.valueOf(CommonChargeItem.ITEM_ID));
        commonInfo.setItemName("通用预存");
        commonInfo.setMoney("15");
        // 构建返回值
        return Arrays.asList(specialInfo, commonInfo);
    }
}
