package com.charge.api.web.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.vo.joylife.request.CommunityHouseQueryParams;
import com.charge.api.web.vo.joylife.request.ReceivableBillQueryParams;
import com.charge.common.enums.common.PaymentSourceEnum;

@SpringBootTest
@AutoConfigureMockMvc
class ReceivableBillControllerTest {
    @Resource
    private MockMvc mockMvc;

    @Test
    void getArrearsItemListAllCommunity() throws Exception {
        List<CommunityHouseQueryParams> communityHouseQueryParamsList = new ArrayList<>();
        communityHouseQueryParamsList.add(CommunityHouseQueryParams.builder().communityMsId("1").houseMsIdList(Arrays.asList("1", "2")).build());
        communityHouseQueryParamsList.add(CommunityHouseQueryParams.builder().communityMsId("2").houseMsIdList(Arrays.asList("1", "2")).build());
        ReceivableBillQueryParams params = ReceivableBillQueryParams.builder().phoneNo("123456678").userMsId("12356778").msCommunityInfoList(communityHouseQueryParamsList)
            .paymentSource(PaymentSourceEnum.YUE_PAY.getCode()).build();
        mockMvc.perform(MockMvcRequestBuilders.post("/app/getArrearsItemList/community").content(JSON.toJSONString(params)).contentType(MediaType.APPLICATION_JSON));
    }
}