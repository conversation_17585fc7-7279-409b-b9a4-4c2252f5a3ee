package com.charge.api.web.bill;


import com.charge.api.web.BaseTest;
import com.charge.api.web.config.AssetPayGrayConfig;
import com.charge.common.exception.ChargeBusinessException;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;

public class URIGrayTest extends BaseTest {

    @Resource
    private AssetPayGrayConfig assetPayGrayConfig;

    @Test
    public void testGray() throws ChargeBusinessException {
        // 不存在的URL
        Assert.assertTrue(assetPayGrayConfig.isOpenGray(1L, "/aa/bb"));
        // 设置为all
        Assert.assertTrue(assetPayGrayConfig.isOpenGray(1L, "/addNewPayOrder"));
        // 设置为null
        Assert.assertTrue(assetPayGrayConfig.isOpenGray(1L, "/addCommunityTempChargeInfo"));
        // 设置为多个
        Assert.assertTrue(assetPayGrayConfig.isOpenGray(3L, "/addNewTempChargeInfo"));
    }
}
