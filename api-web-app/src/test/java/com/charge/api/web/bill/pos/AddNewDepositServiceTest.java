package com.charge.api.web.bill.pos;

import com.charge.api.web.BaseTest;
import com.charge.api.web.service.pos.ChargeBillService;
import com.charge.api.web.vo.lakala.AddDepositRequest;
import com.charge.api.web.vo.lakala.PaymentResponse;
import com.charge.common.exception.ChargeBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 缴押金测试
 */
@Slf4j
public class AddNewDepositServiceTest extends BaseTest {
    @Resource
    private ChargeBillService chargeBillService;

    /**
     * POS房间缴押金下单
     */
    @Test
    public void addNewHouseDepositTest() throws ChargeBusinessException {
        AddDepositRequest request = AddDepositRequest.builder()
                .deviceInfo("deviceInfo")
                .mercid("mercid")
                .houseId(2L) // 对应的项目ID为1
                .collectorId("1")
                .collectorName("justTest")
                .money(new BigDecimal(150))
                .memo("POS房间缴押金测试")
                .itemId(5L)
                .itemName("物业管理费_自测")
                .paymentMethod("4")
                .payMember("测试payMember").build();
        PaymentResponse paymentResponse = chargeBillService.addDeposit(request,"/addNewHouseDeposit");
        log.info("paymentResponse = {}", paymentResponse);
        Assert.assertTrue(Objects.nonNull(paymentResponse) && Objects.nonNull(paymentResponse.getId()));
    }


    /**
     * pos小区交押金下单
     */
    @Test
    public void addNewCommunityDepositTest() throws ChargeBusinessException {
        AddDepositRequest request = AddDepositRequest.builder()
                .deviceInfo("deviceInfo")
                .mercid("mercid")
                .communityUuid(2L)
                .collectorId("1")
                .collectorName("justTest")
                .money(new BigDecimal(150))
                .memo("POS房间缴押金测试")
                .itemId(5L)
                .itemName("物业管理费_自测")
                .paymentMethod("4")
                .payMember("测试payMember").build();
        PaymentResponse response = chargeBillService.addDeposit(request, "/addNewCommunityDeposit");
    }
}
