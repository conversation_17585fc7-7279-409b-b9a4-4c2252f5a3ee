package com.charge.api.web.bill.pos;

import com.charge.api.web.BaseTest;
import com.charge.api.web.service.pos.ChargeBillService;
import com.charge.api.web.vo.lakala.CommunityOrdersBillCreateRequest;
import com.charge.api.web.vo.lakala.PaymentResponse;
import com.charge.common.exception.ChargeBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * pos小区批量临时收费下单
 */
@Slf4j
public class AddNewCommunityTempChargeServiceTest extends BaseTest {

    @Resource
    private ChargeBillService chargeBillService;

    @Test
    public void testCreateBill() throws ChargeBusinessException {
        CommunityOrdersBillCreateRequest request = new CommunityOrdersBillCreateRequest(
                "00000104YP620000514431",
                "822290370110494",
                "2",
                // 构建临时订单类
                BuildCommonUtil.buildOrderItemJson(),
                "100",
                "4",
                "1",
                "justTest",
                "用于测试",
                "440606204040062051",
                // arrivalDate属性无使用场景
                null,
                "440606204040062052",
                "440606204040062053",
                "用于测试",
                null);
        PaymentResponse paymentResponse = chargeBillService.createBill(request);
        log.info("PaymentResponse value = {}", paymentResponse);
        Assert.assertTrue(Objects.nonNull(paymentResponse) && Objects.nonNull(paymentResponse.getId()));
    }
}
