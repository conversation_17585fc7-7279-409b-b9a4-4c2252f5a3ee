package com.charge.api.web.support;

import com.charge.bill.client.LockClient;
import com.charge.bill.dto.LockObjectDTO;
import com.charge.bill.enums.BillPrePaySceneEnum;
import com.charge.bill.enums.PaymentMethodEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/01/03
 */
@RestController
public class LockClientImpl implements LockClient {

    private LockObjectDTO existLockObjectDTO;
    @Override
    public ChargeResponse<LockObjectDTO> billPayLock(LockObjectDTO lockObjectDTO) throws ChargeBusinessException {
        if (lockObjectDTO.getMark().equals("abc_1")){
            return new ChargeResponse<>();
        }
        if (lockObjectDTO.getMark().equals("abc_2")){
            ChargeResponse<LockObjectDTO> response = new ChargeResponse(ErrorInfoEnum.E1019);
            if (Objects.isNull(existLockObjectDTO)){
                existLockObjectDTO = LockObjectDTO.builder().mark("abc_1").businessScene(BillPrePaySceneEnum.ZHAOXI_LIFE.getCode())
                        .paymentMethod(PaymentMethodEnum.WECHAT.getPaymentMethod())
                        .receiveBillIdList(new HashSet<>(Arrays.asList(2L, 3L))).build();
            }
            response.setContent(existLockObjectDTO);
            return response;
        }
        return null;
    }

    @Override
    public ChargeResponse billPayUnlock(LockObjectDTO lockObjectDTO) throws ChargeBusinessException {
        return new ChargeResponse();
    }
}
