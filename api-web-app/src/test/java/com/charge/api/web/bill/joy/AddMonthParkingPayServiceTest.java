package com.charge.api.web.bill.joy;

import com.charge.api.web.BaseTest;
import com.charge.api.web.service.joylife.YueAppParkingService;
import com.charge.api.web.vo.joylife.request.YueCardPayRequestVO;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 朝昔月卡缴费：订单类、结转
 */
@Slf4j
public class AddMonthParkingPayServiceTest extends BaseTest {

    @Resource
    private YueAppParkingService yueAppParkingService;

    @Test
    public void testCreateBill() throws ChargeBusinessException {
        YueCardPayRequestVO payVO = new YueCardPayRequestVO();
        // 对应的项目ID为1，对应的资产ID为2
        payVO.setProjectId("07597776");
        payVO.setHouseUuid("15880619829920890945");
        payVO.setEffectiveTime(new Date());
        payVO.setExpireTime(DateUtils.addDays(new Date(), 10));
        payVO.setCardItem(buildCardItem());
        payVO.setAmount(new BigDecimal(50));

        payVO.setPaymentSource(PaymentTerminalEnum.POS.getCode()); // 支付来源参数缺失,请重试
        payVO.setPaymentMethod("4");
        payVO.setMchCreateIp("***********");
        payVO.setSubAppid("123");
        payVO.setSubOpenid("123456");
        payVO.setPayMember("1");
        payVO.setPayMemberId("用于测试");
//        payVO.setBuyerLogonId();

        // 构建结转单
        payVO.setPropertyId("1");
        payVO.setPropertyName("停车场测试");
        payVO.setCarNo("123456");
        payVO.setApiType("1");
        ChargeResponse chargeResponse = yueAppParkingService.createCardPayBills(payVO);
        log.info("ChargeResponse value = {}", chargeResponse);
        Assert.assertTrue(Objects.nonNull(chargeResponse) && Objects.nonNull(chargeResponse.getContent()));
    }


    private String buildCardItem() {
        return "[{\n" +
                "\t\"itemId\": \"435\",\n" +
                "\t\"itemName\": \"车位租赁费\",\n" +
                "\t\"price\": \"50\"\n" +
                "}]";
    }
}
