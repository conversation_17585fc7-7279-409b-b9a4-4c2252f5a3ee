package com.charge.api.web.bill.pos;

import com.charge.api.web.BaseTest;
import com.charge.api.web.service.pos.ChargeBillService;
import com.charge.api.web.vo.lakala.CommunityOrderBillCreateRequest;
import com.charge.api.web.vo.lakala.PaymentResponse;
import com.charge.common.exception.ChargeBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * pos小区临停收费下单：仅订单类
 */
@Slf4j
public class AddCommunityTempServiceTest extends BaseTest {

    @Resource
    private ChargeBillService chargeBillService;

    @Test
    public void addCommunityTempChargeTest() throws ChargeBusinessException {
        CommunityOrderBillCreateRequest request = new CommunityOrderBillCreateRequest(
                "00000104YP620000514431",
                "822290370110494",
                "2",
                // chargeItemUuid为ItemId
                "5",
                "物业管理费_自测",
                // amount: 订单缴费金额
                "100",
                "4",
                "1",
                "justTest",
                "用于测试",
                "440606204040062051",
                // arrivalDate: 无使用场景
                null,
                "用于测试",
                // token: 无使用场景
                null);
        PaymentResponse paymentResponse = chargeBillService.createBill(request);
        log.info("PaymentResponse value = {}", paymentResponse);
        Assert.assertTrue(Objects.nonNull(paymentResponse) && Objects.nonNull(paymentResponse.getId()));
    }
}
