package com.charge.api.web.bill.pos;

import com.charge.api.web.BaseTest;
import com.charge.api.web.dto.pos.AddPredepositRequestDTO;
import com.charge.api.web.service.pos.PreDepositService;
import com.charge.api.web.vo.pos.AddPredepositResultVo;
import com.charge.common.exception.ChargeBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * POS预存充值：缴预存、临时订单
 */
@Slf4j
public class AddNewDepositInfoServiceTest extends BaseTest {

    @Resource
    private PreDepositService preDepositService;

    @Test
    public void addNewDepositInfoTest() throws ChargeBusinessException {
        AddPredepositRequestDTO requestDTO = AddPredepositRequestDTO.builder()
                .assetId(Long.valueOf(1)) // 项目ID为1
                .money("100")       // 总金额   实收单#实收金额
                .payMoney("50")  // 通用预存充值金额   通用预存#金额    缴欠费然后充值？
                .paymentMethod("4")
                .specialPreStoreJson(buildSpecialInfo()) // 专项预存
                .tempBillJson(buildOrderInfo()) // 临时收费
                .collectorId("1")
                .collectorName("justTest")
                .payMember("用于测试")
                .bankAccountNum("440606204040062051")
                .deviceInfo("00000104YP620000514431")
                .mercid("***************")
                .memo("JustTEST")
                .build();
        AddPredepositResultVo resultVo = preDepositService.addNewDepositInfo4House(requestDTO);
        log.info("response = {}", resultVo);
        Assert.assertTrue(Objects.nonNull(resultVo) && Objects.nonNull(resultVo.getId()));
    }

    /**
     * 构建专项预存
     * @return
     */
    private String buildSpecialInfo() {
        return  "[{\n" +
                "\t\"itemName\": \"物业管理费_自测\",\n" +
                "\t\"itemID\": \"5\",\n" +
                "\t\"price\": \"2000\"\n" +
                "}]";
    }

    /**
     * 构建缴费信息
     * @return
     */
    private String buildOrderInfo() {
        return  "[{\n" +
                "\t\"itemName\": \"物业管理费_自测\",\n" +
                "\t\"itemID\": \"5\",\n" +
                "\t\"price\": \"50\"\n" +
                "}]";
    }
}
