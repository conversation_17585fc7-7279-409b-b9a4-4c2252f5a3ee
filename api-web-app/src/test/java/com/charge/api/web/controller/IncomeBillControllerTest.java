package com.charge.api.web.controller;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.BaseTest;
import com.charge.api.web.controller.joylife.bill.IncomeBillController;
import com.charge.api.web.controller.joylife.bill.request.CreateOrderRequest;
import com.charge.api.web.controller.joylife.bill.request.PayOrAdjustItemVO;
import com.charge.api.web.controller.joylife.bill.response.DetailVO;
import com.charge.api.web.controller.joylife.bill.response.ItemsVO;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class IncomeBillControllerTest extends BaseTest {

    @Resource
    private IncomeBillController incomeBillController;

    @Test
    public void queryBillItemsTest() throws ChargeBusinessException {
        List<ItemsVO> result = AppInterfaceUtil.getResponseDataThrowException(incomeBillController.queryBillItems("07597776", Arrays.asList(1765273773302456388L,1765291675804082272L)));
        log.info("itemsVO = {}", JSON.toJSONString(result));
    }

    @Test
    public void createOrderTest() throws ChargeBusinessException {
        log.info("createOrder response = {}", JSON.toJSONString(AppInterfaceUtil.getResponseDataThrowException(incomeBillController.createOrder(buildRequest()))));
    }

    private CreateOrderRequest buildRequest() {
        CreateOrderRequest request = new CreateOrderRequest();
        request.setCommunityMsId("07597776");       // 朝昔侧的项目ID
        request.setAssetId("15880619829920890945"); // 朝昔侧的资产ID
        request.setDepositItems(buildDepositItems());
        request.setOrderItems(buildOrderItems());
        // 设置金额
        BigDecimal orderMoney = request.getOrderItems().stream().map(item -> new BigDecimal(item.getAmount()).setScale(2,
                RoundingMode.HALF_UP)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal depositMoney = request.getDepositItems().stream().map(item -> new BigDecimal(item.getAmount()).setScale(2,
                RoundingMode.HALF_UP)).reduce(BigDecimal.ZERO, BigDecimal::add);
        request.setTotalPrice(orderMoney.add(depositMoney).toString());
        request.setMchCreateIp("123");
        request.setAssetType(1);
        request.setPaymentSource(PaymentTerminalEnum.POS.getCode());
        request.setSubAppid("123");
        request.setSubOpenid("123456");
        request.setUserName("测试");
        request.setUserId("1");
        request.setBuyerLogonId("支付宝账号");
        // 用于构建实收单信息
        request.setPhone("18311112222");
        request.setMemo("JustTest");
        request.setPaymentMethod("4");
        return request;
    }

    /**
     * 构建临时订单类
     * @return
     */
    private List<PayOrAdjustItemVO> buildOrderItems() {
        PayOrAdjustItemVO orderItem = new PayOrAdjustItemVO();
        orderItem.setItemId(5L);
        orderItem.setItemName("物业管理费_自测");
        orderItem.setAmount("50.12");
        return Arrays.asList(orderItem);
    }

    private List<PayOrAdjustItemVO> buildDepositItems() {
        PayOrAdjustItemVO depositItem = new PayOrAdjustItemVO();
        depositItem.setItemId(50L);
        depositItem.setItemName("押金收费项");
        depositItem.setAmount("30");
        return Arrays.asList(depositItem);
    }

}
