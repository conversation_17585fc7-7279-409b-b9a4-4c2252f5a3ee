package com.charge.api.web.controller;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.BaseTest;
import com.charge.api.web.controller.joylife.yuexin.ArrearsQueryController;
import com.charge.api.web.controller.joylife.yuexin.TempOrderController;
import com.charge.api.web.controller.joylife.yuexin.YueXinAppController;
import com.charge.api.web.dto.yuexin.ListArrearsByAssetIdReq;
import com.charge.api.web.util.ResultPage;
import com.charge.api.web.vo.joylife.CommunityMasterData;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.joylife.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class YueXinAppControllerTest extends BaseTest {

    @Resource
    private YueXinAppController yueXinAppController;
    @Resource
    private ArrearsQueryController arrearsQueryController;
    @Resource
    private TempOrderController tempOrderController;

    @Test
    public void listAssetsArrearsInfoTest() throws ChargeBusinessException {
        ListAssetsArrearsReq req = new ListAssetsArrearsReq();
        req.setCommunityMsId("64206188");
        AssetListReqDTO reqDTO1 = new AssetListReqDTO();
        reqDTO1.setAssetType(1);
        reqDTO1.setHouseMsIdList(Lists.newArrayList("1599603058096136205"));
//        AssetListReqDTO reqDTO2 = new AssetListReqDTO();
//        reqDTO2.setAssetType(2);
//        reqDTO2.setHouseMsIdList(Lists.newArrayList("1599603058096136219","1599603058096136217"));

        req.setAssetListReqDTOS(Lists.newArrayList(reqDTO1));
        log.info("结果：{}",yueXinAppController.listAssetsArrearsInfo(req));
    }

    @Test
    public void getArrearsAssetInfoByConditionTest() throws ChargeBusinessException {
        CommunityArrearsAssetsReq req = new CommunityArrearsAssetsReq();
        req.setAssetType(2);
        req.setCommunityMsId("64206188");
        log.info("结果：{}",yueXinAppController.getArrearsAssetInfoByCondition(req));
    }

    @Test
    public void listArrearsByAssetIdTest() throws ChargeBusinessException {
        CommunityMasterData data = new CommunityMasterData();
        data.setAssetType(1);
        data.setCommunityMsId("64206188");
        data.setHouseMsIdList(Lists.newArrayList("381731052916805"));
        yueXinAppController.getArrearsPrestoreItemAmountYueXinNoPage(data);

        ListArrearsByAssetIdReq req = new ListArrearsByAssetIdReq();
        req.setCommunityMsId("64206188");
        req.setAssetType(1);
        req.setHouseMsIdList(Lists.newArrayList("1599603058096136214","1599603058096136207","1599603058096136222","1599603058096136213","1599603058096136220","1599603058096136205","1599603058096136207","1599603058096136206","1599603058096136208","1599603058096136223","381977739657312","381977739657313","381977739657314","386924045967429","387975635660869","381983966789712","387986725273669","387988278517829","388083668869189","381983966789711","381983966789710","381983966789707","381983966789708","381983966789706","381983966789704","391957336444997","391987610169413","381731206656069","1599603058096136206","1599603058096136207","1599603058096136225","1599603058096136205","380995949219909","1599603058096136209","1599603058096136210","1599603058096136226","1599603058096136226","1599603058096136216","1599603058096136226","1599603058096136225","1599603058096136210","1599603058096136209","381977739665478"));
        ResultPage resultPage = arrearsQueryController.listArrearsByAssetId(req);
        System.out.println(resultPage);
    }


    @Test
    public void getArrearsPrestoreItemAmountYueXinNoPageTest() throws ChargeBusinessException {
        CommunityMasterData data = new CommunityMasterData();
        data.setCommunityMsId("64206188");
        data.setHouseMsIdList(Lists.newArrayList("400476705841221","395502032232517","400473644445765"));
        data.setAssetType(2);
        log.info("yueXinAppController.getArrearsPrestoreItemAmountYueXinNoPage()：{}",yueXinAppController.getArrearsPrestoreItemAmountYueXinNoPage(data));
    }

    @Test
    public void listVirtualAssetTest() throws ChargeBusinessException {
        VirtualAssetReq req = VirtualAssetReq.builder().communityMsId("64206188").build();
        List<VirtualAssetDTO> result =  tempOrderController.listVirtualAsset(req).getContent();
        log.info("查询虚拟房屋:{}", JSON.toJSONString(result));

        result.forEach(item -> Assert.assertNotNull("资产id不应为null", item.getAssetId()));
    }

}
