package com.charge.api.web.bill.joy;

import com.charge.api.web.BaseTest;
import com.charge.api.web.service.bill.joy.AddBatchNormalService;
import com.charge.api.web.vo.joylife.request.BatchPayDataRequest;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.domain.response.CreateBillsResponse;
import com.charge.bill.dto.income.AssetArrearsListDTO;
import com.charge.bill.dto.income.AssetReceivalbeBillListDTO;
import com.charge.bill.enums.PaymentMethodEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.AppInterfaceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 朝昔生活缴费
 */
@Slf4j
public class AddBatchNormalServiceTest extends BaseTest {

    @Resource
    private AddBatchNormalService addBatchNormalService;
    @Resource
    private ReceivableBillClient receivableBillClient;


    @Test
    public void testCreateBill() throws ChargeBusinessException {
        CreateBillsResponse createBillsResponse = addBatchNormalService.createBills(buildBatchPayDataRequest(),
                1L,
                // 违约金
                new BigDecimal(50),
                // 欠费金额
                new BigDecimal(130),
                buildAssetArrearsListDTOS(2L),
                "2023-12-01",
                1,
                // orderNum
                IdGeneratorSupport.getIstance().nextId(),
                false,
                123L);
        log.info("createBillsResponse = {}", createBillsResponse);
        Assert.assertTrue(Objects.nonNull(createBillsResponse) && Objects.nonNull(createBillsResponse.getIncomeBillId()));
    }

    private BatchPayDataRequest buildBatchPayDataRequest() {
        BatchPayDataRequest request = new BatchPayDataRequest();
        request.setCommunityId("1");
        request.setCommunityName("测试");
//        request.setTotalPrice("10");  // 转换为IncomeBillEntity时无使用
        request.setActualPrice("10"); // 实收金额
        request.setPointsMoney(null); // 权益调整金额（含积分抵扣金额）
        request.setPoints(null); // 积分数
        request.setEquityAccount(null); // 权益账号
        request.setMemo("JustForTEST");
        request.setPaymentMethod("4");
        request.setUserName("测试");
        request.setUserId("1");
        request.setPhone("***********");
        request.setCustomId("1");
        return request;
    }

    /**
     * 构建一个资产下的缴费项
     * @param assetId
     * @return
     */
    private List<AssetArrearsListDTO> buildAssetArrearsListDTOS(Long assetId) {
        List<AssetArrearsListDTO> result = new ArrayList<>();
        // 非积分
        result.addAll(buildAssetArrearsListDTOS(assetId, PaymentMethodEnum.WECHAT, Arrays.asList(1727929452404514882L)));
        // 积分通积
        result.addAll(buildAssetArrearsListDTOS(assetId, PaymentMethodEnum.EQUITY, Arrays.asList(1727889715698442313L)));
        return result;
    }

    /**
     * 构建缴欠费
     * @return
     */
    private List<AssetArrearsListDTO> buildAssetArrearsListDTOS(Long assetId, PaymentMethodEnum paymentMethodEnum, List<Long> receivableIds) {
        AssetArrearsListDTO assetArrearsListDTO = new AssetArrearsListDTO();
        assetArrearsListDTO.setAssetId(assetId);
        // 支付方式,用于判断构建单据类型，如果是积分EQUITY则创建权益调整单
        assetArrearsListDTO.setPaymentMethod(paymentMethodEnum.getPaymentCode());
        // 缴欠费的应收单信息
        assetArrearsListDTO.setDetailList(buildAssetReceivalbeBillListDTO(receivableIds));
        // 房屋欠费总金额（含违约金）,用于构建AssetTransactionEntity#money
        BigDecimal assetTotalAmount = assetArrearsListDTO.getDetailList().stream()
                .map(dto -> dto.getItemPenaltyAmount().add(dto.getItemArrearsAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
        assetArrearsListDTO.setAssetTotalAmount(assetTotalAmount);
        return Arrays.asList(assetArrearsListDTO);
    }
    private List<AssetReceivalbeBillListDTO> buildAssetReceivalbeBillListDTO(List<Long> receivableIds) {
        ChargeResponse<List<ReceivableBillDTO>> listChargeResponse = receivableBillClient.queryList(ReceivableConditionDTO.builder()
                .communityId(1L)
                .ids(receivableIds).build());
        List<ReceivableBillDTO> recBillList = AppInterfaceUtil.getResponseData(listChargeResponse);
        if (CollectionUtils.isEmpty(recBillList)) {
            return Arrays.asList();
        }
        return recBillList.stream().map(receivableBillDTO -> {
            AssetReceivalbeBillListDTO billListDTO = new AssetReceivalbeBillListDTO();
            billListDTO.setBillId(receivableBillDTO.getId());
            billListDTO.setItemArrearsAmount(receivableBillDTO.getArrearsAmount());
            billListDTO.setItemPenaltyAmount(receivableBillDTO.getPenaltyArrearsAmount());
            billListDTO.setItemId(receivableBillDTO.getItemId());
            billListDTO.setItemName(receivableBillDTO.getItemName());
            billListDTO.setChargeObject(receivableBillDTO.getChargeObject());
            billListDTO.setBelongYears(receivableBillDTO.getBelongYears());
            return billListDTO;
        }).collect(Collectors.toList());
    }
}


