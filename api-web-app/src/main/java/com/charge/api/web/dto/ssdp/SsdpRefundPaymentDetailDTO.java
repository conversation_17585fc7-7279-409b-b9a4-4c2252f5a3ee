package com.charge.api.web.dto.ssdp;

import java.io.Serializable;

import lombok.Data;

/**
 * 退款支付详情
 * 
 * <AUTHOR>
 * @date 2023/2/27
 */
@Data
public class SsdpRefundPaymentDetailDTO implements Serializable {

    private static final long serialVersionUID = -3170463853211809397L;

    /**
     * 支付时间，格式：yyyy-MM-dd HH:mm:ss SSS
     */
    private String paymentTime;

    /**
     * 支付金额，单元：元
     */
    private String paymentAmount;

    /**
     * 支付银行名称
     */
    private String paymentBankName;

    /**
     * 支付银行账号
     */
    private String paymentBankAccount;

    /**
     * 户名
     */
    private String accountName;

    /**
     * 开户行
     */
    private String accountOpeningBank;

}
