package com.charge.api.web.service.order;

import com.charge.bill.enums.ChargeTypeEnum;
import com.charge.common.enums.common.ChargeObjEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 账单业务
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ReceivablePayBillBusiness extends BillBusiness {

    /**
     * 所属年月
     */
    private String belongYears;

    /**
     * 应收单id
     */
    private Long receivableBillId;

    /**
     * 收费类型
     */
    private ChargeTypeEnum chargeType;

    /**
     * 收费对象
     */
    private ChargeObjEnum chargeObject;

    private String billNum;

    /**
     * 资产使用状态(可传，不传时会在bill中填充)
     */
    private Integer assetUseStatus;


}


