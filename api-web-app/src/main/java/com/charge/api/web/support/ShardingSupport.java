package com.charge.api.web.support;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.google.common.collect.Lists;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/4
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ShardingSupport {

    private final ReceivableBillClient receivableBillClient;
    private final ThreadPoolTaskExecutor chargeCommonExecutor;

    /**
     * 分页查询应收单
     *
     * @param receivableConditionDTO
     * @return
     */
    public List<ReceivableBillDTO> pageReceivableBill(ReceivableConditionDTO receivableConditionDTO) {

        List<ReceivableBillDTO> receivableBills = new ArrayList<>();

        List<Long> communityIds = receivableConditionDTO.getCommunityIds();

        String txn = ThreadContext.peek();

        if (CollectionUtil.isEmpty(communityIds)) {
            // 构建项目数组异步查询
            communityIds = Arrays.asList(receivableConditionDTO.getCommunityId());
        }

        List<CompletableFuture<List<ReceivableBillDTO>>> futures = Lists.newArrayListWithCapacity(communityIds.size());

        communityIds.stream().forEach(communityId -> {

            ReceivableConditionDTO conditionDTO = BeanCopierWrapper.copy(receivableConditionDTO, ReceivableConditionDTO.class);
            conditionDTO.setCommunityId(communityId);
            // 置空项目id列表
            conditionDTO.setCommunityIds(null);

            futures.add(CompletableFuture.supplyAsync(() -> {
                // 跨多个项目，并发分表查询
                ThreadContext.push(txn);
                ChargeResponse<PagingDTO<ReceivableBillDTO>> response = receivableBillClient.queryByPage(conditionDTO);
                try {
                    PagingDTO<ReceivableBillDTO> pagingDTO = AppInterfaceUtil.getDataThrowException(response);
                    return pagingDTO.getList();
                } catch (ChargeBusinessException e) {
                    log.error("{}|查询应收单异常|conditionDTO={}", LogCategoryEnum.BUSSINESS, conditionDTO);
                    return null;
                } finally {
                    ThreadContext.pop();
                }

            }, chargeCommonExecutor));

        });
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()])).join();
        futures.stream().forEach(future -> {
            try {
                List<ReceivableBillDTO> futureReceivalbeBills = future.get();
                if (CollectionUtil.isNotEmpty(futureReceivalbeBills)) {
                    receivableBills.addAll(futureReceivalbeBills);
                }
            } catch (Exception e) {
                log.error("{}|聚合多项目应收单异常", LogCategoryEnum.BUSSINESS, e);
            }
        });

        log.info("{}|项目id={},应收单笔数={}", LogCategoryEnum.BUSSINESS, communityIds, receivableBills.size());

        return receivableBills;
    }
}
