package com.charge.api.web.service.pos.impl;

import com.charge.api.web.convert.EBusinessOrderConverter;
import com.charge.api.web.service.pos.OrderService;
import com.charge.api.web.vo.pos.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.order.client.EBusinessMasterOrderClient;
import com.charge.order.client.OrderInstallmentClient;
import com.charge.order.dto.ebuiness.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单服务实现类
 *
 * <AUTHOR>
 * @date 2024/11/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {

    private final OrderInstallmentClient orderInstallmentClient;

    private final EBusinessMasterOrderClient eBusinessMasterOrderClient;

    @Override
    public List<OrderInstallmentVO> listInstallment(InstallmentQuery installmentQuery) throws ChargeBusinessException {
        ChargeResponse<List<OrderInstallmentDTO1>> response = orderInstallmentClient.rollList(installmentQuery);
        List<OrderInstallmentDTO1> orderInstallmentDTO1List = AppInterfaceUtil.getResponseDataThrowException(response);
        return orderInstallmentDTO1List.stream().map(a -> {
            OrderInstallmentVO vo = EBusinessOrderConverter.INSTANCE.map(a);
            vo.fillVo();
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public OrderInstallmentWithItemsVO installmentDetail(InstallmentDetailQuery conditionDTO) throws ChargeBusinessException {
        ChargeResponse<OrderInstallmentDetailDTO> chargeResponse = orderInstallmentClient.detail(conditionDTO);
        OrderInstallmentDetailDTO installmentDetailDTO = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        return toOrderInstallmentWithItemsVO(installmentDetailDTO);
    }

    private OrderInstallmentWithItemsVO toOrderInstallmentWithItemsVO(OrderInstallmentDetailDTO installmentDetailDTO) {
        List<EBusinessBaseOrderVO1> orders = installmentDetailDTO.getEBusinessBaseOrders().stream().map(baseOrderDTO -> {
            EBusinessBaseOrderVO1 baseOrderVO1 = EBusinessOrderConverter.INSTANCE.mapV1(baseOrderDTO);
            baseOrderVO1.setTotalAmount(baseOrderDTO.getAmountOfInstallment());
            baseOrderVO1.setTotalPaidAmount(baseOrderDTO.getPaymentAmountOfInstallment());
            baseOrderVO1.fillAmount();
            return baseOrderVO1;
        }).collect(Collectors.toList());
        OrderInstallmentDTO orderInstallmentDTO = installmentDetailDTO.getOrderInstallmentDTO();
        OrderInstallmentVO installmentVO = EBusinessOrderConverter.INSTANCE.map(orderInstallmentDTO);
        installmentVO.fillVo();
        return new OrderInstallmentWithItemsVO(installmentVO, orders);
    }

    @Override
    public List<EBusinessMasterOrderVO> rollListPaidEBusinessMasterOrder(EBusinessMasterOrderQuery orderQuery) throws ChargeBusinessException {
        ChargeResponse<List<EBusinessMasterOrderDTO>> chargeResponse = eBusinessMasterOrderClient.rollList(orderQuery);
        List<EBusinessMasterOrderDTO> orders = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        return orders.stream().map(EBusinessOrderConverter.INSTANCE::map).collect(Collectors.toList());
    }

    @Override
    public EBusinessMasterOrderWithItemsVO masterOrderDetail(EBusinessMasterOrderDetailQuery detailQuery) throws ChargeBusinessException {
        ChargeResponse<EBusinessMasterOrderDetailDTO> chargeResponse = eBusinessMasterOrderClient.detail(detailQuery);
        EBusinessMasterOrderDetailDTO detailDTO = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        EBusinessMasterOrderVO masterOrderVO = EBusinessOrderConverter.INSTANCE.map(detailDTO.getMasterOrderDTO());
        List<OrderInstallmentWithItemsVO> installmentWithItemsVOS = detailDTO.getEBusinessBaseOrders().stream().map(this::toOrderInstallmentWithItemsVO).collect(Collectors.toList());
        return new EBusinessMasterOrderWithItemsVO(masterOrderVO, installmentWithItemsVOS);
    }
}
