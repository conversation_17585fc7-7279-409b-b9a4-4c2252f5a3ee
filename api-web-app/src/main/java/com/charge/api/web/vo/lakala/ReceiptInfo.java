package com.charge.api.web.vo.lakala;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
public class ReceiptInfo {
    private String id;

    private String referNo;

    private String companyId;

    private String companyName;

    private String communityId;

    private String communityName;

    private String chargerId;

    private String chargerName;

    private String payTime;

    private String payerId;

    private String payerName;

    private String houseId;

    private String payHouse;

    private String payType;

    private BigDecimal payfee;

    private String url;

    private String orderNo;

    private String receiptNo;

    private String chargeItemInfo;

    private String lastPrintTime;

    private String printer;

    private Integer printTimes;

    private String payId;

    private String createTime;

    private String updateTime;

    private String cropId;

    private String bankReferNo;

    private String arrivalTime;

    private String massBillId;
}