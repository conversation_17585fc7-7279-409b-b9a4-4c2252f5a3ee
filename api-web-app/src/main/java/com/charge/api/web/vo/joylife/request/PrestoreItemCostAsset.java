package com.charge.api.web.vo.joylife.request;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/6/11 10:51
 */
@Data
public class PrestoreItemCostAsset implements Serializable {

    private static final long serialVersionUID = 3295742844983308644L;

    /**
     * 收费系统房间id
     */
    private Long houseId;

    /**
     * 朝昔房屋id
     */
    private String assetMsId;

    /**
     * 朝昔房屋id类型 1房间  2车位
     */
    private Integer assetType;

}
