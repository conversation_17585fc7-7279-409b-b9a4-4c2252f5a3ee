package com.charge.api.web.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/20
 */
@Data
@ConfigurationProperties(prefix = "thread")
@Configuration
@RefreshScope
public class ThreadPoolConfig {

    private Integer corePoolSize;

    private Integer maxPoolSize;

    private Integer keepAliveSeconds;

    private Integer queueCapacity;
}
