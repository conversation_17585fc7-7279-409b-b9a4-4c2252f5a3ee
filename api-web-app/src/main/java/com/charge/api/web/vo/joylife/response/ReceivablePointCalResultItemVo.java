package com.charge.api.web.vo.joylife.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-08-07 09:57
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceivablePointCalResultItemVo implements Serializable {

    private static final long serialVersionUID = -2141395464985518559L;

    /**
     * 资产id
     */
    private String assetId;

    /**
     * 赠送的万象星数量
     */
    private Integer giftStarCount;

}
