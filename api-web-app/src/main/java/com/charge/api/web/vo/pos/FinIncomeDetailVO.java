package com.charge.api.web.vo.pos;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * [收费细项]
 *
 * <AUTHOR> wu<PERSON><PERSON>
 * @version : [v1.0]
 */
@Data
public class FinIncomeDetailVO implements Serializable {

    private static final long serialVersionUID = 3335340998070344017L;
    private String incomeDetailUuid;

    private String communityUuid;

    private String communityName;

    private String incomeItemUuid;

    private String itemUuid;

    private String incomeDetailName;

    /**
     * SBU段Uuid
     */
    private String sbuSegmentUuid;

    /**
     * 成本中心段Uuid
     */
    private String costCenterUuid;

    /**
     * 弹性域-交易对象Uuid
     */
    private String statementTransactionUuid;

    private String relationSegment;

    private String relationSegmentName;

    private String status;

    private String memo;

    private Date updateTs;

    private Date createTs;

    private String createUser;

    private String createUserId;

    private String updateUser;

    private String updateUserId;

    private String detailType;
}
