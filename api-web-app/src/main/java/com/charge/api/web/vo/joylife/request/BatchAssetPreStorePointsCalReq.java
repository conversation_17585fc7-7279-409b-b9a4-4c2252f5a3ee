package com.charge.api.web.vo.joylife.request;

import lombok.Data;

import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/6/12 14:51
 */
@Data
public class BatchAssetPreStorePointsCalReq extends ZhaoXiCommunityReq {


    /**
     * 朝昔项目id（当不传收费的项目id时需要）
     */
    private String communityMsId;

    /**
     * 收费的项目id
     */
    private Long communityId;

    /**
     * 资产费项信息
     */
    private List<BatchAssetPrestore> assetPrestores;
}
