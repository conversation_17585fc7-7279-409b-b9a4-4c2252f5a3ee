package com.charge.api.web.vo.joylife.response;

import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrestoreDiscountConfig implements Serializable {

	Long communityId;
	String communityMsId;
	String houseMsId;
	Long houseId;
	String arrearsAmount;

	public static List<PrestoreDiscountConfig> from(Map<Long, List<CommunityDTO>>  communityMap,List<AssetDTO> houseList, List<ReceivableBillDTO> orderItems) {
		List<PrestoreDiscountConfig> result = new ArrayList<>();
		Map<Long,List<ReceivableBillDTO>> orderGroupByOwnerId=new HashMap<>();
		if(!CollectionUtils.isEmpty(orderItems)){
			orderGroupByOwnerId=orderItems.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId));
		}
		for (AssetDTO house : houseList) {
			String communityMs = null;
            Long communityId=null;
			String houseMsId=null;
			if (Objects.equals(AssetTypeEnum.HOUSE.getCode(), house.getType())) {
				communityId=house.getHouseDTO().getCommunityId();
				houseMsId=house.getHouseDTO().getMsId();
				List<CommunityDTO> communityList = communityMap.get(communityId);
				if (!CollectionUtils.isEmpty(communityList)) {
					communityMs = communityList.get(0).getMsId();
				}
			} else if(Objects.nonNull(house.getParkingSpaceDTO())){
				communityId=house.getParkingSpaceDTO().getCommunityId();
				List<CommunityDTO> communityList = communityMap.get(communityId);
				houseMsId=house.getParkingSpaceDTO().getMsId();
				if (!CollectionUtils.isEmpty(communityList)) {
					communityMs = communityList.get(0).getMsId();
				}
			}

			BigDecimal amount=BigDecimal.ZERO;
			if(orderGroupByOwnerId.get(house.getId())!=null && !CollectionUtils.isEmpty(orderGroupByOwnerId.get(house.getId()))){
				Optional<BigDecimal> amountOptional=orderGroupByOwnerId.get(house.getId()).stream()
						.map(orderItemBS->(orderItemBS.getArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getArrearsAmount()).add(orderItemBS.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getPenaltyArrearsAmount()))
						.reduce(BigDecimal::add);
				amount=amountOptional.isPresent()?amountOptional.get():BigDecimal.ZERO;
			}
			amount.setScale(2, RoundingMode.HALF_UP);
			result.add(PrestoreDiscountConfig.builder()
					.communityId(communityId)
					.houseId(house.getId())
					.houseMsId(houseMsId)
					.communityMsId(communityMs)
					.arrearsAmount(amount.toString())
					.build());
		}
		return result;
	}
}
