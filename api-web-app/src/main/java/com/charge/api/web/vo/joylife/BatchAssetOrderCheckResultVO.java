package com.charge.api.web.vo.joylife;

import com.charge.config.dto.item.PreStoreItemConfigDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 批量资产下单校验实体
 * @Author: yjw
 * @Date: 2024/6/20 10:27
 */
@Data
public class BatchAssetOrderCheckResultVO implements Serializable {

    private static final long serialVersionUID = -3427610980807578114L;

    /**
     * 小区商户号配置是否开启（1-未开启，0-开启）
     */
    private Integer disableStatus;

    /**
     * 小区预存功能是否开启（0-未开启，1-开启）
     */
    private Integer prestoreChargeStatus;

    /**
     * 小区缴费状态说明
     */
    private String message;

    /**
     * 收费系统-项目ID
     */
    private Long communityId;

    /**
     * 收费系统-资产ID列表
     */
    private List<Long> assetIds;

    /**
     * 预存收费项配置信息
     */
    private List<PreStoreItemConfigDTO> preStoreItemConfigDTOS;

}
