package com.charge.api.web.vo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 基础订单操作请求参数
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BaseOrderCmdReq extends BaseOrderReq{
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 备注
     */
    private String memo;
}