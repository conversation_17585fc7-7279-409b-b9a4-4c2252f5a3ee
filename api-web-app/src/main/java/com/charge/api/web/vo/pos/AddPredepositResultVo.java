package com.charge.api.web.vo.pos;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: zhengcanbiao
 */
@Data
public class AddPredepositResultVo {

    @ApiModelProperty("交易id - transactionId")
    private String id;

    @ApiModelProperty("订单号")
    private String orderNum;

    /**
     * 1.0有下面三个字段，但是用于金融平台返回信息，ipos项目备注了弃用了，所以注释掉
     */
//    private String code_url;
//    private String tno;
//    private String payType;
}
