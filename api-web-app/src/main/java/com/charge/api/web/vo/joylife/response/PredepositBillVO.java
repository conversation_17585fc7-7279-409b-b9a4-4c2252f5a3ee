package com.charge.api.web.vo.joylife.response;

import com.charge.common.serializer.IdSerializer;
import com.charge.common.serializer.IdsSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PredepositBillVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonSerialize(using = IdSerializer.class)
    private Long id;

    @ApiModelProperty("资产id")
    private Long assetId;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("资产流水id")
    @JsonSerialize(using = IdSerializer.class)
    private Long assetTransactionId;

    @ApiModelProperty("预收账户id")
    private Long predepositAccountId;

    @ApiModelProperty("预收金额")
    private BigDecimal predepositMoney;

    @ApiModelProperty("支付类型: 0 支付，1待结转，2红冲调整，3预收调整")
    private Integer payType;

    @ApiModelProperty("关联id：pay_type为3时，值为predeposit_adjust.id")
    private Long relationId;

    @ApiModelProperty("支付账号")
    private String paymentAccount;

    @ApiModelProperty("支付时间")
    private Timestamp paymentTime;

    @ApiModelProperty("收款账号")
    private String receiveAccount;

    @ApiModelProperty("单据号")
    private String billNum;

    @ApiModelProperty("收据编号")
    private String receiptNumber;

    @ApiModelProperty("预收费项id(通用预存：0)")
    private Long predepositItemId;

    @ApiModelProperty("预存项名称")
    private String predepositItemName;

    @ApiModelProperty("收费细项ID")
    private Long incomeDetailUuid;

    @ApiModelProperty("收费细项名称")
    private String incomeDetailName;

    @ApiModelProperty("预存类型(0表示通用预存，1表示专项预存，2待结转 3押金)")
    private Integer predepositType;

    @ApiModelProperty("是否对账：0否，1是")
    private Integer isBalance;

    @ApiModelProperty("是否已对账:默认0未对账，1已对账")
    private Integer balanceStatus;

    @ApiModelProperty("入账标志(0表示未入账,1表示入账中，2表示已入账)")
    private Integer enterStatus;

    @ApiModelProperty("账单状态(0表示正常,1表示已作废,2表示已红冲,3表示红冲退款中,4部分退款,5退款完成,6部分退款中,7退款中,8退款失败,9待支付,10已关闭)")
    private Integer billStatus;

    @ApiModelProperty("所属年月")
    private String belongYears;

    @ApiModelProperty("收费对象：0=业户，1=开发商")
    private Integer chargeObject;

    @ApiModelProperty("凭证ID")
    @JsonSerialize(using = IdsSerializer.class)
    private List<Long> certificateIds;

    @ApiModelProperty("凭证类型，枚举类型查看BillTypeDTO")
    private Integer certificateBillType;

    @ApiModelProperty("关联单据信息 - 批量返回")
    private List<BillRelateInfoVO> billRelateInfoList = new ArrayList<>();

    /**
     *是否赠送万象星：0否，1是
     */
    private Integer isEarnPoints;

    /**
     * 赠送万象星数量
     */
    private Integer earnPoints;

    /**
     * 抵扣金额
     */
    private BigDecimal deductAmount;

    /**
     * 调整金额
     */
    private BigDecimal adjustAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
}
