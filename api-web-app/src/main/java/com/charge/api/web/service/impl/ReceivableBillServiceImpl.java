package com.charge.api.web.service.impl;

import com.charge.api.web.constants.JoyLifeErrorMessage;
import com.charge.api.web.service.joylife.ReceivableBillService;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.vo.joylife.request.HomePayItemDetailConditionVO;
import com.charge.api.web.vo.joylife.request.HousePayItemListConditionVO;
import com.charge.api.web.vo.joylife.response.HomePayItemDetailItemVO;
import com.charge.api.web.vo.joylife.response.HomePayItemDetailVO;
import com.charge.api.web.vo.joylife.response.HousePayItemListVO;
import com.charge.api.web.vo.joylife.response.HousePayItemVO;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.JoyLifeErrorInfoEnum;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.maindata.pojo.dto.HouseDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.charge.api.web.service.pos.impl.ChargeBillServiceImpl.nullStr;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/07
 */
@Slf4j
@Service
public class ReceivableBillServiceImpl implements ReceivableBillService {

    @Autowired
    private AssetSupport assetSupport;

    @Autowired
    private ReceivableBillClient receivableBillClient;

    @Override
    public ChargeResponse<HousePayItemListVO> getHousePayItemList(HousePayItemListConditionVO conditionVO) {
        HouseDTO houseInfo = assetSupport.getHouseInfo(conditionVO.getHouseUuid());
        ChargeResponse chargeResponse = new ChargeResponse();
        if (Objects.isNull(houseInfo)) {
            chargeResponse.setCode(JoyLifeErrorInfoEnum.FAIL.getCode());
            chargeResponse.setMessage(JoyLifeErrorMessage.HOUSE_NOT_EXISTS);
            return chargeResponse;
        }

        ReceivableConditionDTO receivableConditionDTO = new ReceivableConditionDTO();
        receivableConditionDTO.setAssetId(houseInfo.getId());
        receivableConditionDTO.setBelongYearsStart(conditionVO.getBelongStartYear());
        receivableConditionDTO.setBelongYearsEnd(conditionVO.getBelongEndYear());
        receivableConditionDTO.setCommunityId(houseInfo.getCommunityId());
        receivableConditionDTO.setPageNum(1);
        receivableConditionDTO.setPageSize(9999);

        ChargeResponse<PagingDTO<ReceivableBillDTO>> receivableBillResponse = receivableBillClient.queryByPage(receivableConditionDTO);
        List<ReceivableBillDTO> receivableBillDTOS = receivableBillResponse.getContent().getList();

        Map<String, List<ReceivableBillDTO>> monthReceivableBillDTOMap = receivableBillDTOS.stream()
                .collect(Collectors.groupingBy(ReceivableBillDTO::getBelongYears));
        String earliestMonth = receivableBillDTOS.stream().map(ReceivableBillDTO::getBelongYears).distinct()
                .min(String::compareTo).get();
        String latestMonth = receivableBillDTOS.stream().map(ReceivableBillDTO::getBelongYears).distinct()
                .max(String::compareTo).get();

        YearMonth yearMonth = YearMonth.parse(latestMonth);
        YearMonth endYearMonth = YearMonth.parse(earliestMonth);
        List<HousePayItemVO> itemVOS = new ArrayList<>();

        // 从最近的年月~最远的年月，每个月都必须有值
        while (yearMonth.isAfter(endYearMonth)) {
            HousePayItemVO itemVO = buildHousePayItemVO(monthReceivableBillDTOMap.get(yearMonth.toString()));
            itemVO.setBelongYear(yearMonth.toString());
            itemVOS.add(itemVO);
            yearMonth = yearMonth.minus(1, ChronoUnit.MONTHS);
        }

        HousePayItemListVO vo = BeanCopierWrapper.copy(houseInfo, HousePayItemListVO.class);
        vo.setHouseId(houseInfo.getMsId());
        BigDecimal totalArrears = itemVOS.stream().map(HousePayItemVO::getArrearsPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setTotalArrears(totalArrears);
        vo.setItemList(itemVOS);

        return new ChargeResponse<>(vo);
    }

    @Override
    public ChargeResponse<HomePayItemDetailVO> getHomePayItemDetail(HomePayItemDetailConditionVO conditionVO) {
        HouseDTO houseInfo = assetSupport.getHouseInfo(conditionVO.getHouseUuid());
        ChargeResponse chargeResponse = new ChargeResponse();
        if (Objects.isNull(houseInfo)) {
            chargeResponse.setCode(JoyLifeErrorInfoEnum.FAIL.getCode());
            chargeResponse.setMessage(JoyLifeErrorMessage.HOUSE_NOT_EXISTS);
            return chargeResponse;
        }

        ReceivableConditionDTO receivableConditionDTO = new ReceivableConditionDTO();
        receivableConditionDTO.setAssetId(houseInfo.getId());
        receivableConditionDTO.setCommunityId(houseInfo.getCommunityId());
        receivableConditionDTO.setBelongYears(conditionVO.getBelongYear());
        receivableConditionDTO.setPageNum(1);
        receivableConditionDTO.setPageSize(9999);

        ChargeResponse<PagingDTO<ReceivableBillDTO>> receivableBillResponse = receivableBillClient.queryByPage(receivableConditionDTO);
        List<ReceivableBillDTO> receivableBillDTOS = receivableBillResponse.getContent().getList();

        Map<Long, List<ReceivableBillDTO>> itemReceivableBillDTOMap = receivableBillDTOS.stream()
                .collect(Collectors.groupingBy(ReceivableBillDTO::getItemId));

        List<HomePayItemDetailItemVO> itemVOS = new ArrayList<>();
        for (Map.Entry<Long, List<ReceivableBillDTO>> entry : itemReceivableBillDTOMap.entrySet()) {
            HomePayItemDetailItemVO itemVO = buildHomePayItemDetailItemVO(entry.getValue());
            itemVOS.add(itemVO);

        }
        HomePayItemDetailVO homePayItemDetailVO = buildHomePayItemDetailVO(houseInfo, itemVOS);

        return new ChargeResponse(homePayItemDetailVO);
    }

    private HousePayItemVO buildHousePayItemVO(List<ReceivableBillDTO> itemReceivableBillDTOS) {
        if (CollectionUtils.isEmpty(itemReceivableBillDTOS)) {
            HousePayItemVO itemVO = new HousePayItemVO();
            return itemVO;
        }

        ReceivableBillTotalAmount receivableBillTotalAmount = buildReceivableBillTotalAmount(itemReceivableBillDTOS);
        ReceivableBillDTO receivableBillDTO = itemReceivableBillDTOS.get(0);
        HousePayItemVO itemVO = BeanCopierWrapper.copy(receivableBillTotalAmount, HousePayItemVO.class);
        itemVO.setBelongYear(receivableBillDTO.getBelongYears());

        return itemVO;
    }

    private HomePayItemDetailItemVO buildHomePayItemDetailItemVO(List<ReceivableBillDTO> itemReceivableBillDTOS) {

        ReceivableBillTotalAmount receivableBillTotalAmount = buildReceivableBillTotalAmount(itemReceivableBillDTOS);

        ReceivableBillDTO receivableBillDTO = itemReceivableBillDTOS.get(0);
        HomePayItemDetailItemVO itemDetailItemVO = BeanCopierWrapper.copy(receivableBillTotalAmount, HomePayItemDetailItemVO.class);
        itemDetailItemVO.setItemId(nullStr(receivableBillDTO.getItemId()));
        itemDetailItemVO.setItemName(receivableBillDTO.getItemName());

        return itemDetailItemVO;
    }

    private HomePayItemDetailVO buildHomePayItemDetailVO(HouseDTO houseInfo, List<HomePayItemDetailItemVO> itemVOS) {

        HomePayItemDetailVO result = BeanCopierWrapper.copy(houseInfo, HomePayItemDetailVO.class);
        ReceivableBillTotalAmount receivableBillTotalAmount = new ReceivableBillTotalAmount();
        result.setHouseId(houseInfo.getMsId());

        for (HomePayItemDetailItemVO itemVO : itemVOS) {
            receivableBillTotalAmount.totalAmount = receivableBillTotalAmount.totalAmount.add(itemVO.getSumPrice());
            receivableBillTotalAmount.totalArrears = receivableBillTotalAmount.totalArrears.add(itemVO.getArrearsPrice());
            receivableBillTotalAmount.totalPay = receivableBillTotalAmount.totalPay.add(itemVO.getPayPrice());
            receivableBillTotalAmount.totalToPay = receivableBillTotalAmount.totalToPay.add(itemVO.getToPayPrice());
            receivableBillTotalAmount.totalHold = receivableBillTotalAmount.totalHold.add(itemVO.getToPayPrice());
            receivableBillTotalAmount.totalCollection = receivableBillTotalAmount.totalCollection.add(itemVO.getCollectionPrice());
            receivableBillTotalAmount.totalPenaltyAmount = receivableBillTotalAmount.totalPenaltyAmount.add(itemVO.getSumPenaltyPrice());
        }
        result.setTotalAmount(receivableBillTotalAmount.totalAmount);
        result.setTotalArrears(receivableBillTotalAmount.totalArrears);
        result.setTotalPay(receivableBillTotalAmount.totalPay);
        result.setTotalToPay(receivableBillTotalAmount.totalToPay);
        result.setTotalHold(receivableBillTotalAmount.totalHold);
        result.setTotalCollection(receivableBillTotalAmount.totalCollection);
        result.setTotalPenaltyAmount(receivableBillTotalAmount.totalPenaltyAmount);
        result.setItemList(itemVOS);
        return result;
    }


    private ReceivableBillTotalAmount buildReceivableBillTotalAmount(List<ReceivableBillDTO> itemReceivableBillDTOS) {
        ReceivableBillTotalAmount receivableBillTotalAmount = new ReceivableBillTotalAmount();
        if (CollectionUtils.isEmpty(itemReceivableBillDTOS)) {
            return receivableBillTotalAmount;
        }

        for (ReceivableBillDTO itemReceivableBillDTO : itemReceivableBillDTOS) {

            // 应收金额
            BigDecimal receivableAmount = getAmount(itemReceivableBillDTO.getReceivableAmount());
            // 违约金应收
            BigDecimal receivablePenaltyAmount = getAmount(itemReceivableBillDTO.getPenaltyReceivable());
            // 欠收金额
            BigDecimal arrearsAmount = getAmount(itemReceivableBillDTO.getArrearsAmount());
            // 违约金欠收
            BigDecimal penaltyArrearsAmount = getAmount(itemReceivableBillDTO.getPenaltyArrearsAmount());
            // 核销金额
            BigDecimal incomeAmount = getAmount(itemReceivableBillDTO.getIncomeAmount());
            // 折扣金额
            BigDecimal discountMoney = getAmount(itemReceivableBillDTO.getDiscountMoney());
            // 违约金实收
            BigDecimal penaltyIncomeAmount = getAmount(itemReceivableBillDTO.getPenaltyIncomeAmount());
            receivableBillTotalAmount.sumPrice = receivableBillTotalAmount.sumPrice.add(receivableAmount).add(receivablePenaltyAmount);
            receivableBillTotalAmount.arrearsPenaltyPrice = receivableBillTotalAmount.arrearsPenaltyPrice.add(penaltyArrearsAmount);
            receivableBillTotalAmount.arrearsPrice = receivableBillTotalAmount.arrearsPrice.add(arrearsAmount).add(penaltyArrearsAmount);
            receivableBillTotalAmount.discountPrice = receivableBillTotalAmount.discountPrice.add(discountMoney);
            receivableBillTotalAmount.sumPenaltyPrice = receivableBillTotalAmount.sumPenaltyPrice.add(receivablePenaltyAmount);
            receivableBillTotalAmount.payPenaltyPrice = receivableBillTotalAmount.payPenaltyPrice.add(penaltyIncomeAmount);
            receivableBillTotalAmount.payPrice = receivableBillTotalAmount.payPrice.add(incomeAmount).add(penaltyIncomeAmount);

            Integer payStatus = itemReceivableBillDTO.getPayStatus();
            Integer billStatus = itemReceivableBillDTO.getBillStatus();
            if (Objects.equals(ReceivableBillPayStatusEnum.COLLECTION.getCode(), payStatus)) {
                receivableBillTotalAmount.collectionPrice = receivableBillTotalAmount.collectionPrice.add(arrearsAmount).add(penaltyArrearsAmount);
                receivableBillTotalAmount.otherPenaltyPrice = receivableBillTotalAmount.otherPenaltyPrice.add(penaltyArrearsAmount);
            } else if (Objects.equals(ReceivableBillPayStatusEnum.PAY_WAIT.getCode(), payStatus)) {
                receivableBillTotalAmount.otherPenaltyPrice = receivableBillTotalAmount.otherPenaltyPrice.add(penaltyArrearsAmount);
                receivableBillTotalAmount.toPayPrice = receivableBillTotalAmount.toPayPrice.add(arrearsAmount).add(penaltyArrearsAmount);
            }

            if (Objects.equals(ReceivableBillStatusEnum.BILL_HOLD.getCode(), billStatus)) {
                receivableBillTotalAmount.otherPenaltyPrice = receivableBillTotalAmount.otherPenaltyPrice.add(penaltyArrearsAmount);
                receivableBillTotalAmount.holdPrice = receivableBillTotalAmount.holdPrice.add(arrearsAmount).add(penaltyArrearsAmount);
            }

        }

        return receivableBillTotalAmount;
    }

    @Data
    class ReceivableBillTotalAmount {
        // 合计金额(应收+违约金应收)
        BigDecimal sumPrice = BigDecimal.ZERO;
        // 违约金欠收
        BigDecimal arrearsPenaltyPrice = BigDecimal.ZERO;
        // 欠费合计金额（含违约金）
        BigDecimal arrearsPrice = BigDecimal.ZERO;
        // 折扣金额
        BigDecimal discountPrice = BigDecimal.ZERO;
        // 托收中欠费
        BigDecimal collectionPrice = BigDecimal.ZERO;
        // 违约金应收金额
        BigDecimal sumPenaltyPrice = BigDecimal.ZERO;
        // 其他状态下违约金欠费合计金额
        BigDecimal otherPenaltyPrice = BigDecimal.ZERO;
        // 违约金已收合计金额
        BigDecimal payPenaltyPrice = BigDecimal.ZERO;
        // 实收合计金额（含违约金）
        BigDecimal payPrice = BigDecimal.ZERO;
        // 待支付金额（含违约金）
        BigDecimal toPayPrice = BigDecimal.ZERO;
        // 挂起合计金额
        BigDecimal holdPrice = BigDecimal.ZERO;

        // 总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        // 总欠费
        BigDecimal totalArrears = BigDecimal.ZERO;
        // 已收金额
        BigDecimal totalPay = BigDecimal.ZERO;
        // 待支付金额
        BigDecimal totalToPay = BigDecimal.ZERO;
        // 挂起状态下欠费总金额
        BigDecimal totalHold = BigDecimal.ZERO;

        // 托收状态下欠费总金额
        BigDecimal totalCollection = BigDecimal.ZERO;
        // 总违约金金额
        BigDecimal totalPenaltyAmount = BigDecimal.ZERO;
    }

    private BigDecimal getAmount(BigDecimal amount) {
        return Objects.isNull(amount) ? BigDecimal.ZERO : amount;
    }

}
