package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class ChargeItemPrice {
    /**
     * 应收单id
     */
    @JsonProperty("itemId")
    private String itemId;
    @JsonProperty("itemName")
    private String itemName;
    @JsonProperty("price")
    private String price;
}


