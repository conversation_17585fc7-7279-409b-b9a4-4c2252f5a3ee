package com.charge.api.web.vo.joylife.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/26 10:01
 */
@Data
public class PointTradeDetailConditionRequest implements Serializable {
    private static final long serialVersionUID = 8041358306707977804L;

    /**
     * 大会员ID（权益账号）
     */
    @NotNull(message = "大会员ID不能为空")
    private String equityAccount;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 页数
     */
    private Integer pageSize;






}
