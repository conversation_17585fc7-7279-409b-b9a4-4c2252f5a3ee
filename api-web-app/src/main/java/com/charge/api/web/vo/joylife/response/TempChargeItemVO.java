package com.charge.api.web.vo.joylife.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/04 14:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TempChargeItemVO {
    /**
     * 分类名称
     */
    private String secondClassificationName;

    /**
     * 分类id
     */
    private String secondClassificationId;

    /**
     * 收费项列表
     */
    private List<ChargeItemSimpleVO> chargeItemSimpleVOList;
}


