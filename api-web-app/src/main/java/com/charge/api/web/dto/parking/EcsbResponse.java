package com.charge.api.web.dto.parking;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Classname EcsbResponse
 * @Date 2021/9/10 10:43 上午
 */
@ToString
public class EcsbResponse<T> {

    private String RETURN_CODE;

    private String RETURN_STAMP;

    private T RETURN_DATA;

    private String RETURN_DESC;

    @JsonProperty("RETURN_CODE")
    public String getRETURN_CODE() {
        return RETURN_CODE;
    }

    public void setRETURN_CODE(String RETURN_CODE) {
        this.RETURN_CODE = RETURN_CODE;
    }

    @JsonProperty("RETURN_STAMP")
    public String getRETURN_STAMP() {
        return RETURN_STAMP;
    }

    public void setRETURN_STAMP(String RETURN_STAMP) {
        this.RETURN_STAMP = RETURN_STAMP;
    }

    @JsonProperty("RETURN_DATA")
    public T getRETURN_DATA() {
        return RETURN_DATA;
    }

    public void setRETURN_DATA(T RETURN_DATA) {
        this.RETURN_DATA = RETURN_DATA;
    }

    @JsonProperty("RETURN_DESC")
    public String getRETURN_DESC() {
        return RETURN_DESC;
    }

    public void setRETURN_DESC(String RETURN_DESC) {
        this.RETURN_DESC = RETURN_DESC;
    }
}
