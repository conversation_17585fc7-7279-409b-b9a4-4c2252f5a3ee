package com.charge.api.web.vo.lakala;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 小区订单账单创建请求
 *
 * <AUTHOR>
 * @date 2023/3/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommunityOrderBillCreateRequest {

    private String deviceInfo;
    private String mercid;
    /**
     * 小区id
     */
    private String ownerId;
    private String chargeItemUuid;
    private String itemName;
    private String amount;
    /**
     * 支付方式（0表示POS,1表示转账,2表示支票,3表示支付宝,4表示微信,6员工代付）
     */
    private String paymentMethod;
    private String collectorId;
    private String collectorName;
    /**
     * 缴费人
     */
    private String payMember;
    private String bankTransactionNo;
    private String arrivalDate;
    private String memo;
    private String token;
}
