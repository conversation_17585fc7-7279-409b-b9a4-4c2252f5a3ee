package com.charge.api.web.vo.joylife.response;

import com.charge.common.util.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 押金调整列表VO
 * <AUTHOR>
 */
@Data
public class DepositAdjustRecordVO {

    /**
     * 调整单号
     */

    private String adjustNum;

    /**
     * 调整时间
     */
    @JsonFormat(pattern = DateUtils.FORMAT_0, timezone = "GMT+8")
    private Date adjustDate;

    /**
     * 调整资产类型 1.资产内调整；2.跨资产调整
     */
    private Integer adjustAssetType;

    /**
     * 调整金额
     */
    private BigDecimal adjustMoney;

    /**
     * 调整状态 默认1-生效
     */
    private Integer adjustStatus;

    /**
     * 调整说明
     */
    private String remark;

    /**
     * 调整人
     */
    private String adjustUser;

    /**
     * 调整后资产名称
     */
    private String adjustAssetName;

    /**
     * 调整后资产id
     */
    private Long adjustAssetId;


}
