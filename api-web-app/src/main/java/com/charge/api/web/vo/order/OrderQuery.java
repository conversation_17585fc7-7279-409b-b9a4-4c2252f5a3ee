package com.charge.api.web.vo.order;

import lombok.*;

/**
 * 订单子单查询条件
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class OrderQuery extends BaseOrderReq {

    /**
     * 是否查询支付信息
     */
    private Boolean queryPay;

    /**
     * 是否查询退款信息
     */
    private Boolean queryRefund;

    /**
     * 是否查询调整信息
     */
    private Boolean queryAdjust;

    public static OrderQuery of (String orderNo,String source,Long communityId,Boolean queryPay){
        OrderQuery query=new OrderQuery();
        query.setOrderNo(orderNo);
        query.setSource(source);
        query.setCommunityId(communityId);
        query.setQueryPay(queryPay);
        return query;
    }

}