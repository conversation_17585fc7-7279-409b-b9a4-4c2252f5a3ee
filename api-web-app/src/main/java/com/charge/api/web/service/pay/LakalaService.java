package com.charge.api.web.service.pay;

import com.charge.api.web.dto.pos.AssetTypeArrearsAmount;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.lakala.SearchPayResultRequest;
import com.charge.api.web.vo.lakala.SearchPayResultResponse;
import com.charge.api.web.vo.pos.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.pos.dto.AccountInfo;


public interface LakalaService {
    ChargeResponse<SearchPayResultResponse> searchPayResult(SearchPayResultRequest resultRequest);

    PosResponse payNotify(PosPayNotify posPayNotify);

    ChargePageResponse<AssetBalancesVO> getArrearsHouseList(Long communityId, String keyword, Integer status, String subjectType, Integer currentPage, Integer pageSize) throws Exception;

    ChargeResponse getHouseInfo(String houseUuid) throws ChargeBusinessException;

    ChargeResponse getHousePayItemList(String houseUuid, Integer currentPage, Integer pageSize);

    HouseArrearsSV1 getCustomerHouseList(String communityId, String customerId) throws ChargeBusinessException;

    BillVO1 getPayOrder(Long assetTransactionId) throws ChargeBusinessException;

    ChargeResponse getCommunityDisable(String communityUuid);

    AssetTypeArrearsAmount getArrearsSubjectTypeCount(Long communityUuid) throws ChargeBusinessException;

    ChargeResponse getLostPayNum(String mercid, String deviceInfo, String communityId);

    ChargeResponse queryHouseAllResident(String communityUuid, String houseUuid);

    ChargeResponse getTempChargeItemByCommunity(String communityId, Integer currentPage, Integer pageSize);

    ChargeResponse updateReceiptInfo(String id, String printer);

    ChargeResponse getPersonData(String userName);

    ChargeResponse listAuthByCommunityUuid(String communityId, String token);

    ChargeResponse<AccountInfo> login(String username, String password, String url, String systemType, String timeStamp) throws Exception;
}
