package com.charge.api.web.vo.joylife;

import com.charge.api.web.vo.joylife.request.ZhaoXiAssertRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 预收押金信息
 */
@Data
public class PredepositDepositDetailQueryVO extends ZhaoXiAssertRequest {

    /**
     * 预收账户id
     */
    @NotNull(message = "预收账户id不允许为空")
    private Long predepositAccountId;

    /**
     * 资产流水id
     */
    @NotNull(message = "资产流水id不能为空")
    private Long assetTransactionId;

}
