package com.charge.api.web.controller.joylife.bill.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class QueryBillDetailRequest implements Serializable {
    private static final long serialVersionUID = 4251160130932511349L;
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID参数缺失,请重试")
    private Long communityMsId;

    /**
     * 实收单ID
     */
    @NotNull(message = "实收单ID参数,请重试")
    private Long incomeBillId;
}
