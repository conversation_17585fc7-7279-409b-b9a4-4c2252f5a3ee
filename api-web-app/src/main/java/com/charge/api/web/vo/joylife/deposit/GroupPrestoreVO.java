package com.charge.api.web.vo.joylife.deposit;

import com.charge.config.dto.item.CommunityPrestoreGroupDetailDTO;
import com.charge.config.dto.item.CommunitySpecialPrestoreGroupItemDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GroupPrestoreVO {

	private static final long serialVersionUID = 4298355523920569733L;

	/**
	 * 预存组合ID
	 */
	private Long groupItemId;

	/**
	 * 预存组合名称
	 */
	private String groupItemName;

	/**
	 * 缴费月数列表
	 */
	private List<Integer> monthList;

	/**
	 * 每月金额
	 */
	private BigDecimal chargeMoneyPerMonth;

	/**
	 * 收费项预存信息列表
	 */
	private List<PrestoreVO> prestoreChargeItemDetailList;


    public static GroupPrestoreVO build(List<PrestoreVO> allPrestoreList, CommunityPrestoreGroupDetailDTO group) {

        List<Long> groupItemIdList = group.getPrestoreItemList().stream().map(CommunitySpecialPrestoreGroupItemDTO::getItemId).distinct().collect(Collectors.toList());
        List<PrestoreVO> groupPrestoreList = allPrestoreList.stream().filter(a -> groupItemIdList.contains(a.getItemId())).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(groupPrestoreList)) {
            return null;
        }
        Map<Long, PrestoreVO> itemPrestoreMap = groupPrestoreList.stream().collect(Collectors.toMap(PrestoreVO::getItemId, a -> a, (a, b) -> b));
        BigDecimal chargeMoneyPerMonth = groupPrestoreList.stream().map(PrestoreVO::getChargeMoneyPerMonth).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<PrestoreVO> groupPrestoreVoList = group.getPrestoreItemList().stream().map(a -> itemPrestoreMap.get(a.getItemId())).filter(Objects::nonNull).collect(Collectors.toList());
        return GroupPrestoreVO.builder()
                .groupItemName(group.getGroupName())
                .groupItemId(group.getId())
                .prestoreChargeItemDetailList(groupPrestoreVoList)
                .chargeMoneyPerMonth(chargeMoneyPerMonth)
                .monthList(Arrays.asList(3, 6, 12))
                .build();
    }

}
