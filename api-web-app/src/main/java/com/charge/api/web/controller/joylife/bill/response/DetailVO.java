package com.charge.api.web.controller.joylife.bill.response;

import com.charge.api.web.controller.joylife.bill.request.PayOrAdjustItemVO;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

@Data
public class DetailVO implements Serializable {

    private static final long serialVersionUID = -4017799504891183444L;
    /**
     * 实收单ID
     */
    private Long incomeBillId;
    /**
     * 资产流水ID
     */
    private Long transactionId;
    /**
     * 项目名称
     */
    private String communityName;
    /**
     * 资产编号
     */
    private String assetCode;
    /**
     * 资产名称
     */
    private String assetName;
    /**
     * 收费员
     */
    private String collectorName;
    /**
     * 订单时间
     */
    private Timestamp createTime;
    /**
     * 备注
     */
    private String memo;

    /**
     * 支付人
     */
    private String payMember;
    /**
     * 手机号
     */
    private String payMemberMobile;
    /**
     * 支付方式（支付宝：2,微信支付：4）
     */
    private Integer paymentMethod;
    /**
     * 支付时间
     */
    private String payTime;
    /**
     * 订单号
     */
    private String orderNum;
    /**
     * 交易号
     */
    private String outTransactionNo;
    /**
     * 押金列表
     */
    private List<PayOrAdjustItemVO> depositItems;

    /**
     * 临时订单列表
     */
    private List<PayOrAdjustItemVO> orderItems;
}
