package com.charge.api.web.vo.arrearsnotice;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 缴费通知查询
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@Data
public class ArrearsNoticeQuery {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @NotNull
    private Long communityId;

    /**
     * 查询参数
     */
    private String searchParam;

    /**
     * 查询数量
     */
    @NotNull
    private Integer querySize;

    /**
     * 起始id（不包含）
     */
    private Long idOffSet;
}
