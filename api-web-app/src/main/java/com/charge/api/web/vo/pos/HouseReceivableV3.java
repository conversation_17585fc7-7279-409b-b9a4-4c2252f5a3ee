package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class HouseReceivableV3 {
    @JsonProperty("buildingName")
    private String buildingName;
    @JsonProperty("houseName")
    private String houseName;
    @JsonProperty("houseId")
    private String houseId;
    @JsonProperty("unitName")
    private String unitName;
    @JsonProperty("orderAmountList")
    private List<ChargeItemReceivablesV3> orderAmountList;
    @JsonProperty("communityName")
    private String communityName;
    private String houseCode;
}


