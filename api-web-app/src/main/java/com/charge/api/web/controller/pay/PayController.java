package com.charge.api.web.controller.pay;

import com.charge.api.web.support.CommunityParamSupport;
import com.charge.api.web.vo.PointsRecordQueryRequestVO;
import com.charge.apicloud.enums.CrmixcTransTypeEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.core.util.CollectionUtil;
import com.charge.pay.client.PayClient;
import com.charge.pay.client.PointsTradeRecordClient;
import com.charge.pay.dto.pay.*;
import com.charge.pay.dto.point.ChargePointsTransRecordDTO;
import com.charge.pay.dto.point.PointsTransRecordConditionDTO;
import com.charge.pay.enums.PointsTradeStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 支付交易相关
 * <AUTHOR>
 * @date 2023/05/26
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping(value = "/pay/v1")
@RefreshScope
public class PayController {


    private final PayClient payClient;

    private final CommunityParamSupport communitySupport;

    private final PointsTradeRecordClient pointsTradeRecordClient;

    @Value("${points.phone.dealStartTime}")
    private String dealStartTime;

    /**
     * 支付交易下单
     *
     * @param requestDTO
     * @return
     */
    @PostMapping(value = "/trade/create")
    public ChargeResponse<PayOrderSubmitResponseDTO> tradeCreate(@Valid @RequestBody PayOrderRequestDTO requestDTO)
            throws ChargeBusinessException {
        communitySupport.fillCommunityId(requestDTO);
        List<PaySubOrderRequestDTO> payItems = requestDTO.getPayItems();
        if(CollectionUtil.isNotEmpty(payItems)){
            for (PaySubOrderRequestDTO payItem : payItems) {
                communitySupport.fillCommunityId(payItem);
            }
        }
        return payClient.tradeCreate(requestDTO);
    }

    /**
     * 支付交易查询
     * @param requestDTO
     * @return
     */
    @RequestMapping(value = "/trade/query", method = RequestMethod.POST)
    public ChargeResponse<QueryOrderResponseDTO> tradeQuery(@Valid @RequestBody QueryOrderRequestDTO requestDTO) throws ChargeBusinessException {
        communitySupport.fillCommunityId(requestDTO);
        return payClient.tradeQuery(requestDTO);
    }


    /**
     * 支付交易批量查询
     *
     * @param requestDTO
     * @return
     */
    @RequestMapping(value = "/trade/batch/query", method = RequestMethod.POST)
    public ChargeResponse<List<QueryOrderResponseDTO>> tradeBatchQuery(@Valid @RequestBody BatchQueryOrderRequestDTO requestDTO){
        return payClient.tradeBatchQuery(requestDTO);
    }

    /**
     * 退款申请
     * @param requestDTO
     * @return
     * @throws ChargeBusinessException
     */
    @RequestMapping(value = "/trade/refund", method = RequestMethod.POST)
    ChargeResponse<RefundOrderResponseDTO> tradeOrderRefund(@Valid @RequestBody RefundOrderRequestDTO requestDTO) throws ChargeBusinessException {
        return payClient.tradeOrderRefund(requestDTO);
    }


    /**
     * 退款状态查询(并更新支付记录)
     * @param requestDTO
     * @return
     * @throws ChargeBusinessException
     */
    @RequestMapping(value = "/trade/refund/query", method = RequestMethod.POST)
    ChargeResponse<RefundStatusQueryResponseDTO> tradeRefundQuery(@Valid @RequestBody RefundStatusQueryRequestDTO requestDTO) throws ChargeBusinessException {
        return payClient.tradeRefundQuery(requestDTO);
    }


    /**
     * 关闭三方预支付订单,并关闭实收单（朝昔关单）
     * @param requestDTO
     * @return
     * @throws ChargeBusinessException
     */
    @RequestMapping(value = "/trade/close", method = RequestMethod.POST)
    ChargeResponse<TradeCloseResponseDTO> closePayTrade(@Valid @RequestBody TradeCloseRequestDTO requestDTO) throws ChargeBusinessException {
        return payClient.closePayTrade(requestDTO);
    }

    /**
     * 关闭三方预支付订单 (电商关单)
     * @param requestDTO
     * @return
     * @throws ChargeBusinessException
     */
    @RequestMapping(value = "/trade/order/close", method = RequestMethod.POST)
    ChargeResponse<TradeCloseResponseDTO> tradeOrderClose(@Valid @RequestBody TradeCloseRequestDTO requestDTO) throws ChargeBusinessException {
        return payClient.tradeOrderClose(requestDTO);
    }

    /**
     * 手机号是否有预存赠分记录查询
     * @param requestVO
     * @return
     * @throws ChargeBusinessException
     */
    @RequestMapping(value = "/points/record/exist", method = RequestMethod.POST)
    ChargeResponse<Boolean> pointsRecordExist(@Valid @RequestBody PointsRecordQueryRequestVO requestVO) throws ChargeBusinessException {
        Boolean result = false;
        PointsTransRecordConditionDTO conditionDTO = PointsTransRecordConditionDTO.builder().phone(requestVO.getPhone())
                .tradeType(CrmixcTransTypeEnum.MIXC_POINTS_TYPE_EARN.getTypeCode())
                .tradeSubType(CrmixcTransTypeEnum.MIXC_POINTS_TYPE_EARN.getSubCode())
                .status(PointsTradeStatusEnum.SUCCESS.getCode())
                .dealStartTime(DateUtils.parseDate(dealStartTime))
                .build();
        List<ChargePointsTransRecordDTO> chargePointsTransRecordDTOS = AppInterfaceUtil.getResponseDataThrowException(pointsTradeRecordClient.getByCondition(conditionDTO));
        if(CollectionUtil.isNotEmpty(chargePointsTransRecordDTOS)){
            //存在预存赠分记录
            result = true;
        }
        return new ChargeResponse<>(result);
    }

}
