package com.charge.api.web.vo.pos.v3;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 缴费或调整费项
 * <AUTHOR>
 * @description
 * @date 2023/02/25
 */
@Data
public class PayOrAdjustItemVO implements Serializable {

    private static final long serialVersionUID = -8655337316016796714L;
    /**
     * 收费项id
     */
    private Long itemId;

    /**
     * 收费项名称
     */
    private String itemName;

    /**
     * 应收单ID（非应收单置空）
     */
    private Long id;

    /**
     * 金额
     */
    private BigDecimal amount;
}
