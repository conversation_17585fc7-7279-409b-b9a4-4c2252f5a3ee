package com.charge.api.web.vo.joylife.deposit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/6/11 14:15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssetPreStoreV0 implements Serializable {

    private static final long serialVersionUID = -3643955506207045645L;
    /**
     * 朝昔资产msId
     */
    private String houseMsId;

    /**
     * 收费系统-资产ID
     */
    private Long houseId;

    /**
     * 资产类型：1-房间,2-车位
     */
    private Integer assetType;

    /**
     * 专项预存
     */
    private List<PrestoreVO> specialPrestoreList;
    /**
     * 专项预存组合
     */
    private List<GroupPrestoreVO> groupPrestoreChargeItemVOList;

    /**
     * 收费对象类型（0-业主，1-开发商）
     */
    private Integer chargeObjectType;

}
