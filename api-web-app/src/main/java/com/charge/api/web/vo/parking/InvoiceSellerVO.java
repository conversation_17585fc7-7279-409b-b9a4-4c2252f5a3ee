package com.charge.api.web.vo.parking;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class InvoiceSellerVO implements Serializable {
    private static final long serialVersionUID = -501568495105805124L;

    /**
     * 销方id
     */
    private Long sellerId;

    /**
     * 纳税主体编码
     */
    private String taxpayerCode;

    /**
     * 纳税主体类型
     */
    private Integer taxpayerType;

    /**
     * 纳税主体
     */
    private String taxpayerName;


    /**
     * 销方名称
     */
    private String sellerName;

    /**
     * 纳税人识别号
     */
    private String taxIdNo;

    /**
     * 电话
     */
    private String tel;

    /**
     * 地址
     */
    private String address;

    /**
     * 开户行名称
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 编辑时间
     */
    private String modifyTime;
}
