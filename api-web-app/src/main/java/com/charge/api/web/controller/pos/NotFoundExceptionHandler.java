package com.charge.api.web.controller.pos;

import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ChargeStatusEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.web.bind.annotation.GetMapping;

import org.springframework.web.bind.annotation.RestController;




/**
 * 404处理器-需要同时有其他的全局异常处理
 *
 * <AUTHOR>
 * @date 2023/3/20
 */
@RestController
@Slf4j
public class NotFoundExceptionHandler implements ErrorController {

    private static final String ERROR_PATH = "/error";

    @Override
    public String getErrorPath() {
        return ERROR_PATH;
    }

    @GetMapping(value = ERROR_PATH)
    public ChargeResponse<String> error() {
        return new ChargeResponse<>(ErrorInfoEnum.E1017);
    }

    @GetMapping("/batchPay/listBatchBill")
    public ChargeResponse<String> listBatchBill() {
        return new ChargeResponse<>(ErrorInfoEnum.E1017);
    }

    @GetMapping("/batchPay/listBatchChargeCustomer")
    public ChargeResponse<String> listBatchChargeCustomer() {
        return new ChargeResponse<>(ErrorInfoEnum.E1017);
    }

}


