package com.charge.api.web.service.order;

import com.charge.bill.client.OrderBillDetailClient;
import com.charge.bill.dto.income.OrderBillDetailDTO;
import com.charge.bill.enums.BusinessTypeEnum;
import com.charge.bill.enums.OrderPayTypeEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.order.client.OrderClient;
import com.charge.order.dto.CreateOrderCmd;
import com.charge.order.dto.Customer;
import com.charge.order.enums.OrderAssetType;
import com.charge.order.enums.OrderStatusEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单账单处理
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
@Component
public class OrderBillBusinessHandler implements BillBusinessHandler {

    @Resource
    private OrderClient orderClient;

    @Resource
    private OrderBillDetailClient orderBillDetailClient;

    @Override
    public BusinessTypeEnum type() {
        return BusinessTypeEnum.TEMP_PAY;
    }

    @Override
    public void validate(BillBusiness billBusiness) {
        Assert.isTrue(billBusiness instanceof OrderBillBusiness, "billBusiness 类型是 TEMPORARY 的类型错误 ");
        OrderBillBusiness orderBillBusiness = (OrderBillBusiness) billBusiness;
        Assert.notNull(orderBillBusiness.getSubsetId(), "OrderBillBusiness 子类别不能为空");
        Assert.notNull(orderBillBusiness.getChargeItemId(), "OrderBillBusiness 收费项id不能为空");
        Assert.hasText(orderBillBusiness.getName(), "OrderBillBusiness 收费项name不能为空");
        Assert.notNull(orderBillBusiness.getMoney(), "OrderBillBusiness 金额不能为空");
    }

    @Override
    public void handle(CreateOrderContext context, AssetBillBusiness assetBillBusiness, List<BillBusiness> billBusinesses) {
        billBusinesses.forEach(billBusiness -> {
            OrderBillBusiness orderBillBusiness = (OrderBillBusiness) billBusiness;
            Customer customer = new Customer();
            AssetDTO asset = assetBillBusiness.getAsset();
            customer.setAssetId(asset.getId());
            if(asset.getType()==null){
                customer.setOrderAssetType(OrderAssetType.COMMUNITY);
            }else {
                customer.setOrderAssetType(OrderAssetType.ofCode(asset.getType()));
            }
            customer.setId(context.getPayMemberId());
            customer.setName(context.getPayMember());
            CreateOrderCmd createOrderCmd = new CreateOrderCmd(null,context.getCommunity().getId(), orderBillBusiness.getSubsetId(),
                    orderBillBusiness.getChargeItemId(), customer, orderBillBusiness.getMoney(), context.getMemo(), context.getCollectorName(),
                    context.getOrderNum(), OrderStatusEnum.PAYING,OrderPayTypeEnum.ORDER_PAY.getCode(),orderBillBusiness.getInstallmentId(),orderBillBusiness.getEBusinessBaseOrderId());
            Long orderId;
            try {
                ChargeResponse<Long> response = orderClient.create(createOrderCmd);
                Assert.isTrue(response.isSuccess(), "创建订单异常" + response.getMessage());
                orderId=response.getContent();
            } catch (Exception e) {
                throw new ChargeRuntimeException("调用创建订单异常:" + e.getMessage(), e);
            }

            OrderBillDetailDTO orderBillDetail = OrderBillDetailDTO.builder()
                    .assetTransactionId(assetBillBusiness.getAssetTransactionId())
                    .actualAmount(billBusiness.getMoney())
                    .itemId(orderBillBusiness.getChargeItemId())
                    .itemName(orderBillBusiness.getName())
                    .memo(context.getMemo())
                    .payType(OrderPayTypeEnum.ORDER_PAY.getCode())
                    .orderId(orderId)
                    .communityId(context.getCommunity().getId())
                    .build();
            try {
                ChargeResponse<Long> response = orderBillDetailClient.create(orderBillDetail);
                Assert.isTrue(response.isSuccess(), "创建订单详情异常" + response.getMessage());
                Assert.notNull(response.getContent(), "创建订单详情返回为空");
                billBusiness.setId(response.getContent());
            } catch (Exception e) {
                throw new ChargeRuntimeException("调用创建订单详情异常:" + e.getMessage(), e);
            }
        });
    }
}


