package com.charge.api.web.vo.pos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PosBillQueryCondition implements Serializable {
    /**
     * 小区uuid
     */
    private Long communityId;

    private Long assetId;
    /**
     * 收据号
     */
    private String receiptNumber;
    /**
     * 类型id
     */
    private String chargeTypeId;
    /**
     * 类型名称
     */
    private String chargeTypeName;
    /**
     * 支付来源
     */
    private String paymentSource;
    /**
     * 当前页数
     */
    private Integer currentPage;
    /**
     * 每页数量
     */
    private Integer pageSize;

    private String token;
}
