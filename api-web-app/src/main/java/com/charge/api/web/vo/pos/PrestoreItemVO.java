package com.charge.api.web.vo.pos;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PrestoreItemVO implements Serializable{
    private static final long serialVersionUID = 1671162756024083809L;
    /**
     * 预存收费项id
     */
    private String preItemUuid;
    /**
     * 预存收费项
     */
    private String preItemName;
    /**
     * 小区id
     */
    private String communityUuid;
    /**
     * 收费项id
     */
    private String itemUuid;
    /**
     * 收费项
     */
    private String itemName;
    /**
     * 收费项编码
     */
    private String itemCode;
    /**
     * 备注
     */
    private String memo;
    /**
     * 删除标识
     */
    private String dr;

    private Date updateTs;

    private Date createTs;

    private String cropId;

    private String appId;
    /**
     * 启用状态(0表示停用，1表示启用)
     */
    private String enableStatus;
    /**
     * 可否开票状态(0表示不可开票，1表示可以开票)
     */
    private String billState;
    /**
     * 可否允许赠分 1 允许，0不允许
     */
    private Integer pointsEarnStatus;
    /**
     * 赠分规则json：默认{"factor":"12","ratio":"1.5","points":"250"}，factor为赠分门槛系数，ratio为赠分计算比例，points为分数
     */
    private String  pointsEarnRule;

}