package com.charge.api.web.convert;

import com.charge.api.web.vo.arrearsnotice.ArrearsNoticeReceivableVO2;
import com.charge.api.web.vo.arrearsnotice.ArrearsNoticeVO;
import com.charge.api.web.vo.arrearsnotice.ArrearsNoticeReceivableVO;
import com.charge.bill.dto.arrearsnotice.ArrearsNoticeAssetReceivableDTO;
import com.charge.bill.dto.arrearsnotice.ArrearsNoticeDTO;
import com.charge.common.util.DateMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;


/**
 * 缴费通知单转换器
 * <AUTHOR>
 * @description
 * @date 2023/02/28
 */
@Mapper(uses = DateMapper.class)
public interface ArrearsNoticeConverter {

    ArrearsNoticeConverter INSTANCE = Mappers.getMapper(ArrearsNoticeConverter.class);
    @Mapping(source = "arrearAmount", target = "arrearsAmount")
    ArrearsNoticeVO map(ArrearsNoticeDTO arrearsNoticeDTO);

    ArrearsNoticeReceivableVO2 mapV2(ArrearsNoticeAssetReceivableDTO assetReceivableDTO);

    ArrearsNoticeReceivableVO map(ArrearsNoticeAssetReceivableDTO assetReceivableDTO);


}
