package com.charge.api.web.vo.lakala;

import com.charge.common.serializer.DesensitizeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

/**
 * Author: yjw
 * Date: 2023/2/24 16:12
 */
@Data
public class PersonData {


    /**
     * oaAccount : ***********
     * gender : 2
     * avatarUrl : https://chargeiposbackend.crlandpm.com.cn/default.png
     * name : **澜
     * mobile : 156****6407
     * communityName : 客服部
     */

    private String oaAccount;
    private int gender;
    private String avatarUrl;
    private String name;
    @JsonSerialize(using = DesensitizeSerializer.class)
    private String mobile;
    private String communityName;

}
