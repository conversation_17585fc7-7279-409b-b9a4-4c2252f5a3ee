package com.charge.api.web.service.order;

import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.pay.client.CommunityRewardClient;
import com.charge.pay.dto.reward.CheckRewardStatusRequestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommunityRewardSupport {

    private final CommunityRewardClient communityRewardClient;

    /**
     * 校验项目是否酬金制
     * @param communityId
     * @return
     * @throws ChargeBusinessException
     */
    @Cacheable(value = "communityRewardStatus", key = "#communityId",cacheManager = "cacheManager10m")
    public boolean checkCommunityRewardStatus(Long communityId) throws ChargeBusinessException {
        return AppInterfaceUtil.getResponseDataThrowException(communityRewardClient.checkRewardStatus(CheckRewardStatusRequestDTO.builder().communityId(communityId).build()));
    }
}
