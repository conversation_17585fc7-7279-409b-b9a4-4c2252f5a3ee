package com.charge.api.web.service.order;

import com.charge.maindata.pojo.dto.AssetDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 资产账单业务
 *
 * <AUTHOR>
 * @date 2023/3/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssetBillBusiness {

    /**
     * 账单业务列表
     */
    private List<BillBusiness> billBusinesses;

    /**
     * 资产
     */
    private AssetDTO asset;

    /**
     * 资产流水id
     */
    private Long assetTransactionId;
}


