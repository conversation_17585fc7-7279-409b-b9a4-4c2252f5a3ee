package com.charge.api.web.vo.joylife.request;

import com.charge.api.web.vo.joylife.PrestoreChargeOrderInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class PrestoreOrderRequest implements Serializable {

    private static final long serialVersionUID = 4784380747264355934L;

    /**
     * 小区收费系统ID
     */
    @NotBlank(message = "小区Id参数缺失,请重试")
    String communityId;

    /**
     * 房间收费系统ID
     */
    @NotBlank(message = "房间Id参数缺失,请重试")
    String houseId;

    /**
     * 支付来源(微信小程序：WECHAT_APPLET，悦家APP：YUEHOME_PAY，润钱包：CRT_PAY)
     */
    @NotBlank(message = "支付来源参数缺失,请重试")
    String paymentSource;

    /**
     * 支付人
     */
    String payMember;

    /**
     * 支付总金额
     */
    String totalPrice;

    /**
     * 商品名称描述
     */
    String desc;

    /**
     * 终端IP
     */
    @NotBlank(message = "终端IP参数缺失,请重试")
    String mchCreateIp;

    /**
     * 备注
     */
    String memo;

    /**
     * AppID
     */
    @NotBlank(message = "AppID参数缺失,请重试")
    String subAppid;

    /**
     * 微信用户小程序关注商家的openid(小程序缴费必传)
     */
    String subOpenid;

    /**
     * 电子发票
     */
    String needReceipt;

    /**
     * 操作系统类型（0：Android；1：iOS）
     */
    @NotNull(message = "操作系统类型参数缺失,请重试")
    private String systemType;

    /**
     * 支付人(当前登录用户姓名)
     */
    @NotNull(message = "支付人参数缺失,请重试")
    private String userName;

    /**
     * 客户ID（当前登录用户唯一标识，记录用户缴费历史)
     */
    @NotBlank(message = "客户ID参数缺失,请重试")
    private String userId;

    /**
     * 缴费明细详情
     */
    @NotEmpty(message = "缴费明细信息缺失，请重试")
    private List<PrestoreChargeOrderInfo> chargeOrderInfoList;

    /**
     * 积分账户
     */
    private String equityAccount;

    /**
     * 电话号码
     */
    @NotBlank(message = "电话号码参数缺失,请重试")
    private String phone;

    /**
     * 抵扣总积分
     */
    private String totalPoints;

    /**
     * 朝昔用户msId
     */
    private String customId;


    /**
     * 买家支付宝账号
     */
    private String buyerLogonId;

    /**
     * 支付方式（支付宝：3,微信支付：4）
     */
    private String paymentMethod;

    /**
     * 是否是app支付，使用其他渠道拉起支付宝app（非支付宝小程序）的需要传该值为true
     */
    private Boolean payNative;

    /**
     * 支付成功后跳转地址
     */
    private String returnUrl;
}
