package com.charge.api.web.dto.joylife;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/4/7 15:23
 */
@Data
public class DistributePointsReq implements Serializable {


    private static final long serialVersionUID = 4274205206480007213L;


    /**
     * 小区Id
     */
    @NotBlank(message = "项目id不能为空")
    private String communityId;
    /**
     * 编码方式 charset=utf-8
     */
    @NotBlank(message = "编码方式必须指定")
    private String charset;
    /**
     * 加密方式 signType=RSA
     */
    @NotBlank(message = "加密方式必须指定")
    private String signType;
    /**
     * 数据格式化方式 format=json
     */
    @NotBlank(message = "数据格式化方式必须指定")
    private String format;
    /**
     * 时间戳
     */
    @NotBlank(message = "时间戳必填")
    private String timestamp;

    /**
     * 签名
     */
    @NotBlank(message = "签名必填")
    private String sign;

    /**
     * AES 秘钥加密
     */
    @NotBlank(message = "AES秘钥加密必填")
    private String randomKey;

    /**
     * 业务参数加密数据
     */
    @NotBlank(message = "业务参数加密数据必填")
    private String bizContent;






}
