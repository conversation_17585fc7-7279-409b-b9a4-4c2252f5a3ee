package com.charge.api.web.vo.order;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 调整到的子订单
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
public class SubOrderAdjustFrom {
    /**
     * 订单号
     */
    @NotBlank(message = "主订单号不能为空")
    private String orderNo;
    /**
     * 子订单列表
     */
    @NotEmpty(message = "子订单号不能为空")
    @Valid
    private List<SubOrderAmount> subOrderAmounts;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;


}