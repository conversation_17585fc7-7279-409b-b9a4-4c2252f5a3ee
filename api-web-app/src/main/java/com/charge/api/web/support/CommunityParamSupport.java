package com.charge.api.web.support;

import com.charge.api.web.vo.BaseCommunityQuery;
import com.charge.api.web.vo.joylife.request.ZhaoXiAssertRequest;
import com.charge.api.web.vo.joylife.request.ZhaoXiCommunityReq;
import com.charge.api.web.vo.order.BaseOrderReq;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AssertUtils;
import com.charge.maindata.pojo.dto.CommunityDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Author: yjw
 * Date: 2023/3/9 14:29
 */
@Data
@Slf4j
@Component
public class CommunityParamSupport {

    @Autowired
    private CommunitySupport communitySupport;

    public void fillCommunityId(ZhaoXiAssertRequest preStorePointsCalReq) throws ChargeBusinessException {
        if(preStorePointsCalReq.getCommunityId()==null){
            AssertUtils.isTrue(preStorePointsCalReq.getCommunityMsId()!=null,"朝昔的项目id不能为空");
            Long communityId = communitySupport.getCommunityIdByMsId(preStorePointsCalReq.getCommunityMsId());
            preStorePointsCalReq.setCommunityId(communityId);
        }
    }

    public void fillCommunityId(BaseOrderReq orderReq) throws ChargeBusinessException {
        if(orderReq.getCommunityId()!=null){
            log.debug("fillCommunityId ignore");
        } else if(StringUtils.isNotBlank(orderReq.getZhaoXiCommunityId())){
            Long communityId =communitySupport.getCommunityIdByMsId(orderReq.getZhaoXiCommunityId());
            orderReq.setCommunityId(communityId);
        }else if (StringUtils.isNotBlank(orderReq.getCommunityMdmCode())){
            Long communityId = communitySupport.getCommunityIdByMdmCode(orderReq.getCommunityMdmCode());
            orderReq.setCommunityId(communityId);
        }else {
            throw new IllegalArgumentException("入参缺少项目信息");
        }

    }

    public void fillCommunityId(com.charge.pay.dto.pay.BaseOrderReq orderReq) throws ChargeBusinessException {
        if(orderReq.getCommunityId()!=null){
            log.debug("fillCommunityId ignore");
        } else if(StringUtils.isNotBlank(orderReq.getZhaoXiCommunityId())){
            Long communityId =communitySupport.getCommunityIdByMsId(orderReq.getZhaoXiCommunityId());
            orderReq.setCommunityId(communityId);
        }else if (StringUtils.isNotBlank(orderReq.getCommunityMdmCode())){
            Long communityId = communitySupport.getCommunityIdByMdmCode(orderReq.getCommunityMdmCode());
            orderReq.setCommunityId(communityId);
        }else {
            log.warn("fillCommunityId 入参缺少项目信息");
        }

    }

    public void fillCommunityId(BaseCommunityQuery orderReq) throws ChargeBusinessException {
        if(orderReq.getCommunityId()!=null){
            log.info("fillCommunityId ignore");
        } else if(StringUtils.isNotBlank(orderReq.getZhaoXiCommunityId())){
            Long communityId = communitySupport.getCommunityIdByMsId(orderReq.getZhaoXiCommunityId());
            orderReq.setCommunityId(communityId);
        }else if (StringUtils.isNotBlank(orderReq.getCommunityMdmCode())){
            Long communityId = communitySupport.getCommunityIdByMdmCode(orderReq.getCommunityMdmCode());
            orderReq.setCommunityId(communityId);
        }else {
            throw new IllegalArgumentException("入参缺少项目信息");
        }

    }

    public CommunityDTO fillCommunityId(ZhaoXiCommunityReq zhaoXiCommunityReq) throws ChargeBusinessException {
        CommunityDTO communityDTO;
        if(StringUtils.isNotBlank(zhaoXiCommunityReq.getCommunityMsId())){
            communityDTO = communitySupport.getCommunityByMsId(zhaoXiCommunityReq.getCommunityMsId());
        }else if(zhaoXiCommunityReq.getCommunityId()!=null) {
            communityDTO=communitySupport.getCommunityById(zhaoXiCommunityReq.getCommunityId());
        }else {
            throw new IllegalArgumentException("入参缺少项目信息");
        }
        zhaoXiCommunityReq.setCommunityId(communityDTO.getId());
        return communityDTO;
    }
}
