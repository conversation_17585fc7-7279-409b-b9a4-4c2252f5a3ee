package com.charge.api.web.vo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 订单子单新增
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SubOrderAdd extends BaseChargeItem {


    /**
     * 子订单号
     */
    @NotBlank(message = "子订单号不能为空")
    private String subOrderNo;

    /**
     * 订单初始金额
     */
    @NotNull(message = "订单初始金额不能为空")
    private BigDecimal originalAmount;

    /**
     * 订单优惠类型
     */
    private String discountType;

    /**
     * 订单优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 已支付金额（添加已支付订单时需要传）
     */
    private BigDecimal payAmount;

    /**
     * 货物名称
     */
    @NotBlank(message = "货物名称不能为空")
    private String goodsName;

    /**
     * 规格型号
     */
    private String specsType;


    /**
     * 货物单价
     */
    private BigDecimal goodsUnitPrice;

    /**
     * 物品数量
     */
    private Integer goodsQuantity;

    /**
     * 物品单位
     */
    private String goodsUnit;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税收分类编码
     */
    private String taxCateCode;

    /**
     * 类目
     */
    private String cateName;

}