package com.charge.api.web.vo.joylife.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/6/12 14:53
 */
@Data
public class BatchAssetPrestore extends ZhaoXiAssertRequest {

    private static final long serialVersionUID = 1874051591999306090L;

    /**
     * 收费的资产id
     */
    private Long assetId;

    /**
     * 朝昔的资产id（当不传收费的资产id时需要）
     */
    private String assetMsId;

    /**
     * 资产类型：1-房间,2-车位 （当不传收费的资产id时需要）
     */
    private Integer assetType;

    /**
     * 费项金额
     */
    @NotEmpty(message = "费项金额不能为空")
    @Valid
    private List<ChargeItemAmount> chargeItemAmounts;

}
