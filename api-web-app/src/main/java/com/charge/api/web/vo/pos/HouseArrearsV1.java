package com.charge.api.web.vo.pos;

import com.charge.common.serializer.DesensitizeSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 资产欠费详情
 *
 * <AUTHOR>
 * @date 2023/12/7
 */
@NoArgsConstructor
@Data
@Builder
@AllArgsConstructor
public class HouseArrearsV1 {

    @JsonProperty("owner")
    @JsonSerialize(using = DesensitizeSerializer.class)
    private String owner;
    @JsonProperty("buildingName")
    private String buildingName;
    @JsonProperty("houseId")
    private String houseId;
    @JsonProperty("unitName")
    private String unitName;
    @JsonProperty("arrearsAmount")
    private BigDecimal arrearsAmount;
    @JsonProperty("houseCode")
    private String houseCode;
    @JsonProperty("mobile")
    @JsonSerialize(using = DesensitizeSerializer.class)
    private String mobile;
    @JsonProperty("penaltyAmount")
    private BigDecimal penaltyAmount;
    @JsonProperty("subjectTYpe")
    private String subjectTYpe;
}


