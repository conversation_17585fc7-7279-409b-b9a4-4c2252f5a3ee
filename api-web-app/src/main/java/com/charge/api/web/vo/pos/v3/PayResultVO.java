package com.charge.api.web.vo.pos.v3;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/10/23 13:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayResultVO {

    /**
     * 外部订单号（拉卡拉订单号）
     */
    private String externalOrderNum;

    /**
     * 订单号（收费系统订单号）
     */
    private String orderNum;

    /**
     * 支付时间	格式yyyyMMddHHmmss
     */
    private String payTime;

    /**
     * 订单状态	0:未支付 1:支付成功 2:支付失败 3:待支付 4:已关闭
     */
    private String payStatus;

    /**
     * 订单金额
     */
    private String totalAmount;

    /**
     * 支付渠道
     */
    private String payMethod;

}
