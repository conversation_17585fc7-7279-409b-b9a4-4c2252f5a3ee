//package com.charge.api.web.config;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 幂等配置
// *
// * <AUTHOR>
// * @date 2023/11/7
// */
//@ConfigurationProperties(prefix = "idempotent")
//@Configuration
//@RefreshScope
//@Data
//public class IdempotentConfig {
//    private Boolean enable=Boolean.TRUE;
//    private Integer expire=10;
//
//}
//
//
