package com.charge.api.web.vo.order;

import com.charge.api.web.vo.BaseVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单调整单vo
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderAdjustVO extends BaseVO {

    private static final long serialVersionUID = 3984622926506892690L;

    /**
     * 子订单号
     */
    private String subOrderNo;

    /**
     * 调整金额
     */
    private BigDecimal amount;
    /**
     *
     * 调整方式（3 调整转出）
     */
    private Integer adjustMode;

    /**
     * 支付完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date adjustTime;

}
