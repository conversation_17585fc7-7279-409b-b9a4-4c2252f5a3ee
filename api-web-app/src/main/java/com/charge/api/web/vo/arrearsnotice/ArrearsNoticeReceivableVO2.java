package com.charge.api.web.vo.arrearsnotice;

import com.charge.common.serializer.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;

/**
 * ArrearsNoticeAssetReceivableVO
 * <p>
 * Description: 缴费通知单明细
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/22
 */
@Data
public class ArrearsNoticeReceivableVO2 {

    /**
     * 资产类型 1 房间 2车位
     */
    private Integer assetType;

    /**
     * 房屋编码
     */
    private String assetCode;

    /**
     * 用户名称
     */
    private String customerNames;

    /**
     * 房屋收费对象
     */
    private String houseHoldNames;

    /**
     * 费项
     */
    private String itemName;

    /**
     * 所属年月
     */
    private String belongYear;

    /**
     * 计费标准
     */
    private String chargeStandard;
    /**
     * 计费数量
     */
    private String chargeAmount;

    /**
     * 应收金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal receivableAmount;

    /**
     * 已收金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal incomeAmount;

    /**
     * 欠收金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal arrearsAmount;

    /**
     * 违约金欠费金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal penaltyArrearsAmount;

    /**
     * 总欠收
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalArrearsAmount;

}