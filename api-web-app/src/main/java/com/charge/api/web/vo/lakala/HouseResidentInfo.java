package com.charge.api.web.vo.lakala;

import com.charge.common.serializer.DesensitizeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Builder;
import lombok.Data;

/**
 * 房屋住户信息业务类
 */
@Data
@Builder
public class HouseResidentInfo {
    private  String residentUuid;//住户编号
    private  String residentName;//业主姓名
    private  String sex;//性别：0未知，1男，2女
    @JsonSerialize(using = DesensitizeSerializer.class)
    private  String telephone;//手机号码
    private  String bankName;//绑定银行
    private  String accountName;//账户名
    private  String bankNo;//银行账号
    private  String identity;//住户身份：0户主，1租客，2家庭成员, 3历史租客
    private  String checkInDate;//入住时间
}
