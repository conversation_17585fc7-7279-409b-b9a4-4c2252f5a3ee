package com.charge.api.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.charge.api.web.convert.ReceivableBillInfoConverter;
import com.charge.api.web.dto.joylife.DistributePointsReq;
import com.charge.api.web.service.joylife.PointsService;
import com.charge.api.web.support.*;
import com.charge.api.web.vo.joylife.BatchArrearsOrderList;
import com.charge.api.web.vo.joylife.PreStorePointsVO;
import com.charge.api.web.vo.joylife.request.*;
import com.charge.api.web.vo.joylife.response.AssetPreStorePointsVO;
import com.charge.api.web.vo.joylife.response.MonthOrderItemArrears;
import com.charge.api.web.vo.joylife.response.OrderItemArrears;
import com.charge.api.web.vo.joylife.response.ReceivablePointCalResultVo;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.income.AssetReceivalbeBillListDTO;
import com.charge.cloud.api.core.OpenApiTool;
import com.charge.cloud.api.dto.OpenApiRequest;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.config.client.points.PointsConfigClient;
import com.charge.config.dto.points.PointsConfigConditionDTO;
import com.charge.config.dto.points.PointsConfigMatchConditionDTO;
import com.charge.config.dto.points.PointsConfigValueDTO;
import com.charge.config.dto.points.PointsSignCommunityDTO;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.JacksonConverter;
import com.charge.core.util.StringUtil;
import com.charge.feecalculte.dto.ReceivableBillDTO;
import com.charge.pay.client.PointClient;
import com.charge.pay.dto.point.PointTradeDetailDTO;
import com.charge.pay.dto.point.PointTradeDetailZXConditionDTO;
import com.charge.pay.dto.point.SignPointDistributeConditionDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/4/7 16:01
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class PointsServiceImpl implements PointsService {

    private final OpenApiTool openApiTool;

    private final PointClient pointClient;

    private final CommunityParamSupport communityPramSupport;

    private final CommunitySupport communitySupport;

    private final PointsConfigClient pointsConfigClient;

    private final ReceivableSupport receivableSupport;

    private final FeeCalculateSupport feeCalculateSupport;

    private final AssetSupport assetSupport;

    private final ReceivableBillClient receivableBillClient;

    @Value("${pay.ras.chargePrivateKey}")
    private String chargePrivateKey;

    @Value("${pay.ras.joyLiftPublicKey}")
    private String joyLiftPublicKey;

    @Override
    public ChargeResponse distributePoints(DistributePointsReq distributePointsReq) throws ChargeBusinessException {

        OpenApiRequest openApiRequest = BeanCopierWrapper.copy(distributePointsReq, OpenApiRequest.class);

        String bizParam = null;

        // 验签
        try {
             bizParam = openApiTool.verifySignAndDecryptData(openApiRequest, chargePrivateKey, joyLiftPublicKey);
            log.info("{}|积分发放解密参数:{}", LogCategoryEnum.BUSSINESS, bizParam);
        } catch (Exception e) {
            log.error("{}|积分发放验签失败:{}", LogCategoryEnum.BUSSINESS, distributePointsReq);
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"积分发放验签失败");
        }

        // 参数解析并校验
        Map<String, Object> map = JacksonConverter.readGeneric(bizParam, new TypeReference<Map<String, Object>>() {});

        SignPointDistributeConditionDTO signPointDistributeConditionDTO = checkParam(map);

        // 发送签到积分发放消息

        return pointClient.distributePoints(signPointDistributeConditionDTO);
    }

    private SignPointDistributeConditionDTO checkParam(Map<String, Object> map) throws ChargeBusinessException {
        if(map.isEmpty()){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"积分发放必填参数为空");
        }

        String communityId = (String) map.get("communityId");
        if(StringUtil.isEmpty(communityId)){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"积分发放签约项目id必填");
        }

        String pid = (String) map.get("pid");
        if(Objects.isNull(pid)){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"积分发放大会员id必填");

        }

        String phone = (String) map.get("phone");
        if(Objects.isNull(phone)){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"积分发放大会员注册手机号必填");

        }
        String points = (String) map.get("points");
        if(Objects.isNull(points)){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"发放积分数量必填");
        }

        String paymentSource = (String) map.get("paymentSource");


        //获取v2.0小区id
        Long comm_Id = communitySupport.getCommunityIdByMsId(communityId);
        SignPointDistributeConditionDTO distributeConditionDTO = SignPointDistributeConditionDTO.builder()
                .communityId(comm_Id)
                .pid(pid)
                .phone(phone)
                .points(Integer.valueOf(points))
                .paymentSource(paymentSource)
                .build();

        return distributeConditionDTO;

    }

    public static BigDecimal nullable(BigDecimal bigDecimal){
        return bigDecimal==null?BigDecimal.ZERO:bigDecimal;
    }


    @Override
    public PreStorePointsVO calPreStorePoints(Long communityId, Long assetId, Long chargeItemId, BigDecimal preStoreAmount) throws ChargeBusinessException {
        PreStorePointsVO preStorePointsVO = new PreStorePointsVO();
        ReceivableBillDTO preStoreReceivableBill = feeCalculateSupport.calculatePreStorePerMonth(communityId,assetId, chargeItemId);

        //预存计算为0 不计算万象星
        if (preStoreReceivableBill == null||preStoreReceivableBill.getReceivableAmount().compareTo(BigDecimal.ZERO)==0) {
            log.info("calPreStorePoints preStoreReceivableBill null, communityId {},assetId{} ,chargeItemId {}",communityId,assetId,chargeItemId);
            return preStorePointsVO;
        }

        BigDecimal point = calPreStorePoint(communityId, chargeItemId, preStoreReceivableBill.getReceivableAmount(), preStoreAmount);

        log.info("calPreStorePoints result ,preStoreAmount {},point {}",preStoreAmount,point);
        preStorePointsVO.setRewardPoints(point.compareTo(BigDecimal.ZERO) > 0);
        preStorePointsVO.setPointSAmount(point);
        return preStorePointsVO;
    }

    private BigDecimal calPreStorePoint(Long communityId, Long chargeItemId, BigDecimal preStore, BigDecimal preStoreAmount) throws ChargeBusinessException {
        //预存月数
        int month = preStoreAmount.divide(preStore, 2, RoundingMode.HALF_UP).intValue();
        log.info("doCalPreStorePoint,preStoreAmount:{},preStore:{},month:{}", preStoreAmount, preStore, month);
        if (month < 1) {
            return BigDecimal.ZERO;
        }
        //根据预存月数、收费项查询 项目预存配置
        PointsConfigValueDTO preStorePointsConfig = AppInterfaceUtil.getResponseDataThrowException(pointsConfigClient.matchPointsConfig(PointsConfigMatchConditionDTO.builder().communityId(communityId).itemId(chargeItemId).month(month).build()));
        if (preStorePointsConfig == null) {
            return BigDecimal.ZERO;
        }
        return calculatePoint(preStorePointsConfig, preStoreAmount);
    }

    private List<PointsConfigValueDTO> getPointsConfigValues() throws ChargeBusinessException {
        ChargeResponse<List<PointsConfigValueDTO>> pointsConfigValueResp = pointsConfigClient.getOnGoingConfig();
        List<PointsConfigValueDTO> pointsConfigValues = AppInterfaceUtil.getResponseDataThrowException(pointsConfigValueResp);
        if(CollectionUtils.isEmpty(pointsConfigValues)){
            return Lists.newArrayList();
        }
        pointsConfigValues.sort(Comparator.comparingInt(PointsConfigValueDTO::getMonthFrom));
        return pointsConfigValues;
    }

    private BigDecimal calPreStorePoint(List<PointsConfigValueDTO> sortPointsConfigValue,BigDecimal preStorePerMonth, BigDecimal preStoreAmount){
        //预存月数
        if (CollectionUtils.isEmpty(sortPointsConfigValue)) {
            return BigDecimal.ZERO;
        }
        int month = preStoreAmount.divide(preStorePerMonth, 2, RoundingMode.HALF_UP).intValue();
        log.info("calPreStorePoint,preStoreAmount:{},preStore:{},month:{}", preStoreAmount, preStorePerMonth, month);
        if (month < 1) {
            return BigDecimal.ZERO;
        }
        return sortPointsConfigValue.stream().filter(a->a.getMonthFrom().compareTo(month)<=0&&a.getMonthTo().compareTo(month)>=0).map(a->
                calculatePoint(a,preStoreAmount)).findFirst().orElse(BigDecimal.ZERO);

    }

    private static BigDecimal calculatePoint(PointsConfigValueDTO preStorePointsConfig, BigDecimal preStoreAmount) {
        return preStoreAmount.multiply(preStorePointsConfig.getRatio()).multiply(new BigDecimal(preStorePointsConfig.getPoints())).divide(new BigDecimal("100"), 0, RoundingMode.DOWN);
    }

    @Override
    public PreStorePointsVO calPreStorePoints(PreStorePointsCalReq calRequest) throws ChargeBusinessException {
        communityPramSupport.fillCommunityId(calRequest);
        assetSupport.fillAssetId(calRequest);
        BigDecimal arrearsAmount = receivableSupport.listReceivableBills(calRequest.getCommunityId(), Lists.newArrayList(calRequest.getAssetId()), Lists.newArrayList(calRequest.getChargeItemAmount().getChargeItemId()),
                        Lists.newArrayList(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode())).stream().map(a -> nullable(nullable(a.getArrearsAmount())).add(nullable(a.getPenaltyArrearsAmount())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return calPreStorePoints(calRequest.getCommunityId(), calRequest.getAssetId(), calRequest.getChargeItemAmount(),arrearsAmount);
    }

    @Override
    public List<PreStorePointsVO> calPreStorePoints(MultiPreStorePointsCalReq calRequest) throws ChargeBusinessException {
        List<PreStorePointsVO> preStorePoints=Lists.newArrayList();
        communityPramSupport.fillCommunityId(calRequest);
        assetSupport.fillAssetId(calRequest);
        List<Long> chargeItemIds = calRequest.getChargeItemAmounts().stream().map(ChargeItemAmount::getChargeItemId).collect(Collectors.toList());
        Integer chargeObjectVal = Objects.nonNull(calRequest.getChargeObjectType()) ? calRequest.getChargeObjectType() : ChargeObjEnum.CHARGE_OBJ_OWNER.getCode();
        Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> chargeItemReceivableMap = receivableSupport.listReceivableBills(calRequest.getCommunityId(),
                Lists.newArrayList(calRequest.getAssetId()),
                Lists.newArrayList(chargeItemIds),
                Lists.newArrayList(chargeObjectVal), DateUtils.format(new Date(),
                        DateUtils.FORMAT_14)).stream().collect(Collectors.groupingBy(com.charge.bill.dto.ReceivableBillDTO::getItemId));
        Map<Long,BigDecimal> chargeItemArrearMap= Maps.newHashMap();
        chargeItemReceivableMap.forEach((k,v)->{
            BigDecimal arrearsAmount = v.stream().map(a->nullable(a.getArrearsAmount()).add(nullable(a.getPenaltyArrearsAmount()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            chargeItemArrearMap.put(k,arrearsAmount);
        });
        for (ChargeItemAmount chargeItemAmount:calRequest.getChargeItemAmounts()){
            BigDecimal arrears = nullable(chargeItemArrearMap.get(chargeItemAmount.getChargeItemId()));
            preStorePoints.add(calPreStorePoints(calRequest.getCommunityId(), calRequest.getAssetId(), chargeItemAmount, arrears)) ;
        }
        return preStorePoints;
    }

    private PreStorePointsVO calPreStorePoints(Long communityId, Long assetId, ChargeItemAmount chargeItemAmount,BigDecimal arrearsAmount) throws ChargeBusinessException {
        PreStorePointsVO preStorePointsVO = new PreStorePointsVO();
        preStorePointsVO.setChargeItemId(chargeItemAmount.getChargeItemId());
        preStorePointsVO.setAmount(chargeItemAmount.getAmount());
        if (chargeItemAmount.getAmount().compareTo(arrearsAmount) < 0) {
            log.info("calPreStorePoints {},arrearsAmount {}",chargeItemAmount,arrearsAmount);
            return preStorePointsVO;
        }
        ReceivableBillDTO preStoreReceivableBill = feeCalculateSupport.calculatePreStorePerMonth(communityId, assetId, chargeItemAmount.getChargeItemId());

        //预存计算为0 不计算万象星
        if (preStoreReceivableBill == null||preStoreReceivableBill.getReceivableAmount().compareTo(BigDecimal.ZERO)==0) {
            log.info("calPreStorePointspreStoreReceivableBill null,communityId:{}, assetId:{},preStoreReceivableBill {}",communityId, assetId,preStoreReceivableBill);
            return preStorePointsVO;
        }

        //根据预存金额得到预存月数，再查询出符合的积分配置
        BigDecimal point = calPreStorePoint(communityId, chargeItemAmount.getChargeItemId(), preStoreReceivableBill.getReceivableAmount(), chargeItemAmount.getAmount().subtract(arrearsAmount));

        log.info("calPreStorePoints result, communityId:{}, assetId:{}arrearsAmount {},point {}", communityId, assetId, arrearsAmount, point);
        preStorePointsVO.setRewardPoints(point.compareTo(BigDecimal.ZERO) > 0);
        preStorePointsVO.setPointSAmount(point);
        return preStorePointsVO;
    }

    @Override
    public ChargeResponse calPointsBySelect(MixPointsCalRequest calRequest) throws ChargeBusinessException {
        log.info("{}|计算积分抵扣金额,入参{}",LogCategoryEnum.BUSSINESS,calRequest);

//      统一按照小区级别规则计算：250分抵扣一块钱
        JSONObject returnObject = new JSONObject();

        //查询可抵扣积分费项
        List<OrderItemArrears> allDetail = new ArrayList<>();
        Iterator<BatchArrearsOrderList> it = calRequest.getArrearsOrderList().iterator();
        while (it.hasNext()){
            BatchArrearsOrderList h = it.next();
            if (h == null ) {
                continue;
            }
            List<MonthOrderItemArrears> billList = h.getBillList();
            Iterator<MonthOrderItemArrears> monthOrderItemArrearsIterator = billList.iterator();
            while (monthOrderItemArrearsIterator.hasNext()){
                MonthOrderItemArrears arrears = monthOrderItemArrearsIterator.next();
                List<OrderItemArrears> detailList = arrears.getDetailList();
                if (detailList == null || detailList.isEmpty()) {
                    continue;
                }
                for (OrderItemArrears o: detailList
                ) {
                    allDetail.add(o);
                }
            }
        }
        //获取v2.0小区id
        Long communityId = communitySupport.getCommunityIdByMsId(calRequest.getCommunityId());
        if (communityId == null) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1006,"查询不到试点小区信息");
        }
        List<Long> itemList =  allDetail.stream().map(e-> Long.valueOf(e.getItemId())).collect(Collectors.toList());

        //查询小区可抵扣收费项信息
        List<PointsSignCommunityDTO> configList = getPointsSignCommunity(communityId);
        if (CollectionUtils.isEmpty(configList) || CollectionUtils.isEmpty(configList.get(0).getPointsDiscountItemIdList())) {
            returnObject.put("totalDiscount","0");
            returnObject.put("totalPoints","0");
            return new ChargeResponse(returnObject);
        }
        List<Long> disItemList =configList.get(0).getPointsDiscountItemIdList().stream().distinct().collect(Collectors.toList());
        log.info("计算积分抵扣金额:订单费项：{},可积分抵扣费项：{}",JSON.toJSONString(itemList),configList.get(0).getPointsDiscountItemIdList());

        //计算可抵扣费项欠费金额
        BigDecimal itemCount = new BigDecimal(0);
        for (int i = 0, size = disItemList.size(); i < size; i++) {
            String itemUuid = disItemList.get(i)+"";
            for (int j = 0,size2 = allDetail.size(); j < size2; j++) {
                String itemId = allDetail.get(j).getItemId();
                if (itemUuid.equals(itemId)) {
                    String itemArrearsAmount = allDetail.get(j).getItemArrearsAmount();
                    itemCount = itemCount.add(new BigDecimal(itemArrearsAmount)) ;
                }
            }
        }

        // TODO 根据积分配置计算可抵扣积分金额

        //根据传入积分计算可抵扣金额
        BigDecimal pointsToMoney = new BigDecimal(calRequest.getPoints()).divide(new BigDecimal(250)).setScale(2,BigDecimal.ROUND_DOWN);

        log.info("{}|计算积分抵扣金额:可抵扣积分项：{},可抵欠费金额：{}，积分可兑金额：{}",LogCategoryEnum.BUSSINESS,disItemList,itemCount,pointsToMoney);
        //对比欠费及可抵扣金额获取最小值
        BigDecimal totalDiscount = pointsToMoney.compareTo(itemCount) >= 0 ? itemCount:pointsToMoney;
        BigDecimal totalPoints = totalDiscount.multiply(new BigDecimal(250)).setScale(0,BigDecimal.ROUND_UP);
        returnObject.put("totalDiscount",totalDiscount.toPlainString());
        returnObject.put("totalPoints",totalPoints.toPlainString());
        return new ChargeResponse(returnObject);
    }

    @Override
    public ChargeResponse<PagingDTO<PointTradeDetailDTO>> pointPageQuery(PointTradeDetailConditionRequest pointDTO) {
        PointTradeDetailZXConditionDTO conditionDTO = BeanCopierWrapper.copy(pointDTO, PointTradeDetailZXConditionDTO.class);
        return pointClient.pointPageQuery(conditionDTO);
    }

    @Override
    public List<AssetPreStorePointsVO> batchCalPreStorePoints(BatchAssetPreStorePointsCalReq batchCalRequest) throws ChargeBusinessException {
        List<AssetPreStorePointsVO> assetPreStorePoints = Lists.newArrayList();
        communityPramSupport.fillCommunityId(batchCalRequest);
        assetSupport.fillAssetIds(batchCalRequest.getAssetPrestores(),batchCalRequest.getCommunityId(), false);

        //校验积分签约
        List<PointsSignCommunityDTO> configList = getPointsSignCommunity(batchCalRequest.getCommunityId());
        if (CollectionUtils.isEmpty(configList)) {
            log.info("batchCalPreStorePoints 项目未签约:{}",batchCalRequest.getCommunityId());
            return buildNoPointAssetPreStorePoints(batchCalRequest);
        }
        List<Long> pointsEarnItems = configList.get(0).getPointsEarnItemIdList();

        //批量计算准备欠费和预存额度和积分配置
        List<Long> chargeItemIds =  batchCalRequest.getAssetPrestores().stream().flatMap(a->a.getChargeItemAmounts().stream().map(ChargeItemAmount::getChargeItemId))
                .filter(pointsEarnItems::contains).distinct().collect(Collectors.toList());
        List<Long> assetIds = batchCalRequest.getAssetPrestores().stream().map(BatchAssetPrestore::getAssetId).collect(Collectors.toList());

        Map<Long, Integer> assetId2ChargeObjectMap = batchCalRequest.getAssetPrestores().stream().collect(Collectors.toMap(BatchAssetPrestore::getAssetId, e -> setChargeObjectTypeVal(e.getChargeObjectType()), (a, b) -> b));
        Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> assetReceivableMap =
                receivableSupport.listReceivableBills(batchCalRequest.getCommunityId(), assetIds,
                        Lists.newArrayList(chargeItemIds),
                        Lists.newArrayList(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode(),
                                ChargeObjEnum.CHARGE_OBJ_DEVELOPER.getCode()), DateUtils.format(new Date(), DateUtils.FORMAT_14))
                .stream().filter(e -> e.getChargeObject().equals(assetId2ChargeObjectMap.get(e.getAssetId()))).collect(Collectors.groupingBy(com.charge.bill.dto.ReceivableBillDTO::getAssetId));

        Map<Long, List<ReceivableBillDTO>> assetPrestoreMap = feeCalculateSupport.batchCalculatePreStorePerMonth(batchCalRequest.getCommunityId(), assetIds, chargeItemIds).stream()
                .collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId));

        log.info("batchCalPreStorePoints assetPrestoreMap:{}",assetPrestoreMap);
        log.info("batchCalPreStorePoints assetReceivableMap:{}",assetReceivableMap);

        List<PointsConfigValueDTO> pointsConfigValues = getPointsConfigValues();
        for (BatchAssetPrestore assetPreStore : batchCalRequest.getAssetPrestores()) {
            Map<Long, BigDecimal> chargeItemArrearMap =  assetReceivableMap.getOrDefault(assetPreStore.getAssetId(),Lists.newArrayList()).stream().collect(Collectors.groupingBy(com.charge.bill.dto.ReceivableBillDTO::getItemId))
                    .entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                            entry->entry.getValue().stream().map(e -> e.getArrearsAmount().add(e.getPenaltyArrearsAmount())).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add)));
            Map<Long, BigDecimal> chargeItemPrestoreMap = assetPrestoreMap.getOrDefault(assetPreStore.getAssetId(),Lists.newArrayList()).stream().collect(Collectors.toMap(ReceivableBillDTO::getChargeItemId, ReceivableBillDTO::getReceivableAmount));
            List<PreStorePointsVO> preStorePoints = assetPreStore.getChargeItemAmounts().stream().map(a -> calculatePreStorePoints(a, chargeItemPrestoreMap, chargeItemArrearMap, pointsConfigValues)).collect(Collectors.toList());
            assetPreStorePoints.add(buildAssetPreStorePointsVO(assetPreStore, preStorePoints));
        }
        return assetPreStorePoints;
    }

    private static AssetPreStorePointsVO buildAssetPreStorePointsVO(BatchAssetPrestore assetPreStore, List<PreStorePointsVO> preStorePoints) {
        AssetPreStorePointsVO assetPreStorePointsVO = new AssetPreStorePointsVO();
        assetPreStorePointsVO.setAssetMsId(assetPreStore.getAssetMsId());
        assetPreStorePointsVO.setAssetId(assetPreStore.getAssetId());
        assetPreStorePointsVO.setAssetType(assetPreStore.getAssetType());
        assetPreStorePointsVO.setPreStorePointsVOList(preStorePoints);
        return assetPreStorePointsVO;
    }

    private PreStorePointsVO calculatePreStorePoints(ChargeItemAmount chargeItemAmount, Map<Long, BigDecimal> chargeItemPrestoreMap, Map<Long, BigDecimal> chargeItemArrearMap, List<PointsConfigValueDTO> pointsConfigValues) {
        PreStorePointsVO preStorePointsVO = new PreStorePointsVO();
        BigDecimal preStorePerMonth = chargeItemPrestoreMap.getOrDefault(chargeItemAmount.getChargeItemId(),BigDecimal.ZERO);
        if (preStorePerMonth.compareTo(BigDecimal.ZERO) <= 0) {
            preStorePointsVO.setPointSAmount(BigDecimal.ZERO);
        } else {
            BigDecimal arrears = chargeItemArrearMap.getOrDefault(chargeItemAmount.getChargeItemId(),BigDecimal.ZERO);
            preStorePointsVO.setPointSAmount(calPreStorePoint(pointsConfigValues, preStorePerMonth, chargeItemAmount.getAmount().subtract(arrears)));
        }
        preStorePointsVO.setChargeItemId(chargeItemAmount.getChargeItemId());
        preStorePointsVO.setAmount(chargeItemAmount.getAmount());
        preStorePointsVO.setRewardPoints(preStorePointsVO.getPointSAmount().compareTo(BigDecimal.ZERO) > 0);
        return preStorePointsVO;
    }

    private List<PointsSignCommunityDTO> getPointsSignCommunity(Long communityId) throws ChargeBusinessException {
        ChargeResponse<List<PointsSignCommunityDTO>> communityConfig = pointsConfigClient.getCommunityConfig(PointsConfigConditionDTO.builder().communityId(communityId).enableConfig(true).build());
        return AppInterfaceUtil.getResponseDataThrowException(communityConfig);
    }

    private static List<AssetPreStorePointsVO> buildNoPointAssetPreStorePoints(BatchAssetPreStorePointsCalReq batchCalRequest) {
        return batchCalRequest.getAssetPrestores().stream().map(batchAssetPrestore -> {
            AssetPreStorePointsVO assetPreStorePointsVO = new AssetPreStorePointsVO();
            assetPreStorePointsVO.setAssetType(batchAssetPrestore.getAssetType());
            assetPreStorePointsVO.setAssetId(batchAssetPrestore.getAssetId());
            assetPreStorePointsVO.setAssetMsId(batchAssetPrestore.getAssetMsId());
            List<PreStorePointsVO> preStorePoints = batchAssetPrestore.getChargeItemAmounts().stream().map(chargeItemAmount -> {
                PreStorePointsVO preStorePointsVO = new PreStorePointsVO();
                preStorePointsVO.setAmount(chargeItemAmount.getAmount());
                preStorePointsVO.setPointSAmount(BigDecimal.ZERO);
                preStorePointsVO.setRewardPoints(false);
                preStorePointsVO.setChargeItemId(chargeItemAmount.getChargeItemId());
                return preStorePointsVO;
            }).collect(Collectors.toList());
            assetPreStorePointsVO.setPreStorePointsVOList(preStorePoints);
            return assetPreStorePointsVO;
        }).collect(Collectors.toList());
    }

    private Integer setChargeObjectTypeVal(Integer chargeObjectType){
        return Objects.nonNull(chargeObjectType) ? chargeObjectType : ChargeObjEnum.CHARGE_OBJ_OWNER.getCode();
    }

    @Override
    public ReceivablePointCalResultVo calPreStorePointsByReceivableBill(ReceivablePointCalRequest receivablePointCalRequest) throws ChargeBusinessException  {
        //校验入参
        if (receivablePointCalRequest.getAssetBillInfos()== null || receivablePointCalRequest.getAssetBillInfos().isEmpty()) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "资产应收账单信息不能为空");
        }
        //1.先看是否存在积分抵扣pointMoney,存在则调用积分抵扣计算抵扣后的用于赠星的实付金额
        if (receivablePointCalRequest.getPointsMoney() != null && StringUtils.isNotEmpty(receivablePointCalRequest.getPointsMoney()) && new BigDecimal(receivablePointCalRequest.getPointsMoney()).compareTo(BigDecimal.ZERO) > 0) {
            return calPreStorePoint(calPointsBySelect(receivablePointCalRequest));
        }
        //2.不存在积分抵扣则直接调用计算规则算分
        CalPreStorePointRequest calPreStorePointRequest = new CalPreStorePointRequest();
        calPreStorePointRequest.setCommunityId(receivablePointCalRequest.getCommunityId());
        calPreStorePointRequest.setAssetBillInfos(convertReceivableBillInfoList(receivablePointCalRequest.getCommunityId(), receivablePointCalRequest.getAssetBillInfos()));
        return calPreStorePoint(calPreStorePointRequest);
    }


    /**
     * @description 转换List<ReceivableAssetBillInfo>为List<ReceivableAssetBillInfoDTO>
     * <AUTHOR>
     * @param[1] receivableBillInfoList
     * @throws
     * @return List<ReceivableBillInfoDTO>
     * @time 2025/8/7 15:37
     */
    private List<ReceivableAssetBillInfoDTO> convertReceivableBillInfoList(String communityId, List<ReceivableAssetBillInfo> assetBillInfos) throws ChargeBusinessException {
        //构建返回对象
        List<ReceivableAssetBillInfoDTO> assetBillInfoDTOS = new ArrayList<>();

        //从assetBillInfos的billInfoList中获取应收单id，返回List<Long>
        List<Long> billIdLists = assetBillInfos.stream().map(ReceivableAssetBillInfo::getReceivableBillInfos).flatMap(Collection::stream).map(e -> Long.parseLong(e.getBillId())).collect(Collectors.toList());
        ReceivableConditionDTO receivableCondition = ReceivableConditionDTO.builder().communityId(Long.valueOf(communityId))
                .ids(billIdLists).build();
        ChargeResponse<List<com.charge.bill.dto.ReceivableBillDTO>> receivablesResp = receivableBillClient.queryList(receivableCondition);
        List<com.charge.bill.dto.ReceivableBillDTO> receivableBillsResponseData = AppInterfaceUtil.getResponseDataThrowException(receivablesResp);
        Assert.notEmpty(receivableBillsResponseData,"查询应收单失败");
        if (billIdLists.size() != receivableBillsResponseData.size()) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "查询应收单数量与传入数量不一致");
        }
        //receivableBillsResponseData转换为Map<id,ReceivableBillDTO>
        Map<Long, com.charge.bill.dto.ReceivableBillDTO> receivableBillMap = receivableBillsResponseData.stream().collect(Collectors.toMap(com.charge.bill.dto.ReceivableBillDTO::getId, Function.identity()));

        assetBillInfos.forEach(assetBillInfo -> {
            ReceivableAssetBillInfoDTO assetBillInfoDTO = new ReceivableAssetBillInfoDTO();
            assetBillInfoDTO.setAssetId(assetBillInfo.getAssetId());
            List<ReceivableBillInfoDTO> receivableBillInfoDTOS = ReceivableBillInfoConverter.INSTANCE.toReceivableBillInfoDTOS(assetBillInfo.getReceivableBillInfos());
            receivableBillInfoDTOS.forEach(billInfo -> {
                com.charge.bill.dto.ReceivableBillDTO receivableBillDTO = receivableBillMap.get(Long.valueOf(billInfo.getBillId()));
                billInfo.setChargeObject(receivableBillDTO.getChargeObject());
            });
            assetBillInfoDTO.setBillInfoList(receivableBillInfoDTOS);
            assetBillInfoDTOS.add(assetBillInfoDTO);
        });
        return assetBillInfoDTOS;
    }

    /**
     * @description 处理积分抵扣和数据转换
     * <AUTHOR>
     * @param[1] receivablePointCalRequest
     * @return CalPreStorePointRequest
     * @time 2025/8/7 14:51
     */
    private CalPreStorePointRequest calPointsBySelect(ReceivablePointCalRequest receivablePointCalRequest) throws ChargeBusinessException {
        //查询可抵扣项信息
        ChargeResponse<List<PointsSignCommunityDTO>> communityConfig = pointsConfigClient.getCommunityConfig(PointsConfigConditionDTO.builder().communityId(Long.valueOf(receivablePointCalRequest.getCommunityId())).enableConfig(true).build());
        List<PointsSignCommunityDTO> configList = AppInterfaceUtil.getResponseData(communityConfig);
        log.info("{}|可积分抵扣费项：{}",LogCategoryEnum.BUSSINESS,configList);
        if (CollectionUtils.isEmpty(configList) || CollectionUtils.isEmpty(configList.get(0).getPointsDiscountItemIdList())) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "无可抵扣积分费项");
        }
        List<Long> disItemIdList =configList.get(0).getPointsDiscountItemIdList();
        //构建积分抵扣之后的应收单用于算分
        return buildCalPreStorePointRequest(receivablePointCalRequest,disItemIdList);
    }

    private CalPreStorePointRequest buildCalPreStorePointRequest(ReceivablePointCalRequest receivablePointCalRequest, List<Long> disItemIdList) throws ChargeBusinessException {
        CalPreStorePointRequest calPreStorePointRequest = new CalPreStorePointRequest();
        calPreStorePointRequest.setCommunityId(receivablePointCalRequest.getCommunityId());
        List<ReceivableAssetBillInfoDTO> assetBillInfoDTOS = convertReceivableBillInfoList(receivablePointCalRequest.getCommunityId(), receivablePointCalRequest.getAssetBillInfos());
        //遍历抵扣
        BigDecimal disMoney = new BigDecimal(receivablePointCalRequest.getPointsMoney());
        for (ReceivableAssetBillInfoDTO assetBillInfoDTO : assetBillInfoDTOS) {
            List<ReceivableBillInfoDTO> detailList = assetBillInfoDTO.getBillInfoList();
            detailList = detailList.stream().sorted(Comparator.comparing(ReceivableBillInfoDTO::getBelongYears,Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
            BigDecimal pointsDisHouseAmount = BigDecimal.ZERO;
            List<ReceivableAssetBillInfoDTO> disDTOList = new ArrayList<>(detailList.size()+1);
            List<ReceivableAssetBillInfoDTO> leftDTOList = new ArrayList<>(detailList.size()+1);
            for (ReceivableBillInfoDTO recDTO : detailList) {
                ReceivableBillInfoDTO dis = BeanCopierWrapper.copy(recDTO, ReceivableBillInfoDTO.class);
                ReceivableBillInfoDTO left = BeanCopierWrapper.copy(recDTO, ReceivableBillInfoDTO.class);
                if (disItemIdList.contains(Long.valueOf(recDTO.getItemId()))) {

                }
            }

        }



    }


    /**
     * @description 计算赠分私有方法
     * <AUTHOR>
     * @param[1] calPreStorePointRequest
     * @return ReceivablePointCalResultVo
     * @time 2025/8/7 14:51
     */
    private ReceivablePointCalResultVo calPreStorePoint(CalPreStorePointRequest calPreStorePointRequest){


    }


}
