package com.charge.api.web.vo.lakala;

import com.charge.common.serializer.DesensitizeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 房屋搜索业务类
 * Created by yjw on 2017/4/26.
 */
@Data
@Builder
public class HouseSearchInfo {
    private String houseUuid;//房屋编号
    private String residentUuid;//住户编号
    private String userName;//业主姓名
    @JsonSerialize(using = DesensitizeSerializer.class)
    private String mobile;//手机号码
    private Integer gender;//性别
    private BigDecimal arrearAmount;//欠费金额
    private BigDecimal balanceAmount;//余额
    private BigDecimal depositAmount;//押金金额
    private String communityName;//小区名称
    private String buildingName;//楼栋名称
    private String unitName;//单元名称
    private String houseName;//房屋名称
    private Integer status;//欠费状态，0-未欠费，1-欠费
    //房屋标的物类型
    private String subjectType;



}
