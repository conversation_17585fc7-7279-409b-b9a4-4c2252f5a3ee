package com.charge.api.web.vo.joylife.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/07
 */
@Data
public class HomePayItemDetailConditionVO {
    /**
     * 朝夕房间Id
     */
    @NotBlank(message = "房屋参数不能为空")
    private String houseUuid;

    /**
     * 费用所属年月
     */
    @NotBlank(message = "所属年月不能为空")
    private String belongYear;
}
