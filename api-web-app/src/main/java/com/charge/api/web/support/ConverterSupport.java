package com.charge.api.web.support;

import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.apicloud.enums.CrmixcTransTypeEnum;
import com.charge.bill.dto.income.*;
import com.charge.bill.enums.*;
import com.charge.common.util.DateUtils;
import com.charge.core.util.CollectionUtil;
import com.charge.joylife.vo.AssetBillVO;
import com.charge.joylife.vo.BillDataInfoVO;
import com.charge.joylife.vo.BillInfoVO;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.pay.dto.point.ChargePointsTransRecordDTO;
import com.charge.pay.dto.point.PointTradeDetailDTO;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

public class ConverterSupport {

    public static AssetBillVO convertTrDtoToAssetBillVo(AssetTransactionDTO trBill, String communityMsId, Map<Long, AssetAdapter> assetAdapterHashMap) {
        AssetBillVO assetBillVO = new AssetBillVO();
        assetBillVO.setCommunityMsId(communityMsId);
        assetBillVO.setItemName(trBill.getGoodsName());
        assetBillVO.setPrice(trBill.getMoney());
        assetBillVO.setTradeTime(DateUtils.format(trBill.getPaymentTime(), DateUtils.FORMAT_0));
        assetBillVO.setTransactionId(trBill.getId());
        assetBillVO.setSubjectInfo(trBill.getAssetName());
        if (Objects.nonNull(assetAdapterHashMap.get(trBill.getAssetId()))) {
            AssetAdapter assetAdapter = assetAdapterHashMap.get(trBill.getAssetId());
            assetBillVO.setSubjectType(assetAdapter.getType());
            assetBillVO.setSubjectInfo(assetAdapter.getAssetName());
        }
        if (CollectionUtil.isNotEmpty(trBill.getBusinessTypeList()) && Objects.nonNull(BusinessTypeEnum.fromCode(trBill.getBusinessTypeList().get(0)))) {
            assetBillVO.setBillTypeName(BusinessTypeEnum.fromCode(trBill.getBusinessTypeList().get(0)).getShowDesc());
        }
        if(Objects.equals(PaymentMethodEnum.DEDUCT.getPaymentCode(),trBill.getPaymentMethod()) ||Objects.equals(PaymentMethodEnum.CARRY_FORWARD_DEDUCTIONS.getPaymentCode(),trBill.getPaymentMethod()) ){
            assetBillVO.setBillTypeName(BusinessTypeEnum.PREDEPOSIT_DEDUCT.getShowDesc());

        }
        return assetBillVO;
    }

    public static BillInfoVO convertTrDtoToAssetBillVoDetail(AssetTransactionDTO transactionDTO, PointTradeDetailDTO earnPoints, List<ChargePointsTransRecordDTO> pointRedeemList, AssetAdapter adapter) {
        BillInfoVO result = convertTransactionTOBillInfo(transactionDTO);

        if (Objects.nonNull(earnPoints)) {
            result.setPoints(earnPoints.getPoints());
            result.setMixcTransType(Integer.valueOf(earnPoints.getType()));
        }
        List<EquityAdjustBillDetailDTO> equityAdjustBillDetailDTOList = transactionDTO.getEquityAdjustBillDetailDTOList();
        if (CollectionUtil.isNotEmpty(equityAdjustBillDetailDTOList)) {
            BigDecimal pointsMoney = equityAdjustBillDetailDTOList.stream().map(EquityAdjustBillDetailDTO::getEquityAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            Integer points = 0;
            if (CollectionUtil.isNotEmpty(pointRedeemList)) {
                points = pointRedeemList.stream().map(ChargePointsTransRecordDTO::getPoints).reduce(0,Integer::sum);
            }
            result.setPoints(points);
            result.setDiscountPrice(pointsMoney);
            result.setMixcTransType(Integer.valueOf(CrmixcTransTypeEnum.MIXC_POINTS_TYPE_REDEEM.getTypeCode()));
        }

        List<WriteOffBillDTO> writeOffBillDTOList = transactionDTO.getWriteOffBillDTOList();
        if (!CollectionUtils.isEmpty(writeOffBillDTOList)) {
            BigDecimal penaltyMoney = BigDecimal.ZERO;
            for (WriteOffBillDTO writeOffBillDTO : writeOffBillDTOList) {
                if (BillChargeTypeEnum.PENALTY.getCode().equals(writeOffBillDTO.getChargeType())) {
                    penaltyMoney = penaltyMoney.add(writeOffBillDTO.getActualAmount() == null ? BigDecimal.ZERO : writeOffBillDTO.getActualAmount());
                }
            }
            result.setPayPenaltyPrice(penaltyMoney);
        }
        List<BillDataInfoVO> dataInfoDTOList = new ArrayList<>();
        BillDataInfoVO billDataInfoDTO = new BillDataInfoVO();
        billDataInfoDTO.setTranFlowId(transactionDTO.getId().toString());
        billDataInfoDTO.setBillTimeSection(ConverterSupport.buildTimeSection(writeOffBillDTOList, equityAdjustBillDetailDTOList));
        if (Objects.nonNull(adapter)) {
            billDataInfoDTO.setSubjectType(adapter.getType());
            billDataInfoDTO.setSubjectInfo(adapter.getAssetName());
        }
        dataInfoDTOList.add(billDataInfoDTO);
        result.setDataInfoList(dataInfoDTOList);
        return result;


    }

    private static String buildTimeSection(List<WriteOffBillDTO> writeOffBillDTOList, List<EquityAdjustBillDetailDTO> equityAdjustBillDetailDTOList) {
        String max = null;
        String min = null;
        List<String> belongYears = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(writeOffBillDTOList)) {
            List<String> belongYearWriteOffs = writeOffBillDTOList.stream().map(item -> item.getBelongYears())
                    .filter(belongYear -> StringUtils.isNotBlank(belongYear)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(belongYearWriteOffs)) {
                belongYears.addAll(belongYearWriteOffs);
            }
        }
        if (CollectionUtil.isNotEmpty(equityAdjustBillDetailDTOList)) {
            List<String> belongYearEquitys = equityAdjustBillDetailDTOList.stream().map(item -> item.getBelongYears())
                    .filter(belongYear -> StringUtils.isNotBlank(belongYear)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(belongYearEquitys)) {
                belongYears.addAll(belongYearEquitys);
            }
        }
        if (CollectionUtil.isNotEmpty(belongYears)) {
            Optional<String> belongYearsMax = belongYears.stream().max(Comparator.naturalOrder());
            Optional<String> belongYearsMin = belongYears.stream().min(Comparator.naturalOrder());
            max = belongYearsMax.isPresent() ? belongYearsMax.get() : max;
            min = belongYearsMin.isPresent() ? belongYearsMin.get() : min;
            if (Objects.equals(min, max) && (CollectionUtil.isNotEmpty(equityAdjustBillDetailDTOList) || CollectionUtil.isNotEmpty(writeOffBillDTOList))) {
                return DateUtils.format(min, DateUtils.FORMAT_14, DateUtils.FORMAT_15);
            } else {
                return DateUtils.format(min, DateUtils.FORMAT_14, DateUtils.FORMAT_15)
                        + "-" + DateUtils.format(max, DateUtils.FORMAT_14, DateUtils.FORMAT_15);
            }
        }
        return "";
    }

    private static BillInfoVO convertTransactionTOBillInfo(AssetTransactionDTO transactionDTO){
        BillInfoVO billInfoVO = new BillInfoVO();
        IncomeBillDTO incomeBillDTO = transactionDTO.getIncomeBillDTO();
        if (Objects.nonNull(incomeBillDTO)) {
            billInfoVO.setPayMember(incomeBillDTO.getPayMember());
            billInfoVO.setPaymentChannel(incomeBillDTO.getPaymentChannel());
            billInfoVO.setPaymentTerminal(incomeBillDTO.getPaymentTerminal());
            billInfoVO.setTransactionNo(incomeBillDTO.getOutTransactionNo());
        }
        if(Objects.equals(PaymentMethodEnum.DEDUCT.getPaymentCode(),transactionDTO.getPaymentMethod()) ||Objects.equals(PaymentMethodEnum.CARRY_FORWARD_DEDUCTIONS.getPaymentCode(),transactionDTO.getPaymentMethod()) ){
            billInfoVO.setPaymentChannel(PaymentChannelEnum.TRANSFER_OFFLINE.getCode());
            billInfoVO.setPaymentTerminal(PaymentTerminalEnum.CHARGE_SYSTEM.getCode());
            billInfoVO.setTransactionNo("");
            billInfoVO.setPayMember("");
        }
        billInfoVO.setOrderNum(transactionDTO.getAssetOrderNum());
        billInfoVO.setPaymentMethod(transactionDTO.getPaymentMethod());
        billInfoVO.setPayTime(DateUtils.format(transactionDTO.getPaymentTime(), DateUtils.FORMAT_0));
        if(!Objects.equals(PaymentMethodEnum.EQUITY.getPaymentCode(),transactionDTO.getPaymentMethod())){
            billInfoVO.setPayPrice(transactionDTO.getMoney());
        }
        billInfoVO.setTotalPrice(transactionDTO.getMoney());
        return billInfoVO;
    }
}
