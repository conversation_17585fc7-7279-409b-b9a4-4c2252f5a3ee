package com.charge.api.web.controller.order;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.charge.api.web.convert.EBusinessBaseOrderConverter;
import com.charge.api.web.convert.EBusinessMasterOrderConverter;
import com.charge.api.web.service.ebusiness.EBusinessOrderService;
import com.charge.api.web.support.CommunityParamSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.support.EBusinessOrderSupport;
import com.charge.api.web.vo.BankAccountVO;
import com.charge.api.web.vo.BaseCommunityQuery;
import com.charge.api.web.vo.ebusiness.EBusinessPayBankInfo;
import com.charge.api.web.vo.order.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.order.client.EBusinessOrderQueryClient;
import com.charge.order.dto.ebuiness.EBusinessBaseOrderDTO;
import com.charge.order.dto.ebuiness.EBusinessOrderDetailDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单查询相关接口
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping(value = "order")
public class OrderQueryController {

    private final EBusinessOrderQueryClient orderQueryClient;

    private final EBusinessOrderService  eBusinessOrderService;

    private final EBusinessOrderSupport eBusinessOrderSupport;

    private final CommunityParamSupport communitySupport;

    /**
     * 查询子订单详情
     *
     * @param query 子单查询
     * @return 子单详情
     */
    @PostMapping(value = "/subOrder/detail")
    public ChargeResponse<SubOrderVO> querySubOrder(@RequestBody SubOrderQuery query) throws ChargeBusinessException {
        communitySupport.fillCommunityId(query);
        ChargeResponse<EBusinessBaseOrderDTO> chargeResponse = orderQueryClient.subOrderDetail(
                EBusinessBaseOrderConverter.INSTANCE.map(query));
        EBusinessBaseOrderDTO orderDTO = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        return new ChargeResponse<>(EBusinessBaseOrderConverter.INSTANCE.map(orderDTO));
    }

    /**
     * 查询订单详情
     *
     * @param orderQuery 订单查询
     * @return 订单详情
     */
    @PostMapping(value = "/detail")
    public ChargeResponse<OrderDetailVO> queryOrder(@RequestBody OrderQuery orderQuery) throws ChargeBusinessException {
        EBusinessOrderDetailDTO detailDTO = eBusinessOrderSupport.getEBusinessOrderDetail(orderQuery);
        OrderDetailVO detailVO = EBusinessMasterOrderConverter.INSTANCE.map(detailDTO);
        if(detailDTO!=null&&CollectionUtils.isNotEmpty(detailDTO.getAdjusts())){
            Map<Long, EBusinessBaseOrderDTO> baseOrderMap = detailDTO.getBaseOrders().stream().collect(Collectors.toMap(EBusinessBaseOrderDTO::getId, Function.identity(), (a, b) -> b));
            List<OrderAdjustVO> adjustVOS = detailDTO.getAdjusts().stream().map(a -> {
                OrderAdjustVO adjustVO = EBusinessMasterOrderConverter.INSTANCE.map(a);
                EBusinessBaseOrderDTO eBusinessBaseOrderDTO = baseOrderMap.get(a.getFromSubOrderId());
                if (eBusinessBaseOrderDTO != null) {
                    adjustVO.setSubOrderNo(eBusinessBaseOrderDTO.getExtendOrderId());
                }
                return adjustVO;
            }).collect(Collectors.toList());
            detailVO.setAdjusts(adjustVOS);
        }
        return new ChargeResponse<>(detailVO);
    }

    /**
     * 根据退款单号查询退款单详情
     *
     * @param refundDetailQuery 退款详情查询
     * @return 退款单详情
     */
    @PostMapping(value = "/refund/detail")
    public ChargeResponse<OrderRefundDetailVO> queryRefundDetail(@RequestBody OrderRefundDetailQuery refundDetailQuery) {
        return new ChargeResponse<>();
    }

    /**
     * 查询项目收款银行账号
     *
     * @param communityQuery 项目查询
     * @return 银行账号
     */
    @PostMapping(value = "/community/acceptor-account")
    public ChargeResponse<BankAccountVO> queryCommunityAcceptorAccount(@RequestBody BaseCommunityQuery communityQuery) throws ChargeBusinessException {
        communitySupport.fillCommunityId(communityQuery);
        EBusinessPayBankInfo bankInfo = eBusinessOrderService.getEBusinessPayBankInfo(communityQuery.getCommunityId());
        return new ChargeResponse<>(new BankAccountVO(bankInfo.getBankAccountNo(),bankInfo.getAcceptAccountName()));
    }

}