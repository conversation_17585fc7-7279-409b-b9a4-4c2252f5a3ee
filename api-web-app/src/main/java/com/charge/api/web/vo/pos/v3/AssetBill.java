package com.charge.api.web.vo.pos.v3;

import com.charge.api.web.vo.BillItem;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/02/25
 */
@Data
public class AssetBill implements Serializable {

    private static final long serialVersionUID = 1143838172525744243L;
    /**
     * 资产id
     */
    @NotNull(message = "资产id不能为空")
    private Long assetId;

    /**
     * 缴费金额
     */
    @NotNull(message = "资产缴费金额不能为空")
    private BigDecimal amount;

    /**
     * 应收单列表
     */
    @Valid
    private List<BillItem> receivables;

    /**
     * 预存列表
     */
    @Valid
    private List<BillItem> prestores;

    /**
     * 押金列表
     */
    @Valid
    private List<BillItem> deposits;

    /**
     * 临时订单列表
     */
    @Valid
    private List<BillItem> orders;
}
