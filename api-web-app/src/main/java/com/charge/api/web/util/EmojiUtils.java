package com.charge.api.web.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Date: 2024/12/10/ 16:53
 * @description
 */
@Slf4j
public class EmojiUtils {

    public static String filterOffUtf8Mb4_2(String text) {
        if (StringUtils.isBlank(text)) {
            return StringUtils.EMPTY;
        }
        byte[] bytes = text.getBytes(StandardCharsets.UTF_8);
        ByteBuffer buffer = ByteBuffer.allocate(bytes.length);
        try {
            int i = 0;
            while (i < bytes.length) {
                short b = bytes[i];
                if (b > 0) {
                    buffer.put(bytes[i++]);
                    continue;
                }
                b += 256; //去掉符号位
                if (((b >> 5) ^ 0x06) == 0) {
                    buffer.put(bytes, i, 2);
                    i += 2;
                } else if (((b >> 4) ^ 0x0E) == 0) {
                    buffer.put(bytes, i, 3);
                    i += 3;
                } else if (((b >> 3) ^ 0x1E) == 0) {
                    i += 4;
                } else {
                    i += 6;
                }
            }
            buffer.flip();
        } catch (Exception e) {
            log.error("过滤emoji错误， 源字符串:{}，错误详情:", text, e);
        }
        return new String(buffer.array(), StandardCharsets.UTF_8);
    }
}
