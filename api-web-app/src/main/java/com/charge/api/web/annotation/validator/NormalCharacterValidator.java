package com.charge.api.web.annotation.validator;

import com.charge.api.web.annotation.NormalCharacter;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NormalCharacterValidator implements ConstraintValidator<NormalCharacter, String> {

    private NormalCharacter anno;

    public Pattern p;

    @Override
    public boolean isValid(String key, ConstraintValidatorContext constraintValidatorContext) {
        if (key == null) {
            return true;
        }
        Matcher m = p.matcher(key);
        if (m.find()) {
            System.out.println(m.group(0));
            return false;
        }
        return true;
    }

    @Override
    public void initialize(NormalCharacter constraintAnnotation) {
        String regex = "([,，?？= ;；：:\"\"“”‘’''~\\.。\\[\\]【】!！@#$%^&*()_+\\-<>、（）《》\\{\\}\\\\/])+";
        this.anno = constraintAnnotation;
        this.p = Pattern.compile(regex);
    }
}
