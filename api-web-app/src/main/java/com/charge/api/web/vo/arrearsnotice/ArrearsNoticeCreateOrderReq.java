package com.charge.api.web.vo.arrearsnotice;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 缴费通知下单请求
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@Data
public class ArrearsNoticeCreateOrderReq {

    /**
     * 项目ID
     */
    @NotNull
    private Long communityId;

    /**
     * 缴费通知单id
     */
    @NotNull
    private Long id;

    private String user;

    /**
     * 收费员ID
     */
    private String collectorId;

    /**
     * 收费员姓名
     */
    private String collectorName;

    @NotNull
    private BigDecimal totalAmount;

    private String token;
}
