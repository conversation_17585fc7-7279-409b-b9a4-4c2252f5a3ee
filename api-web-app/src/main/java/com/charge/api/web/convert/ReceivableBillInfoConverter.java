package com.charge.api.web.convert;

import com.charge.api.web.vo.joylife.request.ReceivableBillInfo;
import com.charge.api.web.vo.joylife.request.ReceivableBillInfoDTO;
import com.charge.common.util.DateMapper;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/08/07
 */
@Mapper(uses = {DateMapper.class})
public interface ReceivableBillInfoConverter {

    ReceivableBillInfoConverter INSTANCE = Mappers.getMapper(ReceivableBillInfoConverter.class);

    ReceivableBillInfoDTO toReceivableBillInfoDTO(ReceivableBillInfo receivableBillInfo);

    List<ReceivableBillInfoDTO> toReceivableBillInfoDTOS(List<ReceivableBillInfo> receivableBillInfos);

}
