package com.charge.api.web.support;

import com.charge.common.util.AppInterfaceUtil;
import com.charge.config.client.meter.MeterBookDetailClient;
import com.charge.config.dto.meter.MeterBookAssetDetailDTO;
import com.charge.config.dto.meter.condition.MeterBookDetailConditionDTO;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MeterBookSupport {

    private final MeterBookDetailClient meterBookDetailClient;

    public List<MeterBookAssetDetailDTO> getMeterDetailByCondition(MeterBookDetailConditionDTO conditionDTO){
        List<MeterBookAssetDetailDTO> meterDetails = new ArrayList<>();
        try{
            meterDetails = AppInterfaceUtil.getResponseDataThrowException(meterBookDetailClient.listByCondition(conditionDTO));
        } catch (Exception e){
            log.error("{}|查询仪表信息异常,入参：{}，异常：{}", LogCategoryEnum.BUSSINESS,conditionDTO,e);
        }
        return CollectionUtil.isEmpty(meterDetails)?new ArrayList<>():meterDetails;
    }
}
