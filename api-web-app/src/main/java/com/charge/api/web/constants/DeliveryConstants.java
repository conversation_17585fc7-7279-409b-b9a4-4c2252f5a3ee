package com.charge.api.web.constants;

/**
 * 专项预存组合（云交付）常量类
 * Author: yjw
 * Date: 2023/1/11 16:56
 */
public class DeliveryConstants {

    /**
     * 云交付缴费异常状态码：
     * -5：资产组合未配置/配置组合作废/不在有效期内
     * -6：提交的支付金额和当前生效的预存组合金额有差异，或者预存组合已作废或者已过有效期间
     * -7：该预存组合已被其他人支付
     */
    public static final Integer ERROR_CODE_GROUP_INVALID = -5;
    public static final Integer ERROR_CODE_GROUP_MONEY_WRONG = -6;
    public static final Integer ERROR_CODE_GROUP_PAYED = -7;

    public static final int RST_CODE_FOUNDNODATA_ERROR = 902;//暂无数据状态码

}
