package com.charge.api.web.service.bill.pos;


import com.charge.api.web.vo.lakala.CommunityOrderBillCreateRequest;
import com.charge.bill.dto.BillAssetInfoDTO;
import com.charge.bill.dto.PayOrAdjustItemDTO;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.domain.AssetPayDTO;
import com.charge.bill.dto.domain.AssetPaymentDetailDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * POS小区临停收费下单
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddCommunityTempService {

    private final CreateBillFlowService createOrderService;

    public CreateBillResponse createBill(CommunityOrderBillCreateRequest request, CommunityDTO community, AssetDTO asset) throws ChargeBusinessException {
        return createOrderService.createOrder(buildAssetPayDTO(request, community, asset));
    }

    private AssetPayDTO buildAssetPayDTO(CommunityOrderBillCreateRequest request, CommunityDTO community, AssetDTO asset) throws ChargeBusinessException {
        AssetPayDTO assetPayDTO = new AssetPayDTO();
        assetPayDTO.setAssetPayBaseDTO(buildAssetPayBaseDTO(request, community));
        assetPayDTO.setAssetPaymentDetailDTO(buildAssetPaymentDetailDTO(request, asset, community));
        return assetPayDTO;
    }

    private AssetPayBaseDTO buildAssetPayBaseDTO(CommunityOrderBillCreateRequest request, CommunityDTO community) throws ChargeBusinessException {
        AssetPayBaseDTO assetPayBaseDTO = new AssetPayBaseDTO();
        assetPayBaseDTO.setCommunityId(community.getId());
        assetPayBaseDTO.setCommunityName(community.getName());
        assetPayBaseDTO.setOrderNum(IdGeneratorSupport.getIstance().nextId());
        assetPayBaseDTO.setOutTransactionNo(request.getBankTransactionNo());
        assetPayBaseDTO.setActualPrice(new BigDecimal(request.getAmount()));
        assetPayBaseDTO.setPaymentMethod(FillCommonUtil.toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())).getPaymentCode());
        assetPayBaseDTO.setPayMember(request.getPayMember());
        assetPayBaseDTO.setCollectorId(request.getCollectorId());
        assetPayBaseDTO.setCollectorName(request.getCollectorName());
        assetPayBaseDTO.setMemo(request.getMemo());
        assetPayBaseDTO.setDeviceInfo(request.getDeviceInfo());
        assetPayBaseDTO.setMercid(request.getMercid());
        FillCommonUtil.fillAssetPayBaseCommon(request.getPaymentMethod(), assetPayBaseDTO);
        assetPayBaseDTO.setPaymentTerminal(PaymentTerminalEnum.POS.getCode());
        assetPayBaseDTO.setPayHouseCount(1);
        assetPayBaseDTO.setClientSourceEnum(ClientSourceEnum.POS_TEMP_PAY);
        return assetPayBaseDTO;
    }

    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(CommunityOrderBillCreateRequest request, AssetDTO assetDTO, CommunityDTO communityDTO) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 临时订单信息
        assetPaymentDetailDTO.setOrderItems(buildOrderItem(request));
        // 资产信息
        BillAssetInfoDTO billAssetInfoDTO = FillCommonUtil.buildBillAssetInfo(assetDTO);
        billAssetInfoDTO.setCommunityId(communityDTO.getId());
        billAssetInfoDTO.setCommunityName(communityDTO.getName());
        assetPaymentDetailDTO.setBillAssetInfoDTO(billAssetInfoDTO);
        return assetPaymentDetailDTO;
    }

    private List<PayOrAdjustItemDTO> buildOrderItem(CommunityOrderBillCreateRequest request) {
        PayOrAdjustItemDTO orderItem = new PayOrAdjustItemDTO();
        orderItem.setItemId(Long.valueOf(request.getChargeItemUuid()));
        orderItem.setItemName(request.getItemName());
        orderItem.setAmount(new BigDecimal(request.getAmount()));
        return Arrays.asList(orderItem);
    }

}
