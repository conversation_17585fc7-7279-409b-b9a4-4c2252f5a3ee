package com.charge.api.web.service.bill.joy;

import com.charge.api.web.adapter.PayOrderAdapter;
import com.charge.bill.client.flow.AssetPaymentClient;
import com.charge.bill.dto.domain.AssetPayDTO;
import com.charge.bill.dto.domain.AssetsPayDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.dto.domain.response.CreateBillsResponse;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.joylife.dto.BatchPayInfoDTO;
import com.charge.joylife.dto.PayInfoDTO;
import com.charge.pay.client.PayClient;
import com.charge.pay.dto.pay.PayOrderRequestDTO;
import com.charge.pay.dto.pay.PayOrderSubmitResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class CreateOrderFlowService {

    private final PayClient payClient;
    private final AssetPaymentClient assetPaymentClient;

    /**
     * 下单并返回支付信息
     * @param assetPayDTO
     * @param payOrderRequestDTO
     * @return
     * @throws ChargeBusinessException
     */
    public ChargeResponse<Object> createOrder(AssetPayDTO assetPayDTO, PayOrderRequestDTO payOrderRequestDTO) throws ChargeBusinessException {
        // 创建订单
        PayOrderSubmitResponseDTO payReturnDTO = orderPay(payOrderRequestDTO);
        // 创建账单
        assetPayDTO.getAssetPayBaseDTO().setOutTransactionNo(payReturnDTO.getOutTradeNo());
        assetPayDTO.getAssetPayBaseDTO().setIsMergeBill(payReturnDTO.getIsMergeBill());
        assetPayDTO.getAssetPayBaseDTO().setSubBills(PayOrderAdapter.fillSubBills(payReturnDTO.getSubItems()));
        CreateBillResponse createBillResponse = createBill(assetPayDTO);
        log.info("{}|【朝昔】账单下单，返回下单信息|{}", LogCategoryEnum.BUSSINESS, createBillResponse);
        // 返回Response
        PayInfoDTO payInfoDTO = PayOrderAdapter.responseParamConvert(payReturnDTO, createBillResponse.getIncomeBillId());
        log.info("{}|【朝昔】编排下单，返回下单信息|{}", LogCategoryEnum.BUSSINESS, payInfoDTO);
        return new ChargeResponse<>(payInfoDTO);
    }

    /**
     * 创建支付信息
     * @param payOrderRequestDTO
     * @return
     * @throws ChargeBusinessException
     */
    private PayOrderSubmitResponseDTO orderPay(PayOrderRequestDTO payOrderRequestDTO) throws ChargeBusinessException {
        ChargeResponse<PayOrderSubmitResponseDTO> payReturnChargeResponse = payClient.tradeCreate(payOrderRequestDTO);
        if (!payReturnChargeResponse.isSuccess() || payReturnChargeResponse.getContent() == null) {
            log.error("{}|预存缴费下单异常|{}", LogCategoryEnum.BUSSINESS, payReturnChargeResponse);
            throw new ChargeBusinessException(String.valueOf(payReturnChargeResponse.getCode()), payReturnChargeResponse.getMessage());
        }
        return payReturnChargeResponse.getContent();
    }

    /**
     * 下单创建账单
     * @param assetPayDTO
     * @return
     * @throws ChargeBusinessException
     */
    private CreateBillResponse createBill(AssetPayDTO assetPayDTO) throws ChargeBusinessException {
        return AppInterfaceUtil.getResponseDataThrowException(assetPaymentClient.createBill(assetPayDTO));
    }

    /**
     * 批量资产下单并返回支付信息
     * @param assetsPayDTO
     * @param payOrderRequestDTO
     * @return
     * @throws ChargeBusinessException
     */
    public ChargeResponse<BatchPayInfoDTO> createBatchAssetOrder(AssetsPayDTO assetsPayDTO, PayOrderRequestDTO payOrderRequestDTO) throws ChargeBusinessException {
        // 创建订单
        PayOrderSubmitResponseDTO payReturnDTO = orderPay(payOrderRequestDTO);
        assetsPayDTO.getAssetPayBaseDTO().setOutTransactionNo(payReturnDTO.getOutTradeNo());
        assetsPayDTO.getAssetPayBaseDTO().setIsMergeBill(payReturnDTO.getIsMergeBill());
        assetsPayDTO.getAssetPayBaseDTO().setSubBills(PayOrderAdapter.fillSubBills(payReturnDTO.getSubItems()));

        // 创建账单
        CreateBillsResponse createBillsResponse = createBills(assetsPayDTO);
        log.info("{}|【朝昔】批量资产账单下单，返回下单信息|{}", LogCategoryEnum.BUSSINESS, createBillsResponse);
        // 返回Response
        BatchPayInfoDTO batchPayInfoDTO = PayOrderAdapter.responseParamConvertForBatch(payReturnDTO, createBillsResponse.getIncomeBillId());
        log.info("{}|【朝昔】批量资产编排下单，返回下单信息|{}", LogCategoryEnum.BUSSINESS, batchPayInfoDTO);
        return new ChargeResponse<>(batchPayInfoDTO);
    }

    /**
     * 下单创建账单
     * @param assetsPayDTO
     * @return
     * @throws ChargeBusinessException
     */
    private CreateBillsResponse createBills(AssetsPayDTO assetsPayDTO) throws ChargeBusinessException {
        return AppInterfaceUtil.getResponseDataThrowException(assetPaymentClient.createBills(assetsPayDTO));
    }


}
