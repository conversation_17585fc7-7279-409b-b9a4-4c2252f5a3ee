package com.charge.api.web.vo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 子订单调整到命令
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SubOrderAdjustToCmd extends BaseOrderCmdReq {

    /**
     * 调整原子订单
     */
    @NotNull(message = "调整原子订单不能为空")
    @Valid
    private SubOrderAdjustFrom fromSubOrders;

    /**
     * 调整目标子订单
     */
    @NotNull(message = "调整目标子订单不能为空")
    @Valid
    private SubOrderAdjustTo toSubOrders;

}