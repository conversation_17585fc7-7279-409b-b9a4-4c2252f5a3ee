package com.charge.api.web.convert;

import com.charge.api.web.dto.joylife.TradeDetailDTO;
import com.charge.api.web.vo.joylife.request.YueCardPayRequestVO;
import com.charge.bill.dto.income.AssetTransactionDTO;
import com.charge.joylife.dto.DeliveryPayDataRequest;
import com.charge.pay.dto.PayAndRecordDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/21
 */
@Mapper(uses = {StringToLongMapper.class})
public interface TradeDetailConverter {

    TradeDetailConverter INSTANCE = Mappers.getMapper(TradeDetailConverter.class);

    @Mapping(source = "assetId", target = "houseId")
    @Mapping(source = "assetName", target = "houseName")
    @Mapping(source = "money", target = "totalPay")
    TradeDetailDTO toTradeDetailDTO(AssetTransactionDTO assetTransactionDTO);

    @Mapping(target = "actualPrice", source = "money")
    @Mapping(target = "paymentTerminal", source = "paymentSource")
    PayAndRecordDTO toPayAndRecordDTO(DeliveryPayDataRequest request);

    @Mapping(target = "actualPrice", source = "amount")
    @Mapping(target = "paymentTerminal", source = "paymentSource")
    PayAndRecordDTO toPayAndRecordDTO(YueCardPayRequestVO request);
}
