package com.charge.api.web.vo.order;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 子单进度单金额
 *
 * <AUTHOR>
 * @date 2024/12/3
 */
@Data
public class SubOrderInstallmentAmount {
    /**
     * 子订单号
     */
    @NotNull(message = "子订单号不能为空")
    private String subOrderNo;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    /**
     * 订单分期id
     */
    @NotBlank(message = "支付进度id不能为空")
    private String progressId;


}