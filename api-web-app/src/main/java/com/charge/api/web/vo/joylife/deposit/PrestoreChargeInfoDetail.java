package com.charge.api.web.vo.joylife.deposit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PrestoreChargeInfoDetail {
	String info;
	String money;

	public static List<PrestoreChargeInfoDetail> fromDefault() {
		List<PrestoreChargeInfoDetail> info=new ArrayList<>();
		info.add(new PrestoreChargeInfoDetail("","300") );
		info.add(new PrestoreChargeInfoDetail("","500") );
		info.add(new PrestoreChargeInfoDetail("","1000") );
		return info;
	}

	public static List<PrestoreChargeInfoDetail> from(BigDecimal moneyMonth) {
		if(ObjectUtils.isEmpty(moneyMonth)){
			moneyMonth=BigDecimal.ZERO;
		}
		List<PrestoreChargeInfoDetail> info=new ArrayList<>();
		info.add(new PrestoreChargeInfoDetail("3个月",moneyMonth.multiply(new BigDecimal(3)).setScale(2,BigDecimal.ROUND_HALF_UP).toString()) );
		info.add(new PrestoreChargeInfoDetail("6个月",moneyMonth.multiply(new BigDecimal(6)).setScale(2,BigDecimal.ROUND_HALF_UP).toString()) );
		info.add(new PrestoreChargeInfoDetail("12个月",moneyMonth.multiply(new BigDecimal(12)).setScale(2,BigDecimal.ROUND_HALF_UP).toString())  );
		return info;
	}
}
