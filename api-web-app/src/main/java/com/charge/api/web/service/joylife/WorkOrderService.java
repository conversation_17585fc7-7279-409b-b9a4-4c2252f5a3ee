package com.charge.api.web.service.joylife;

import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.joylife.dto.PayInfoDTO;
import com.charge.order.dto.WorkOrderPayCmd;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 9:51
 */
public interface WorkOrderService {

    ChargeResponse<PayInfoDTO> orderPay( WorkOrderPayCmd workOrderPayCmd,String msid) throws ChargeBusinessException;
}
