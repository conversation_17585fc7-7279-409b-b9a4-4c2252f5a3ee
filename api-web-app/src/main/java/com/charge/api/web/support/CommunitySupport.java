package com.charge.api.web.support;

import com.charge.api.web.util.FeignUtil;
import com.charge.api.web.vo.joylife.request.ZhaoXiCommunityReq;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.enums.redis.RedisKeyCommonEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.AssertUtils;
import com.charge.common.util.DateUtils;
import com.charge.common.util.SpringContextUtil;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.pay.client.CommunityMerchantClient;
import com.charge.pay.client.PayRecordClient;
import com.charge.pay.dto.CommunityMerchantDTO;
import com.charge.pay.dto.CommunityMerchantTradePayTypeDTO;
import com.charge.pay.dto.PayRecord;
import com.charge.pay.dto.condition.MerchantConditionDTO;
import com.charge.pay.dto.enums.CommunityMerchantStatusEnum;
import com.charge.starter.jedis.JedisManager;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Author: yjw
 * Date: 2023/3/9 14:29
 */
@Data
@Slf4j
@Component
@RefreshScope
public class CommunitySupport {

    @Autowired
    private CommunityClient communityClient;

    @Autowired
    private JedisManager jedisManager;

    @Autowired
    private PayRecordClient payRecordClient;

    @Autowired
    private CommunityMerchantClient communityMerchantClient;

    private static final int TRANSACTION_COMMUNITY_RELATION_TTL_SECONDS = (int)Duration.of(7, ChronoUnit.DAYS).getSeconds();

    @Cacheable(value = "community", key = "#communityMsId",cacheManager = "cacheManager10m")
    public Long getCommunityIdByMsId(String communityMsId) throws ChargeBusinessException {
        return getCommunityByMsId(communityMsId).getId();
    }

    @Cacheable(value = "communityMerchantTradePayType", key = "#communityId",cacheManager = "cacheManager10m")
    public CommunityMerchantTradePayTypeDTO getMerchantTradePayType( Long communityId) throws ChargeBusinessException {
        ChargeResponse<CommunityMerchantTradePayTypeDTO> merchantTradePayType = communityMerchantClient.getMerchantTradePayType(communityId);
        return AppInterfaceUtil.getResponseDataThrowException(merchantTradePayType);
    }

    @Cacheable(value = "community1", key = "#mdmCode",cacheManager = "cacheManager10m")
    public Long getCommunityIdByMdmCode(String mdmCode) throws ChargeBusinessException {
        return getCommunityByMdmCode(mdmCode).getId();
    }

    @Cacheable(value = "communityMerchant", key = "#communityId",cacheManager = "cacheManager10m")
    public List<CommunityMerchantDTO> getCommunityMerchants(Long communityId) throws ChargeBusinessException {
        MerchantConditionDTO query = new MerchantConditionDTO();
        query.setCommunityId(communityId);
        query.setStatus(CommunityMerchantStatusEnum.MERCHANT_STATUS_ENABLE.getCode());
        ChargeResponse<List<CommunityMerchantDTO>> config = communityMerchantClient.selectByCondition(query);
        return AppInterfaceUtil.getResponseDataThrowException(config);
    }



    public CommunityDTO fillCommunityId(ZhaoXiCommunityReq zhaoXiCommunityReq) throws ChargeBusinessException {
        CommunityDTO communityDTO;
        if(zhaoXiCommunityReq.getCommunityId()==null){
            AssertUtils.isTrue(zhaoXiCommunityReq.getCommunityMsId()!=null,"朝昔的项目id不能为空");
            communityDTO = getCommunityByMsId(zhaoXiCommunityReq.getCommunityMsId());
            AssertUtils.isTrue(communityDTO!=null,"朝昔的项目id查不到:"+zhaoXiCommunityReq.getCommunityMsId());
        }else {
            communityDTO=getCommunityById(zhaoXiCommunityReq.getCommunityId());
        }
        Assert.notNull(communityDTO,"项目不存在");
        zhaoXiCommunityReq.setCommunityId(communityDTO.getId());
        return communityDTO;
    }

    public CommunityDTO getCommunityByMdmCode(String mdmCode) throws ChargeBusinessException {
        ChargeResponse<List<CommunityDTO>> communityDTOChargeResponse =
                communityClient.listCommunity(CommunityCondition.builder().mdmCode(mdmCode).build());
        List<CommunityDTO> communityDTOList = AppInterfaceUtil.getResponseDataThrowException(communityDTOChargeResponse);
        if (CollectionUtils.isEmpty(communityDTOList)) {
            throw new ChargeBusinessException( ErrorInfoEnum.E1002.getCode(),"该mdmCode的项目不存在:"+mdmCode);
        }
        return communityDTOList.get(0);
    }

    public CommunityDTO getCommunityByMsId(String communityMsId) throws ChargeBusinessException {
        ChargeResponse<List<CommunityDTO>> communityDTOChargeResponse =
                communityClient.listCommunity(CommunityCondition.builder().msIds(Collections.singletonList(communityMsId)).build());
        List<CommunityDTO> communityDTOList = AppInterfaceUtil.getResponseDataThrowException(communityDTOChargeResponse);
        if (CollectionUtils.isEmpty(communityDTOList)) {
            throw new ChargeBusinessException( ErrorInfoEnum.E1002.getCode(),"朝昔的项目不存在:"+communityMsId);
        }
        return communityDTOList.get(0);
    }

    public void setAssetTransactionIdCommunityIdRelation(Long assetTransactionId, Long communityId) {
        jedisManager.setValue(getAssetTransactionIdCommunityIdRelationKey(assetTransactionId), communityId, TRANSACTION_COMMUNITY_RELATION_TTL_SECONDS);
    }

    public Long getCommunityId(Long assetTransactionId) {
        return (Long)jedisManager.getValue(getAssetTransactionIdCommunityIdRelationKey(assetTransactionId));
    }

    private String getAssetTransactionIdCommunityIdRelationKey(Long assetTransactionId) {
        return RedisKeyCommonEnum.ASSET_TRANSACTION_ID_MATCH_COMMUNITY.key(assetTransactionId);
    }

    public void cacheCommunityIdWithAssetTrId(List<String> transactionIds, Long communityId) {
        if (transactionIds.size() > 1) {
            Map<String, Object> dataMap = Maps.newHashMapWithExpectedSize(transactionIds.size());
            transactionIds.stream().forEach(id -> {
                dataMap.put(RedisKeyCommonEnum.ASSET_TRANSACTION_ID_MATCH_COMMUNITY.key(id), communityId);
            });
            SpringContextUtil.getJedisManager().setValues(dataMap, DateUtils.DataTimeSec.WEEK);
        } else {
            cacheCommunityIdWithAssetTrId(transactionIds.get(0), communityId);
        }
    }

    public void cacheCommunityIdWithAssetTrId(String transactionId, Long communityId) {
        SpringContextUtil.getJedisManager().setValue(RedisKeyCommonEnum.ASSET_TRANSACTION_ID_MATCH_COMMUNITY.key(transactionId), communityId, DateUtils.DataTimeSec.WEEK);
    }
    public Long getCommunityIdByIncomeId(Long incomeId) {
        return (Long)jedisManager.getValue(RedisKeyCommonEnum.INCOME_BILL_ID_MATCH_COMMMUNITY.key(incomeId));
    }

    public void cacheCommunity(Map<String ,Object> dataMap) {
        jedisManager.setValues(dataMap,DateUtils.DataTimeSec.WEEK);
    }

    public Long getCommunityIdByOrderNum(String orderNum) {
        ChargeResponse<PayRecord> payRecordResponse = payRecordClient.selectByOrderNum(orderNum);
        PayRecord payRecord = FeignUtil.getContent(payRecordResponse, "查询支付记录信息");
        return payRecord.getCommunityId();
    }

    public CommunityDTO getCommunityById(Long communityId) throws ChargeBusinessException {
        Assert.notNull(communityId,"项目id不能为空");
        ChargeResponse<CommunityDTO> response = communityClient.oneByCondition(CommunityCondition.builder().id(communityId).build());
        CommunityDTO communityDTO = AppInterfaceUtil.getResponseDataThrowException(response);
        Assert.notNull(communityDTO, "通过项目id查询不到项目信息");
        return communityDTO;
    }

    public List<CommunityDTO> getV2CommunityByIds(List<String> communityMsIds) throws ChargeBusinessException {
        ChargeResponse<List<CommunityDTO>> communityDTOChargeResponse =
                communityClient.listCommunity(CommunityCondition.builder().msIds(communityMsIds).build());
        List<CommunityDTO> communityDTOList = AppInterfaceUtil.getResponseDataThrowException(communityDTOChargeResponse);
        if (CollectionUtils.isEmpty(communityDTOList)) {
            log.error("通过ms id：{}查询不到对应小区", communityMsIds);
            return new ArrayList<>();
        }
        return communityDTOList;
    }
}
