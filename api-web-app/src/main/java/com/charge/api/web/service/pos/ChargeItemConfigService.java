package com.charge.api.web.service.pos;

import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.DepositItemVO;
import com.charge.api.web.vo.pos.PrestoreItemVO;
import com.charge.api.web.vo.pos.SystemChargeItemVO;
import com.charge.api.web.vo.pos.TemporaryChargeItemVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.pos.dto.OrderRuleChargeItemDTO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * [收费项2.0 服务接口]
 *
 * <AUTHOR> wuChao
 * @version : [v1.0]
 */
public interface ChargeItemConfigService {


    /**
     * description: 订单品类收费项 分页查询
     * author: wuChao
     * date: 2023/3/1
     * param [dto]
     * return com.charge.api.web.vo.ChargePageResponse<java.util.List<com.charge.api.web.vo.pos.SystemChargeItemVO>>
     **/
    ChargePageResponse<List<SystemChargeItemVO>> getOrderRuleChargeItemPage(OrderRuleChargeItemDTO dto);
    /**
     * description: 收费项 分页查询  订单品类临时-临停
     * author: wuChao
     * date: 2023/3/1
     * param [dto]
     * return java.util.List<com.charge.api.web.vo.pos.SystemChargeItemVO>
     **/
    List<TemporaryChargeItemVO> getOrderRuleChargeItemTemporaryStop(OrderRuleChargeItemDTO dto);

    /**
     * description: 收费项 分页查询
     * author: wuChao
     * date: 2023/3/1
     * param [dto]
     * return com.charge.common.dto.ChargeResponse<java.util.List<com.charge.api.web.vo.pos.PrestoreItemVO>>
     **/
    ChargeResponse<List<PrestoreItemVO>> getPrestoreItem(OrderRuleChargeItemDTO dto);


    /**
     * 分页查询押金项
     * @param communityId 项目id
     * @param keyword 搜索的关键字
     * @param pageNum 页码
     * @param pageSize 页面尺寸
     */
    ChargePageResponse<List<DepositItemVO>> pageQueryDepositItem(Long communityId, Integer pageNum, Integer pageSize, String keyword ) throws ChargeBusinessException;
}
