package com.charge.api.web.service.order;

import com.alibaba.fastjson.JSONArray;
import com.charge.api.web.convert.EBusinessOrderConverter;
import com.charge.api.web.service.ebusiness.EBusinessOrderService;
import com.charge.api.web.support.*;
import com.charge.api.web.vo.ebusiness.EBusinessEmsRefundItem;
import com.charge.api.web.vo.ebusiness.EBusinessEmsRefundOrderCondition;
import com.charge.api.web.vo.order.SubOrderAmount;
import com.charge.api.web.vo.order.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.order.client.EBusinessOrderCmdClient;
import com.charge.order.client.OrderRuleClient;
import com.charge.order.dto.ebuiness.*;
import com.charge.order.dto.rule.CommunityBaseOrderRuleDTO;
import com.charge.order.dto.rule.CommunityOrderRuleQueryDTO;
import com.charge.order.enums.EBusinessOrderPayStatusEnum;
import com.charge.order.enums.OrderSourceEnum;
import com.charge.order.enums.PaymentModeEnum;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.charge.common.util.DateUtils.FORMAT_0;

/**
 * OrderService 订单服务
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RestController
@Slf4j
@RequiredArgsConstructor
public class OrderService {

    private final OrderRuleClient orderRuleClient;

    private final EBusinessOrderCmdClient eBusinessOrderCmdClient;

    private final CommunityParamSupport communitySupport;

    private final AssetSupport assetSupport;

    private final EBusinessOrderSupport eBusinessOrderSupport;

    private final ChargeItemSupport chargeItemSupport;

    private final EBusinessOrderService businessOrderService;

    public void addOrder(OrderAddCmd orderAddCmd) throws ChargeBusinessException {
        fillParam(orderAddCmd);
        validOrderAddCmd(orderAddCmd);
        addEBusinessOrder(orderAddCmd);
    }

    public void subOrderAdjustTo(SubOrderAdjustToCmd subOrderAdjustToCmd) throws ChargeBusinessException {
        fillParam(subOrderAdjustToCmd);
        validSubOrderAdjustToCmd(subOrderAdjustToCmd);
        eBusinessSubOrderAdjustTo(subOrderAdjustToCmd);
    }

    public String orderRefund(OrderRefundCmd refundCmd) throws ChargeBusinessException {
        Assert.hasText(refundCmd.getMemo(),"退款备注不能为空");
        communitySupport.fillCommunityId(refundCmd);
        if(refundCmd.getPayStatus().equals(EBusinessOrderPayStatusEnum.TO_PAY.getCode())){
            List<EBusinessEmsRefundItem> emsRefundItems = refundCmd.getSubOrders().stream().map(subOrderAdd -> EBusinessEmsRefundItem.builder()
                    .oid(subOrderAdd.getSubOrderNo())
                            .num(1L)
                    .refundAmount(subOrderAdd.getAmount()).build())
                    .collect(Collectors.toList());
            EBusinessEmsRefundOrderCondition refundOrderCondition=
                    EBusinessEmsRefundOrderCondition.builder().communityId(refundCmd.getCommunityId())
                            .orderNo(refundCmd.getOrderNo())
                            .refundNo(refundCmd.getRefundNo())
                            .refundItems(emsRefundItems)
                            .refundAmount(refundCmd.getRefundAmount())
                            .refundTime(DateUtils.format(refundCmd.getRefundTime(),FORMAT_0))
                            .bankAccountNo(refundCmd.getAcceptBankAccountNo())
                            .acceptAccountOpeningBank(refundCmd.getAcceptAccountOpeningBank())
                            .acceptAccountCity(refundCmd.getAcceptAccountCity())
                            .acceptAccountName(refundCmd.getAcceptAccountName())
                            .refundRemark(refundCmd.getMemo())
                            .operatorName(refundCmd.getOperatorName())
                            .build();
            return businessOrderService.emsRefundOrders(refundOrderCondition);
        }else {
            throw new ChargeBusinessException("暂不支持推送已支付的退款");
        }

    }



        public void subOrderAdjustAdd(SubOrderAdjustAddCmd adjustAddCmd) throws ChargeBusinessException {
        fillParam(adjustAddCmd);
        EBusinessOrderDetailDTO eBusinessOrderDetail = eBusinessOrderSupport.getEBusinessOrderDetail(OrderQuery.of(adjustAddCmd.getOrderNo()
                , adjustAddCmd.getSource(), adjustAddCmd.getCommunityId(),true));
        Assert.notNull(eBusinessOrderDetail, "订单不存在");
        validSubOrderAdjustAddCmd(adjustAddCmd,eBusinessOrderDetail.getMasterOrder());
        eBusinessSubOrderAdjustAdd(adjustAddCmd,eBusinessOrderDetail);
    }

    private void eBusinessSubOrderAdjustAdd(SubOrderAdjustAddCmd orderAdjustAddCmd,EBusinessOrderDetailDTO eBusinessOrderDetail) throws ChargeBusinessException {
        EBusinessSubOrderAdjustAddCmd eBusinessSubOrderAdjustAddCmd = EBusinessOrderConverter.INSTANCE.map(orderAdjustAddCmd);
        eBusinessSubOrderAdjustAddCmd.setSubOrderAmounts(EBusinessOrderConverter.INSTANCE.map(orderAdjustAddCmd.getSubOrderAmounts()));
        if(!CollectionUtils.isEmpty(eBusinessOrderDetail.getBaseOrders())){
            EBusinessBaseOrderDTO eBusinessBaseOrderDTO = eBusinessOrderDetail.getBaseOrders().get(0);
            eBusinessSubOrderAdjustAddCmd.getAssetCustomer().setAssetId(eBusinessBaseOrderDTO.getAssetId());
        }
        ChargeResponse<Void> chargeResponse = eBusinessOrderCmdClient.subOrderAdjustAdd(eBusinessSubOrderAdjustAddCmd);
        AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
    }

    private void validSubOrderAdjustAddCmd(SubOrderAdjustAddCmd orderAddCmd, EBusinessMasterOrderDTO eBusinessMasterOrderDTO) throws ChargeBusinessException {
        BigDecimal addAmount = orderAddCmd.getSubOrderAmounts().stream().map(a -> a.getOriginalAmount().add(a.getDiscountAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(addAmount.compareTo(orderAddCmd.getAmount()) == 0, "订单新增金额与子订单原始金额之和不相等");
        Assert.isTrue(EBusinessOrderPayStatusEnum.TO_PAY.getCode().equals(orderAddCmd.getPayStatus())
                ||EBusinessOrderPayStatusEnum.PAID.getCode().equals(orderAddCmd.getPayStatus()) ,
                "订单调整的支付状态必须是已支付或者未支付");
        if (EBusinessOrderPayStatusEnum.PAID.getCode().equals(orderAddCmd.getPayStatus())){
            Assert.hasText(orderAddCmd.getPayOrderNum(), "已支付订单的订单调整的支付单号不能为空");
        }
        List<Long> chargeItemIds = getChargeItemIds(orderAddCmd.getCommunityId(), eBusinessMasterOrderDTO.getSecondClassificationId());
        Assert.isTrue(!CollectionUtils.isEmpty(chargeItemIds), "项目该二级分类未配置收费项");
        orderAddCmd.getSubOrderAmounts().forEach(subOrderAdd -> {
            Assert.isTrue(chargeItemIds.contains(subOrderAdd.getItemId()), "子订单收费项id必须是项目该二级分类下的收费项");
        });
    }


    public void subOrderAdjustCancel(SubOrderAdjustCancelCmd adjustCancelCmd) throws ChargeBusinessException {
        communitySupport.fillCommunityId(adjustCancelCmd);
        validSubOrderAdjustCancelCmd(adjustCancelCmd);
        eBusinessSubOrderAdjustCancel(adjustCancelCmd);
    }

    private void validSubOrderAdjustCancelCmd(SubOrderAdjustCancelCmd adjustCancelCmd){

    }

    private void eBusinessSubOrderAdjustCancel(SubOrderAdjustCancelCmd adjustCancelCmd) throws ChargeBusinessException {
        EBusinessSubOrderAdjustCancelCmd eBusinessSubOrderAdjustCancelCmd = EBusinessOrderConverter.INSTANCE.map(adjustCancelCmd);
        ChargeResponse<Void> chargeResponse = eBusinessOrderCmdClient.subOrderAdjustCancel(eBusinessSubOrderAdjustCancelCmd);
        AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
    }

    private void eBusinessSubOrderAdjustTo(SubOrderAdjustToCmd subOrderAdjustToCmd) throws ChargeBusinessException {
        EBusinessSubOrderAdjustFrom eBusinessSubOrderAdjustFrom = EBusinessOrderConverter.INSTANCE.map(subOrderAdjustToCmd.getFromSubOrders());
        EBusinessSubOrderAdjustTo eBusinessSubOrderAdjustTo = EBusinessOrderConverter.INSTANCE.map(subOrderAdjustToCmd.getToSubOrders());

        EBusinessSubOrderAdjustToCmd eBusinessSubOrderAdjustToCmd=new EBusinessSubOrderAdjustToCmd();
        eBusinessSubOrderAdjustToCmd.setMemo(subOrderAdjustToCmd.getMemo());
        eBusinessSubOrderAdjustToCmd.setOperatorName(subOrderAdjustToCmd.getOperatorName());
        eBusinessSubOrderAdjustToCmd.setSource(OrderSourceEnum.of(subOrderAdjustToCmd.getSource()));
        eBusinessSubOrderAdjustToCmd.setCommunityId(subOrderAdjustToCmd.getCommunityId());
        eBusinessSubOrderAdjustToCmd.setOrderNo(subOrderAdjustToCmd.getOrderNo());
        eBusinessSubOrderAdjustToCmd.setFromSubOrders(eBusinessSubOrderAdjustFrom);
        eBusinessSubOrderAdjustToCmd.setToSubOrders(eBusinessSubOrderAdjustTo);
        ChargeResponse<Void> chargeResponse = eBusinessOrderCmdClient.subOrderAdjustTo(eBusinessSubOrderAdjustToCmd);
        AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
    }

    private void validSubOrderAdjustToCmd(SubOrderAdjustToCmd subOrderAdjustToCmd) throws ChargeBusinessException {
        BigDecimal fromAmount = subOrderAdjustToCmd.getFromSubOrders().getSubOrderAmounts().stream().map(SubOrderAmount::getAmount).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        BigDecimal toAmount = subOrderAdjustToCmd.getToSubOrders().getSubOrderAmounts().stream().map(SubOrderAdjustToItem::getAmount).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        Assert.isTrue(subOrderAdjustToCmd.getFromSubOrders().getAmount()
                .compareTo(subOrderAdjustToCmd.getToSubOrders().getAmount())==0,"调出调入金额必须相同");

        Assert.isTrue(subOrderAdjustToCmd.getFromSubOrders().getAmount()
                .compareTo(fromAmount)==0,"调出总金额与调出明细金额之和必须相同");

        Assert.isTrue(subOrderAdjustToCmd.getToSubOrders().getAmount()
                .compareTo(toAmount)==0,"调入总金额必须与调入金额之和必须相同");
        //订单状态与金额在order进行校验

        List<Long> chargeItemIds = getChargeItemIds(subOrderAdjustToCmd.getCommunityId()
                , subOrderAdjustToCmd.getToSubOrders().getSecondClassificationId());

        Assert.isTrue(!CollectionUtils.isEmpty(chargeItemIds), "项目该二级分类未配置收费项");
        subOrderAdjustToCmd.getToSubOrders().getSubOrderAmounts().forEach(subOrderAdd -> {
            Assert.isTrue(chargeItemIds.contains(subOrderAdd.getItemId()), "子订单收费项id必须是项目该二级分类下的收费项");
        });
    }

    private void fillParam(SubOrderAdjustToCmd orderAdjustToCmd) throws ChargeBusinessException {
        communitySupport.fillCommunityId(orderAdjustToCmd);
        chargeItemSupport.fillChargeItem(orderAdjustToCmd.getToSubOrders().getSubOrderAmounts());
        fillAssetId(orderAdjustToCmd);
        fillDefault(orderAdjustToCmd);
    }

    private void fillParam(SubOrderAdjustAddCmd orderAdjustToCmd) throws ChargeBusinessException {
        communitySupport.fillCommunityId(orderAdjustToCmd);
        chargeItemSupport.fillChargeItem(orderAdjustToCmd.getSubOrderAmounts());
        fillDefault(orderAdjustToCmd);
    }

    private void fillParam(OrderAddCmd orderAddCmd) throws ChargeBusinessException {
        communitySupport.fillCommunityId(orderAddCmd);
        chargeItemSupport.fillChargeItem(orderAddCmd.getSubOrders());
        fillAssetId(orderAddCmd);
        fillDefault(orderAddCmd);
    }

    private void addEBusinessOrder(OrderAddCmd orderAddCmd) throws ChargeBusinessException {
        EBusinessOrderAddCmd eBusinessOrderAddCmd = EBusinessOrderConverter.INSTANCE.map(orderAddCmd);
        eBusinessOrderAddCmd.setSubOrders(EBusinessOrderConverter.INSTANCE.map(orderAddCmd.getSubOrders()));
        ChargeResponse<Void> voidChargeResponse = eBusinessOrderCmdClient.addOrder(eBusinessOrderAddCmd);
        AppInterfaceUtil.getResponseDataThrowException(voidChargeResponse);
    }

    private void fillAssetId(SubOrderAdjustToCmd orderAddCmd) throws ChargeBusinessException {
        if(orderAddCmd.getToSubOrders().getAssetId()==null){
            AssetDTO communityVirtualHouse = assetSupport.getCommunityVirtualHouse(orderAddCmd.getCommunityId());
            orderAddCmd.getToSubOrders().setAssetId(communityVirtualHouse.getId());
        }
    }

    private void fillAssetId(OrderAddCmd orderAddCmd) throws ChargeBusinessException {
        if(orderAddCmd.getAssetId()==null){
            AssetDTO communityVirtualHouse = assetSupport.getCommunityVirtualHouse(orderAddCmd.getCommunityId());
            orderAddCmd.setAssetId(communityVirtualHouse.getId());
        }
    }

    private void validOrderAddCmd(OrderAddCmd orderAddCmd) throws ChargeBusinessException {
        Assert.isTrue(orderAddCmd.getPayStatus() != null && (orderAddCmd.getPayStatus().equals(EBusinessOrderPayStatusEnum.TO_PAY.getCode())
                        || orderAddCmd.getPayStatus().equals(EBusinessOrderPayStatusEnum.PAID.getCode())),
                "订单支付状态必须是已支付或者未支付");
        Assert.isTrue(orderAddCmd.getPayMode() != null && (orderAddCmd.getPayMode().equals(PaymentModeEnum.INSTALLMENT.getCode()) ||
                orderAddCmd.getPayMode().equals(PaymentModeEnum.FULL_AMOUNT.getCode())), "订单支付模式必须是全额支付或者分期支付");

        BigDecimal totalOriginalAmount = orderAddCmd.getSubOrders().stream().map(SubOrderAdd::getOriginalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(totalOriginalAmount.compareTo(orderAddCmd.getOriginalAmount()) == 0, "订单原始金额与子订单原始金额之和不相等");

        BigDecimal totalDiscountAmount = orderAddCmd.getSubOrders().stream().map(SubOrderAdd::getDiscountAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(totalDiscountAmount.compareTo(orderAddCmd.getDiscountAmount()) == 0, "订单优惠金额与子订单优惠金额之和不相等");

        BigDecimal totalPayAmount = orderAddCmd.getSubOrders().stream().map(SubOrderAdd::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.isTrue(totalPayAmount.compareTo(orderAddCmd.getPayAmount()) == 0, "订单支付金额与子订单支付金额之和不相等");
        Assert.isTrue(orderAddCmd.getOriginalAmount().add(orderAddCmd.getDiscountAmount()).compareTo(BigDecimal.ZERO) >= 0, "原始金额加折扣金额必须大于等于0");

        if (Objects.equals(orderAddCmd.getPayStatus(), EBusinessOrderPayStatusEnum.PAID.getCode())) {
            Assert.isTrue(orderAddCmd.getPayAmount().compareTo(orderAddCmd.getOriginalAmount().add(orderAddCmd.getDiscountAmount())) == 0,
                    "已支付订单的支付金额必须等于原始金额加折扣金额");
            Assert.hasText(orderAddCmd.getPayOrderNum(), "已支付订单的支付订单号不能为空");
        } else {
            Assert.isTrue(orderAddCmd.getPayAmount() == null || orderAddCmd.getPayAmount().compareTo(BigDecimal.ZERO) == 0, "未支付订单的支付金额必须等于0");
        }

        List<Long> chargeItemIds = getChargeItemIds(orderAddCmd.getCommunityId(), orderAddCmd.getSecondClassificationId());
        Assert.isTrue(!CollectionUtils.isEmpty(chargeItemIds), "项目该二级分类未配置收费项");
        orderAddCmd.getSubOrders().forEach(subOrderAdd -> {
            Assert.isTrue(chargeItemIds.contains(subOrderAdd.getItemId()), "子订单收费项id必须是项目该二级分类下的收费项");
        });
    }

    private List<Long> getChargeItemIds(Long communityId,String  secondClassificationId) throws ChargeBusinessException {
        CommunityOrderRuleQueryDTO communityOrderRuleQueryDTO = new CommunityOrderRuleQueryDTO();
        communityOrderRuleQueryDTO.setCommunityId(communityId);
        communityOrderRuleQueryDTO.setSecondClassificationIds(Lists.newArrayList(secondClassificationId));
        communityOrderRuleQueryDTO.setStatus(Lists.newArrayList(1));
        ChargeResponse<List<CommunityBaseOrderRuleDTO>> listChargeResponse = orderRuleClient.listByCommunityIdAndSubsetId(communityOrderRuleQueryDTO);
        List<CommunityBaseOrderRuleDTO> communityBaseOrderRules = AppInterfaceUtil.getResponseDataThrowException(listChargeResponse);

        Assert.isTrue(communityBaseOrderRules != null && communityBaseOrderRules.size() == 1, "项目没有配置该二级分类下的收费规则");
        CommunityBaseOrderRuleDTO communityBaseOrderRuleDTO = communityBaseOrderRules.get(0);
        return JSONArray.parseArray(communityBaseOrderRuleDTO.getChargeItemId(), Long.class);
    }

    private  void fillDefault(SubOrderAdjustToCmd subOrderAdjustToCmd) {
        subOrderAdjustToCmd.getToSubOrders().getSubOrderAmounts().forEach(subOrderAdd -> {
            if(subOrderAdd.getGoodsQuantity()==null){
                subOrderAdd.setGoodsQuantity(1);
            }
            if(subOrderAdd.getGoodsUnit()==null){
                subOrderAdd.setGoodsUnit("");
            }
        });
    }

    private  void fillDefault(OrderAddCmd orderAddCmd) {
        orderAddCmd.getSubOrders().forEach(subOrderAdd -> {
            if(subOrderAdd.getDiscountAmount()==null){
                subOrderAdd.setDiscountAmount(BigDecimal.ZERO);
            }
            if(subOrderAdd.getPayAmount()==null){
                subOrderAdd.setPayAmount(BigDecimal.ZERO);
            }
            if(subOrderAdd.getGoodsQuantity()==null){
                subOrderAdd.setGoodsQuantity(1);
            }
            if(subOrderAdd.getGoodsUnit()==null){
                subOrderAdd.setGoodsUnit("");
            }
        });
        if(orderAddCmd.getDiscountAmount()==null){
            orderAddCmd.setDiscountAmount(BigDecimal.ZERO);
        }
        if(orderAddCmd.getPayAmount()==null){
            orderAddCmd.setPayAmount(BigDecimal.ZERO);
        }
    }

    private  void fillDefault(SubOrderAdjustAddCmd orderAddCmd) {
        orderAddCmd.getSubOrderAmounts().forEach(subOrderAdd -> {
            if(subOrderAdd.getDiscountAmount()==null){
                subOrderAdd.setDiscountAmount(BigDecimal.ZERO);
            }
            if(subOrderAdd.getPayAmount()==null){
                subOrderAdd.setPayAmount(BigDecimal.ZERO);
            }
            if(subOrderAdd.getGoodsQuantity()==null){
                subOrderAdd.setGoodsQuantity(1);
            }
            if(subOrderAdd.getGoodsUnit()==null){
                subOrderAdd.setGoodsUnit("");
            }

        });
        if(orderAddCmd.getPayStatus()==null){
            orderAddCmd.setPayStatus(EBusinessOrderPayStatusEnum.TO_PAY.getCode());
        }
    }

}