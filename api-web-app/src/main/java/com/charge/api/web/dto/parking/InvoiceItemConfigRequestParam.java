package com.charge.api.web.dto.parking;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class InvoiceItemConfigRequestParam implements Serializable {

    private static final long serialVersionUID = -7768972737931243459L;
    /**
     * 小区code
     */
    @NotBlank(message = "小区编码不能为空")
    private String communityCode;
    /**
     * 收费项id
     */
    @NotBlank(message = "费项编码不能为空")
    private String itemCode;

}
