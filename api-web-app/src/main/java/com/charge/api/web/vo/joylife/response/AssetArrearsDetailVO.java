package com.charge.api.web.vo.joylife.response;

import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.util.YueXinDateUtils;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.assetoverview.AssetOverViewDTO;
import com.charge.bill.dto.predeposit.AsssetPredepositCountDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.DateUtils;
import com.charge.maindata.enums.AssetTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssetArrearsDetailVO implements Serializable {
    @ApiModelProperty(value = "小区运营平台Id")
    private String communityMsId;
    @ApiModelProperty(value = "小区Id")
    private String communityId;
    @ApiModelProperty(value = "小区名称")
    private String communityName;
    @ApiModelProperty(value = "房间ID")
    private String houseId;
    @ApiModelProperty(value = "房间类型")
    private String houseType;
    @ApiModelProperty(value = "房间编码")
    private String houseCode;
    @ApiModelProperty(value = "房间全部名称")
    private String houseName;
    @ApiModelProperty(value = "房间运营平台Id")
    private String houseMsId;
    @ApiModelProperty(value = "房间欠费总额(业主维度，保持不变)")
    private String houseTotalArrears;
    /**
     * 押金余额
     */
    private String depositAvailableBalance;
    /**
     * 房间欠费总额-业主
     */
    private String houseOwnerTotalArrears;
    /**
     * 房间欠费总额-开发商
     */
    private String houseDeveloperTotalArrears;
    /**
     * 房间欠费总额-业主+开发商
     */
    private String houseWholeTotalArrears;
    @ApiModelProperty(value = "房间往月欠费总额")
    private String houseLastMonthSumArrears;
    @ApiModelProperty(value = "房间本月欠费总额")
    private String houseThisMonthArrears;
    @ApiModelProperty(value = "违约金")
    private String housePenaltyArrears;
    @ApiModelProperty(value = "房间预存总额（业主维度，保持不变）")
    private String houseTotalPrestore;
    /**
     * 房间预存总额-业主
     */
    private String houseOwnerTotalPrestore;
    /**
     * 房间预存总额-开发商
     */
    private String houseDeveloperTotalPrestore;
    /**
     * 房间预存总额-业主+开发商
     */
    private String houseWholeTotalPrestore;
    @ApiModelProperty(value = "账龄")
    private int arrearsAge;
    /**
     * 欠费月数
     */
    @ApiModelProperty(value = "欠费月数(业主维度，保持不变)")
    private Integer monthCount;
    /**
     * 欠费月数-业主
     */
    private Integer ownerMonthCount;
    /**
     * 欠费月数-开发商
     */
    private Integer developerMonthCount;
    /**
     * 欠费月数-业主+开发商
     */
    private Integer wholeMonthCount;
    /**
     * 收费对象类型（0-业主，1-开发商）
     */
    private Integer chargeObjectType;

    public static String bigDecimalToStr(BigDecimal bigDecimal){
        if(bigDecimal == null){
            return "0";
        }
        return bigDecimal.setScale(2, RoundingMode.HALF_UP).toString();
    }
    public static BigDecimal nullDecimal(BigDecimal bigDecimal){
        if(bigDecimal == null){
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }

    public static List<AssetArrearsDetailVO> ofList(List<AssetOverViewDTO> assetOverViews, String communityMsId, Map<Long,BigDecimal> thisMonthArrearsMap, Map<Long,AssetAdapter> adapterMap){
        if(CollectionUtils.isEmpty(assetOverViews)){
            return Collections.emptyList();
        }
        return assetOverViews.stream()
                .map(assetOverViewDTO -> getAssetArrearsDetailVO(communityMsId, thisMonthArrearsMap, adapterMap, assetOverViewDTO)).collect(Collectors.toList());
    }

    private static AssetArrearsDetailVO getAssetArrearsDetailVO(String communityMsId, Map<Long, BigDecimal> thisMonthArrearsMap, Map<Long, AssetAdapter> adapterMap, AssetOverViewDTO assetOverViewDTO)  {
        AssetAdapter adapter = adapterMap.get(assetOverViewDTO.getAssetId());
        BigDecimal thisMonthArrears = nullDecimal(thisMonthArrearsMap.get(assetOverViewDTO.getAssetId()));
        BigDecimal houseLastMonthSumArrears = assetOverViewDTO.getEffectiveArrearsAmount().subtract(thisMonthArrears);
        return of(communityMsId, assetOverViewDTO, adapter, houseLastMonthSumArrears, thisMonthArrears);
    }

    public static AssetArrearsDetailVO of(String communityMsId, AssetOverViewDTO assetOverViewDTO, AssetAdapter adapter, BigDecimal houseLastMonthSumArrears, BigDecimal thisMonthArrears) {
        return AssetArrearsDetailVO.builder()
                .communityId(assetOverViewDTO.getCommunityId().toString())
                .communityName(adapter.getCommunityName())
                .communityMsId(communityMsId)
                .houseId(assetOverViewDTO.getAssetId().toString())
                .houseCode(assetOverViewDTO.getAssetCode())
                .houseName(adapter.getBuildingName() + adapter.getUnitName() + adapter.getAssetNum())
                .houseType(AssetTypeEnum.HOUSE.getCode().equals(assetOverViewDTO.getAssetType()) ? "house" : "position")
                .houseMsId(assetOverViewDTO.getMsId())
                .chargeObjectType(assetOverViewDTO.getChargeObjType())
                .houseTotalArrears(bigDecimalToStr(assetOverViewDTO.getEffectiveOwnerArrearsAmount()))
                .houseOwnerTotalArrears(bigDecimalToStr(assetOverViewDTO.getEffectiveOwnerArrearsAmount()))
                .houseDeveloperTotalArrears(bigDecimalToStr(assetOverViewDTO.getEffectiveArrearsAmount()
                        .subtract(assetOverViewDTO.getEffectiveOwnerArrearsAmount())))
                .houseWholeTotalArrears(bigDecimalToStr(assetOverViewDTO.getEffectiveArrearsAmount()))
                .houseLastMonthSumArrears(bigDecimalToStr(houseLastMonthSumArrears))
                .houseThisMonthArrears(bigDecimalToStr(thisMonthArrears))
                .housePenaltyArrears(bigDecimalToStr(assetOverViewDTO.getPenaltyArrearsAmount()))
                .houseTotalPrestore(bigDecimalToStr(assetOverViewDTO.getTotalOwnerPrestoreBalance()))
                .houseOwnerTotalPrestore(bigDecimalToStr(assetOverViewDTO.getTotalOwnerPrestoreBalance()))
                .houseDeveloperTotalPrestore(bigDecimalToStr(assetOverViewDTO.getTotalDevelopPrestoreBalance()))
                .houseWholeTotalPrestore(bigDecimalToStr(assetOverViewDTO.getTotalPrestoreBalance()))
                .depositAvailableBalance(bigDecimalToStr(assetOverViewDTO.getTotalDepositeBalance()))
                .arrearsAge(StringUtils.hasText(assetOverViewDTO.getEarlyArrearsBelongYears()) ?YueXinDateUtils.getMonth(assetOverViewDTO.getEarlyArrearsBelongYears(), DateUtils.getCurrentDate()):0)
                .monthCount(assetOverViewDTO.getTotalOwnerArrearsMonth())
                .ownerMonthCount(assetOverViewDTO.getTotalOwnerArrearsMonth())
                .developerMonthCount(assetOverViewDTO.getTotalDevelopArrearsMonth())
                .wholeMonthCount(assetOverViewDTO.getTotalArrearsMonth())
                .build();
    }


    public static List<AssetArrearsDetailVO> fromV2(List<ReceivableBillDTO> receivableBillDTOS, String communityMsId,
                                                    List<AssetAdapter> assetList, Map<Long,List<AsssetPredepositCountDTO>> predepositMap) throws ChargeBusinessException {
        List<AssetArrearsDetailVO> responseList = new ArrayList<>();
        Map<Long, List<ReceivableBillDTO>> arrearsOrderGroupByOwnerId = receivableBillDTOS.stream()
                .filter(receivableBillDTO -> receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode()) || receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                .collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId));
        for (AssetAdapter assetAdapter : assetList) {
            Long assetId = assetAdapter.getId();
            String  communityId = assetAdapter.getCommunityId().toString();
            String communityName = assetAdapter.getCommunityName();
            String assetNum = assetAdapter.getAssetNum();
            String assetCode = assetAdapter.getAssetCode();
            Integer chargeObjectType = assetAdapter.getChargeObject();
            String type = AssetTypeEnum.HOUSE.getCode().equals(assetAdapter.getType())?"house":"position";
            String assetMsId = assetAdapter.getMsId();
            AssetArrearsDetailVO houseOrderItemArrears = new AssetArrearsDetailVO();
            houseOrderItemArrears.setCommunityId(communityId);
            houseOrderItemArrears.setCommunityName(communityName);
            houseOrderItemArrears.setCommunityMsId(communityMsId);
            StringBuilder houseName = new StringBuilder();
            String buildingName = assetAdapter.getBuildingName();
            String unitName = assetAdapter.getUnitName();
            houseName.append(buildingName).append(unitName).append(assetNum);
            BigDecimal amount = BigDecimal.ZERO;
            BigDecimal houseOwnerAmount = BigDecimal.ZERO;
            BigDecimal houseDeveloperAmount = BigDecimal.ZERO;
            BigDecimal lastMonthAmount = BigDecimal.ZERO;
            BigDecimal thisMonthAmount = BigDecimal.ZERO;
            BigDecimal penaltyAmount = BigDecimal.ZERO;
            BigDecimal ownerPrestoreBalance = BigDecimal.ZERO;
            BigDecimal developerPrestoreBalance = BigDecimal.ZERO;
            BigDecimal prestoreBalance = BigDecimal.ZERO;
            BigDecimal depositBalance = BigDecimal.ZERO;
            int arrearsAge = 0;
            int monthCount =0;
            int ownerMonthCount =0;
            int developerMonthCount =0;
            if (!CollectionUtils.isEmpty(arrearsOrderGroupByOwnerId.get(assetId))) {
                Optional<BigDecimal> amountOptional = arrearsOrderGroupByOwnerId.get(assetId).stream()
                        .filter(receivableBillDTO -> (receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                                || receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                                && ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus()))
                        .map(receivableBillDTO -> (receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
                        .reduce(BigDecimal::add);
                amount = amountOptional.isPresent() ? amountOptional.get() : BigDecimal.ZERO;

                Optional<BigDecimal> houseOwnerAmountOptional = arrearsOrderGroupByOwnerId.get(assetId).stream()
                        .filter(receivableBillDTO -> (receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                                || receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                                && ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus())
                                && ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(receivableBillDTO.getChargeObject()))
                        .map(receivableBillDTO -> (receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
                        .reduce(BigDecimal::add);
                houseOwnerAmount = houseOwnerAmountOptional.isPresent() ? houseOwnerAmountOptional.get() : BigDecimal.ZERO;

                Optional<BigDecimal> houseDeveloperAmountOptional = arrearsOrderGroupByOwnerId.get(assetId).stream()
                        .filter(receivableBillDTO -> (receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                                || receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                                && ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus())
                                && ChargeObjEnum.CHARGE_OBJ_DEVELOPER.getCode().equals(receivableBillDTO.getChargeObject()))
                        .map(receivableBillDTO -> (receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
                        .reduce(BigDecimal::add);
                houseDeveloperAmount = houseDeveloperAmountOptional.isPresent() ? houseDeveloperAmountOptional.get() : BigDecimal.ZERO;


                Optional<BigDecimal> amountOptionalLastMonth = arrearsOrderGroupByOwnerId.get(assetId).stream()
                        .filter(receivableBillDTO -> (receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                                || receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                                && !YearMonth.now().toString().equals(receivableBillDTO.getBelongYears())
                                && ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus()))
                        .map(receivableBillDTO -> (receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()))
                        .reduce(BigDecimal::add);
                lastMonthAmount = amountOptionalLastMonth.isPresent() ? amountOptionalLastMonth.get() : BigDecimal.ZERO;

                Optional<BigDecimal> amountOptionalThisMonth = arrearsOrderGroupByOwnerId.get(assetId).stream()
                        .filter(receivableBillDTO -> (receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                                || receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                                && YearMonth.now().toString().equals(receivableBillDTO.getBelongYears())
                                && ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus()))
                        .map(receivableBillDTO -> (receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()))
                        .reduce(BigDecimal::add);
                thisMonthAmount = amountOptionalThisMonth.isPresent() ? amountOptionalThisMonth.get() : BigDecimal.ZERO;


                Optional<BigDecimal> amountOptionalPenalty = arrearsOrderGroupByOwnerId.get(assetId).stream()
                        .filter(receivableBillDTO -> (receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                                || receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                                && ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus()))
                        .map(receivableBillDTO -> (receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
                        .reduce(BigDecimal::add);
                penaltyAmount = amountOptionalPenalty.isPresent() ? amountOptionalPenalty.get() : BigDecimal.ZERO;


                List<ReceivableBillDTO> arrearsOrderList = arrearsOrderGroupByOwnerId.get(assetId).stream()
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(arrearsOrderList)) {
                    List<String> arrearsAges = arrearsOrderList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(receivableBillDTO ->receivableBillDTO.getBelongYears()))), ArrayList::new)).stream().map(ReceivableBillDTO::getBelongYears).collect(Collectors.toList());
                    //收费对象为业主的欠费月数
                    List<String> ownerArrearAges = arrearsOrderList.stream().filter(e -> ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(e.getChargeObject())).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(receivableBillDTO ->receivableBillDTO.getBelongYears()))), ArrayList::new)).stream().map(ReceivableBillDTO::getBelongYears).collect(Collectors.toList());
                    //收费对象为业主的欠费月数
                    List<String> developerArrearAges = arrearsOrderList.stream().filter(e -> ChargeObjEnum.CHARGE_OBJ_DEVELOPER.getCode().equals(e.getChargeObject())).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(receivableBillDTO ->receivableBillDTO.getBelongYears()))), ArrayList::new)).stream().map(ReceivableBillDTO::getBelongYears).collect(Collectors.toList());
                    monthCount = arrearsAges.size();
                    ownerMonthCount = ownerArrearAges.size();
                    developerMonthCount = developerArrearAges.size();
                    arrearsAges.sort(String::compareTo);
                    arrearsAge = YueXinDateUtils.getMonth(arrearsAges.get(0), DateUtils.getCurrentDate());
                }
            }
            if (!Objects.isNull(predepositMap) && !Objects.isNull(predepositMap.get(assetId))) {
                prestoreBalance = CollectionUtils.isEmpty(predepositMap.get(assetId))? BigDecimal.ZERO:predepositMap.get(assetId).get(0).getPreDeposit();
                ownerPrestoreBalance = CollectionUtils.isEmpty(predepositMap.get(assetId))? BigDecimal.ZERO:predepositMap.get(assetId).get(0).getOwnerPreDeposit();
                developerPrestoreBalance = CollectionUtils.isEmpty(predepositMap.get(assetId))? BigDecimal.ZERO:predepositMap.get(assetId).get(0).getDeveloperPreDeposit();
                depositBalance = CollectionUtils.isEmpty(predepositMap.get(assetId))? BigDecimal.ZERO:predepositMap.get(assetId).get(0).getDeposit();

                if (Objects.isNull(prestoreBalance)) {
                    prestoreBalance = BigDecimal.ZERO;
                    ownerPrestoreBalance = BigDecimal.ZERO;
                    developerPrestoreBalance = BigDecimal.ZERO;
                }
            }
            houseOrderItemArrears.setHouseId(assetId.toString());
            houseOrderItemArrears.setHouseCode(assetCode);
            houseOrderItemArrears.setHouseName(houseName.toString());
            houseOrderItemArrears.setHouseTotalArrears(houseOwnerAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHouseOwnerTotalArrears(houseOwnerAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHouseDeveloperTotalArrears(houseDeveloperAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHouseWholeTotalArrears(amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHouseLastMonthSumArrears(lastMonthAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHouseThisMonthArrears(thisMonthAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHousePenaltyArrears(penaltyAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHouseTotalPrestore(ownerPrestoreBalance.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHouseOwnerTotalPrestore(ownerPrestoreBalance.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHouseDeveloperTotalPrestore(developerPrestoreBalance.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHouseWholeTotalPrestore(prestoreBalance.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setDepositAvailableBalance(depositBalance.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            houseOrderItemArrears.setHouseType(type);
            houseOrderItemArrears.setArrearsAge(arrearsAge);
            houseOrderItemArrears.setMonthCount(ownerMonthCount);
            houseOrderItemArrears.setOwnerMonthCount(ownerMonthCount);
            houseOrderItemArrears.setDeveloperMonthCount(developerMonthCount);
            houseOrderItemArrears.setWholeMonthCount(monthCount);
            houseOrderItemArrears.setHouseMsId(assetMsId);
            houseOrderItemArrears.setChargeObjectType(chargeObjectType);
            responseList.add(houseOrderItemArrears);
        }
        return responseList;
    }
}
