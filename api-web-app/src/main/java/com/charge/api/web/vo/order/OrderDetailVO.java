package com.charge.api.web.vo.order;

import lombok.Data;

import java.util.List;

/**
 * 子订单详情
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
public class OrderDetailVO {
    /**
     * 订单信息
     */
    private OrderVO order;

    /**
     * 支付信息列表
     */
    private List<OrderPayVO> pays;

    /**
     * 调整信息
     */
    private List<OrderAdjustVO> adjusts;

    /**
     * 退款信息
     */
    private List<OrderRefundDetailVO> refunds;
}