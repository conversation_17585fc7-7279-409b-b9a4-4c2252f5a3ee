package com.charge.api.web.service.order;

import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentMethodEnum;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.order.dto.ebuiness.EBusinessMasterOrderDTO;
import com.charge.order.dto.ebuiness.OrderInstallmentDetailDTO;
import com.charge.pay.dto.CommunityBankAccountGroupDTO;
import com.charge.pay.dto.reward.CommunityRewardConfigDetailDTO;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 下单上下文
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
@Data
@Builder
@Accessors(chain = true)
public class CreateOrderContext {

    private BigDecimal totalAmount;
    /**
     * 订单号-来源于支付模块
     */
    private String orderNum;

    /**
     * 实收单id
     */
    private Long incomeBillId;

    /**
     * 项目
     */
    private CommunityDTO community;


    private AssetType assetType;

    /**
     * 支付人
     */
    private String payMember;
    /**
     * 支付人id
     */
    private Long payMemberId;
    /**
     * 支付人手机号
     */
    private String payMemberMobile;

    /**
     * 外部交易id
     */
    private String outTransactionNo;
    /**
     * 收费来源
     */
    private PaymentTerminalEnum paymentTerminal;
    /**
     * 收费方式
     */
    private PaymentMethodEnum paymentMethod;
    /**
     * 支付通道
     */
    private PaymentChannelEnum paymentChannel;
    /**
     * 支付总金额
     */
    private BigDecimal totalPrice;
    /**
     * 支付使用积分
     */
    private Integer totalPoints;
    /**
     * 积分账户
     */
    private String equityAccount;
    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 备注
     */
    private String memo;

    /**
     * 是否开票：0否，1是
     */
    private Integer isInvoiced;
    /**
     * 收据类型：0打印批量账单收据，1打印每个房屋收据
     */
    private Integer receiptType;

    /**
     * 支付房间数
     */
    private Integer payHouseCount;
    /**
     * 收费员id
     */
    private String collectorId;

    /**
     * 收费员姓名
     */
    private String collectorName;

    private List<AssetBillBusiness> assetBillBusinesses;

    private String bankAccountUuid;

    private String bankAccountNum;

    private Date createTime;
    /**
     * 商户号-前端传的
     */
    private String mercid;


    /**
     * 拉卡拉商户号-根据项目查询到配置的商户号
     */
    private String lakalaMerchant;

    /**
     * 银联商户号-根据项目查询到配置的商户号
     */
    private String unionPayMerchant;


    /**
     * 实体银行卡号-招行转账下单用
     */
    private CommunityBankAccountGroupDTO communityBankAccount;
    /**
     * 虚拟银行卡号-招行转账下单用
     */
    private String communityBankVirtualAccount;

    /**
     * 设备号
     */
    private String deviceInfo;

    /**
     * 资产的应收单id到未核销的应收单的映射
     */
    private Map<Long, ReceivableBillDTO> receivableMap;

    private Map<Long,List<ReceivableBillDTO>> assertIdToEffectiveReceivablesMapByIds;

    private Map<Long,List<ReceivableBillDTO>> assertIdToEffectiveReceivablesMap;

    private Map<Long, List<Long>> itemIdByAssetId;

    private List<ReceivableBillDTO> checkingReceivableList;

    private Map<Long, OrderInstallmentDetailDTO> orderInstallmentDetailMap;

    private Map<Long, EBusinessMasterOrderDTO> ebusinessBaseIdToMasterOrderMap;

    /**
     * 资产的id到资产应收单列表
     */
    private Map<Long, List<ReceivableBillDTO>> assetReceivableMap;

    private Map<Long, AssetDTO> assetMap;
    /**
     * 项目酬金制状态
     */
    private CommunityRewardConfigDetailDTO communityRewardConfigDetail;

    private Boolean allowPenaltyIncrease;

    /**
     * 客户msId
     */
    private String customerMsId;

}


