package com.charge.api.web.dto.invoice;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/09/11/ 20:46
 * @description
 */
@Data
public class ThirdPartyInvoiceFailCallbackRequestDTO implements Serializable {

    private static final long serialVersionUID = -5824117783021844677L;

    /**
     * 代码，0-全部失败，1-全部成功，2-部分失败
     */
    private String code;

    /**
     * 描述
     */
    private String msg;

    /**
     * 业务单号
     */
    private String salesBillNos;

    /**
     * 预制发票数量
     */
    private Integer preInvoiceCount;

    /**
     * 预制发票失败信息列表
     */
    private List<ThirdPartyInvoiceFailProcessDTO> processlist;
}
