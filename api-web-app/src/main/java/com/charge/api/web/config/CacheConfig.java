package com.charge.api.web.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * CacheConfig
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Configuration
@EnableCaching
public class CacheConfig {
    @Bean
    @Primary
    public CacheManager cacheManager10m() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        // 设置默认的缓存配置
        Caffeine<Object, Object>  caffeine=Caffeine.newBuilder()
                .maximumSize(3000)
                .expireAfterWrite(10, TimeUnit.MINUTES);

        cacheManager.setCaffeine(caffeine);
        cacheManager.setCacheNames(Lists.newArrayList("community","community1","communityMerchant"
                ,"communityMerchantTradePayType","communityRewardStatus"));
        return cacheManager;
    }

}