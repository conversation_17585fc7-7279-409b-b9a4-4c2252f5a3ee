package com.charge.api.web.vo.joylife.invoice;

import com.charge.api.web.vo.joylife.request.ZhaoXiAssertRequest;
import com.charge.bill.enums.BillTypeEnum;
import com.charge.invoice.enums.BusinessBillTypeEnum;
import com.charge.invoice.enums.InvoiceTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@Data
public class InvoiceMultiAssetReceivableBillRequestVO {

    private static final long serialVersionUID = 2249224867719401704L;
    /**
     * 小区id
     */
    @NotBlank(
            message = "communityMsId 不能为空"
    )
    private String communityMsId;

    /**
     * 资产列表
     */
    List<ZhaoXiAssertRequest>  assetList;

    /**
     * 开始时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            locale = "zh_CN",
            timezone = "GMT+8"
    )
    private Date beginTime;

    /**
     * 结束时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            locale = "zh_CN",
            timezone = "GMT+8"
    )
    private Date endTime;
    /**
     * com.charge.invoice.enums.BusinessBillTypeEnum
     */

    private List<String> businessBillTypeList;

    /**
     *  1 有偿订单 2 停车月卡 3 临时类 4 车辆临停
     */
    private List<Integer> subOrderTypeList;


    private List<String> orderNumList;

}
