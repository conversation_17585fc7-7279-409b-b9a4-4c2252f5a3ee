package com.charge.api.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

/**
 * 预存积分配置
 *
 * <AUTHOR>
 * @date 2023/11/27
 */

@ConfigurationProperties(prefix = "points-reward-rule")
@Configuration
@RefreshScope
@Data
public class PreStorePointsConfig {
    /**
     * 比例
     */
    private BigDecimal ratio;
    /**
     * 积分基数
     */
    private BigDecimal points;
    /**
     * 月份数量
     */
    private BigDecimal factor;
}


