package com.charge.api.web.vo.joylife.deposit;

import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.bill.dto.ReceivableAmountTotalDTO;
import com.charge.bill.dto.predeposit.PredepositAccountDTO;
import com.charge.config.dto.item.ItemInfo;
import com.charge.config.dto.item.SpecialPreStoreItemDTO;
import com.charge.config.dto.points.PointsSignCommunityDTO;
import com.charge.core.util.CollectionUtil;
import com.charge.feecalculte.dto.ReceivableBillDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrestoreChargeInfoVO {
	String totalMoney;
	String freezeMoney;
	String availableMoney;
	String refundMoney;
	String arrearsMoney;
	String businessType;
	String itemId;
	String itemCode;
	String itemName;
	String preItemName;
	String preItemUuid;
    List<PrestoreChargeInfoDetail> chargeInfoDetailList;
	String chargeInfo;
	BigDecimal chargeMoneyPerMonth;
	List<Integer> monthList;
	private Integer pointsEarnStatus;
	private String pointsEarnRule;
	/*收费面积*/
	BigDecimal chargeArea;
	/*计费模型*/
	String chargeModel;
	/*收费金额类型（用于周期指定型）*/
	Integer amountType;
	/*单价*/
	String standardStr;

	public static PrestoreChargeInfoVO fromGeneralPrestore(PredepositAccountDTO generalPre) {
		BigDecimal totalMoney = BigDecimal.ZERO;
		BigDecimal freezeMoney = BigDecimal.ZERO;
		BigDecimal availableMoney = BigDecimal.ZERO;
		BigDecimal refundMoney = BigDecimal.ZERO;
		if (generalPre != null) {
			freezeMoney = generalPre.getFreezedMoney().setScale(2,BigDecimal.ROUND_HALF_UP);
			availableMoney =generalPre.getAvailableBalance().setScale(2,BigDecimal.ROUND_HALF_UP);
			totalMoney = freezeMoney.add(availableMoney).setScale(2,BigDecimal.ROUND_HALF_UP);
			refundMoney = generalPre.getRefundedMoney().add(generalPre.getRefunddingMoney()).setScale(2,BigDecimal.ROUND_HALF_UP);
		}
		return PrestoreChargeInfoVO.builder().totalMoney(totalMoney.toString())
				.freezeMoney(freezeMoney.toString())
				.availableMoney(availableMoney.toString())
				.refundMoney(refundMoney.toString())
				.businessType(PayRelatedConstants.ORDER_TYPE_PRESTORE_PAY)
				.itemId(Long.toString(PayRelatedConstants.PREDEPOSIT_GENERAL_ID_V2))
				.preItemName(PayRelatedConstants.PREDEPOSIT_GENERAL_NAME)
				.preItemUuid(Long.toString(PayRelatedConstants.PREDEPOSIT_GENERAL_ID_V2))
//				.itemCode("0")
				.itemName(PayRelatedConstants.PREDEPOSIT_GENERAL_NAME)
				.arrearsMoney("0")
				.pointsEarnRule(null)
				.pointsEarnStatus(PayRelatedConstants.POINTS_EARN_STATUS_NO)
				.monthList(Arrays.asList(3,6,12))
				.chargeMoneyPerMonth(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP))
				.chargeInfoDetailList(PrestoreChargeInfoDetail.fromDefault())
				.chargeInfo("")
				.build();

	}

	public static PrestoreChargeInfoVO fromSpecialPrestore(ReceivableAmountTotalDTO amountTotalDTO, PredepositAccountDTO accountDTO, ReceivableBillDTO billDTO,
														   String info, ItemInfo itemInfo, String chargeModel, Integer amountType) {
		BigDecimal totalMoney = BigDecimal.ZERO;
		BigDecimal freezeMoney = BigDecimal.ZERO;
		BigDecimal availableMoney = BigDecimal.ZERO;
		BigDecimal refundMoney = BigDecimal.ZERO;
		BigDecimal perMonthMoney = billDTO == null?null:billDTO.getReceivableAmount().setScale(2,BigDecimal.ROUND_HALF_UP);
		String standardStr = "";
		if(billDTO != null && StringUtils.isNotBlank(billDTO.getStandardStr())){
			standardStr = billDTO.getStandardStr().split("元")[0];
		}
		if (accountDTO != null) {
			freezeMoney = accountDTO.getFreezedMoney().setScale(2,BigDecimal.ROUND_HALF_UP);
			availableMoney =accountDTO.getAvailableBalance().setScale(2,BigDecimal.ROUND_HALF_UP);
			totalMoney = freezeMoney.add(availableMoney).setScale(2,BigDecimal.ROUND_HALF_UP);
			refundMoney = accountDTO.getRefundedMoney().add(accountDTO.getRefunddingMoney()).setScale(2,BigDecimal.ROUND_HALF_UP);
		}
		PrestoreChargeInfoVO prestoreChargeInfoVO= PrestoreChargeInfoVO.builder().totalMoney(totalMoney.toString())
				.freezeMoney(freezeMoney.toString())
				.availableMoney(availableMoney.toString())
				.refundMoney(refundMoney.toString())
				.businessType(PayRelatedConstants.ORDER_TYPE_PRESTORE_PAY)
				.itemId(itemInfo.getItemId().toString())
//				.itemCode()
				.itemName(itemInfo.getItemName())
				.preItemUuid(accountDTO==null ? itemInfo.getItemId().toString():accountDTO.getPredepositItemId().toString())
				.preItemName(accountDTO==null ? PayRelatedConstants.SPECIAL_PREDEPOSIT_PREFIX+itemInfo.getItemName():accountDTO.getPredepositItemName())
				.pointsEarnRule(null)
				.pointsEarnStatus(PayRelatedConstants.POINTS_EARN_STATUS_NO)
				.monthList(Arrays.asList(3,6,12))
				.arrearsMoney(amountTotalDTO==null? new BigDecimal(0).toString():amountTotalDTO.getArrearsAmount().setScale(2,BigDecimal.ROUND_HALF_UP).toString())
				.chargeMoneyPerMonth(perMonthMoney)
				.chargeInfoDetailList(perMonthMoney==null|| BigDecimal.ZERO.compareTo(perMonthMoney)>=0?PrestoreChargeInfoDetail.fromDefault():PrestoreChargeInfoDetail.from(perMonthMoney))
				.chargeInfo(info)
				.standardStr(standardStr)
				.build();
		//TODO 增分配置
//		if(pointsEarn!=null && pointsEarn){
//			prestoreChargeInfoVO.setPointsEarnRule(prestoreItem.getPointsEarnRule());
//			prestoreChargeInfoVO.setPointsEarnStatus(prestoreItem.getPointsEarnStatus()==null?PayRelatedConstants.POINTS_EARN_STATUS_NO:prestoreItem.getPointsEarnStatus());
//		}else{
//			prestoreChargeInfoVO.setPointsEarnRule(null);
//			prestoreChargeInfoVO.setPointsEarnStatus(PayRelatedConstants.POINTS_EARN_STATUS_NO);
//		}
		prestoreChargeInfoVO.setPointsEarnRule(null);
		prestoreChargeInfoVO.setPointsEarnStatus(PayRelatedConstants.POINTS_EARN_STATUS_NO);
		if(billDTO != null && billDTO.getChargeArea() != null) {
			prestoreChargeInfoVO.setChargeArea(billDTO.getChargeArea());
		}
		prestoreChargeInfoVO.setChargeModel(chargeModel);
		prestoreChargeInfoVO.setAmountType(amountType);
		return prestoreChargeInfoVO;
	}

	public static PrestoreChargeInfoVO fromSpecialPrestoreForV2(Map<String, BigDecimal> prestoreAccountInfo, SpecialPreStoreItemDTO prestoreItem, BigDecimal moneyMonth, BigDecimal arrearsMoney, String info, List<PointsSignCommunityDTO>  communityPointsConfigList, String pointsEarnRule) {
		List<Long> pointsEarnItemIdList=new ArrayList<>();
		if(CollectionUtil.isNotEmpty(communityPointsConfigList)) {
			pointsEarnItemIdList = communityPointsConfigList.get(0).getPointsEarnItemIdList();
		}

		BigDecimal refundingMoney = BigDecimal.ZERO;
		BigDecimal totalMoney = BigDecimal.ZERO;
		BigDecimal freezeMoney = BigDecimal.ZERO;
		BigDecimal availableMoney = BigDecimal.ZERO;
		if (prestoreAccountInfo != null) {
			refundingMoney = prestoreAccountInfo.get("refundingMoney");
			totalMoney = prestoreAccountInfo.get("totalMoney");
			freezeMoney = prestoreAccountInfo.get("freezeMoney");
			availableMoney = prestoreAccountInfo.get("availableMoney");
		}
		PrestoreChargeInfoVO prestoreChargeInfoVO= PrestoreChargeInfoVO.builder().totalMoney(totalMoney.toString())
				.freezeMoney(freezeMoney.toString())
				.availableMoney(availableMoney.toString())
				.refundMoney(refundingMoney.toString())
				.businessType(PayRelatedConstants.ORDER_TYPE_PRESTORE_PAY)
				.itemId(prestoreItem.getItemId().toString())
				.itemCode(prestoreItem.getItemCode())
				.itemName(prestoreItem.getItemName())
				.preItemUuid(prestoreItem.getId().toString())
				.preItemName(prestoreItem.getPreItemName())
				.pointsEarnRule(prestoreItem.getPointsEarnRule())
				.pointsEarnStatus(prestoreItem.getPointsEarnStatus())
				.monthList(Arrays.asList(3,6,12))
				.arrearsMoney(arrearsMoney.setScale(2,BigDecimal.ROUND_HALF_UP).toString())
				.chargeMoneyPerMonth(ObjectUtils.isEmpty(moneyMonth)?null:moneyMonth.setScale(2,BigDecimal.ROUND_HALF_UP))
				.chargeInfoDetailList(ObjectUtils.isEmpty(moneyMonth)|| BigDecimal.ZERO.compareTo(moneyMonth)>=0?PrestoreChargeInfoDetail.fromDefault():PrestoreChargeInfoDetail.from(moneyMonth))
				.chargeInfo(info)
				.build();
		if(pointsEarnItemIdList.contains(prestoreItem.getItemId())){
			prestoreChargeInfoVO.setPointsEarnRule(pointsEarnRule);
			prestoreChargeInfoVO.setPointsEarnStatus(PayRelatedConstants.POINTS_EARN_STATUS_YES);
		}else{
			prestoreChargeInfoVO.setPointsEarnRule(null);
			prestoreChargeInfoVO.setPointsEarnStatus(PayRelatedConstants.POINTS_EARN_STATUS_NO);
		}
		return prestoreChargeInfoVO;
	}

	public static PrestoreChargeInfoVO fromSpecialPrestore(ReceivableAmountTotalDTO amountTotalDTO, PredepositAccountDTO accountDTO, BigDecimal recPerMonthMOney, String info, ItemInfo itemInfo,Boolean  pointsEarn,String pointsEarnRule) {
		BigDecimal totalMoney = BigDecimal.ZERO;
		BigDecimal freezeMoney = BigDecimal.ZERO;
		BigDecimal availableMoney = BigDecimal.ZERO;
		BigDecimal refundMoney = BigDecimal.ZERO;
		BigDecimal perMonthMoney = recPerMonthMOney.setScale(2,BigDecimal.ROUND_HALF_UP);
		if (accountDTO != null) {
			freezeMoney = accountDTO.getFreezedMoney().setScale(2,BigDecimal.ROUND_HALF_UP);
			availableMoney =accountDTO.getAvailableBalance().setScale(2,BigDecimal.ROUND_HALF_UP);
			totalMoney = freezeMoney.add(availableMoney).setScale(2,BigDecimal.ROUND_HALF_UP);
			refundMoney = accountDTO.getRefundedMoney().add(accountDTO.getRefunddingMoney()).setScale(2,BigDecimal.ROUND_HALF_UP);
		}
		PrestoreChargeInfoVO prestoreChargeInfoVO= PrestoreChargeInfoVO.builder().totalMoney(totalMoney.toString())
				.freezeMoney(freezeMoney.toString())
				.availableMoney(availableMoney.toString())
				.refundMoney(refundMoney.toString())
				.businessType(PayRelatedConstants.ORDER_TYPE_PRESTORE_PAY)
				.itemId(itemInfo.getItemId().toString())
				.itemName(itemInfo.getItemName())
				.preItemUuid(itemInfo.getItemId().toString())
				.preItemName(PayRelatedConstants.SPECIAL_PREDEPOSIT_PREFIX + itemInfo.getItemName())
				.pointsEarnRule(null)
				.pointsEarnStatus(PayRelatedConstants.POINTS_EARN_STATUS_NO)
				.monthList(Arrays.asList(3,6,12))
				.arrearsMoney(amountTotalDTO==null? new BigDecimal(0).toString():amountTotalDTO.getArrearsAmount().setScale(2,BigDecimal.ROUND_HALF_UP).toString())
				.chargeMoneyPerMonth(perMonthMoney)
				.chargeInfoDetailList(perMonthMoney==null|| BigDecimal.ZERO.compareTo(perMonthMoney)>=0?PrestoreChargeInfoDetail.fromDefault():PrestoreChargeInfoDetail.from(perMonthMoney))
				.chargeInfo(info)
				.build();
		if(pointsEarn!=null && pointsEarn){
			prestoreChargeInfoVO.setPointsEarnRule(pointsEarnRule);
			prestoreChargeInfoVO.setPointsEarnStatus(PayRelatedConstants.POINTS_EARN_STATUS_YES);
		}else{
			prestoreChargeInfoVO.setPointsEarnRule(null);
			prestoreChargeInfoVO.setPointsEarnStatus(PayRelatedConstants.POINTS_EARN_STATUS_NO);
		}

		return prestoreChargeInfoVO;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (!(o instanceof PrestoreChargeInfoVO)){
			return false;
		}
		PrestoreChargeInfoVO that = (PrestoreChargeInfoVO) o;
		return Objects.equals(getItemId(), that.getItemId());
	}

	@Override
	public int hashCode() {
		return Objects.hash(getItemId());
	}
}
