package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/15
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class ReceivableItemsV1 {
    @JsonProperty("chargeItemInfo")
    private List<ReceivableItemV1> chargeItemInfo;

}


