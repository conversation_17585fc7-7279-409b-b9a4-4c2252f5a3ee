package com.charge.api.web.constants;

/**
 *业主APP相关常量
 *
 * <AUTHOR>
 * @date 2018/5/3
 */
public class YueConstants {

    public static final int CODE_SUCCESS = 0; //成功

    public static final int CODE_FAILED = -1; //失败

    public static final int CODE_COMMUNITY_NULL = -2; //小区未上线

    // 返回给朝昔未支付异常code
    public static final int CODE_NO_PAY = -3;

    public static final String RESOURCE_NOT_FOUND = "resource_not_found";

    /*订单支付状态*/
    public static final String ORDER_NOT_PAY = "0"; //未支付
    public static final String ORDER_PAY_SUCCESS = "1"; //已支付
    public static final String ORDER_PAY_FAILED = "2"; //支付失败
    public static final String ORDER_PAY_BANK = "3"; //银行托收中
    public static final String ORDER_PAY_HOLD = "4"; //挂起


    /**
     * 记录微信支付查询订单返回状态：支付成功
     */
    public static final String YUE_PAY_SUCCESS = "SUCCESS";
    /**
     * 转入退款
     */
    public static final String YUE_PAY_REFUND = "REFUND"; //转入退款
    /**
     * 未支付
     */
    public static final String YUE_PAY_NOTPAY = "NOTPAY"; //未支付
    /**
     * 已关闭
     */
    public static final String YUE_PAY_CLOSED = "CLOSED"; //已关闭
    /**
     * 支付失败(其他原因，如银行返回失败)
     */
    public static final String YUE_PAY_PAYERROR = "PAYERROR";


    public final static String SWITCH_ON = "1";//开启缴费

    public final static String SWITCH_OFF = "0";//关闭缴费

    public final static String APP_ID = "wx2b3f39a082ccbadd";//悦家APP_ID

    /**
     * 支付渠道
     */

    public static final String SOURCE_YUEHOME_PAY = "YUEHOME_PAY";//业主APP

    public static final String SOURCE_WECHAT_APPLET = "WECHAT_APPLET";//微信小程序

    public static final String SOURCE_CRTHOME_PAY = "CRT_PAY";//润钱包

    /**
     * 系统类型
     */
    public static final String SYSTEM_TYPE_WECHAT_APPLET = "2";//业主APP

    public final static String MONTH_LEAST="2000-01";

    /**
     *  APP回调支付结果
     */
    public static final Integer APP_TRADE_STATUS_CALLBACK_SUCCESS = 0;//支付成功
    public static final Integer APP_TRADE_STATUS_CALLBACK_CANCEL = 1;//支付撤销/失败


    public final static Integer COMMUNITY_PAY_ENABLE = 1;//小区可缴费
    public final static Integer COMMUNITY_PAY_DISABLE = 0;//小区不可缴费

    //批量资产缴费收费对象不一致错误码
    public static final String CHARGE_OBJ_CODE_FAILED = "1001";
}
