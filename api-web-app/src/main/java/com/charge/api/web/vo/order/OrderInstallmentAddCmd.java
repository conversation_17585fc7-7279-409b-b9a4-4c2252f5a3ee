package com.charge.api.web.vo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 添加支付订单分期
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderInstallmentAddCmd extends BaseOrderCmdReq {


    /**
     * 子订单列表
     */
    @NotBlank(message = "子订单号不能为空")
    private List<SubOrderAmount> subOrderAmounts;

    /**
     * 订单分期id
     */
    @NotBlank(message = "支付进度id不能为空")
    private String progressId;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0.01", message = "金额不能小于0.01")
    private BigDecimal amount;

    /**
     * 支付状态：0-待支付，1-已支付
     */
    @NotNull(message = "支付状态不能为空")
    private Integer payStatus;


    /**
     * 支付模式：1-分期支付，2-全额支付
     */
    @NotNull(message = "支付模式不能为空")
    private Integer payMode;

    /**
     * 支付方式（2支付宝，3微信，）-对于已支付的必须传
     */
    private Integer payMethod;

    /**
     * 支付时间-对于同步已支付的必须指定支付时间
     */
    private Date payTime;

    /**
     * 支付渠道（TRANSFER_OFFLINE线下转帐、ALI支付宝、WECHAT 微信-对于已支付的必须传
     * WECHAT微信）
     */
    private String payChannel;

    /**
     * 支付终端（POS\MALL\LEASE\YUEHOME_PAY）-对于已支付的必须传
     */
    @NotBlank(message = "支付终端不能为空")
    private String payTerminal;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    @NotBlank(message = "用户名称不能为空")
    @Length(max = 200, message = "用户名称最大长度200")
    private String customerName;



}