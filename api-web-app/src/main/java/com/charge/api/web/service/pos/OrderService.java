package com.charge.api.web.service.pos;

import com.charge.api.web.vo.pos.EBusinessMasterOrderVO;
import com.charge.api.web.vo.pos.EBusinessMasterOrderWithItemsVO;
import com.charge.api.web.vo.pos.OrderInstallmentVO;
import com.charge.api.web.vo.pos.OrderInstallmentWithItemsVO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.order.dto.ebuiness.EBusinessMasterOrderDetailQuery;
import com.charge.order.dto.ebuiness.EBusinessMasterOrderQuery;
import com.charge.order.dto.ebuiness.InstallmentDetailQuery;
import com.charge.order.dto.ebuiness.InstallmentQuery;

import java.util.List;

/**
 * 订单服务
 *
 * <AUTHOR>
 * @date 2024/11/15
 */
public interface OrderService {

    List<OrderInstallmentVO> listInstallment(InstallmentQuery installmentQuery) throws ChargeBusinessException;

    OrderInstallmentWithItemsVO installmentDetail(InstallmentDetailQuery conditionDTO) throws ChargeBusinessException;

    List<EBusinessMasterOrderVO> rollListPaidEBusinessMasterOrder(EBusinessMasterOrderQuery orderQuery) throws ChargeBusinessException;

    EBusinessMasterOrderWithItemsVO masterOrderDetail(EBusinessMasterOrderDetailQuery detailQuery) throws ChargeBusinessException;


}
