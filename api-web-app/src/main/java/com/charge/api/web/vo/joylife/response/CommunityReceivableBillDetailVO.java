package com.charge.api.web.vo.joylife.response;

import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户小区级别欠费明细和统计
 */
@Data
public class CommunityReceivableBillDetailVO  implements Serializable {
	/**
	 * 小区是否可用  0:可以正常支付 1：不可以正常支付
	 */
	private Integer disableStatus;
	/**
	 * 预存开启状态 1 开启 0 关闭
	 */
	private Integer prestoreChargeStatus;
	/**
	 * 缴费信息
	 */
	private String message;
	/**
	 * 小区名称
	 */
	private String communityName;
	/**
	 * 小区编码
	 */
	private String communityCode;
	/**
	 * 小区收费系统id
	 */
	private Long communityId;
	/**
	 * 消歧义朝昔ID
	 */
	private String communityMsId;
	/**
	 * 用户在该小区的欠费
	 */
	private String totalArrearsAmount;
	/**
	 * 用户在该小区的欠费明细
	 */
	private List<HouseReceiveBillSumVO> arrearsOrderList;

	public static List<CommunityReceivableBillDetailVO> from(List<ReceivableBillDTO> orderItems, List<CommunityDTO> communityList, Map<Long,List<HouseDTO> > houseListMap, Map<Long, CommunityPayStatusVO> CommunityPayStatusMap) {
		List<CommunityReceivableBillDetailVO> result= new ArrayList<>() ;
		Map<Long,List<ReceivableBillDTO>> orderGroupByCommunityId=orderItems.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getCommunityId));
		for(CommunityDTO community:communityList){
			List<HouseDTO> houseDToList=houseListMap.get(community.getId());
			List<ReceivableBillDTO> communityOrder=orderGroupByCommunityId.get(community.getId());
			CommunityReceivableBillDetailVO orderArrearsCommunityResponse=new CommunityReceivableBillDetailVO();
			orderArrearsCommunityResponse.setCommunityCode(community.getMdmCode());
			orderArrearsCommunityResponse.setCommunityId(community.getId());
			orderArrearsCommunityResponse.setCommunityName(community.getName());
			//orderArrearsCommunityResponse.setCommunityMsId(community.get());
			BigDecimal totalArrearsAmount=BigDecimal.ZERO;
			List<HouseReceiveBillSumVO> arrearsOrderList=new ArrayList<>();
			if(!CollectionUtils.isEmpty(houseDToList)){
				Map<Long,List<ReceivableBillDTO>> arrearsOrderGroupByOwnerId=new HashMap<>();
				if(!CollectionUtils.isEmpty(communityOrder)){
					arrearsOrderGroupByOwnerId=communityOrder.stream()
							.filter(orderItemBS->orderItemBS.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
									||orderItemBS.getPayStatus().equals(ReceivableBillPayStatusEnum.COLLECTION.getCode()) ||
									orderItemBS.getBillStatus().equals(ReceivableBillStatusEnum.BILL_HOLD.getCode()))
							.collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId));
					arrearsOrderList.sort(Comparator.comparing(HouseReceiveBillSumVO::getHouseCode));
					Optional<BigDecimal> totalArrearsAmountOptional=communityOrder.stream()
							.filter(orderItemBS->orderItemBS.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode()))
							.map(orderItemBS->(orderItemBS.getArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getArrearsAmount()).add(orderItemBS.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getPenaltyArrearsAmount()))
							.reduce(BigDecimal::add);
					totalArrearsAmount=totalArrearsAmountOptional.isPresent()?totalArrearsAmountOptional.get():BigDecimal.ZERO;
				}
				for(HouseDTO item:houseDToList){
					arrearsOrderList.add(HouseReceiveBillSumVO.fromPrestore(item,arrearsOrderGroupByOwnerId.get(item.getId()),null));
				}
				}
			orderArrearsCommunityResponse.setTotalArrearsAmount(totalArrearsAmount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
			orderArrearsCommunityResponse.setArrearsOrderList(arrearsOrderList);
			//CommunityPayStatusVO communityPayStatus=CommunityPayStatusMap.get(community.getId());
//			if(communityPayStatus!=null){
//				orderArrearsCommunityResponse.setDisableStatus(communityPayStatus.getDisableStatus());
//				orderArrearsCommunityResponse.setMessage(communityPayStatus.getMessage());
//				orderArrearsCommunityResponse.setPrestoreChargeStatus(communityPayStatus.getPrestoreChargeStatus());
//			}
			result.add(orderArrearsCommunityResponse);
		}
		result.sort(Comparator.comparing(CommunityReceivableBillDetailVO::getCommunityName));
		return result;
	}
}
