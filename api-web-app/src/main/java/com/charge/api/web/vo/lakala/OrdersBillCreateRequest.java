package com.charge.api.web.vo.lakala;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 房间批量订单账单创建请求
 *
 * <AUTHOR>
 * @date 2023/3/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrdersBillCreateRequest {


    String deviceInfo;
    String mercid;
    /**
     * 房屋id
     */
    String ownerId;
    String tempChargeData;
    String amount;
    /**
     * 支付方式（0表示POS;1表示转账;2表示支票;3表示支付宝;4表示微信;6员工代付）
     */
    String paymentMethod;
    String collectorId;
    String collectorName;
    String payMember;
    String bankTransactionNo;
    String arrivalDate;
    String bankAccountUuid;
    String bankAccountNum;
    String memo;
    String token;
}
