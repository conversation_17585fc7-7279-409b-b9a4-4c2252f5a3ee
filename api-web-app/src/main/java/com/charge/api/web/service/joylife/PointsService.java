package com.charge.api.web.service.joylife;

import com.charge.api.web.dto.joylife.DistributePointsReq;
import com.charge.api.web.vo.joylife.PreStorePointsVO;
import com.charge.api.web.vo.joylife.request.*;
import com.charge.api.web.vo.joylife.response.AssetPreStorePointsVO;
import com.charge.api.web.vo.joylife.response.ReceivablePointCalResultVo;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.pay.dto.point.PointTradeDetailDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 积分服务
 * @date 2023/4/7 16:00
 */
public interface PointsService {
    ChargeResponse distributePoints(DistributePointsReq distributePointsReq) throws ChargeBusinessException;

    ChargeResponse calPointsBySelect(MixPointsCalRequest calRequest) throws ChargeBusinessException;

    PreStorePointsVO calPreStorePoints(PreStorePointsCalReq calRequest) throws ChargeBusinessException;

    List<PreStorePointsVO> calPreStorePoints(MultiPreStorePointsCalReq calRequest) throws ChargeBusinessException;


    PreStorePointsVO calPreStorePoints(Long communityId, Long assetId, Long chargeItemId, BigDecimal preStoreAmount) throws ChargeBusinessException;

    ChargeResponse<PagingDTO<PointTradeDetailDTO>> pointPageQuery(PointTradeDetailConditionRequest pointDTO);

    List<AssetPreStorePointsVO> batchCalPreStorePoints(BatchAssetPreStorePointsCalReq batchCalRequest) throws ChargeBusinessException;

    ReceivablePointCalResultVo calPreStorePointsByReceivableBill (ReceivablePointCalRequest receivablePointCalRequest) throws ChargeBusinessException ;
}
