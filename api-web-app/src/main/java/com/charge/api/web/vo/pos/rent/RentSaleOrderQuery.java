package com.charge.api.web.vo.pos.rent;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/12/11 11:01
 */
@Data
public class RentSaleOrderQuery {

    /**
     * 起始id（不包含）
     */
    private Long offset;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long communityId;

    /**
     * 查询参数（订单号、子订单号、客户名称等）
     */
    private String searchParam;
    /**
     * 订单创建时间(开始日期)
     */
    private String createDateFrom;
    /**
     * 订单创建时间(截止日期)
     */
    private String createDateTo;
    /**
     * 支付状态（0-待支付，1-已支付）
     */
    @NotNull(message = "支付状态不能为空")
    @Min(value = 0, message = "支付状态值必须为0或1")
    @Max(value = 1, message = "支付状态值必须为0或1")
    private Integer payStatus;

    /**
     * 单页数量
     */
    @Max(value = 500L, message = "单页最大数量为500条")
    @NotNull(message = "单页数量不能为空")
    private Integer size=20;

    /**
     * 来源
     */
    private String source;

}
