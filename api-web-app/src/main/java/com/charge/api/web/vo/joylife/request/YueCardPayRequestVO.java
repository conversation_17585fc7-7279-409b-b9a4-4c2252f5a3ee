package com.charge.api.web.vo.joylife.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Classname YueCardPayVO
 * @Description 月卡办理支付请求参数
 * @Date 2022/5/10 14:00
 */
@Data
@ApiModel("月卡办理请求参数")
public class YueCardPayRequestVO implements Serializable {

    @ApiModelProperty("项目id（小区id）")
    private String projectId;

    @ApiModelProperty("车场id")
    @NotBlank(message = "propertyId不能为空")
    private String propertyId;

    @ApiModelProperty("车场名称")
    @NotBlank(message = "propertyName不能为空")
    private String propertyName;

    @ApiModelProperty("车场编码")
    private String propertyCode;

    @ApiModelProperty("月卡生效时间")
    @NotNull(message = "effectiveTime不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveTime;

    @ApiModelProperty("月卡到期时间")
    @NotNull(message = "expireTime不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    @ApiModelProperty("车牌号")
    @NotBlank(message = "carNo不能为空")
    private String carNo;

    @ApiModelProperty("月卡ID（或月卡产品ID）")
    @NotBlank(message = "cardId不能为空")
    private String cardId;

    @ApiModelProperty("月卡名称")
    @NotBlank(message = "cardName不能为空")
    private String cardName;

    @ApiModelProperty("月卡类型:(例：1停车场月卡，2停车场季卡，3年卡)")
    @NotBlank(message = "cardType不能为空")
    private String cardType;

    @ApiModelProperty("卡费项明细数组")
    @NotBlank(message = "cardItem不能为空")
    private String cardItem;

    @ApiModelProperty("应付金额")
    @DecimalMin(value = "0.00",message = "金额格式不正确")
    @NotNull(message = "amount不能为空")
    private BigDecimal amount;

    @ApiModelProperty("房屋 ID")
    private String houseUuid;

    @ApiModelProperty("缴费人")
    @NotBlank(message = "payMember不能为空")
    private String payMember;

    @ApiModelProperty("缴费id")
    @NotBlank(message = "payMemberId不能为空")
    private String payMemberId;

    @ApiModelProperty("此字段区分api接口功能类型:（1-月卡办理，2-月卡续费）")
    @NotBlank(message = "apiType不能为空")
    private String apiType;

    @ApiModelProperty(value = "终端IP", required = true)
    @NotNull(message = "终端IP参数缺失,请重试")
    private String mchCreateIp;

    @ApiModelProperty(value = "AppID", required = true)
    @NotNull(message = "APPId参数缺失,请重试")
    private String subAppid;

    @ApiModelProperty(value = "支付来源(微信小程序：WECHAT_APPLET，悦家APP：YUEHOME_PAY，润钱包：CRT_PAY)", required = true)
    @NotNull(message = "支付来源参数缺失,请重试")
    private String paymentSource;

    @ApiModelProperty(value = "微信用户小程序关注商家的openid(小程序缴费必传)", required = false)
    private String subOpenid;

    /**
     * 买家支付宝账号(支付宝时必填)
     */
    private String buyerLogonId;

    /**
     * 支付方式（支付宝：3,微信支付：4）
     */
    private String paymentMethod;

    @ApiModelProperty("停车月卡ID（停车云平台月卡ID）")
    private String parkingCardId;

    /**
     * 是否是app支付，使用其他渠道拉起支付宝app（非支付宝小程序）的需要传该值为true
     */
    private Boolean payNative;

    /**
     * 支付成功后跳转地址
     */
    private String returnUrl;

}
