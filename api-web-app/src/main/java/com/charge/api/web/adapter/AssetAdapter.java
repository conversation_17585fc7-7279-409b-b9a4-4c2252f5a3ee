package com.charge.api.web.adapter;

import com.charge.api.web.constants.AssetTypeEnum;
import com.charge.api.web.vo.lakala.SubjectSearch;
import com.charge.common.util.EnumUtil;
import com.charge.maindata.enums.DwellStateEnum;
import com.charge.maindata.enums.UseStatusEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import com.charge.maindata.pojo.dto.ParkingSpaceDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.charge.api.web.service.pos.impl.ChargeBillServiceImpl.nullStr;
import static com.charge.api.web.util.DesensitizeUtil.desensitize;

@Component
@RequiredArgsConstructor
public class AssetAdapter {

    public static SubjectSearch toSubjectSearch(AssetDTO assetDTO) {
        SubjectSearch.SubjectSearchBuilder searchBuilder = SubjectSearch.builder().subjectId(nullStr(assetDTO.getId()))
                .subjectType(EnumUtil.getEnumByCode(AssetTypeEnum.values(), assetDTO.getType()).getValue());
        List<CustomerDTO> customerDTOList = null;
        if (assetDTO.getType().equals(com.charge.maindata.enums.AssetTypeEnum.HOUSE.getCode()) && assetDTO.getHouseDTO() != null) {
            HouseDTO houseDTO = assetDTO.getHouseDTO();
            customerDTOList = houseDTO.getListCustomer();
            searchBuilder.subjectName(houseDTO.getHouseName())
                    .buildArea(houseDTO.getConstructionArea())
                    .insideArea(houseDTO.getSetWithinArea())
                    .chargeArea(houseDTO.getChargeArea())
                    .communityName(houseDTO.getCommunityName())
                    .communityId(nullStr(houseDTO.getCommunityId()))
                    .buildingName(houseDTO.getBuildingName())
                    .unitName(houseDTO.getUnitName())
                    .memo(houseDTO.getRemark())
                    .build();
        } else if (assetDTO.getType().equals(com.charge.maindata.enums.AssetTypeEnum.PARKING_SPACE.getCode())&& assetDTO.getParkingSpaceDTO() != null) {
            ParkingSpaceDTO parkingSpaceDTO = assetDTO.getParkingSpaceDTO();
            customerDTOList = parkingSpaceDTO.getListCustomer();
            searchBuilder.subjectName(parkingSpaceDTO.getParkingSpaceName())
                    .communityName(parkingSpaceDTO.getCommunityName())
                    .buildingName(parkingSpaceDTO.getParkingName())
                    .communityId(nullStr(parkingSpaceDTO.getCommunityId()))
            .buildArea(nullStr(parkingSpaceDTO.getParkingSpaceArea()))
            .insideArea("0")
            .chargeArea(nullStr(parkingSpaceDTO.getParkingSpaceArea()))
            .memo(parkingSpaceDTO.getRemark())
            ;
        }
        if (CollectionUtils.isNotEmpty(customerDTOList)) {
            customerDTOList = customerDTOList.stream()
                    .filter(customerDTO -> DwellStateEnum.STAY.getCode().equals(customerDTO.getDwellState())
                            || DwellStateEnum.LEAVE.getCode().equals(customerDTO.getDwellState())
                            || UseStatusEnum.USING.getCode().equals(customerDTO.getUseStatus())
                            || UseStatusEnum.UNUSED.getCode().equals(customerDTO.getUseStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(customerDTOList)) {
                searchBuilder.houseownerName(customerDTOList.stream().map(CustomerDTO::getCustomerName).collect(Collectors.joining(",")))
                        .houseownerMobile(customerDTOList.stream().map(a -> desensitize(a.getPhone())).collect(Collectors.joining(",")))
                        .houseownerId(nullStr(customerDTOList.get(0).getId()));
            }
        }
        return searchBuilder.build();
    }
}
