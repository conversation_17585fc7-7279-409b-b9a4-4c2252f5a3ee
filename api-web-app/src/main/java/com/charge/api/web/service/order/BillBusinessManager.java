package com.charge.api.web.service.order;

import com.charge.bill.enums.BusinessTypeEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 账单业务管理器
 *
 * <AUTHOR>
 * @date 2023/2/28
 */
@Component
public class BillBusinessManager {

    private final Map<BusinessTypeEnum, BillBusinessHandler> billBusinessHandlerMap;

    public BillBusinessManager(List<BillBusinessHandler> billBusinessHandlers) {
        billBusinessHandlerMap = billBusinessHandlers.stream().collect(Collectors.toMap(BillBusinessHandler::type, a -> a, (a, b) -> b));
    }

    public void handleBillBusiness(CreateOrderContext context) {
        context.getAssetBillBusinesses().forEach(assetBillBusiness -> {
            assetBillBusiness.getBillBusinesses().stream().collect(Collectors.groupingBy(BillBusiness::getType))
                    .forEach((type, billBusinesses) -> {
                        BillBusinessHandler handler = billBusinessHandlerMap.get(type);
                        handler.handle(context, assetBillBusiness, billBusinesses);
                    });
        });
    }

    public void validateBillBusiness(CreateOrderContext context) {
        context.getAssetBillBusinesses().forEach(assetBillBusiness -> {
            assetBillBusiness.getBillBusinesses().forEach(billBusiness -> {
                Assert.isTrue(billBusinessHandlerMap.containsKey(billBusiness.getType()), "billBusiness type 不支持:" + billBusiness.getType());
                BillBusinessHandler handler = billBusinessHandlerMap.get(billBusiness.getType());
                handler.validate(billBusiness);
            });
        });

    }
}


