package com.charge.api.web.vo.pos.points;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/11/3 14:06
 */

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AssetCustomerQueryVO implements Serializable {

    @ApiModelProperty("资产ID")
    @NotBlank(message = "资产id不能为空")
    private String assetId;

}
