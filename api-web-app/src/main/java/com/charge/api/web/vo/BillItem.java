package com.charge.api.web.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/10/26
 */
@Data
public class BillItem implements Serializable {

    private static final long serialVersionUID = -8655337316016796714L;
    /**
     * 收费项id
     */
    @NotNull(message = "收费项不能为空")
    private Long itemId;

    /**
     * 收费项名称
     */
    @NotNull(message = "收费项名称不能为空")
    private String itemName;

    /**
     * 应收单ID（非应收单置空）
     */
    private Long id;

    /**
     * 金额
     */
    @NotNull(message = "订单项金额不能为空")
    @Min(value = 0L,message = "订单项金额必须大于0")
    private BigDecimal amount;

    /**
     * 预存该费项赠送的积分
     */
    @Min(value = 1L)
    private Long points;


    /**
     * 分期id
     */
    private Long installmentId;


    /**
     * 电商子订单id
     */
    @JsonProperty( "eBusinessBaseOrderId")
    private Long eBusinessBaseOrderId;
}
