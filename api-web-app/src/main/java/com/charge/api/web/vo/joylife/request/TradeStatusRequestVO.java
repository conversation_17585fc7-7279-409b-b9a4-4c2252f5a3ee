package com.charge.api.web.vo.joylife.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/11 10:06
 */
@Data
public class TradeStatusRequestVO implements Serializable {
    @NotNull(message = "项目id")
    private String communityMsId;
    @NotBlank(message = "商户订单号不能为空")
    private String orderNum;

    /**
     *
     * 是否同步查询三方结果
     */
    private boolean syncQuery = false;

}
