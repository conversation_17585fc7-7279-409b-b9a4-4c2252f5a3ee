package com.charge.api.web.vo.joylife.response;

import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.maindata.pojo.dto.CommunityDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
public class CommunityReceivableBillSumVO implements Serializable {
    /**
     * 小区欠费总额
     */
    @ApiModelProperty(value = "小区欠费总额")
    private String totalArrearsAmount;
    /**
     * 小区欠费总额明细
     */
    @ApiModelProperty(value = "小区欠费总额")
    private List<CommunityArearsDetail> communityArearsDetailList;

    public static CommunityReceivableBillSumVO  from(List<ReceivableBillDTO> orderItems, List<CommunityDTO> communities) {
        List<CommunityArearsDetail> result = new ArrayList<>();
        Map<Long, List<ReceivableBillDTO>> orderGroupByCommunityId = orderItems.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getCommunityId));
        BigDecimal totalCommunityArrearsAmount=BigDecimal.ZERO;
        for (CommunityDTO community : communities) {
            List<ReceivableBillDTO> communityOrder = orderGroupByCommunityId.get(community.getId());

            Optional<BigDecimal> totalArrearsAmountOptional = communityOrder.stream()
                    .filter(orderItemBS -> orderItemBS.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                            || orderItemBS.getPayStatus().equals(ReceivableBillPayStatusEnum.COLLECTION.getCode()))
                    .map(orderItemBS -> (orderItemBS.getArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getArrearsAmount()).add(orderItemBS.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getPenaltyArrearsAmount()))
                    .reduce(BigDecimal::add);
            BigDecimal totalArrearsAmount = totalArrearsAmountOptional.isPresent() ? totalArrearsAmountOptional.get() : BigDecimal.ZERO;
            Optional<BigDecimal> totalHangupAmountOptional = communityOrder.stream()
                    .filter(orderItemBS -> orderItemBS.getBillStatus().equals(ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                    .map(orderItemBS -> (orderItemBS.getArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getArrearsAmount()).add(orderItemBS.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getPenaltyArrearsAmount()))
                    .reduce(BigDecimal::add);
            BigDecimal totalHangupAmount = totalHangupAmountOptional.isPresent() ? totalHangupAmountOptional.get() : BigDecimal.ZERO;
            totalCommunityArrearsAmount=totalCommunityArrearsAmount.add(totalHangupAmount).add(totalArrearsAmount);
            CommunityArearsDetail orderArrearsCommunityResponse = new CommunityArearsDetail();
            orderArrearsCommunityResponse.setCommunityCode(community.getMdmCode());
            orderArrearsCommunityResponse.setCommunityId(String.valueOf(community.getId()));
            //orderArrearsCommunityResponse.setCommunityMsId(community.getM());
            orderArrearsCommunityResponse.setCommunityName(community.getName());
            orderArrearsCommunityResponse.setArrearsAmount(totalArrearsAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            orderArrearsCommunityResponse.setHangupAmount(totalHangupAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            result.add(orderArrearsCommunityResponse);
        }
        CommunityReceivableBillSumVO response=new CommunityReceivableBillSumVO();
        response.setCommunityArearsDetailList(result);
        response.setTotalArrearsAmount(totalCommunityArrearsAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        return response;
    }


    @Data
    static class CommunityArearsDetail {
        /**
         * 小区名称
         */
        @ApiModelProperty(value = "小区名称")
        private String communityName;
        /**
         * 小区编码
         */
        @ApiModelProperty(value = "小区编码")
        private String communityCode;
        /**
         * 收费系统测小区id
         */
        @ApiModelProperty(value = "小区id")
        private String communityId;
        /**
         * 朝昔测小区ID
         */
        private String communityMsId;
        /**
         * 小区欠费总额
         */
        @ApiModelProperty(value = "小区欠费总额")
        private String arrearsAmount;
        /**
         * 小区冻结欠费总额
         */
        @ApiModelProperty(value = "小区冻结欠费总额")
        private String hangupAmount;
    }
}
