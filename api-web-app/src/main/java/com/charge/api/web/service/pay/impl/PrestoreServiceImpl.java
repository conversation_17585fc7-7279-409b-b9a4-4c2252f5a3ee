package com.charge.api.web.service.pay.impl;

import com.alibaba.fastjson.JSONObject;
import com.charge.api.web.adapter.PayOrderAdapter;
import com.charge.api.web.config.AssetPayGrayConfig;
import com.charge.api.web.constants.IncomeBillConstants;
import com.charge.api.web.constants.YueConstants;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.service.bill.joy.AddPrestorePayService;
import com.charge.api.web.service.joylife.PointsService;
import com.charge.api.web.service.pay.PrestoreService;
import com.charge.api.web.support.*;
import com.charge.api.web.util.ShardingUtil;
import com.charge.api.web.vo.joylife.AssetChargeStandVO;
import com.charge.api.web.vo.joylife.BatchAssetOrderCheckResultVO;
import com.charge.api.web.vo.joylife.PreStorePointsVO;
import com.charge.api.web.vo.joylife.PrestoreChargeOrderInfo;
import com.charge.api.web.vo.joylife.deposit.AssetPreStoreV0;
import com.charge.api.web.vo.joylife.deposit.GroupPrestoreVO;
import com.charge.api.web.vo.joylife.deposit.PrestoreChargeInfoVO;
import com.charge.api.web.vo.joylife.deposit.PrestoreVO;
import com.charge.api.web.vo.joylife.request.*;
import com.charge.api.web.vo.joylife.response.BatchCalculatePreStoreVO;
import com.charge.api.web.vo.joylife.response.ChargeStandardVO;
import com.charge.api.web.vo.joylife.response.PrestorePayStatusVO;
import com.charge.bill.client.*;
import com.charge.bill.dto.ReceivableAmountTotalDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.income.*;
import com.charge.bill.dto.predeposit.PredepositAccountConditionDTO;
import com.charge.bill.dto.predeposit.PredepositAccountDTO;
import com.charge.bill.enums.*;
import com.charge.common.constant.PayRelatedConstants;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ChargeStatusEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.config.client.assets.AssetsChargeConfigClient;
import com.charge.config.client.item.CommunityPreStoreItemClient;
import com.charge.config.client.item.CommunitySpecialPreStoreGroupClient;
import com.charge.config.client.points.PointsConfigClient;
import com.charge.config.constant.CommonChargeItem;
import com.charge.config.dto.assets.AssetChargeConfigDetailDTO;
import com.charge.config.dto.assets.condition.AssetChargeConfigQueryConditionDTO;
import com.charge.config.dto.item.*;
import com.charge.config.dto.item.condition.PreStoreItemConfigQueryConditionDTO;
import com.charge.config.dto.item.condition.SpecialItemStandardConditionDTO;
import com.charge.config.dto.item.condition.ZXSpecialPreStoreQueryConditionDTO;
import com.charge.config.dto.points.PointsConfigConditionDTO;
import com.charge.config.dto.points.PointsConfigValueDTO;
import com.charge.config.dto.points.PointsSignCommunityDTO;
import com.charge.config.enums.AssetChargeConfigStatusEnum;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.TraceContextUtil;
import com.charge.feecalculte.dto.ReceivableBillDTO;
import com.charge.general.client.bff.PayCallbackClient;
import com.charge.general.dto.PayCallbackResultDTO;
import com.charge.general.dto.PayOrderResultDTO;
import com.charge.joylife.dto.PayInfoDTO;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.enums.ChargeObjTypeEnum;
import com.charge.maindata.enums.CustomerTypeEnum;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.pay.client.CommunityMerchantClient;
import com.charge.pay.client.PayClient;
import com.charge.pay.dto.ChargePayResponse;
import com.charge.pay.dto.CommunityMerchantDTO;
import com.charge.pay.dto.condition.MerchantConditionDTO;
import com.charge.pay.dto.pay.PayOrderSubmitResponseDTO;
import com.charge.pay.dto.pay.QueryOrderRequestDTO;
import com.charge.pay.dto.pay.QueryOrderResponseDTO;
import com.charge.pay.enums.PayTradeStateEnum;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.charge.api.web.constants.YueConstants.SOURCE_YUEHOME_PAY;

/**
 * 预存充值销单
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class PrestoreServiceImpl implements PrestoreService {

    private final IncomeBillClient incomeBillClient;
    private final PredepositBillClient predepositBillClient;
    private final AssetTransactionClient assetTransactionClient;
    private final FeeCalculateSupport feeCalculateSupport;
    private final PointsConfigClient pointsConfigClient;
    private final PayClient payClient;
    private final CommunityParamSupport communityParamSupport;

    private final CommunitySupport communitySupport;


    private final AssetSupport assetSupport;
    private final CommunityPreStoreItemClient communityPreStoreItemClient;
    private final ReceivableBillClient receivableBillClient;
    private final BillNumGeneratorClient billNumGeneratorClient;
    private final AssetBillInfoClient assetBillInfoClient;
    private final WriteOffBillClient writeOffBillClient;
    private final TransactionRelationClient transactionRelationClient;
    private final BankCollectionCheckSupport bankCollectionCheckSupport;
    private final AddPrestorePayService addPrestorePayService;
    private final AssetPayGrayConfig assetPayGrayConfig;
    private final PointsService pointsService;

    private final PayCallbackClient payCallbackClient;

    private final CommunityMerchantClient communityMerchantClient;

    private final PredepositAccountClient predepositAccountClient;

    private final AssetsChargeConfigClient assetsChargeConfigClient;

    private final CommunitySpecialPreStoreGroupClient communitySpecialPreStoreGroupClient;

    private final ReceivableSupport receivableSupport;

    private final Cache<LocalDate, String> pointCongihCache = CacheBuilder.newBuilder()
            .maximumSize(20)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build();


    /**
     * 预存充值销单
     *
     * @param paymentSource 支付渠道
     * @param transactionId 实收单id
     * @return success
     */
    @Override
    public ChargeResponse postPayStatus(String paymentSource, String transactionId) {
        String key = null;
        try {
            // 根据batchOrderId查询小区id、paymentSource
            Long transactionIdLong = Long.valueOf(transactionId);
            IncomeConditionDTO incomeConditionDTO = new IncomeConditionDTO();
            incomeConditionDTO.setId(transactionIdLong);
            incomeConditionDTO.setPageNum(1);
            incomeConditionDTO.setPageSize(1);
            // NOTE 从缓存读取communityId
            Long communityId = communitySupport.getCommunityIdByIncomeId(transactionIdLong);
            incomeConditionDTO.setCommunityId(communityId);
            ShardingUtil.addCommunityId2ThreadContext(communityId);
            ChargeResponse<PagingDTO<IncomeBillDTO>> incomeBillResp = incomeBillClient.selectByCondition(incomeConditionDTO);
            if (!incomeBillResp.isSuccess()) {
                log.info("账单服务异常");
                return new ChargeResponse<>(YueConstants.CODE_FAILED, "账单服务异常" + incomeBillResp.getMessage());
            }

            PagingDTO<IncomeBillDTO> pageIncomeBillDTO = incomeBillResp.getContent();
            if (pageIncomeBillDTO == null) {
                log.info("订单号:{},查询不到对应记录", transactionId);
                return new ChargeResponse<>(YueConstants.CODE_FAILED, "查询不到对应记录");
            }
            List<IncomeBillDTO> listIncomeBillDTO = pageIncomeBillDTO.getList();
            if (listIncomeBillDTO.size() != 1) {
                log.info("订单号:{},查询不到对应记录，或者查到多条记录", transactionId);
                return new ChargeResponse<>(YueConstants.CODE_FAILED, "查询不到对应记录，或者查到多条记录");
            }
            IncomeBillDTO incomeBillDTO = listIncomeBillDTO.get(0);
            if (IncomeBillConstants.PAYMENT_STATUS_PAY_SUCCESS == incomeBillDTO.getPayStatus()) {
                log.info("订单号:{},该订单已缴费完成，请勿重复操作", transactionId);
                PrestorePayStatusVO payStatusVO = PrestorePayStatusVO.builder()
                        .transactionNo(transactionId)
                        .orderNum(incomeBillDTO.getOrderNum())
                        .points(incomeBillDTO.getPoints())
                        .payStatus(String.valueOf(BillPayStatusEnum.SUCCESS.getCode()))
                        .amount(incomeBillDTO.getIncomeMoney())
                        .build();
                return new ChargePayResponse<>(YueConstants.CODE_SUCCESS, "该订单已缴费完成，请勿重复操作", payStatusVO);
            }
            //订单号
            String orderNum = incomeBillDTO.getOrderNum();

            QueryOrderRequestDTO queryOrderRequestDTO=new QueryOrderRequestDTO();
            queryOrderRequestDTO.setPaymentTerminal(paymentSource);
            queryOrderRequestDTO.setOutTradeNo(orderNum);
            ChargeResponse<QueryOrderResponseDTO> chargeResponse = payClient.tradeQuery(queryOrderRequestDTO);
            if (!chargeResponse.isSuccess()) {
                log.info("查询支付下单结果接口失败,{}", chargeResponse);
                return new ChargeResponse<>(YueConstants.CODE_FAILED, chargeResponse.getMessage());
            }
            if (!Objects.equals(chargeResponse.getContent().getTradeState(), PayTradeStateEnum.SUCCESS.getCode())) {
                log.info("查询支付下单结果查询,{}", chargeResponse);
                return new ChargeResponse<>(YueConstants.CODE_NO_PAY, "本单还没有进行支付或支付中");
            }
            // 调用实收单接口（校验、更新状态）
            PayOrderResultDTO payOrderResultDTO = BeanCopierWrapper.copy(chargeResponse.getContent(), PayOrderResultDTO.class);
            payOrderResultDTO.setCommunityId(communityId);
            ChargeResponse<PayCallbackResultDTO> operateResponse = payCallbackClient.operate(payOrderResultDTO);
            if (!operateResponse.isSuccess()) {
                log.info("更新账单异常" + operateResponse.getMessage());
                return new ChargePayResponse<>(YueConstants.CODE_FAILED, "更新账单异常" + operateResponse.getMessage());
            }
            PrestorePayStatusVO payStatusVO = PrestorePayStatusVO.builder()
                    .transactionNo(transactionId)
                    .orderNum(orderNum)
                    .points(Objects.nonNull(operateResponse.getContent())
                            ? operateResponse.getContent().getEarnPoints() : null)
                    .payStatus(String.valueOf(BillPayStatusEnum.SUCCESS.getCode()))
                    .amount(incomeBillDTO.getIncomeMoney())
                    .build();
            return new ChargePayResponse<>(YueConstants.CODE_SUCCESS, "success", payStatusVO);
        } catch (Exception e) {
            log.error("{}|预存充值主动查询异常", LogCategoryEnum.BUSSINESS, e);
            return new ChargeResponse<>(YueConstants.CODE_FAILED, "预存充值销单异常");
        }
    }

    @Override
    public ChargeResponse<Object> addPrestoreInfo(PrestoreOrderRequest request) throws ChargeBusinessException {
        // 1. 校验是否满足预存缴费规则
        String assetId = request.getHouseId();
        String communityId = request.getCommunityId();
        List<PrestoreChargeOrderInfo> chargeOrderInfoList = request.getChargeOrderInfoList();
        List<PrestoreChargeOrderInfo> commonPrestoreList =
                chargeOrderInfoList.stream().filter(item -> Objects.equals(CommonChargeItem.ITEM_ID,
                        Long.valueOf(item.getItemId()))).collect(Collectors.toList());
        List<PrestoreChargeOrderInfo> specialPrestoreList =
                chargeOrderInfoList.stream().filter(item -> !Objects.equals(CommonChargeItem.ITEM_ID,
                        Long.valueOf(item.getItemId()))).collect(Collectors.toList());

        // 1.1 校验金额相关
        this.checkAddPrestoreInfo(request, commonPrestoreList);
        // 1.2 校验基础数据
        CommunityDTO communityDTO = communitySupport.getCommunityById(Long.valueOf(communityId));
        if (Objects.isNull(communityDTO)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E8001);
        }
        AssetAdapter assetInfo = assetSupport.getAssetInfoById(assetId);
        if (Objects.isNull(assetInfo)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E8002);
        }
        if (Objects.isNull(assetInfo.getChargeObject())) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1018.getCode(),"资产收费对象缺失,无法缴费");
        }
        //校验收费项是否过期
        checkPreStoreItemIds(communityId, assetId, chargeOrderInfoList);

        log.info("{}|【预存缴费】生成预支付订单开始：通用预存个数:{}，{}", LogCategoryEnum.BUSSINESS, commonPrestoreList.size(),
                commonPrestoreList);
        log.info("{}|【预存缴费】生成预支付订单开始：专项预存个数:{}，{}", LogCategoryEnum.BUSSINESS, specialPrestoreList.size(),
                specialPrestoreList);

        // 2. 生成支付单
        String orderNum = IdGeneratorSupport.getIstance().nextId();
        ChargeResponse<PayOrderSubmitResponseDTO> payReturnChargeResponse =
                payClient.tradeCreate(PayOrderAdapter.requestParamConvert(request, communityDTO.getId(), orderNum));
        if (!payReturnChargeResponse.isSuccess() || payReturnChargeResponse.getContent() == null) {
            log.error("{}|预存缴费下单异常|{}", LogCategoryEnum.BUSSINESS, payReturnChargeResponse);
            return new ChargeResponse<>(payReturnChargeResponse.getCode(), payReturnChargeResponse.getMessage());
        }
        PayOrderSubmitResponseDTO payReturnDTO = payReturnChargeResponse.getContent();

        // 3. 创建单据并返回支付单
        return this.addPredeposit(commonPrestoreList, specialPrestoreList, request, communityDTO, assetInfo,
                payReturnDTO, orderNum);
    }

    private void checkPreStoreItemIds(String communityId, String assetId, List<PrestoreChargeOrderInfo> preStoreOrders) throws ChargeBusinessException {
        List<Long> preStoreItemIds = preStoreOrders.stream().map(t -> Long.valueOf(t.getItemId())).collect(Collectors.toList());
        //仅存通用预存不用校验
        if(preStoreItemIds.size() == 1&&  Objects.equals(preStoreItemIds.get(0), CommonChargeItem.ITEM_ID)){
            return ;
        }
        preStoreItemIds.remove(CommonChargeItem.ITEM_ID);

        ZXSpecialPreStoreQueryConditionDTO condition = new ZXSpecialPreStoreQueryConditionDTO();
        condition.setCommunityId(Long.valueOf(communityId));
        condition.setAssetId(Long.valueOf(assetId));
        condition.setCheckDate(true);
        ChargeResponse<List<ItemInfo>> assetBandItemsResp = communityPreStoreItemClient.listSpecialItemForZX(condition);
        List<ItemInfo> assetBandItems = AppInterfaceUtil.getDataThrowException(assetBandItemsResp);
        if (CollectionUtils.isEmpty(assetBandItems)){
            throw new ChargeBusinessException(ErrorInfoEnum.E3020.getCode(),"资产收费项未在生效时间，无法缴费!");
        }
        List<Long> assetBandItemIds = assetBandItems.stream().map(ItemInfo::getItemId).collect(Collectors.toList());
        preStoreItemIds.removeAll(assetBandItemIds);
        if (CollectionUtils.isNotEmpty(preStoreItemIds)){
            throw new ChargeBusinessException(ErrorInfoEnum.E3020.getCode(),"资产收费项未在生效时间，无法缴费!");
        }
    }

    private ChargeResponse<Object> addPredeposit(List<PrestoreChargeOrderInfo> commonPrestoreList,
                                                 List<PrestoreChargeOrderInfo> specialPrestoreList,
                                                 PrestoreOrderRequest request, CommunityDTO communityDTO, AssetAdapter assetInfo,
                                                 PayOrderSubmitResponseDTO payReturnDTO, String orderNum) throws ChargeBusinessException {
        Long communityId = communityDTO.getId();
        Long assetId = assetInfo.getId();
        //查询通用预存可抵扣费项列表
        PreStoreItemConfigQueryConditionDTO query = new PreStoreItemConfigQueryConditionDTO();
        query.setCommunityIds(Collections.singletonList((communityId)));
        ChargeResponse<List<PreStoreItemConfigDTO>> preStoreItemConfigList =
                communityPreStoreItemClient.getPreStoreItemConfig(query);
        if (!Objects.equals(ChargeStatusEnum.SUCCESS.getCode(), preStoreItemConfigList.getCode()) || CollectionUtils.isEmpty(preStoreItemConfigList.getContent())) {
            log.error("{}|查询小区预存配置信息异常|{}", LogCategoryEnum.BUSSINESS, preStoreItemConfigList);
            return new ChargeResponse<>(YueConstants.CODE_FAILED, "当前小区不能预存，请联系管家");
        }
        //查询业主信息
        List<CustomerDTO> customerDTOList = assetInfo.getListCustomer();
        log.info("{}|【朝昔】预存充值2.0，业主信息|{}", LogCategoryEnum.BUSSINESS, customerDTOList);

        // 查询业主信息
        List<CustomerDTO> ownerDTOList = org.apache.commons.collections.CollectionUtils.isEmpty(customerDTOList) ?
                null :
                customerDTOList.stream().filter(item -> CustomerTypeEnum.OWNER.getCode().equals(item.getCustomerType())).collect(Collectors.toList());

        // 不存在业主信息则直接返回错误
        if (org.apache.commons.collections.CollectionUtils.isEmpty(ownerDTOList)) {
            return new ChargeResponse<>(YueConstants.CODE_FAILED, "房间不存在业主信息，有疑问请联系管家");
        }
        Map<String, CustomerDTO> msIdKeyCustomerMap =
                ownerDTOList.stream().filter(customerDTO -> org.apache.commons.lang.StringUtils.isNotBlank(customerDTO.getMsId()))
                        .collect(Collectors.toMap(CustomerDTO::getMsId, Function.identity()));

        // 校验是否是业主
        CustomerDTO ownerDTO = msIdKeyCustomerMap.get(request.getCustomId());
        Long userId;
        Long ownerId;
        String ownerName;
        if (Objects.nonNull(ownerDTO)) {
            ownerId = ownerDTO.getId();
            userId = ownerDTO.getId();
            ownerName = ownerDTO.getCustomerName();
        } else {
            // 非业主缴费时， 取第一个业主
            ownerDTO = ownerDTOList.get(0);
            ownerId = ownerDTO.getId();
            ownerName = ownerDTO.getCustomerName();

            // 非业主需找到对应那个用户进行充值
            CustomerDTO customerDTO = customerDTOList.stream().filter(item -> Objects.equals(item.getMsId(),
                    request.getCustomId()) && (!Objects.equals(item.getCustomerType(),
                    CustomerTypeEnum.OWNER.getCode()))).findFirst().orElse(null);
            userId = Objects.isNull(customerDTO)?ownerId:customerDTO.getId();
        }
        // 执行新方法
        if (assetPayGrayConfig.isOpenGray(communityId, "/new/weixin/addPrestoreInfo")) {
            return addPrestorePayService.createBill(request, communityDTO, payReturnDTO, assetInfo, orderNum, ownerName, ownerId);
        }
        // 获取缴欠费的应收单
        PreStoreItemConfigDTO preStoreItemConfig = preStoreItemConfigList.getContent().get(0);
        List<Long> genaralList = preStoreItemConfig.getCommonPreStoreItems();
        //  所有的  TODO应该要过来掉
        List<Long> deductList =
                request.getChargeOrderInfoList().stream().map(t -> Long.valueOf(t.getItemId())).collect(Collectors.toList());
        deductList.addAll(genaralList);
        //查询房间欠费列表
        List<com.charge.bill.dto.ReceivableBillDTO> recBillList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deductList)) {
            // 只需要拉取当前月份之前的欠费应收
            ReceivableConditionDTO recCondition =
                    ReceivableConditionDTO.builder()
                            .communityId(communityId)
                            .assetId(assetId)
                            .billStatus(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode())
                            .payStatuses(Lists.newArrayList(ReceivableBillPayStatusEnum.NOT_PAY.getCode(), ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                            .itemIdList(deductList).chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode())
                            .belongYearsEnd(DateUtils.format(new Date(), DateUtils.FORMAT_14))
                            .build();
            ChargeResponse<List<com.charge.bill.dto.ReceivableBillDTO>> listChargeResponse = receivableBillClient.queryList(recCondition);
            recBillList = listChargeResponse.getContent();
        }
        log.info("{}|【朝昔】预存充值2.0，房间欠费|{}", LogCategoryEnum.BUSSINESS, recBillList);
        //保存实收单
        BigDecimal totalMoney = new BigDecimal(request.getTotalPrice());
        String billNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.INCOME_BILL.getCode()).getContent();
        Integer points = org.apache.commons.lang.StringUtils.isEmpty(request.getTotalPoints()) ? 0 :
                Integer.parseInt(request.getTotalPoints());
        IncomeBillDTO income =
                IncomeBillDTO.builder()
                        .orderNum(orderNum)
                        .billNum(billNum)
                        .outTransactionNo(payReturnDTO.getOutTradeNo())
                        .communityId(communityId)
                        .communityName(communityDTO.getName())
                        .incomeMoney(totalMoney).points(points)
                        .equityAccount(request.getEquityAccount())
                        .goodsName(PayRelatedConstants.GOODSNAME_FOR_PRESTORE)
                        .paymentTerminal(PaymentTerminalEnum.handleJoyLifeWechatApplet(request.getPaymentSource()))
                        .paymentTime(new Date())
                        .payMemberId(request.getUserId())
                        .payMember(request.getUserName())
                        .payMemberMobile(request.getPhone())
                        .payHouseCount(1)
                        .balanceStatus(BalanceStatusEnum.UNRECONCILED.getCode())
                        .memo(request.getMemo())
                        .msAssetOwnerId(request.getCustomId())
                        .isMergeBill(payReturnDTO.getIsMergeBill())
                        .subBills(PayOrderAdapter.fillSubBills(payReturnDTO.getSubItems()))
                        .build();
        PayOrderAdapter.fillPaymentMethod(income,request.getPaymentMethod());
        if (Objects.equals(PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel(),request.getPaymentSource())) {
            income.setPaymentChannel(request.getPaymentSource());
        }
        income.setCreateUser(request.getUserName());
        income.setModifyUser(request.getUserName());
        income.setCreateTime(DateUtils.getCurrentTimestamp());
        income.setEquityAccount(request.getEquityAccount());
        income.setPayMemberMobile(request.getPhone());
        income.setPayMember(request.getUserName());
        income.setPayMemberId(request.getUserId());
        income = incomeBillClient.create(income).getContent();
        log.info("{}|【朝昔】预存充值2.0，成功新增实收单|{}", LogCategoryEnum.BUSSINESS, income);

        //保存资产流水单
        Integer assetType = Objects.isNull(assetInfo.getType())? AssetTypeEnum.HOUSE.getCode() : assetInfo.getType();
        String assetBillType = Objects.isNull(AssetBillTypeEnums.getFromCode(assetType))?AssetBillTypeEnums.HOUSE.getSubjectType():AssetBillTypeEnums.getFromCode(assetType).getSubjectType();
        AssetTransactionDTO assetTransactionDTO =
                AssetTransactionDTO.builder().incomeId(income.getId()).money(totalMoney).goodsName(income.getGoodsName())
                        .assetOrderNum(orderNum).communityId(income.getCommunityId()).communityName(income.getCommunityName()).buildingId(assetInfo.getBuildingId())
                        .buildingName(assetInfo.getBuildingName()).unitId(assetInfo.getUnitId()).unitName(assetInfo.getUnitName())
                        .assetId(assetId).assetName(assetInfo.getSubName()).assetCode(assetInfo.getSubCode()).paymentMethod(income.getPaymentMethod())
                        .paymentChannel(income.getPaymentChannel()).paymentTerminal(income.getPaymentTerminal()).paymentTime(new Date())
                        .assetUseStatus(assetInfo.getAssetUseStatus())
                        .ownerId(ownerId).ownerName(ownerName).assetBillType(assetBillType).build();
        assetTransactionDTO.setCreateUser(request.getUserName());
        assetTransactionDTO.setModifyUser(request.getUserName());
        assetTransactionDTO.setCreateTime(DateUtils.getCurrentTimestamp());
        assetTransactionDTO = assetTransactionClient.create(assetTransactionDTO).getContent();
        log.info("{}|【朝昔】预存充值2.0，成功新增资产流水|{}", LogCategoryEnum.BUSSINESS, assetTransactionDTO);

        //创建流水信息表数据
        AssetBillInfoDTO assetBillInfoDO =
                AssetBillInfoDTO.builder().communityId(income.getCommunityId()).assetTransactionId(assetTransactionDTO.getId())
                        .orderNum(income.getOrderNum()).payMemberId(income.getPayMemberId()).payMember(income.getPayMember()).build();
        assetBillInfoDO.setCreateUser(request.getUserName());
        assetBillInfoDO.setModifyUser(request.getUserName());
        assetBillInfoClient.create(assetBillInfoDO);

        List<PrestoreChargeOrderInfo> preInfoList = new ArrayList<>();
        BigDecimal actualAmount = BigDecimal.ZERO;
        BigDecimal penaltyAmount = BigDecimal.ZERO;
        List<WriteOffBillDTO> writeOffList = new ArrayList<>();
        List<TransactionRelationDTO> relationList = new ArrayList<>();
        Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> recBillMap = new HashMap<>();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(recBillList)) {
            recBillMap =
                    recBillList.stream().collect(Collectors.groupingBy(com.charge.bill.dto.ReceivableBillDTO::getItemId));//升序
            log.info("{}|【朝昔】预存充值2.0，欠费信息map|{}", LogCategoryEnum.BUSSINESS, recBillMap);
        }
        if (recBillMap.isEmpty()) {
            preInfoList = request.getChargeOrderInfoList();
        } else {
            //缴欠费
            this.deduction(commonPrestoreList, specialPrestoreList, request.getUserName(), communityId, assetId,
                    genaralList, ownerId, income,
                    assetTransactionDTO, preInfoList, actualAmount, penaltyAmount, writeOffList, recBillMap);
        }

        log.info("预存：{}，抵扣费用：{}", preInfoList, writeOffList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(writeOffList)) {
            writeOffList = writeOffBillClient.batchCreate(writeOffList).getContent();
            log.info("{}|【朝昔】预存充值2.0，条数：{}，成功新增核销明细|{}", LogCategoryEnum.BUSSINESS, writeOffList.size(), writeOffList);
            for (WriteOffBillDTO w : writeOffList) {
                //添加关联表数据
                TransactionRelationDTO relationDTO =
                        TransactionRelationDTO.builder().communityId(income.getCommunityId())
                                .assetTransactionId(assetTransactionDTO.getId())
                                .businessId(w.getId())
                                .businessType(BusinessTypeEnum.NORMAL_PAY.getCode()).build();
                relationDTO.setCreateTime(income.getCreateTime());
                relationList.add(relationDTO);
            }

        }

        //组装充值数据
        List<PredepositBillDTO> predepositBillList = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(preInfoList)) {
            log.info("【预存缴费】生成预支付订单，order_num:{},总金额：{}，小区：{}，预存充值个数：{}", orderNum, actualAmount, communityId,
                    preInfoList.size());
            List<PointsSignCommunityDTO> communityPointsConfigList =
                    AppInterfaceUtil.getResponseData(pointsConfigClient.getCommunityConfig(
                            PointsConfigConditionDTO.builder().communityId(communityId).enableConfig(true).build()));
            List<Long> pointsEarnItemIdList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(communityPointsConfigList)) {
                pointsEarnItemIdList = communityPointsConfigList.get(0).getPointsEarnItemIdList();
            }
            for (PrestoreChargeOrderInfo preInfo : preInfoList) {
                if ((CollectionUtil.isEmpty(communityPointsConfigList) || Objects.equals(CommonChargeItem.ITEM_ID,
                        Long.valueOf(preInfo.getItemId())) || !pointsEarnItemIdList.contains(Long.parseLong(preInfo.getItemId())))
                        && !org.apache.commons.lang.StringUtils.isEmpty(preInfo.getPoints())
                        && new BigDecimal(preInfo.getPoints()).setScale(2, RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)) != 0) {
                    new ChargeResponse<>(YueConstants.CODE_FAILED, "预存赠万象星规则与系统不匹配，请重试");
                }
                if (pointsEarnItemIdList.contains(Long.parseLong(preInfo.getItemId())) && points > 0) {
                    this.prestoreEarnPoints(preInfo.getPoints(), Long.valueOf(request.getCommunityId()),
                            Long.valueOf(request.getHouseId()), preInfo, orderNum);
                }
                Integer predepositType = Objects.equals(CommonChargeItem.ITEM_ID, Long.valueOf(preInfo.getItemId()))
                        ? PredepositTypeEnum.COMMON_DEPOSIT.getCode() : PredepositTypeEnum.SPECIAL_DEPOSIT.getCode();

                String preBillNum =
                        billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.PREDEPOSIT_BILL.getCode()).getContent();
                String itemName = preInfo.getItemName();

                PredepositBillDTO preBill = PredepositBillDTO.builder().communityId(communityId).assetId(assetId)
                        .assetTransactionId(assetTransactionDTO.getId()).billNum(preBillNum).predepositItemId(Long.valueOf(preInfo.getItemId()))
                        .predepositMoney(new BigDecimal(preInfo.getMoney())).memo(income.getMemo()).predepositItemName(itemName)
                        .predepositType(predepositType).isBalance(IsBalanceEnum.YES.getCode())
                        .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode())
                        .userId(userId)
                        .build();
                preBill.setCreateTime(DateUtils.getCurrentTimestamp());
                preBill.setCreateUser(request.getUserName());
                preBill.setModifyUser(request.getUserName());
                predepositBillList.add(preBill);

            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(predepositBillList)) {
            predepositBillList =
                    predepositBillClient.batchCreate(BatchPredepositBillDTO.builder().predepositBillDTOList(predepositBillList).build()).getContent();
            log.info("{}|【朝昔】预存充值2.0，条数：{}，成功新增预收单明细|{}", LogCategoryEnum.BUSSINESS, predepositBillList.size(),
                    predepositBillList);
            for (PredepositBillDTO preBill : predepositBillList) {
                //添加关联表数据
                TransactionRelationDTO relationDTO =
                        TransactionRelationDTO.builder().assetTransactionId(assetTransactionDTO.getId()).businessId(preBill.getId())
                                .businessType(BusinessTypeEnum.PREDEPOSIT_PAY.getCode()).build();
                relationDTO.setCreateTime(income.getCreateTime());
                relationList.add(relationDTO);
            }
        }
        TraceContextUtil.setCommunityId(communityId);
        //创建关联表数据
        relationList = transactionRelationClient.batchCreate(relationList).getContent();
        log.info("{}|【朝昔】预存充值2.0，条数：{}，成功新增资产流水关联信息|{}", LogCategoryEnum.BUSSINESS, relationList.size(), relationList);
        PayInfoDTO payInfoDTO = PayOrderAdapter.responseParamConvert(payReturnDTO, income.getId());
        log.info("{}|【朝昔】预存充值2.0，返回下单信息|{}", LogCategoryEnum.BUSSINESS, payInfoDTO);
        return new ChargeResponse<>(payInfoDTO);
    }


    public PreStorePointsCalReq buildPreStorePointsCalReq(Long communityId, Long houseId,Long chargeItemId,BigDecimal amount){
        PreStorePointsCalReq preStorePointsCalReq=new PreStorePointsCalReq();
        preStorePointsCalReq.setCommunityId(communityId);
        preStorePointsCalReq.setAssetId(houseId);
        preStorePointsCalReq.setChargeItemAmount(new ChargeItemAmount(amount,chargeItemId,""));
        return preStorePointsCalReq;

    }


    private void prestoreEarnPoints(String pointsInput, Long communityId, Long houseId,
                                    PrestoreChargeOrderInfo preInfo, String orderNum) throws ChargeBusinessException {

        if (StringUtils.isNotEmpty(pointsInput)) {
            BigDecimal inputPoints = new BigDecimal(pointsInput).setScale(2, RoundingMode.HALF_UP);
            PreStorePointsVO preStorePoints = pointsService.calPreStorePoints(communityId, houseId, Long.parseLong(preInfo.getItemId()), new BigDecimal(preInfo.getMoney()));
            BigDecimal pointsBigDecimal = preStorePoints.getPointSAmount();
            if (inputPoints.compareTo(pointsBigDecimal)!=0  ) {
                log.warn("prestoreEarnPoints point not equal,preInfo {},preStorePoints {}",preInfo,preStorePoints);
                throw new ChargeBusinessException(ErrorInfoEnum.E1013.getCode(), "预存赠万象星规则更新，请重试");
            }
        }
    }

    private void deduction(List<PrestoreChargeOrderInfo> commonPrestore,
                           List<PrestoreChargeOrderInfo> specialPrestore, String userName, Long comId, Long assetId,
                           List<Long> genaralList, Long ownerId, IncomeBillDTO income, AssetTransactionDTO asTr,
                           List<PrestoreChargeOrderInfo> preInfoList, BigDecimal actualAmount,
                           BigDecimal penaltyAmount, List<WriteOffBillDTO> writeOffList, Map<Long,
            List<com.charge.bill.dto.ReceivableBillDTO>> recBillMap) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(specialPrestore)) {
            //组装缴费明细数据
            for (PrestoreChargeOrderInfo specPre : specialPrestore) {
                String itemId = specPre.getItemId();
                String itemName = specPre.getItemName();
                BigDecimal price = new BigDecimal(specPre.getMoney());
                List<com.charge.bill.dto.ReceivableBillDTO> orderList = recBillMap.get(Long.valueOf(itemId));
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderList)) {
                    orderList.sort(Comparator.comparing(com.charge.bill.dto.ReceivableBillDTO::getBelongYears));
                    log.info("【预存缴费】生成预支付订单--专项预存抵扣：{}，item_name:{}查询待预存冲抵的欠费{}", comId, itemName, orderList);

                    Iterator<com.charge.bill.dto.ReceivableBillDTO> iterator = orderList.iterator();
                    while (iterator.hasNext()) {
                        com.charge.bill.dto.ReceivableBillDTO recBill = iterator.next();
                        if (price.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        }
                        if (BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount()) < 0 && (price.compareTo(BigDecimal.ZERO) > 0 && price.compareTo(recBill.getArrearsAmount().add(recBill.getPenaltyArrearsAmount())) >= 0)) {
                            log.info("【预存缴费】生成预支付订单---专项预存抵扣：{}，item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}",
                                    comId, itemName, price, recBill.getItemId(), recBill);
                            actualAmount = actualAmount.add(recBill.getArrearsAmount());
                            penaltyAmount = penaltyAmount.add(recBill.getPenaltyArrearsAmount());
                            price = price.subtract(recBill.getArrearsAmount()).subtract(recBill.getPenaltyArrearsAmount());
                            //新增核销单
                            this.addWriteOffBills(userName, assetId, ownerId, income, asTr, writeOffList, itemName,
                                    recBill, Long.valueOf(itemId));
                            iterator.remove();
                        } else if (price.compareTo(BigDecimal.ZERO) > 0 && BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount()) == 0) {
                            log.info("【预存缴费】生成预支付订单---专项预存抵扣：{}，item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}",
                                    comId, itemName, price, recBill.getItemId(), recBill);
                            BigDecimal arrears = recBill.getArrearsAmount();
                            BigDecimal deductMoney = price.compareTo(arrears) < 0 ? price : arrears;
                            actualAmount = actualAmount.add(deductMoney);
                            price = price.subtract(deductMoney);
                            recBill.setArrearsAmount(deductMoney);
                            //新增核销单
                            this.addWriteOffBills(userName, assetId, ownerId, income, asTr, writeOffList, itemName,
                                    recBill, Long.valueOf(itemId));
                            if (arrears.compareTo(deductMoney) > 0) {
                                recBill.setArrearsAmount(arrears.subtract(deductMoney));
                            } else {
                                iterator.remove();
                            }
                        }

                    }

                }
                if (price.compareTo(BigDecimal.ZERO) > 0) {
                    PrestoreChargeOrderInfo newPre = new PrestoreChargeOrderInfo();
                    newPre.setMoney(price.toPlainString());
                    newPre.setItemId(itemId);
                    newPre.setItemName(itemName);
                    newPre.setPoints(specPre.getPoints());
                    preInfoList.add(newPre);
                    log.info("【预存缴费】生成预支付订单-专项预存抵扣：{}，item_name:{}price:{}剩余预存进行存储：{}", comId, itemName, price,
                            preInfoList);
                }
            }
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(commonPrestore)) {
            PrestoreChargeOrderInfo orderInfo = commonPrestore.get(0);
            BigDecimal commonAmount = new BigDecimal(orderInfo.getMoney());
            for (Long itemId : genaralList) {
                //获取指定收费项并按时间排序的欠费数据
                List<com.charge.bill.dto.ReceivableBillDTO> orderList = recBillMap.get(itemId);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderList)) {
                    orderList.sort(Comparator.comparing(com.charge.bill.dto.ReceivableBillDTO::getBelongYears));
                    for (com.charge.bill.dto.ReceivableBillDTO recBill : orderList) {
                        if (BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount()) < 0 && (commonAmount.compareTo(BigDecimal.ZERO) > 0 && commonAmount.compareTo(recBill.getArrearsAmount().add(recBill.getPenaltyArrearsAmount())) >= 0)) {
                            log.info("【预存缴费】生成预支付订单---通用预存抵扣：{}，item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}",
                                    comId, recBill.getItemName(), commonAmount, recBill.getItemId(), recBill);
                            actualAmount = actualAmount.add(recBill.getArrearsAmount());
                            penaltyAmount = penaltyAmount.add(recBill.getPenaltyArrearsAmount());
                            commonAmount =
                                    commonAmount.subtract(recBill.getArrearsAmount()).subtract(recBill.getPenaltyArrearsAmount());
                            //新增核销单
                            this.addWriteOffBills(userName, assetId, ownerId, income, asTr, writeOffList,
                                    recBill.getItemName(), recBill, itemId);
                        } else if (commonAmount.compareTo(BigDecimal.ZERO) > 0 && BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount()) == 0) {
                            log.info("【预存缴费】生成预支付订单---通用预存抵扣：{}，item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}",
                                    comId, recBill.getItemName(), commonAmount, recBill.getItemId(), recBill);
                            BigDecimal arrears = recBill.getArrearsAmount();
                            BigDecimal deductMoney = commonAmount.compareTo(arrears) < 0 ? commonAmount : arrears;
                            actualAmount = actualAmount.add(deductMoney);
                            commonAmount = commonAmount.subtract(deductMoney);
                            recBill.setArrearsAmount(deductMoney);
                            //新增核销单
                            this.addWriteOffBills(userName, assetId, ownerId, income, asTr, writeOffList,
                                    recBill.getItemName(), recBill, itemId);
                        } else {
                            break;
                        }
                    }
                }
            }

            if (commonAmount.compareTo(BigDecimal.ZERO) > 0) {
                log.info("【预存缴费】生成预支付订单-通用预存抵扣：{}，item_name:{}price:{}剩余预存进行存储", comId, orderInfo.getItemName(),
                        commonAmount);
                PrestoreChargeOrderInfo newPre = new PrestoreChargeOrderInfo();
                newPre.setMoney(commonAmount.toPlainString());
                newPre.setItemId(String.valueOf(CommonChargeItem.ITEM_ID));
                newPre.setItemName(PayRelatedConstants.GOODSNAME_COMMON_PRESTORE);
                newPre.setPoints("0");
                preInfoList.add(newPre);
            }

        }
    }

    private void addWriteOffBills(String userName, Long assetId, Long ownerId, IncomeBillDTO income, AssetTransactionDTO asTr, List<WriteOffBillDTO> writeOffList, String itemName, com.charge.bill.dto.ReceivableBillDTO recBill, Long aLong) {
        if (BigDecimal.ZERO.compareTo(recBill.getArrearsAmount()) < 0) {
            String writeBillNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.WRITE_OFF_BILL.getCode()).getContent();
            WriteOffBillDTO writeOff =
                    WriteOffBillDTO.builder().communityId(income.getCommunityId()).assetId(assetId).userId(ownerId)
                            .assetTransactionId(asTr.getId()).receivableBillId(recBill.getId()).billNum(writeBillNum).itemId(aLong)
                            .itemName(itemName).memo(recBill.getMemo()).actualAmount(recBill.getArrearsAmount()).chargeType(ChargeTypeEnum.PRINCIPAL.getCode())
                            .writeOffType(WriteOffTypeEnum.PAY.getCode()).belongYears(recBill.getBelongYears()).isBalance(IsBalanceEnum.YES.getCode())
                            .chargeObject(recBill.getChargeObject()).build();
            writeOff.setCreateUser(userName);
            writeOff.setModifyUser(userName);
            writeOff.setCreateTime(income.getCreateTime());
            writeOff.setAssetUseStatus(recBill.getAssetUseStatus());
            writeOffList.add(writeOff);
        }
        if (BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount()) < 0) {
            String writeBillNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.WRITE_OFF_BILL.getCode()).getContent();
            WriteOffBillDTO writeOff = WriteOffBillDTO.builder().communityId(income.getCommunityId()).assetId(assetId).userId(ownerId)
                    .assetTransactionId(asTr.getId()).receivableBillId(recBill.getId()).billNum(writeBillNum).itemId(aLong)
                    .itemName(itemName).memo(recBill.getMemo()).actualAmount(recBill.getPenaltyArrearsAmount()).chargeType(ChargeTypeEnum.PENALTY.getCode())
                    .writeOffType(WriteOffTypeEnum.PAY.getCode()).belongYears(recBill.getBelongYears()).isBalance(IsBalanceEnum.YES.getCode())
                    .chargeObject(recBill.getChargeObject()).build();
            writeOff.setCreateUser(userName);
            writeOff.setModifyUser(userName);
            writeOff.setCreateTime(income.getCreateTime());
            writeOff.setAssetUseStatus(recBill.getAssetUseStatus());
            writeOffList.add(writeOff);
        }
    }

    private void checkAddPrestoreInfo(PrestoreOrderRequest request, List<PrestoreChargeOrderInfo> commonPrestoreList) throws ChargeBusinessException {
        // 校验资产是否在托收中
        if (bankCollectionCheckSupport.bankCollectingByAssetId(Long.valueOf(request.getHouseId()))) {
            throw new ChargeBusinessException(ErrorInfoEnum.E3042.getCode(), ErrorInfoEnum.E3042.getValue());
        }

        List<PrestoreChargeOrderInfo> chargeOrderInfoList = request.getChargeOrderInfoList();
        if (CollectionUtils.isEmpty(chargeOrderInfoList)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【预存缴费】充值无收费项，请重新输入");
        }
        if (StringUtils.isNotEmpty(request.getTotalPrice()) && request.getTotalPrice().length() > 11) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【预存缴费】充值金额超出数据库存储范围，请重新输入");
        }
        if (StringUtils.isEmpty(request.getTotalPrice()) || BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).compareTo(new BigDecimal(request.getTotalPrice()).setScale(2, RoundingMode.HALF_UP)) > 0) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【预存缴费】充值金额不符合要求，请重新输入");
        }
        if (StringUtils.isNotEmpty(request.getTotalPoints()) && (!StringUtils.isNumeric(request.getTotalPoints()) || request.getTotalPoints().length() > 11)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【预存缴费】充值赠星数超限制，请重新输入");
        }
        for (PrestoreChargeOrderInfo prestoreChargeOrderInfo : chargeOrderInfoList) {
            if (BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).compareTo(new BigDecimal(prestoreChargeOrderInfo.getMoney()).setScale(2, RoundingMode.HALF_UP)) > 0) {
                throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【预存缴费】充值金额不符合要求，请重新输入");
            }
            if (StringUtils.isNotEmpty(prestoreChargeOrderInfo.getPoints()) && (!StringUtils.isNumeric(prestoreChargeOrderInfo.getPoints()) || prestoreChargeOrderInfo.getPoints().length() > 11)) {
                throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【预存缴费】充值赠星数超限制，请重新输入");
            }
        }
        BigDecimal allMoney = chargeOrderInfoList.stream().map(item -> new BigDecimal(item.getMoney()).setScale(2,
                RoundingMode.HALF_UP)).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (allMoney.compareTo(new BigDecimal(request.getTotalPrice()).setScale(2, RoundingMode.HALF_UP)) != 0) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【预存缴费】充值金额明细不等于总和，请重新输入");
        }
        BigDecimal totalPointsNum = StringUtils.isEmpty(request.getTotalPoints()) ? BigDecimal.ZERO :
                new BigDecimal(request.getTotalPoints());
        BigDecimal allPoints = chargeOrderInfoList.stream()
                .map(item -> StringUtils.isEmpty(item.getPoints()) ? BigDecimal.ZERO : new BigDecimal(item.getPoints())
                        .setScale(2, RoundingMode.HALF_UP))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalPointsNum.compareTo(allPoints.setScale(2, RoundingMode.HALF_UP)) != 0) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【预存缴费】充值积分明细不等于总和，请重新输入");
        }

        // 进入2.0灰度小区
        BigDecimal totalMoney = new BigDecimal(request.getTotalPrice());
        BigDecimal amount = BigDecimal.ZERO;
        for (PrestoreChargeOrderInfo info : chargeOrderInfoList) {
            amount = amount.add(new BigDecimal(info.getMoney()));
        }
        if (totalMoney.compareTo(amount) != 0) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "总金额与预存明细金额不对应，请重试");
        }

        //校验通用预存项只能有一个
        if (CollectionUtils.isNotEmpty(commonPrestoreList) && commonPrestoreList.size() > 1) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【预存缴费】通用预存缴费项为多个，请重新输入");
        }
        if (CollectionUtils.isNotEmpty(commonPrestoreList) && commonPrestoreList.size() == 1
                && StringUtils.isNotEmpty(commonPrestoreList.get(0).getPoints()) &&
                new BigDecimal(commonPrestoreList.get(0).getPoints()).setScale(2, RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)) != 0) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【预存缴费】通用预存积分参数异常，请重新输入");
        }
    }

    @Override
    public List<AssetChargeStandVO> listAssetChargeStand(BatchZhaoXiAssetReq zhaoXiAssetReq) throws ChargeBusinessException {
        communityParamSupport.fillCommunityId(zhaoXiAssetReq);
        assetSupport.fillAssetIds(zhaoXiAssetReq.getAssetList(),zhaoXiAssetReq.getCommunityId(), false);
        List<Long> assetIds = zhaoXiAssetReq.getAssetList().stream().map(ZhaoXiAssertRequest::getAssetId).distinct().collect(Collectors.toList());
        ChargeResponse<List<AssetChargeConfigDetailDTO>> chargeResponse = assetsChargeConfigClient.queryAssetChargeConfigList(AssetChargeConfigQueryConditionDTO.builder()
                .communityId(Lists.newArrayList(zhaoXiAssetReq.getCommunityId())).assetIds(assetIds).status(AssetChargeConfigStatusEnum.EFFECTIVE.getCode()).build());
        List<AssetChargeConfigDetailDTO> assetChargeConfigDetails = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        if(CollectionUtils.isEmpty(assetChargeConfigDetails)){
            return Lists.newArrayList();
        }
        Map<Long, List<AssetChargeConfigDetailDTO>> assetStandardMap = assetChargeConfigDetails.stream().collect(Collectors.groupingBy(AssetChargeConfigDetailDTO::getAssetId));
        return zhaoXiAssetReq.getAssetList().stream().filter(a->assetStandardMap.containsKey(a.getAssetId())).map(asset->{
            List<AssetChargeConfigDetailDTO> assetChargeConfigDetailList = assetStandardMap.get(asset.getAssetId());
            List<ChargeStandardVO> chargeStandards = assetChargeConfigDetailList.stream().map(a -> new ChargeStandardVO(a.getId(), a.getStandardConfigName())).collect(Collectors.toList());
            AssetChargeStandVO assetChargeStandVO=new AssetChargeStandVO();
            assetChargeStandVO.setAssetMsId(asset.getAssetMsId());
            assetChargeStandVO.setAssetId(asset.getAssetId());
            assetChargeStandVO.setAssetType(asset.getAssetType());
            assetChargeStandVO.setChargeStandards(chargeStandards);
            return assetChargeStandVO;
        }).collect(Collectors.toList());
    }

    @Override
    public BatchCalculatePreStoreVO batchCalculatePreStoreAmount(BatchZhaoXiAssetReq req) throws ChargeBusinessException {
        //转换和校验
        BatchCalculatePreStoreVO batchCalculatePreStoreVO=new BatchCalculatePreStoreVO();
        communityParamSupport.fillCommunityId(req);
        Long communityId = req.getCommunityId();
        batchCalculatePreStoreVO.setCommunityId(communityId);
        batchCalculatePreStoreVO.setCommunityMsId(req.getCommunityMsId());
        Map<Long, AssetAdapter> assetAdapterMap = assetSupport.fillAssetIds(req.getAssetList(),req.getCommunityId(), false);
        boolean communityMerchantConfigured = checkCommunityMerchantConfigured(req.getCommunityId());
        batchCalculatePreStoreVO.setDisableStatus(communityMerchantConfigured?0:1);
        PreStoreItemConfigDTO preStoreChargeItem = getPreStoreChargeItem(req.getCommunityId());
        boolean enablePreStore=preStoreChargeItem!=null;
        batchCalculatePreStoreVO.setPrestoreChargeStatus(enablePreStore?1:0);
        if((!enablePreStore)|| (!communityMerchantConfigured)){
            return batchCalculatePreStoreVO;
        }

        //准备批处理的数据
        List<Long> specialPreStoreItems = preStoreChargeItem.getSpecialPreStoreItems();
        List<Long> assetIds = req.getAssetList().stream().map(ZhaoXiAssertRequest::getAssetId).distinct().collect(Collectors.toList());

        ZXSpecialPreStoreQueryConditionDTO condition = new ZXSpecialPreStoreQueryConditionDTO();
        condition.setCommunityId(communityId);
        condition.setAssetIds(assetIds);
        condition.setCheckDate(true);
        ChargeResponse<List<ItemInfo>> checkDateItemInfoResponse = communityPreStoreItemClient.listSpecialItemForZX(condition);
        List<ItemInfo> checkDateItemInfos = AppInterfaceUtil.getDataThrowException(checkDateItemInfoResponse);

        Map<Long, List<ItemInfo>> assetIdToItemInfoMap = checkDateItemInfos.stream()
                .filter(t -> specialPreStoreItems.contains(t.getItemId()))
                .collect(Collectors.groupingBy(
                        ItemInfo::getAssetId)
                );

        //准备项目积分配置
        PointsSignCommunityDTO pointsSignCommunity = getPointsSignCommunity(communityId);
        //准备资产费项每月预存
        Map<Long,List<ReceivableBillDTO>> assetPreStorMap = feeCalculateSupport.batchCalculatePreStorePerMonth(req.getCommunityId(), assetIds, specialPreStoreItems).stream()
                .collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId));

        Map<Long, Map<Long,String>> assetBindItemsMap = feeCalculateSupport.getAssetBindItemsMap(req.getCommunityId(), assetIds, specialPreStoreItems);
        //准备资产费项欠费
        Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> assetReceivableBillMap =
                receivableSupport.listReceivableBills(req.getCommunityId(), assetIds, specialPreStoreItems,
                                Lists.newArrayList(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode(),
                                        ChargeObjEnum.CHARGE_OBJ_DEVELOPER.getCode()), DateUtils.format(new Date(), DateUtils.FORMAT_14))
                .stream().collect(Collectors.groupingBy(com.charge.bill.dto.ReceivableBillDTO::getAssetId));
        //准备资产预收账户配置
        Map<Long, List<PredepositAccountDTO>> preStoreAccountMap = getPreStoreAccountMap(communityId, assetIds, specialPreStoreItems);
        // 项目组合预存配置
        List<CommunityPrestoreGroupDetailDTO> preStoreGroups = getPreStoreGroups(communityId);
        batchCalculatePreStoreVO.setPrestoreAssetChargeInfos(new ArrayList<>(assetIds.size()));
        for (ZhaoXiAssertRequest assertRequest:req.getAssetList()){
            Long assetId = assertRequest.getAssetId();
            AssetPreStoreV0 assetPreStoreV0=AssetPreStoreV0.builder()
                    .assetType(assertRequest.getAssetType())
                    .houseMsId(assertRequest.getAssetMsId())
                    .houseId(assetId)
                    .chargeObjectType(assetAdapterMap.get(assetId).getChargeObject())
                    .build();
           if (CollectionUtils.isNotEmpty(assetIdToItemInfoMap.get(assetId)) ){
               Integer chargeObjectType = Objects.nonNull(assetAdapterMap.get(assetId).getChargeObject()) ? assetAdapterMap.get(assetId).getChargeObject() : ChargeObjEnum.CHARGE_OBJ_OWNER.getCode();

               List<Long> checkDateItemIds = assetIdToItemInfoMap.get(assetId).stream().map(t -> t.getItemId()).distinct().collect(Collectors.toList());
               Map<Long, String> bindItemIds = assetBindItemsMap.getOrDefault(assetId, new HashMap<>()).entrySet().stream().filter(entry -> checkDateItemIds.contains(entry.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

               List<CommunityPrestoreGroupDetailDTO> effectPreStoreGroups = preStoreGroups.stream().filter(a ->
                       bindItemIds.keySet().containsAll(a.getPrestoreItemList().stream().map(CommunitySpecialPrestoreGroupItemDTO::getItemId).collect(Collectors.toSet()))).collect(Collectors.toList());
               Set<Long> effectPreStoreGroupItemIds = effectPreStoreGroups.stream().flatMap(a -> a.getPrestoreItemList().stream().map(CommunitySpecialPrestoreGroupItemDTO::getItemId)).collect(Collectors.toSet());
               List<PrestoreVO> allPrestoreList = PrestoreVO.of(bindItemIds,assetPreStorMap.get(assetId), assetReceivableBillMap.get(assetId), preStoreAccountMap.get(assetId), pointsSignCommunity, chargeObjectType);
               List<PrestoreVO> specialPrestoreList=allPrestoreList.stream().filter(a->!effectPreStoreGroupItemIds.contains(a.getItemId())).collect(Collectors.toList());
               List<GroupPrestoreVO> groupPrestoreList=effectPreStoreGroups.stream().map(preStoreGroup-> GroupPrestoreVO.build(allPrestoreList,preStoreGroup)).filter(Objects::nonNull).collect(Collectors.toList());

               assetPreStoreV0.setSpecialPrestoreList(specialPrestoreList);
               assetPreStoreV0.setGroupPrestoreChargeItemVOList(groupPrestoreList);
           }
            batchCalculatePreStoreVO.getPrestoreAssetChargeInfos().add(assetPreStoreV0);
        }
        return batchCalculatePreStoreVO;

    }

    private Map<Long, List<PredepositAccountDTO>> getPreStoreAccountMap(Long communityId, List<Long> assetIds, List<Long> chargeItemIds) throws ChargeBusinessException {
        ChargeResponse<List<PredepositAccountDTO>> response = predepositAccountClient.listPredepositAccount(
                PredepositAccountConditionDTO.builder()
                        .communityId(communityId)
                        .assetIds(assetIds)
                        .billType(PredepositBillTypeEnum.PRE_DEPOSIT.getCode())
                        .chargeObjects(Lists.newArrayList(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode(), ChargeObjEnum.CHARGE_OBJ_DEVELOPER.getCode()))
                        .itemIds(chargeItemIds)
                        .build());
        List<PredepositAccountDTO> responseDataThrowException = AppInterfaceUtil.getResponseDataThrowException(response);
        if (org.springframework.util.CollectionUtils.isEmpty(responseDataThrowException)) {
            return Maps.newHashMap();
        } else {
            return responseDataThrowException.stream().collect(Collectors.groupingBy(PredepositAccountDTO::getAssetId));
        }
    }

    private PointsSignCommunityDTO getPointsSignCommunity(Long communityId) throws ChargeBusinessException {
        ChargeResponse<List<PointsSignCommunityDTO>> response = pointsConfigClient.getCommunityConfig(
                PointsConfigConditionDTO.builder().communityId(communityId).enableConfig(true).build());
        List<PointsSignCommunityDTO> communityPointsConfigList = AppInterfaceUtil.getResponseDataThrowException(response);
        if (CollectionUtil.isEmpty(communityPointsConfigList)) {
            return null;
        }
        return communityPointsConfigList.get(0);
    }


    private boolean checkCommunityMerchantConfigured(Long communityId) throws ChargeBusinessException {
        MerchantConditionDTO merDTO = new MerchantConditionDTO();
        merDTO.setCommunityId(communityId);
        merDTO.setPaymentSource(SOURCE_YUEHOME_PAY);
        ChargeResponse<CommunityMerchantDTO> response = communityMerchantClient.getMerchant(merDTO);
        CommunityMerchantDTO merchantDTO = AppInterfaceUtil.getResponseDataThrowException(response);
        return merchantDTO!=null;
    }

    private PreStoreItemConfigDTO getPreStoreChargeItem(Long communityId) throws ChargeBusinessException {
        PreStoreItemConfigQueryConditionDTO query = new PreStoreItemConfigQueryConditionDTO();
        query.setCommunityIds(Lists.newArrayList(communityId));

        ChargeResponse<List<PreStoreItemConfigDTO>> chargeResponse = communityPreStoreItemClient.getPreStoreItemConfig(query);
        List<PreStoreItemConfigDTO> preStoreItemConfigs = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        return org.springframework.util.CollectionUtils.isEmpty(preStoreItemConfigs)?null:preStoreItemConfigs.get(0);
    }

    private void populateResultVO(BatchAssetOrderCheckResultVO resultVO, Long communityId, List<Long> assetIds) {
        resultVO.setCommunityId(communityId);
        resultVO.setAssetIds(assetIds);
        resultVO.setDisableStatus(PayRelatedConstants.COMMUNITY_PAY_ENABLE);
        resultVO.setPrestoreChargeStatus(PayRelatedConstants.PRESTORE_PAY_ENABLE);
        resultVO.setMessage("");
    }

    /**
     * 获取专项预存项月度金额
     * @param houseId
     * @param comId
     * @param itemInfoList
     * @param communityPointsConfigList
     * @return
     */
    private List<PrestoreChargeInfoVO> getSpecialChargeInfo(Long houseId, Long comId, List<ItemInfo> itemInfoList,  List<PointsSignCommunityDTO>  communityPointsConfigList) throws ChargeBusinessException {
        List<Long> pointsEarnItemIdList = extractPointsEarnItemIdList(communityPointsConfigList);
        log.info("[预存缴费查询]，可积分赠分的itemId:{}", pointsEarnItemIdList);

        List<PrestoreChargeInfoVO> list = new ArrayList<>();
        for (ItemInfo itemInfo : itemInfoList) {
            PrestoreChargeInfoVO prestoreChargeInfoVO = processSingleItem(houseId, comId, itemInfo, pointsEarnItemIdList);
            list.add(prestoreChargeInfoVO);
        }
        return list;
    }

    private List<Long> extractPointsEarnItemIdList(List<PointsSignCommunityDTO> communityPointsConfigList) {
        if (CollectionUtil.isNotEmpty(communityPointsConfigList)) {
            return communityPointsConfigList.get(0).getPointsEarnItemIdList();
        }
        return new ArrayList<>();
    }

    private PrestoreChargeInfoVO processSingleItem(Long houseId, Long comId, ItemInfo itemInfo, List<Long> pointsEarnItemIdList) throws ChargeBusinessException {
        Long itemId = itemInfo.getItemId();
        log.info("[预存缴费查询]，专项预存itemId:{}", itemId);

        ReceivableAmountTotalDTO amountTotalDTO = fetchReceivableAmountTotalDTO(comId, houseId, itemId);
        PredepositAccountDTO preAccDTO = fetchPredepositAccountDTO(comId, houseId, itemId);

        ReceivableBillDTO reDTO = feeCalculateSupport.calculatePreStorePerMonth(comId, houseId, itemId);
        log.info("{}|V2计算月度金额，参数：{}，金额结果：{}", LogCategoryEnum.BUSSINESS, reDTO);

        BigDecimal perMonthMoney = calculatePerMonthMoney(reDTO);
        String info = generateInfo(reDTO, itemInfo);

        return PrestoreChargeInfoVO.fromSpecialPrestore(amountTotalDTO, preAccDTO, perMonthMoney, info, itemInfo, pointsEarnItemIdList.contains(itemId), getPointConfig());
    }

    private ReceivableAmountTotalDTO fetchReceivableAmountTotalDTO(Long comId, Long houseId, Long itemId) {
        List<Integer> payStatusList = Lists.newArrayList(ReceivableBillPayStatusEnum.NOT_PAY.getCode(), ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode(), ReceivableBillPayStatusEnum.PAY_WAIT.getCode());

        ChargeResponse<ReceivableAmountTotalDTO> totalResponse = receivableBillClient.amountTotal(
                ReceivableConditionDTO.builder()
                        .communityId(comId)
                        .assetId(houseId)
                        .itemId(itemId)
                        .payStatuses(payStatusList)
                        .billStatus(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode())
                        .chargeObject(ChargeObjTypeEnum.OWNER.getCode())
                        .build()
        );

        if (totalResponse != null && totalResponse.getContent() != null) {
            return totalResponse.getContent();
        }
        return null;
    }

    private PredepositAccountDTO fetchPredepositAccountDTO(Long comId, Long houseId, Long itemId) {
        ChargeResponse<List<PredepositAccountDTO>> specListResult = predepositAccountClient.listPredepositAccount(
                PredepositAccountConditionDTO.builder()
                        .communityId(comId)
                        .assetIds(Lists.newArrayList(houseId))
                        .billType(PredepositBillTypeEnum.PRE_DEPOSIT.getCode())
                        .chargeObj(com.charge.api.web.constants.PayRelatedConstants.PREDEPOSIT_CHARGE_OBJ_OWNER)
                        .itemId(itemId)
                        .build()
        );

        if (specListResult != null && specListResult.getContent() != null && !specListResult.getContent().isEmpty()) {
            return specListResult.getContent().get(0);
        }
        return null;
    }

    private BigDecimal calculatePerMonthMoney(ReceivableBillDTO reDTO) {
        if (reDTO != null) {
            return reDTO.getReceivableAmount();
        }
        return BigDecimal.ZERO;
    }

    private String generateInfo(ReceivableBillDTO reDTO, ItemInfo itemInfo) {
        if (reDTO != null) {
            if (reDTO.getChargeArea() != null && BigDecimal.ZERO.compareTo(reDTO.getChargeArea()) < 0) {
                return "每月" + itemInfo.getItemName() + " " + reDTO.getStandardStr() + "*" + reDTO.getChargeArea() + "平米 = " + reDTO.getReceivableAmount() + "元";
            } else if (reDTO.getChargeArea() == null || BigDecimal.ZERO.compareTo(reDTO.getChargeArea()) == 0) {
                return "每月" + itemInfo.getItemName() + " " + reDTO.getReceivableAmount() + "元";
            }
        }
        return "";
    }

    /**
     * 返回样例{"ratio":"2","points":"25000","factor":"2"}
     */
    private  String getPointConfig() throws ChargeBusinessException {
        try {
            return pointCongihCache.get(LocalDate.now(),()->{
                List<PointsConfigValueDTO> pointsConfigs = AppInterfaceUtil.getResponseDataThrowException(pointsConfigClient.getOnGoingConfig());
                if(!org.springframework.util.CollectionUtils.isEmpty(pointsConfigs)){
                    pointsConfigs.sort(Comparator.comparingInt(PointsConfigValueDTO::getMonthFrom));
                    PointsConfigValueDTO pointsConfigValueDTO = pointsConfigs.get(0);
                    return new JSONObject().fluentPut("ratio", pointsConfigValueDTO.getRatio()).fluentPut("points", pointsConfigValueDTO.getPoints()).fluentPut("factor", pointsConfigValueDTO.getMonthFrom())
                            .toJSONString();
                }
                return "";
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    private List<PrestoreChargeInfoVO> getPrestoreInfoAllSpecialNew(Long communityId, Long houseId) throws ChargeBusinessException {
        log.info("【专项预存组合】查询小区预存信息:communityId={}", communityId);

        List<PrestoreChargeInfoVO> result = new ArrayList<>();

        List<SpecialPreStoreItemDTO> itemList = fetchSpecialPrestoreItems(communityId);
        List<ItemInfo> itemInfos = BeanCopierWrapper.copy(itemList, ItemInfo.class);

        Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> arrearsListMap = fetchArrearsListMap(houseId);

        List<PointsSignCommunityDTO> communityPointsConfigList = fetchCommunityPointsConfig(communityId);

        processPrestoreItems(communityId, houseId, itemList, itemInfos, arrearsListMap, communityPointsConfigList, result);

        return result;
    }

    private List<SpecialPreStoreItemDTO> fetchSpecialPrestoreItems(Long communityId) {
        log.info("【专项预存组合】查询小区专项预存信息:communityId={}", communityId);
        List<SpecialPreStoreItemDTO> itemList = this.getPrestoreItemList(communityId);
        log.info("【专项预存组合】查询小区专项预存信息:communityId={}，个数：{}", communityId, itemList.size());
        return itemList;
    }

    private Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> fetchArrearsListMap(Long houseId) throws ChargeBusinessException {
        log.info("【专项预存组合】查询小区专项预存信息：获取该房间{}的欠费", houseId);

        ReceivableConditionDTO receivableConditionDTO = ReceivableConditionDTO.builder()
                .assetId(houseId)
                .payStatuses(Lists.newArrayList(ReceivableBillPayStatusEnum.NOT_PAY.getCode(), ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                .billStatuses(Lists.newArrayList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode()))
                .build();

        ChargeResponse<List<com.charge.bill.dto.ReceivableBillDTO>> receivableResponse = receivableBillClient.listReceivableBillGroupByItem(receivableConditionDTO);

        if (!receivableResponse.isSuccess() || receivableResponse.getContent() == null) {
            log.error("【专项预存组合】查询小区专项预存信息：获取该房间{}的欠费,欠费个数为空，或异常", houseId, receivableResponse.getMessage());
            return new HashMap<>();
        }

        List<com.charge.bill.dto.ReceivableBillDTO> arrearsList = receivableResponse.getContent();
        log.info("【专项预存组合】查询小区专项预存信息：获取该房间{}的欠费,欠费项个数为{}", houseId, arrearsList.size());

        return arrearsList.stream().collect(Collectors.groupingBy(com.charge.bill.dto.ReceivableBillDTO::getItemId));
    }

    private List<PointsSignCommunityDTO> fetchCommunityPointsConfig(Long communityId)  throws ChargeBusinessException {
        log.info("{}|V2计算月度费用查询房间专项预存费项积分相关，community+id：{}", LogCategoryEnum.BUSSINESS, communityId);
        return AppInterfaceUtil.getResponseData(pointsConfigClient.getCommunityConfig(
                PointsConfigConditionDTO.builder().communityId(communityId).enableConfig(true).build()));
    }

    private void processPrestoreItems(Long communityId, Long houseId, List<SpecialPreStoreItemDTO> itemList, List<ItemInfo> itemInfos, Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> arrearsListMap, List<PointsSignCommunityDTO> communityPointsConfigList, List<PrestoreChargeInfoVO> result) {
        for (int i = 0; i < itemList.size(); i++) {
            SpecialPreStoreItemDTO prestoreItem = itemList.get(i);
            try {
                processSinglePrestoreItem(communityId, houseId, prestoreItem, itemInfos.get(i), arrearsListMap, communityPointsConfigList, result);
            } catch (Exception e) {
                log.error("【专项预存组合】查询小区houseId:{},item_id:{},预存信息出错：{}", houseId, prestoreItem.getItemId(), e.getMessage());
            }
        }
    }

    private void processSinglePrestoreItem(Long communityId, Long houseId, SpecialPreStoreItemDTO prestoreItem, ItemInfo itemInfo, Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> arrearsListMap, List<PointsSignCommunityDTO> communityPointsConfigList, List<PrestoreChargeInfoVO> result) throws ChargeBusinessException {
        log.info("【专项预存组合】查询小区houseId:{},item_id:{},计费配置", houseId, prestoreItem.getItemId());

        List<AssetChargeConfigDetailDTO> configDetails = fetchAssetChargeConfigDetails(communityId, houseId, prestoreItem);
        if (CollectionUtil.isEmpty(configDetails)) {
            return;
        }

        Map<String, BigDecimal> prestoreAccountInfo = getPrestoreAccountInfo(communityId, houseId, prestoreItem.getItemId());
        List<com.charge.bill.dto.ReceivableBillDTO> arrearsList = arrearsListMap.get(prestoreItem.getItemId());

        BigDecimal arrearsMoney = calculateArrearsMoney(arrearsList);

        List<PrestoreChargeInfoVO> prestoreDetailVOList = getSpecialChargeInfo(houseId, communityId, Lists.newArrayList(itemInfo), communityPointsConfigList);

        BigDecimal money1Month = CollectionUtil.isNotEmpty(prestoreDetailVOList) ? prestoreDetailVOList.get(0).getChargeMoneyPerMonth() : null;
        String info = CollectionUtil.isNotEmpty(prestoreDetailVOList) ? prestoreDetailVOList.get(0).getChargeInfo() : null;

        result.add(PrestoreChargeInfoVO.fromSpecialPrestoreForV2(prestoreAccountInfo, prestoreItem, money1Month, arrearsMoney, info, communityPointsConfigList, getPointConfig()));
    }

    private List<AssetChargeConfigDetailDTO> fetchAssetChargeConfigDetails(Long communityId, Long houseId, SpecialPreStoreItemDTO prestoreItem) throws ChargeBusinessException {
        AssetChargeConfigQueryConditionDTO conditionDTO = AssetChargeConfigQueryConditionDTO.builder()
                .assetId(houseId)
                .communityId(Lists.newArrayList(communityId))
                .itemIds(Lists.newArrayList(prestoreItem.getItemId()))
                .status(AssetChargeConfigStatusEnum.EFFECTIVE.getCode())
                .queryStandardDetailFlag(false)
                .build();

        ChargeResponse<List<AssetChargeConfigDetailDTO>> response = assetsChargeConfigClient.queryAssetChargeConfigList(conditionDTO);
        if (!response.isSuccess() || response.getContent() == null || CollectionUtil.isEmpty(response.getContent())) {
            log.error("【专项预存组合】按条件查询资产计费配置信息异常，小区houseId:{},item_id:{},计费配置响应信息：{}", houseId, prestoreItem.getItemId(), response);
            return Collections.emptyList();
        }

        log.info("【专项预存组合】查询小区houseId:{},item_id:{},计费配置个数：{}", houseId, prestoreItem.getItemId(), response.getContent().size());
        return response.getContent();
    }

    private BigDecimal calculateArrearsMoney(List<com.charge.bill.dto.ReceivableBillDTO> arrearsList) {
        if (CollectionUtils.isEmpty(arrearsList)) {
            return BigDecimal.ZERO;
        }

        return arrearsList.stream()
                .map(item -> item.getArrearsAmount().add(item.getPenaltyArrearsAmount()))
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
    }

    private List<SpecialPreStoreItemDTO> getPrestoreItemList(Long communityId) {
        log.info("【专项预存组合】查询小区预存信息:communityId={}", communityId);
        List<SpecialPreStoreItemDTO> itemList = new ArrayList<>();
        ChargeResponse<List<SpecialPreStoreItemDTO>> resultResponse = communityPreStoreItemClient.getSpecialPreStoreItemByCommunityId(communityId);

        if (!resultResponse.isSuccess() || resultResponse.getContent() == null) {
            log.info("{}|【专项预存组合】查询小区专项预存信息异常:communityId={}, 异常原因：{}", LogCategoryEnum.BUSSINESS, communityId, resultResponse.getMessage());
        } else {
            itemList = resultResponse.getContent();
        }
        if (itemList.size() == 0) {
            log.info("{}|【专项预存组合】查询小区预存信息，小区无开启预存信息:communityId={}，, 异常原因：{}", LogCategoryEnum.BUSSINESS, communityId, resultResponse.getMessage());
        }
        return itemList;
    }

    /**
     * 获取专项预存账户相关余额数据
     *
     * @param houseId
     * @param itemId
     * @return
     */
    private Map<String, BigDecimal> getPrestoreAccountInfo(Long communityId, Long houseId, Long itemId) {
        Map<String, BigDecimal> result = new HashMap<>();
        BigDecimal refundingMoney = BigDecimal.ZERO;
        BigDecimal totalMoney = BigDecimal.ZERO;
        BigDecimal freezeMoney = BigDecimal.ZERO;
        BigDecimal availableMoney = BigDecimal.ZERO;
        PredepositAccountConditionDTO predepositAccountConditionDTO = PredepositAccountConditionDTO.builder().communityId(communityId)
                .assetIds(Lists.newArrayList(houseId))
                .itemId(itemId)
                .predepositType(PredepositTypeEnum.SPECIAL_DEPOSIT.getCode())
                .build();
        ChargeResponse<List<PredepositAccountDTO>> preDepositAccountResponse = predepositAccountClient.listPredepositAccount(predepositAccountConditionDTO);
        if (!preDepositAccountResponse.isSuccess() || preDepositAccountResponse.getContent() == null) {
            log.info("{}|【专项预存组合】查询预收账户列表，异常:houseId={}，itemId={}", LogCategoryEnum.BUSSINESS, houseId, itemId, preDepositAccountResponse.getMessage());
        } else {
            List<PredepositAccountDTO> predepositAccountDTOS = preDepositAccountResponse.getContent();
            for (PredepositAccountDTO predepositAccountDTO : predepositAccountDTOS) {
                refundingMoney = refundingMoney.add(predepositAccountDTO.getRefunddingMoney());
                totalMoney = totalMoney.add(predepositAccountDTO.getTotalBalance());
                freezeMoney = freezeMoney.add(predepositAccountDTO.getFreezedMoney());
                availableMoney = availableMoney.add(predepositAccountDTO.getAvailableBalance());
            }
        }
        result.put("refundingMoney", refundingMoney);
        result.put("totalMoney", totalMoney);
        result.put("freezeMoney", freezeMoney);
        result.put("availableMoney", availableMoney);
        return result;
    }

    private Map<Long, PrestoreChargeInfoVO> getGeneralPre(PreStoreItemConfigDTO config, Long communityId, List<Long> assetIds){
        Map<Long, PrestoreChargeInfoVO> generalPreMap = null;
        if (CollectionUtil.isNotEmpty(config.getCommonPreStoreItems())) {
            ChargeResponse<List<PredepositAccountDTO>> listGerPre = predepositAccountClient.listPredepositAccount(PredepositAccountConditionDTO.builder().communityId(communityId)
                    .assetIds(assetIds).billType(PredepositBillTypeEnum.PRE_DEPOSIT.getCode())
                    .itemId(CommonChargeItem.ITEM_ID).build());

            List<PredepositAccountDTO> generalDepList = null;
            if (listGerPre != null && listGerPre.getContent() != null && !listGerPre.getContent().isEmpty()) {
                generalDepList = listGerPre.getContent();
            }
            if(CollectionUtil.isNotEmpty(generalDepList)){
                generalPreMap = generalDepList.stream().collect(Collectors.toMap(PredepositAccountDTO::getAssetId, e -> PrestoreChargeInfoVO.fromGeneralPrestore(e), (a,b) -> b));
            }
        }
        return generalPreMap;
    }

    private List<PrestoreChargeInfoVO> handleSpecialPrestore(PreStoreItemConfigDTO config, Long communityId, Long assetId) throws ChargeBusinessException{
        List<PrestoreChargeInfoVO> prestoreDetailVOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(config.getSpecialPreStoreItems())) {
            //获取房间专项预存项-已配置计费标准且开启预存
            ZXSpecialPreStoreQueryConditionDTO condition = new ZXSpecialPreStoreQueryConditionDTO();
            condition.setCommunityId(communityId);
            condition.setAssetId(assetId);
            ChargeResponse<List<ItemInfo>> specialList = communityPreStoreItemClient.listSpecialItemForZX(condition);
            log.info("{}|V2计算月度费用查询房间专项预存费项，参数：{}，房间专项预存项有：{}", LogCategoryEnum.BUSSINESS, condition, specialList);
            if (specialList != null && specialList.getContent() != null && !specialList.getContent().isEmpty()) {
                log.info("{}|V2计算月度费用查询房间专项预存费项积分相关，community+id：{}，房间专项预存项有：{}", LogCategoryEnum.BUSSINESS, communityId, specialList);
                List<PointsSignCommunityDTO> communityPointsConfig = AppInterfaceUtil.getResponseData(pointsConfigClient.getCommunityConfig(
                        PointsConfigConditionDTO.builder().communityId(communityId).enableConfig(true).build()));
                log.info("{}|V2计算月度费用查询房间专项预存费项积分相关，community+id：{}，房间专项预存项有：{}", LogCategoryEnum.BUSSINESS, communityId, communityPointsConfig);
                prestoreDetailVOList = getSpecialChargeInfo(assetId, communityId, specialList.getContent(), communityPointsConfig);
            }
        }
        return prestoreDetailVOList;
    }


    private List<PrestoreChargeInfoVO> fetchPrestoreDetails(Long communityId, Long assetId) throws ChargeBusinessException {
        return this.getPrestoreInfoAllSpecialNew(communityId, assetId);
    }

    private List<CommunityPrestoreGroupDetailDTO> getPreStoreGroups(Long communityId) throws ChargeBusinessException {
        SpecialItemStandardConditionDTO conditionDTO = new SpecialItemStandardConditionDTO();
        conditionDTO.setCommunityId(communityId);
        //0-专项预存组合，1-云交付
        conditionDTO.setEnable(0);
        ChargeResponse<List<CommunityPrestoreGroupDetailDTO>> response = communitySpecialPreStoreGroupClient.getPrestoreGroupList(conditionDTO);
        if (!response.isSuccess()) {
            log.info("【专项预存组合】查询小区专项预存分组信息：获取该小区{}的预存分组或异常：{}", communityId, response.getMessage());
            throw new ChargeBusinessException(ErrorInfoEnum.E1015.getCode(), "【专项预存组合】查询小区专项预存分组信息：获取该小区的预存分组或异常, 请稍后重试");
        }
        return Optional.ofNullable(response.getContent()).orElse(Lists.newArrayList());
    }

}
