package com.charge.api.web.vo.joylife.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/02
 */
@Data
public class HousePayItemVO implements Serializable {
    /**
     * 费用所属年月
     */
    @ApiModelProperty(value = "费用所属年月")
    private String belongYear;

    /**
     * 总应收金额（初始应收+违约金应收）
     */
    @ApiModelProperty(value = "总应收金额（初始应收+违约金应收）")
    private BigDecimal sumPrice = BigDecimal.ZERO;

    /**
     * 欠收金额（欠收金额+违约金欠收）
     */
    @ApiModelProperty(value = "欠收金额（欠收金额+违约金欠收）")
    private BigDecimal arrearsPrice = BigDecimal.ZERO;

    /**
     * 实收金额（实收金额+违约金实收）
     */
    @ApiModelProperty(value = "实收金额（实收金额+违约金实收）")
    private BigDecimal payPrice = BigDecimal.ZERO;

    /**
     * 折扣金额
     */
    @ApiModelProperty(value = "折扣金额")
    private BigDecimal discountPrice = BigDecimal.ZERO;

    /**
     * 托收中总欠收（欠收金额+违约金欠收）
     */
    @ApiModelProperty(value = "托收中总欠收（欠收金额+违约金欠收）")
    private BigDecimal collectionPrice = BigDecimal.ZERO;

    /**
     * 挂起总欠收（欠收金额+违约金欠收）
     */
    @ApiModelProperty(value = "挂起总欠收（欠收金额+违约金欠收）")
    private BigDecimal holdPrice = BigDecimal.ZERO;
}
