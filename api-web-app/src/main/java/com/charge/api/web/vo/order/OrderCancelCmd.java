package com.charge.api.web.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单新增
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderCancelCmd extends BaseOrderReq {

    /**
     * 定金/意向金 600   佣金 601
     */
    @NotBlank(message = "二级分类不能为空")
    private String secondClassificationId;

    /**
     * 资产id(无指定资产则默认是项目虚拟房间)
     */
    private Long assetId;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    @NotBlank(message = "用户名称不能为空")
    private String customerName;

    /**
     * 货物名称
     */
    @NotBlank(message = "货物名称不能为空")
    private String goodsName;


    /**
     * 订单初始金额
     */
    @NotNull(message = "订单初始金额不能为空")
    private BigDecimal originalAmount;

    /**
     * 订单优惠类型
     */
    private String discountType;

    /**
     * 订单优惠金额
     */
    private BigDecimal discountAmount;


    /**
     * 支付模式：1-分期支付，2-全额支付
     */
    @NotNull(message = "支付模式不能为空")
    private Integer payMode;

    /**
     * 支付状态：0-待支付，1-已支付
     */
    @NotNull(message = "支付状态不能为空")
    private Integer payStatus;

    /**
     * 订单下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "下单时间不能为空")
    private Date orderTime;

    /**
     * 子订单
     */
    @NotEmpty(message = "子单列表不能为空")
    private List<SubOrderAdd> subOrders;


}