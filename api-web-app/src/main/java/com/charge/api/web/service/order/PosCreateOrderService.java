package com.charge.api.web.service.order;

import com.charge.bill.enums.BalanceStatusEnum;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.common.util.DateUtils;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.pay.client.PayRecordClient;
import com.charge.pay.dto.PayRecordDTO;
import com.charge.pay.dto.PayRecordExtendDTO;
import com.charge.pay.dto.PayRecordWithExtend;
import com.charge.pay.enums.PayTradeStateEnum;
import com.charge.pay.enums.TradeSceneEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * pos下单服务
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
@Service
@Slf4j
public  class PosCreateOrderService extends BaseCreateOrderService {

    @Resource
    protected PayRecordClient payRecordClient;

    @Override
    public void orderPay(CreateOrderContext context) {
        CommunityDTO community = context.getCommunity();
        PayRecordDTO payRecord = PayRecordDTO.builder()
                .orderNum(context.getOrderNum())
                .payMember(context.getPayMember())
                .payMemberId(context.getPayMember())
                .payStatus(0)
                .paymentMethod(context.getPaymentMethod().getPaymentCode())
                .paymentChannel(context.getPaymentChannel().getPaymentChannel())
                .paymentTerminal(context.getPaymentTerminal().getCode())
                .money(context.getTotalPrice())
                .memo(context.getMemo())
                .communityId(community.getId())
                .communityName(community.getName())
                .goodsName(context.getGoodsName())
                .balanceStatus(BalanceStatusEnum.UNRECONCILED.getCode())
                .bankAccountNo(context.getBankAccountNum())
                .referNo(context.getOutTransactionNo())
                .mercid(getMercid(context))
                .bankAccountUuid(context.getBankAccountUuid()).build();
        //pos是收费的下单，无外部订单，以内部订单号作为外部订单号
        String outTradeNo = context.getOrderNum();
        PayRecordExtendDTO payRecordExtendDTO = PayRecordExtendDTO.builder()
                .orderNum(context.getOrderNum())
                .communityId(community.getId())
                .outTradeNo(outTradeNo)
                .globalOutTradeNo(context.getPaymentTerminal().getCode() + "_" + outTradeNo)
                .tradeScene(TradeSceneEnum.POS.getCode())
                .payChannel(context.getPaymentChannel().getPaymentChannel())
                .paymentTerminal(context.getPaymentTerminal().getCode())
                .createTime(DateUtils.getCurrentTimestamp())
                .tradeState(PayTradeStateEnum.NOTPAY.getCode())
                .build();
        PayRecordWithExtend payRecordWithExtend = new PayRecordWithExtend(payRecord, payRecordExtendDTO);
        try {
            ChargeResponse<Void> response = payRecordClient.saveWithExtend(payRecordWithExtend);
            Assert.isTrue(response.isSuccess(), "写入交易记录异常" + response.getMessage());
        } catch (Exception e) {
            throw new ChargeRuntimeException("调用创建支付记录异常:" + e.getMessage(), e);
        }
    }

    public static String getMercid(CreateOrderContext context) {
        return PaymentTerminalEnum.POS.equals(context.getPaymentTerminal())
                ? context.getLakalaMerchant() : context.getUnionPayMerchant();
    }
}


