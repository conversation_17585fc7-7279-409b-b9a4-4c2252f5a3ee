package com.charge.api.web.vo.joylife.response;

import com.charge.api.web.vo.joylife.deposit.AssetPreStoreV0;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchCalculatePreStoreVO implements Serializable {

	private static final long serialVersionUID = 6351740739768290408L;

	/**
	 * 小区商户号配置是否开启（1-未开启，0-开启）
	 */
    private Integer disableStatus;

	/**
	 * 小区预存功能是否开启（0-未开启，1-开启）
	 */
    private Integer prestoreChargeStatus;

	/**
	 * 收费系统-项目ID
	 */
    private Long communityId;

	/**
	 * 朝昔-项目msId
	 */
    private String communityMsId;

	/**
	 * 批量资产预存信息
	 */
	private List<AssetPreStoreV0> prestoreAssetChargeInfos;


	public static BatchCalculatePreStoreVO from(List<AssetPreStoreV0> assetPreStoreV0s, Long communityId, String communityMsId,
                                                Integer disableStatus, Integer prestoreChargeStatus, String message) {
        return BatchCalculatePreStoreVO.builder()
				.prestoreAssetChargeInfos(assetPreStoreV0s)
				.communityId(communityId)
				.communityMsId(communityMsId)
				.disableStatus(disableStatus)
				.prestoreChargeStatus(prestoreChargeStatus)
				.build();
	}
}
