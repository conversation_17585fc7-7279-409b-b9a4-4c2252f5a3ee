package com.charge.api.web.service.impl.parking;

import com.charge.api.web.constants.ParkingErrorConstants;
import com.charge.api.web.dto.parking.ParkingSpaceParam;
import com.charge.api.web.service.parking.PlatformParkingSpaceService;
import com.charge.api.web.vo.ParkingPlaceBalanceVO;
import com.charge.api.web.vo.ParkingPlacePeriodVO;
import com.charge.bill.client.PredepositAccountClient;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.predeposit.PredepositAccountConditionDTO;
import com.charge.bill.dto.predeposit.PredepositAccountDTO;
import com.charge.bill.enums.PredepositTypeEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.bill.enums.ReceivalbleBillPayStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 获取停车云平台产权车位余额及有效期间实现类
 * @Author: yjw
 * @Date: 2024/3/7 17:03
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PlatformParkingSpaceServiceImpl implements PlatformParkingSpaceService {

    private final AssetClient assetClient;

    private final PredepositAccountClient predepositAccountClient;

    private final ReceivableBillClient receivableBillClient;

    /**单次支持查询车位最大数量**/
    private final static Integer MAX_PARKING_SPACE_NUM = 50;

    @Override
    public List<ParkingPlaceBalanceVO> getParkingPlaceBalance(ParkingSpaceParam parkingSpaceParam) throws ChargeBusinessException{

        checkRequestParam(parkingSpaceParam);
        List<String> parkingSpaceMsIds = parkingSpaceParam.getParkingSpaceMsId();
        ChargeResponse<List<AssetDTO>> assetDTOResponse = assetClient.listAsset(AssetCondition.builder().msIds(parkingSpaceMsIds).build());
        if(!assetDTOResponse.isSuccess()){
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "查询资产为空，暂无法预估");
        } else if (CollectionUtil.isEmpty(assetDTOResponse.getContent())){
            return Collections.emptyList();
        } else if(assetDTOResponse.getContent().size() != parkingSpaceMsIds.size()){
            log.info("{}|部分车位查询失败", LogCategoryEnum.BUSSINESS);
        }

        List<AssetDTO> assetDTOList = assetDTOResponse.getContent();
        List<Long> communityIds = assetDTOList.stream().filter(item -> AssetTypeEnum.PARKING_SPACE.getCode().equals(item.getType())).map(e -> e.getParkingSpaceDTO().getCommunityId()).distinct().collect(Collectors.toList());
        if(CollectionUtil.isEmpty(communityIds)){
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "车位数据异常，请稍后重试");
        } else if(communityIds.size() > 1){
            throw new ChargeBusinessException(ErrorInfoEnum.E2004.getCode(), "只允许查询同一项目下的车位");
        }

        Map<Long, String> assetId2MsIdMap = assetDTOList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getParkingSpaceDTO().getMsId(), (a, b) -> b));
        List<Long> assetIds = assetDTOList.stream().map(AssetDTO::getId).collect(Collectors.toList());

        //获取资产符合要求的应收单初始金额
        List<ReceivableBillDTO> receivableBillDTOS = AppInterfaceUtil.getResponseDataThrowException(receivableBillClient.queryList(ReceivableConditionDTO.builder().communityId(communityIds.get(0)).assetIdList(assetIds)
                .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode()).billStatuses(Arrays.asList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),
                        ReceivableBillStatusEnum.BILL_HOLD.getCode())).build()));
        if(CollectionUtil.isEmpty(receivableBillDTOS)){
            throw new ChargeBusinessException(ParkingErrorConstants.CODE_BILL_FAILED, "车位暂无历史欠费，无法预估可用余额期数");
        }

        //资产对应应收余额
        Map<Long, Map<Long, BigDecimal>> assetReceivableAmount = handleReceivableAmount(receivableBillDTOS);

        //获取资产预存余额（可用）、待结转余额（可用）
        List<PredepositAccountDTO> predepositAccountDTOList = AppInterfaceUtil.getResponseDataThrowException(predepositAccountClient.listPredepositAccount(
                PredepositAccountConditionDTO.builder().communityId(communityIds.get(0)).assetIds(assetIds).chargeObj(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode())
                        .predepositTypes(Arrays.asList(PredepositTypeEnum.SPECIAL_DEPOSIT.getCode(), PredepositTypeEnum.CARRY_FORWARD.getCode())).build()));
        if(CollectionUtil.isEmpty(predepositAccountDTOList)) {
            throw new ChargeBusinessException(ParkingErrorConstants.CODE_ACCOUNT_FAILED, "查询车位可用余额为空，无法预估可用余额期数");
        }

        return handleAssetBalance(predepositAccountDTOList, assetId2MsIdMap, assetReceivableAmount);
    }

    @Override
    public List<ParkingPlacePeriodVO> getParkingPlacePeriod(ParkingSpaceParam parkingSpaceParam) throws ChargeBusinessException {

        checkRequestParam(parkingSpaceParam);
        List<String> parkingSpaceMsIds = parkingSpaceParam.getParkingSpaceMsId();
        ChargeResponse<List<AssetDTO>> assetDTOResponse = assetClient.listAsset(AssetCondition.builder().msIds(parkingSpaceMsIds).build());
        if(!assetDTOResponse.isSuccess()){
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "查询资产为空，暂无法预估");
        } else if (CollectionUtil.isEmpty(assetDTOResponse.getContent())){
            return Collections.emptyList();
        } else if(assetDTOResponse.getContent().size() != parkingSpaceMsIds.size()){
            log.info("{}|部分车位查询失败", LogCategoryEnum.BUSSINESS);
        }

        List<AssetDTO> assetDTOList = assetDTOResponse.getContent();
        List<Long> communityIds = assetDTOList.stream().filter(item -> AssetTypeEnum.PARKING_SPACE.getCode().equals(item.getType())).map(e -> e.getParkingSpaceDTO().getCommunityId()).distinct().collect(Collectors.toList());
        if(CollectionUtil.isEmpty(communityIds)){
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "车位数据异常，请稍后重试");
        } else if(communityIds.size() > 1){
            throw new ChargeBusinessException(ErrorInfoEnum.E2004.getCode(), "只允许查询同一项目下的车位");
        }

        Map<Long, String> assetId2MsIdMap = assetDTOList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getParkingSpaceDTO().getMsId(), (a, b) -> b));
        List<Long> assetIds = assetDTOList.stream().map(AssetDTO::getId).collect(Collectors.toList());

        //获取资产符合要求的应收单(生效、挂起、审核中 & 已核销 & 收费对象为业主)
        List<ReceivableBillDTO> receivableBillDTOS = AppInterfaceUtil.getResponseDataThrowException(receivableBillClient.queryList(ReceivableConditionDTO.builder().communityId(communityIds.get(0)).assetIdList(assetIds)
                .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode()).payStatus(ReceivalbleBillPayStatusEnum.PAY_SUCCESS.getCode()).billStatuses(Arrays.asList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),
                        ReceivableBillStatusEnum.BILL_HOLD.getCode(), ReceivableBillStatusEnum.BILL_CHECKING.getCode())).build()));
        if(CollectionUtil.isEmpty(receivableBillDTOS)){
            throw new ChargeBusinessException(ParkingErrorConstants.CODE_BILL_FAILED, "车位暂无历史账单，无法获取产权车位期限");
        }

        //资产对应周期
        return handleAssetPeriod(receivableBillDTOS, assetId2MsIdMap);
    }

    private Map<Long, Map<Long, BigDecimal>> handleReceivableAmount(List<ReceivableBillDTO> receivableBillDTOS){
        Map<Long, Map<Long, BigDecimal>> assetReceivableAmount = new HashMap<>();
        Map<Long, List<ReceivableBillDTO>> assetReceivableMap = receivableBillDTOS.stream().collect(Collectors.groupingBy(e -> e.getAssetId()));
        String yearMonth = DateUtils.getDateStr(DateUtils.FORMAT_14);
        Set<Map.Entry<Long, List<ReceivableBillDTO>>> entries = assetReceivableMap.entrySet();
        for (Map.Entry<Long, List<ReceivableBillDTO>> entry : entries){
            Map<Long, BigDecimal> assetItemReceivableAmount = new HashMap<>();
            List<ReceivableBillDTO> assetReceivableBillDTOS = entry.getValue();
            Map<Long, List<ReceivableBillDTO>> assetItemReceivableMap = assetReceivableBillDTOS.stream().collect(Collectors.groupingBy(e -> e.getItemId()));
            Set<Map.Entry<Long, List<ReceivableBillDTO>>> itemEntries = assetItemReceivableMap.entrySet();
            for (Map.Entry<Long, List<ReceivableBillDTO>> itemEntry : itemEntries) {
                BigDecimal monthAmount = new BigDecimal(0);
                List<ReceivableBillDTO> assetItemReceivableBillDTOS = itemEntry.getValue();
                if (CollectionUtil.isNotEmpty(assetItemReceivableBillDTOS)) {
                    List<ReceivableBillDTO> matchReceivables = assetItemReceivableBillDTOS.stream().filter(e -> yearMonth.equals(e.getBelongYears())).collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(matchReceivables)) {
                        //不存在当月的应收单，取费用所属年月不大于当年时间的应收单并排序分组
                        Map<String, List<ReceivableBillDTO>> latestReceivables = assetItemReceivableBillDTOS.stream().filter(e -> yearMonth.compareTo(e.getBelongYears()) > -1)
                                .sorted(Comparator.comparing(ReceivableBillDTO::getBelongYears).reversed()).collect(Collectors.groupingBy(e -> e.getBelongYears()));
                        if (CollectionUtil.isNotEmpty(latestReceivables)) {
                            String latestBelongYear = assetItemReceivableBillDTOS.stream().filter(e -> yearMonth.compareTo(e.getBelongYears()) > -1).max(Comparator.comparing(ReceivableBillDTO::getBelongYears)).get().getBelongYears();
                            monthAmount = latestReceivables.get(latestBelongYear).stream().map(ReceivableBillDTO::getOriginReceivable).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        }
                    } else {
                        //存在当月的应收单
                        monthAmount = matchReceivables.stream().map(ReceivableBillDTO::getOriginReceivable).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    }
                }
                assetItemReceivableAmount.put(itemEntry.getKey(), monthAmount);
            }
            assetReceivableAmount.put(entry.getKey(), assetItemReceivableAmount);
        }
        return assetReceivableAmount;

    }

    private List<ParkingPlaceBalanceVO> handleAssetBalance(List<PredepositAccountDTO> predepositAccountDTOList,
                                    Map<Long, String> assetId2MsIdMap, Map<Long, Map<Long, BigDecimal>> assetReceivableAmount) {
        List<ParkingPlaceBalanceVO> resultList = new ArrayList<>();
        Map<Long, List<PredepositAccountDTO>> assetAccountMap = predepositAccountDTOList.stream().collect(Collectors.groupingBy(e -> e.getAssetId()));
        Set<Map.Entry<Long, List<PredepositAccountDTO>>> assetEntries = assetAccountMap.entrySet();
        for (Map.Entry<Long, List<PredepositAccountDTO>> assetEntry : assetEntries) {
            List<PredepositAccountDTO> assetPredepositAccountDTOS = assetEntry.getValue();
            Map<Long, List<PredepositAccountDTO>> assetItemIdAccountMap = assetPredepositAccountDTOS.stream().collect(Collectors.groupingBy(e -> e.getPredepositItemId()));
            Set<Map.Entry<Long, List<PredepositAccountDTO>>> assetItemIdEntries = assetItemIdAccountMap.entrySet();
            for (Map.Entry<Long, List<PredepositAccountDTO>> assetItemIdEntry : assetItemIdEntries) {
                //预估费用为0的车位收费项直接过滤
                if(Objects.isNull(assetReceivableAmount.get(assetEntry.getKey()))
                        || Objects.isNull(assetReceivableAmount.get(assetEntry.getKey()).get(assetItemIdEntry.getKey()))
                        || assetReceivableAmount.get(assetEntry.getKey()).get(assetItemIdEntry.getKey()).compareTo(BigDecimal.ZERO) == 0){
                    log.info("{}|该车位[{}]的收费项[{}]资产预估每月费用为0，无法计算可用余额期数", LogCategoryEnum.BUSSINESS, assetEntry.getKey(), assetItemIdEntry.getKey());
                    continue;
                }
                List<PredepositAccountDTO> itemPredepositAccountDTOS = assetItemIdEntry.getValue();
                BigDecimal monthAmount = assetReceivableAmount.get(assetEntry.getKey()).get(assetItemIdEntry.getKey());
                ParkingPlaceBalanceVO parkingPlaceBalanceVO = buildParkingPlaceBalanceVO(itemPredepositAccountDTOS, assetId2MsIdMap, monthAmount);
                resultList.add(parkingPlaceBalanceVO);
            }
        }
        return resultList;
    }

    private ParkingPlaceBalanceVO buildParkingPlaceBalanceVO(List<PredepositAccountDTO> itemPredepositAccountDTOS, Map<Long, String> assetId2MsIdMap,
                                                             BigDecimal monthAmount){
        ParkingPlaceBalanceVO parkingPlaceBalanceVO = new ParkingPlaceBalanceVO();
        parkingPlaceBalanceVO.setItemId(itemPredepositAccountDTOS.get(0).getPredepositItemId());
        parkingPlaceBalanceVO.setItemName(itemPredepositAccountDTOS.get(0).getPredepositItemName());
        parkingPlaceBalanceVO.setParkingSpaceMsId(assetId2MsIdMap.get(itemPredepositAccountDTOS.get(0).getAssetId()));
        BigDecimal totalAmount = itemPredepositAccountDTOS.stream().map(PredepositAccountDTO::getAvailableBalance).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal predepositAvailableAmount = itemPredepositAccountDTOS.stream().filter(e -> PredepositTypeEnum.SPECIAL_DEPOSIT.getCode().equals(e.getPredepositType()))
                .map(PredepositAccountDTO::getAvailableBalance).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal carryForwardAvailableAmount = itemPredepositAccountDTOS.stream().filter(e -> PredepositTypeEnum.CARRY_FORWARD.getCode().equals(e.getPredepositType()))
                .map(PredepositAccountDTO::getAvailableBalance).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        parkingPlaceBalanceVO.setPredepositAmount(predepositAvailableAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        parkingPlaceBalanceVO.setCarryForwardAmount(carryForwardAvailableAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        parkingPlaceBalanceVO.setTotalAmount(totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        parkingPlaceBalanceVO.setMonthAmount(monthAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        parkingPlaceBalanceVO.setMonth(totalAmount.divide(monthAmount, 2, BigDecimal.ROUND_HALF_UP).toString());
        return parkingPlaceBalanceVO;
    }

    private List<ParkingPlacePeriodVO> handleAssetPeriod(List<ReceivableBillDTO> receivableBillDTOS, Map<Long, String> assetId2MsIdMap) {
        List<ParkingPlacePeriodVO> resultList = new ArrayList<>();
        Map<Long, List<ReceivableBillDTO>> receivableMap = receivableBillDTOS.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId));
        Set<Map.Entry<Long, List<ReceivableBillDTO>>> assetEntries = receivableMap.entrySet();
        for (Map.Entry<Long, List<ReceivableBillDTO>> assetEntry : assetEntries) {
            List<ReceivableBillDTO> assetReceivableBillDTOS = assetEntry.getValue();
            Map<Long, List<ReceivableBillDTO>> assetItemIdReceivableMap = assetReceivableBillDTOS.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getItemId));
            Set<Map.Entry<Long, List<ReceivableBillDTO>>> assetItemIdEntries = assetItemIdReceivableMap.entrySet();
            for (Map.Entry<Long, List<ReceivableBillDTO>> assetItemIdEntry : assetItemIdEntries) {
                List<ReceivableBillDTO> itemReceivableBillDTOS = assetItemIdEntry.getValue();
                ParkingPlacePeriodVO parkingPlacePeriodVO = buildParkingPlacePeriodVO(itemReceivableBillDTOS, assetId2MsIdMap);
                resultList.add(parkingPlacePeriodVO);
            }
        }
        return resultList;
    }

    private ParkingPlacePeriodVO buildParkingPlacePeriodVO(List<ReceivableBillDTO> itemReceivableBillDTOS,  Map<Long, String> assetId2MsIdMap){
        ParkingPlacePeriodVO parkingPlacePeriodVO = new ParkingPlacePeriodVO();
        parkingPlacePeriodVO.setItemId(itemReceivableBillDTOS.get(0).getItemId());
        parkingPlacePeriodVO.setItemName(itemReceivableBillDTOS.get(0).getItemName());
        parkingPlacePeriodVO.setParkingSpaceMsId(assetId2MsIdMap.get(itemReceivableBillDTOS.get(0).getAssetId()));
        String validStartTime = DateUtils.formatLocalDate(itemReceivableBillDTOS.stream().filter(a->a.getChargeStartTime()!=null).min(Comparator.comparing(ReceivableBillDTO::getBelongYears)).get().getChargeStartTime(), DateUtils.FORMAT_1);
        String validEndTime = DateUtils.formatLocalDate(itemReceivableBillDTOS.stream().filter(a->a.getChargeEndTime()!=null).max(Comparator.comparing(ReceivableBillDTO::getBelongYears)).get().getChargeEndTime(), DateUtils.FORMAT_1);
        parkingPlacePeriodVO.setValidStartTime(validStartTime);
        parkingPlacePeriodVO.setValidEndTime(validEndTime);
        return parkingPlacePeriodVO;
    }

    private void checkRequestParam(ParkingSpaceParam parkingSpaceParam) throws ChargeBusinessException {
        List<String> parkingSpaceMsIds = parkingSpaceParam.getParkingSpaceMsId();
        if(parkingSpaceMsIds.size() > MAX_PARKING_SPACE_NUM){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "车位数量不得超过50个");
        }
    }

}
