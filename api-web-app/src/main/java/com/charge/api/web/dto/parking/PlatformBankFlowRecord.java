package com.charge.api.web.dto.parking;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName: ParkingSyncRecord
 * @Author: wangle
 * @Description: 云平台银行收款业务流水实体类
 * @Date: 2021/11/11 15:36:16
 * @Version: 1.0
 */
@Data
public class PlatformBankFlowRecord {
    /**
     * 主键
     */
    private String parkingRecordId;

    /**
     * 小区id
     */
    private String communityId;

    /**
     * 小区code
     */
    private String communityCode;

    /**
     * 小区名称
     */
    private String communityName;

    /**
     * 停车场id
     */
    private String parkingId;

    /**
     * 停车场名称
     */
    private String parkingName;

    /**
     * 收费项id
     */
    private String itemId;

    /**
     * 收费项名称
     */
    private String itemName;

    /**
     * 收款金额
     */
    private BigDecimal money;

    /**
     * 交易日期
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    private Date chargeTime;

    /**
     * 所属年月
     */
    private String belongYears;

    /**
     * 停车场云平台交易流水号
     */
    private String transferId;

    /**
     * 支付渠道
     */
    private String paymentChannel;

    /**
     * 支付来源
     */
    private String paymentSource;

    /**
     * 支付方式(10表示云平台)
     */
    private String paymentMethod;

    /**
     * 入账标志(0表示未入账,1表示入账中,2表示已入账)
     */
    private String enterStatus;

    /**
     * 收款类型(0表示本金，1表示违约金)
     */
    private String chargeType;

    /**
     * 创建时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date createTs;

    /**
     * 更新时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date updateTs;

}

