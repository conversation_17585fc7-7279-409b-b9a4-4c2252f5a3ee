package com.charge.api.web.util;

import org.apache.commons.lang3.StringUtils;

/**
 * DesensitizeUtil
 * <p>
 * Description:
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
public class DesensitizeUtil {
    public static String desensitize(String value){
        String result = value;
        if (StringUtils.isNotEmpty(value)) {
            int length = value.length();
            //姓名字段脱敏，如果2,3位名字处理第二位，4位处理3,4
            if (length == 2 || length == 3) {
                result = StringUtils.overlay(value, "*", 1, 2);
            } else if (length == 4 || length == 5) {
                result = StringUtils.overlay(value, "**", 1, 3);
            }
            //手机号处理 开头：+86-
            else if (value.startsWith("+86-") && length == 15) {
                result = StringUtils.overlay(value, "****", 7, 11);
            } else if (length == 11) {
                result = StringUtils.overlay(value, "****", 3, 7);
            }
            //身份证处理
            else if (length == 18) {
                result = StringUtils.overlay(value, "********", 6, 14);
            } else if (length >= 6) {
                //>=6位的
                int start = (length / 2) - 2;
                int end = (length / 2) + 2;
                result = StringUtils.overlay(value, "****", start, end);
            }
        }
        return result;
    }
}