package com.charge.api.web.controller.joylife.point;

import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.dto.joylife.DistributePointsReq;
import com.charge.api.web.service.joylife.PointsService;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.vo.joylife.PreStorePointsVO;
import com.charge.api.web.vo.joylife.request.*;
import com.charge.api.web.vo.joylife.response.*;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.bill.enums.ReceivalbleBillPayStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.config.client.assets.AssetsChargeConfigClient;
import com.charge.config.client.points.PointsConfigClient;
import com.charge.config.dto.assets.AssetChargeConfigDetailDTO;
import com.charge.config.dto.assets.condition.AssetChargeConfigBatchQueryConditionDTO;
import com.charge.config.dto.points.PointsConfigConditionDTO;
import com.charge.config.dto.points.PointsSignCommunityDTO;
import com.charge.config.enums.PointsConfigStatusEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.pay.dto.point.PointTradeDetailDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/4/7 15:21
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointController {

    private final PointsService paymentSource;

    private final PointsConfigClient pointsConfigClient;

    private final AssetClient  assetClient;

    private final CommunityClient communityClient;

    private final AssetsChargeConfigClient assetsChargeConfigClient;

    private final AssetSupport assetSupport;

    private final ReceivableBillClient receivableBillClient;

    @PostMapping("/new/chargeAPI/distributepoints")
    public ChargeResponse distributePoints(@Valid @RequestBody DistributePointsReq distributePointsReq) throws ChargeBusinessException {
        return paymentSource.distributePoints(distributePointsReq);
    }


    /**计算可抵扣积分-根据传入欠费信息
     * @param
     * @return
     * @throws IOException
     */
    @PostMapping("/new/chargeAPI/calPointsBySelect")
    public ChargeResponse calPointsBySelect(@RequestBody @Validated MixPointsCalRequest calRequest) throws ChargeBusinessException {
        return paymentSource.calPointsBySelect(calRequest);
    }


    /**
     * 计算预存的积分数量-根据传入欠费信息
     *
     * @param calRequest 预存计算请求
     * @return 预存积分
     * @throws ChargeBusinessException 业务异常
     */
    @PostMapping("/new/chargeAPI/calPreStorePoints")
    @Deprecated
    public ChargeResponse<PreStorePointsVO> calPreStorePoints(@RequestBody @Validated PreStorePointsCalReq calRequest) throws ChargeBusinessException {
        return new ChargeResponse<>(paymentSource.calPreStorePoints(calRequest));
    }

    /**
     * 计算预存的积分数量-根据传入欠费信息
     *
     * @param calRequest 预存计算请求
     * @return 预存积分
     * @throws ChargeBusinessException 业务异常
     */
    @PostMapping("/new/chargeAPI/multiCalPreStorePoints")
    public ChargeResponse<List<PreStorePointsVO>> multiCalPreStorePoints(@RequestBody @Validated MultiPreStorePointsCalReq calRequest) throws ChargeBusinessException {
        return new ChargeResponse<>(paymentSource.calPreStorePoints(calRequest));
    }

    /**
     * 批量资产下计算预存的积分数量-根据传入欠费信息
     *
     * @param batchCalRequest 预存计算请求
     * @return 预存积分
     * @throws ChargeBusinessException 业务异常
     */
    @PostMapping("/new/chargeAPI/batchAsset/multiCalPreStorePoints")
    public ChargeResponse<List<AssetPreStorePointsVO>> batchMultiCalPreStorePoints(@RequestBody @Valid BatchAssetPreStorePointsCalReq batchCalRequest) throws ChargeBusinessException {
        return new ChargeResponse<>(paymentSource.batchCalPreStorePoints(batchCalRequest));
    }


    /**
     * 小区积分抵扣开启状态查询
     * @param communityId
     * @param userId
     * @param userName
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "小区积分抵扣开启状态查询")
    @RequestMapping(value = "/new/chargeAPI/getPointsDiscountEnable", method = {RequestMethod.GET})
    public ChargeResponse<PointsDiscountEnableVO> getPointsDiscountEnable(String communityId, String userId,String userName) throws Exception {
        if (communityId == null || StringUtils.isEmpty(communityId)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"小区参数缺失");
        }
        CommunityDTO communityDTO=AppInterfaceUtil.getResponseData(communityClient.oneByCondition(CommunityCondition.builder().msId(communityId).build()));
        if (Objects.isNull(communityDTO)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(),"查不到该小区信息，请重试或联系管家处理");
        }

        List<PointsSignCommunityDTO>  communityPointsConfig =  AppInterfaceUtil.getResponseData(pointsConfigClient.getCommunityConfig(
                PointsConfigConditionDTO.builder().communityId(communityDTO.getId()).enableConfig(true).build()));
        PointsDiscountEnableVO result=new PointsDiscountEnableVO();
         if (CollectionUtil.isEmpty(communityPointsConfig)) {
            //无开启数据
             result.setEnableStatus(PointsConfigStatusEnum.DISABLE.getCode().toString());
        } else {
             result.setEnableStatus(PointsConfigStatusEnum.ENABLE.getCode().toString());
        }
        return new ChargeResponse<>(result);
    }

    /**
     * 获取可积分赠分的房间
     * @param houseIds
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取可积分赠分的房间")
    @GetMapping("/app/pointEarnHouseList")
    public ChargeResponse pointEarnHouseList(@RequestParam("houseIds") List<String> houseIds) throws Exception {
            if (CollectionUtils.isEmpty(houseIds)) {
                throw new ChargeBusinessException(ErrorInfoEnum.E1002,"房间id不能为空", null);
            }
            if(houseIds.size()>300){
                throw new ChargeBusinessException(ErrorInfoEnum.E1002, "查询数量超过最大限制", null);
            }

            List<AssetDTO> houseInfoList = AppInterfaceUtil.getResponseData(assetClient.listAsset(AssetCondition.builder().msIds(houseIds).build()));
            if (CollectionUtils.isEmpty(houseInfoList)) {
                throw new ChargeBusinessException(ErrorInfoEnum.E1002, "根据房间运营平台id无法查询欠费信息" + houseIds, null);
            }
            List<Long> communityIdList = houseInfoList.stream().map(item -> AssetSupport.buildAsserAdapter(item).getCommunityId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(communityIdList)) {
                throw new ChargeBusinessException(ErrorInfoEnum.E1002, "该房间查询小区信息为空" + houseIds, null);
            }

            List<CommunityDTO> communityList = AppInterfaceUtil.getResponseData(communityClient.listCommunity(CommunityCondition.builder().ids(communityIdList).build()));
            if (CollectionUtils.isEmpty(communityList)) {
                throw new ChargeBusinessException(ErrorInfoEnum.E1002, "该房间查询小区信息为空" + houseIds, null);
            }

            Map<Long, List<CommunityDTO>> communityMap = communityList.stream().collect(Collectors.groupingBy(item -> item.getId()));
            List<AssetDTO> result = new ArrayList<>();
            for (AssetDTO house : houseInfoList) {
                List<PointsSignCommunityDTO>  communityPointsConfig =  AppInterfaceUtil.getResponseData(pointsConfigClient.getCommunityConfig(
                        PointsConfigConditionDTO.builder().communityId(AssetSupport.buildAsserAdapter(house).getCommunityId()).enableConfig(true).build()));
                if(CollectionUtil.isNotEmpty(communityPointsConfig)) {
                    List<Long> pointEarnList = communityPointsConfig.get(0).getPointsEarnItemIdList();
                    if (CollectionUtil.isEmpty(pointEarnList)) {
                        continue;
                    }
                    List<AssetChargeConfigDetailDTO> prestoreItem = AppInterfaceUtil.getResponseData(assetsChargeConfigClient.batchList(
                            AssetChargeConfigBatchQueryConditionDTO.builder().assetIds(Arrays.asList(house.getId())).build()));
                    if (!CollectionUtils.isEmpty(prestoreItem)) {
                        for (AssetChargeConfigDetailDTO item : prestoreItem) {
                            if (pointEarnList.contains(item.getItemId())) {
                                result.add(house);
                                break;
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(result)) {
                return new ChargeResponse<>(Collections.EMPTY_LIST);
            }
            return new ChargeResponse<>(PrestoreEarnConfig.from(communityMap, result));
    }

    /**
     * 获取可积分抵扣的房间
     * @param houseIds
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取可积分抵扣的房间")
    @GetMapping("/app/pointDeductHouseList")
    public ChargeResponse getPointDeductHouseList(@RequestParam("houseIds") List<String> houseIds) throws Exception {
        if (CollectionUtils.isEmpty(houseIds)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "房间id不能为空", null);
        }
        if (houseIds.size() > 300) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "查询数量超过最大限制", null);
        }

        List<AssetDTO> houseInfoList =
                AppInterfaceUtil.getResponseData(assetClient.listAsset(AssetCondition.builder().msIds(houseIds).build()));
        if (CollectionUtils.isEmpty(houseInfoList)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "根据房间运营平台id无法查询欠费信息" + houseIds, null);
        }

        List<AssetAdapter> assetAdapterList = houseInfoList.stream().map(AssetSupport::buildAsserAdapter).collect(Collectors.toList());

        Map<Long, List<Long>> communityIdKeyAssetIdsMap = assetAdapterList.stream().collect(Collectors.groupingBy(AssetAdapter::getCommunityId,
                Collectors.mapping(AssetAdapter::getId, Collectors.toList())));

        List<Long> communityIdList =
                assetAdapterList.stream().map(AssetAdapter::getCommunityId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(communityIdList)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "该房间查询小区信息为空" + houseIds, null);
        }

        List<CommunityDTO> communityList =
                AppInterfaceUtil.getResponseData(communityClient.listCommunity(CommunityCondition.builder().ids(communityIdList).build()));
        if (CollectionUtils.isEmpty(communityList)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "该房间查询小区信息为空" + houseIds, null);
        }
        Map<Long, List<CommunityDTO>> communityMap =
                communityList.stream().collect(Collectors.groupingBy(CommunityDTO::getId));
        List<AssetDTO> result = new ArrayList<>();
        for (AssetDTO house : houseInfoList) {
            List<PointsSignCommunityDTO> communityPointsConfig =
                    AppInterfaceUtil.getResponseData(pointsConfigClient.getCommunityConfig(
                    PointsConfigConditionDTO.builder().communityId(AssetSupport.buildAsserAdapter(house).getCommunityId()).enableConfig(true).build()));
            if (CollectionUtil.isNotEmpty(communityPointsConfig)) {
                List<Long> pointEarnList = communityPointsConfig.get(0).getPointsDiscountItemIdList();
                if (CollectionUtil.isEmpty(pointEarnList)) {
                    continue;
                }
                List<AssetChargeConfigDetailDTO> prestoreItem =
                        AppInterfaceUtil.getResponseData(assetsChargeConfigClient.batchList(
                                AssetChargeConfigBatchQueryConditionDTO.builder().assetIds(Collections.singletonList(house.getId())).build()));
                if (!CollectionUtils.isEmpty(prestoreItem)) {
                    for (AssetChargeConfigDetailDTO item : prestoreItem) {
                        if (pointEarnList.contains(item.getItemId())) {
                            result.add(house);
                            break;
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(result)) {
            return new ChargeResponse<>(Collections.EMPTY_LIST);
        }

        List<ReceivableBillDTO> receivableBillDTOList = new ArrayList<>();
        List<Long> assetIds = houseInfoList.stream().map(AssetDTO::getId).distinct().collect(Collectors.toList());
        ReceivableConditionDTO receivableConditionDTO = ReceivableConditionDTO.builder()
                .payStatuses(Arrays.asList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(),
                        ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()))
                .billStatuses(Arrays.asList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),
                        ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode())
                .build();
        for (CommunityDTO communityDTO : communityList) {
            List<Long> itemAssetIds = communityIdKeyAssetIdsMap.get(communityDTO.getId());
            if (CollectionUtil.isNotEmpty(itemAssetIds)) {
                receivableConditionDTO.setAssetIdList(assetIds);
                receivableConditionDTO.setCommunityId(communityDTO.getId());
                ChargeResponse<List<ReceivableBillDTO>> chargeResponse = receivableBillClient.queryList(receivableConditionDTO);
                List<ReceivableBillDTO> itemReceivableList = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
                if (CollectionUtils.isEmpty(itemReceivableList)) {
                    continue;
                }
                receivableBillDTOList.addAll(itemReceivableList);
            }
        }
        List<PrestoreDiscountConfig> configList = PrestoreDiscountConfig.from(communityMap, result, receivableBillDTOList);
        return new ChargeResponse<>(configList);

    }



    @PostMapping(value = "/app/pointPageQuery")
    public ChargeResponse<PagingDTO<PointTradeDetailDTO>> pointPageQuery(HttpServletRequest request,
                                                                         @Valid @RequestBody PointTradeDetailConditionRequest pointDTO) {
        return paymentSource.pointPageQuery(pointDTO);
    }

    /**
     * 计算未来和清欠应收单赠分
     * <AUTHOR>
     * @param[1] receivablePointCalRequest
     * @return ChargeResponse<ReceivablePointCalResultVo>
     * @time 2025/8/5 18:37
     */
    @PostMapping(value = "/new/chargeAPI/calPreStorePointsByReceivableBill")
    public ChargeResponse<ReceivablePointCalResultVo> calPreStorePointsByReceivableBill (@Valid @RequestBody ReceivablePointCalRequest receivablePointCalRequest) throws Exception {
        return new ChargeResponse<>(paymentSource.calPreStorePointsByReceivableBill(receivablePointCalRequest));
    }


}
