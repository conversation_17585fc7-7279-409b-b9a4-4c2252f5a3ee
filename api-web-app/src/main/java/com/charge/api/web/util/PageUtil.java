package com.charge.api.web.util;

import com.charge.api.web.vo.ChargePageResponse;
import com.charge.common.dto.PagingDTO;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/5/16
 */
public class PageUtil<T> {

    public static <T> ChargePageResponse<List<T>> toChargePageResponse(PagingDTO pagingDTO) {
        return new ChargePageResponse<>(null, pagingDTO.getPageSize()
                , (int) Math.ceil(pagingDTO.getTotalCount() / pagingDTO.getPageSize().doubleValue()), pagingDTO.getPageNum(), pagingDTO.getTotalCount());
    }
}


