package com.charge.api.web.dto.invoice;

import lombok.Data;
import net.sf.json.JSONObject;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/09/05/ 19:46
 * @description
 */
@Data
public class ThirdPartyInvoiceApplyCallbackRequestDTO implements Serializable {
    private static final long serialVersionUID = 2454259235196069261L;

    /**
     * 业务单号
     */
    private String salesbillNo;

    /**
     * 处理状态（-1：上传失败；1：上传成功；）
     */
    private Integer processStatus;

    /**
     * 错误原因,业务单salesBillMain数据有问题
     */
    private String remark;

    /**
     * 是否作废：1-是
     */
    private String isInvalid;

    /**
     * 明细行失败原因,只传有异常的明细行
     */
    private List<JSONObject> errorInfo;

    /**
     * 明细号
     */
    private String salesbillItemNo;

    /**
     * 处理备注
     */
    private String processRemark;

}
