package com.charge.api.web.controller.joylife.yuexin;

import com.charge.api.web.dto.yuexin.ListArrearsByAssetIdReq;
import com.charge.api.web.dto.yuexin.ListArrearsByCommunityIdReq;
import com.charge.api.web.dto.yuexin.YueXinOrderArrearsCommunityResponse;
import com.charge.api.web.service.joylife.YueXinAppService;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.util.ResultPage;
import com.charge.api.web.util.YueXinDateUtils;
import com.charge.api.web.vo.joylife.request.BillListReq;
import com.charge.api.web.vo.joylife.response.BillDetailReq;
import com.charge.api.web.vo.joylife.response.BillRecordDetailVO;
import com.charge.api.web.vo.joylife.response.BillRecordVO;
import com.charge.bill.client.AssetTransactionClient;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.bill.enums.ReceivalbleBillPayStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.DateUtils;
import com.charge.core.util.CollectionUtil;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.client.PositionClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.condition.BuildingCondition;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.BuildingDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 悦心-员工端 接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/app/yuexin")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ArrearsQueryController {

    private final CommunityClient communityClient;
    private final AssetClient assetClient;
    private final PositionClient positionClient;
    private final ReceivableBillClient receivableBillClient;
    private final AssetTransactionClient assetTransactionClient;

    private final YueXinAppService yueXinAppService;

    private final CommunitySupport communitySupport;

    /**
     * 悦心-项目id查询资产欠费
     *
     * @param reqDTO 请求dto
     * @return success
     */
    @PostMapping(value = "/getArrearsItemAmount/community")
    public ResultPage listArrearsByCommunityId(@Valid @RequestBody ListArrearsByCommunityIdReq reqDTO) {
        CommunityDTO communityDTO = communityClient.oneByCondition(CommunityCondition.builder().msId(reqDTO.getCommunityMsId()).build()).getContent();
        Long communityId = communityDTO.getId();

        List<String> listBuildingMsId = reqDTO.getBuildingMsIdList() == null ? new ArrayList<>() : reqDTO.getBuildingMsIdList();
        List<Long> listBuildingId = new ArrayList<>();
        listBuildingMsId.forEach(msId -> {
            BuildingDTO buildingDTO = positionClient.listBuilding(BuildingCondition.builder().msId(msId).build()).getContent().get(0);
            listBuildingId.add(buildingDTO.getId());
        });

        PagingDTO<AssetDTO> pageAsset = assetClient.pageAssetExcludeCustomer(AssetCondition.builder().communityId(communityId).buildingIds(listBuildingId).pageNum(reqDTO.getPageNum()).pageSize(reqDTO.getPageSize()).build()).getContent();
        List<Long> listAssetId = pageAsset.getList().stream().map(AssetDTO::getId).collect(Collectors.toList());
        //根据资产id，支付状态：未核销，部分核销,单据状态为生效中、已挂起、审核中 查询应收单
        List<ReceivableBillDTO> listReceivable = receivableBillClient.
                queryList(ReceivableConditionDTO.builder()
                        .communityId(communityId)
                        .assetIdList(listAssetId)
                        .payStatuses(Arrays.asList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(),
                                ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()))
                        .billStatuses(Arrays.asList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),
                                ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                        .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode())
                        .build()).getContent();
        return ResultPage.builder().code(0).content(receivableV2ToV1(reqDTO.getCommunityMsId(), pageAsset.getList(), listReceivable)).pageSize(reqDTO.getPageSize()).
                totalPage(pageAsset.getTotalCount() / pageAsset.getPageSize() + 1).currentPage(reqDTO.getPageNum()).totalRecord(pageAsset.getTotalCount()).build();
    }

    /**
     * 悦心-资产id查询资产欠费
     *
     * @param reqDTO 请求dto
     * @return success
     */
    @PostMapping(value = "/getArrearsItemAmount/house")
    public ResultPage listArrearsByAssetId(@Valid @RequestBody ListArrearsByAssetIdReq reqDTO) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(reqDTO.getCommunityMsId());
        List<AssetDTO> listAsset = assetClient.listAssetLessInfo(AssetCondition.builder().communityId(communityId).msIds(reqDTO.getHouseMsIdList()).type(reqDTO.getAssetType()).build()).getContent();
        if (CollectionUtil.isEmpty(listAsset)) {
            return ResultPage.builder().code(0).content(new ArrayList<>()).message(ErrorInfoEnum.E8002.getValue()).build();
        }
        List<Long> listAssetId = listAsset.stream().map(AssetDTO::getId).collect(Collectors.toList());
        //根据资产id，支付状态：未核销，部分核销,单据状态为生效中、已挂起、审核中 查询应收单
        List<ReceivableBillDTO> listReceivable = receivableBillClient.
                queryList(ReceivableConditionDTO.builder()
                        .communityId(communityId)
                        .assetIdList(listAssetId)
                        .payStatuses(Arrays.asList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(),
                                ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()))
                        .billStatuses(Arrays.asList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),
                                ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                        .communityId(communityId).build()).getContent();
        return ResultPage.builder().code(0).content(receivableV2ToV1(reqDTO.getCommunityMsId(), listAsset, listReceivable)).message("success").build();
    }

    private List<YueXinOrderArrearsCommunityResponse> receivableV2ToV1(String communityMsId, List<AssetDTO> listAsset, List<ReceivableBillDTO> listReceivable) {
        List<YueXinOrderArrearsCommunityResponse> resp = new ArrayList<>();
        //根据资产id分组
        Map<Long, List<ReceivableBillDTO>> map = new HashMap<>(16);
        if (CollectionUtil.isNotEmpty(listReceivable)) {
            map.putAll(listReceivable.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId)));
        }

        listAsset.forEach(asset -> {
            List<ReceivableBillDTO> listBill = map.get(asset.getId());
            BigDecimal totalArrears = BigDecimal.ZERO;
            BigDecimal ownerTotalArrears = BigDecimal.ZERO;
            BigDecimal developerTotalArrears = BigDecimal.ZERO;
            int arrearsAge = 0;
            int monthCount =0;
            int ownerMonthCount =0;
            int developerMonthCount =0;
            if (CollectionUtil.isNotEmpty(listBill)) {
//                totalArrears = listBill.stream().map(bill -> bill.getArrearsAmount().add(bill.getPenaltyArrearsAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
                Optional<BigDecimal> amountOptional  = listBill.stream().filter(receivableBillDTO -> (receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                                || receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                                && ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus()))
                        .map(receivableBillDTO -> (receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
                        .reduce(BigDecimal::add);
                totalArrears = amountOptional.isPresent() ? amountOptional.get() : BigDecimal.ZERO;

                Optional<BigDecimal> ownerAmountOptional  = listBill.stream().filter(receivableBillDTO -> (receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                                || receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                                && ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus())
                                && ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(receivableBillDTO.getChargeObject()))
                        .map(receivableBillDTO -> (receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
                        .reduce(BigDecimal::add);
                ownerTotalArrears = ownerAmountOptional.isPresent() ? ownerAmountOptional.get() : BigDecimal.ZERO;

                Optional<BigDecimal> developerAmountOptional  = listBill.stream().filter(receivableBillDTO -> (receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                                || receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                                && ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus())
                                && ChargeObjEnum.CHARGE_OBJ_DEVELOPER.getCode().equals(receivableBillDTO.getChargeObject()))
                        .map(receivableBillDTO -> (receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
                        .reduce(BigDecimal::add);
                developerTotalArrears = developerAmountOptional.isPresent() ? developerAmountOptional.get() : BigDecimal.ZERO;

                List<String> collect = listBill.stream().map(ReceivableBillDTO::getBelongYears).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
                monthCount = collect.size();

                List<String> ownerCollect = listBill.stream().filter(e -> ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(e.getChargeObject())).map(ReceivableBillDTO::getBelongYears).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
                ownerMonthCount = ownerCollect.size();

                List<String> developerCollect = listBill.stream().filter(e -> ChargeObjEnum.CHARGE_OBJ_DEVELOPER.getCode().equals(e.getChargeObject())).map(ReceivableBillDTO::getBelongYears).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
                developerMonthCount = developerCollect.size();

                collect.sort(String::compareTo);
                arrearsAge = YueXinDateUtils.getMonth(collect.get(0), DateUtils.getCurrentDate());
            }
            String type = AssetTypeEnum.HOUSE.getCode().equals(asset.getType())?"house":"position";
            Integer chargeObjectType = asset.getHouseDTO() != null ? asset.getHouseDTO().getChargeObjType():asset.getParkingSpaceDTO().getChargeObjType();
            YueXinOrderArrearsCommunityResponse arrears = YueXinOrderArrearsCommunityResponse.builder().houseTotalArrears(ownerTotalArrears)
                    .houseOwnerTotalArrears(ownerTotalArrears).houseDeveloperTotalArrears(developerTotalArrears).houseWholeTotalArrears(totalArrears)
                    .houseId(asset.getId().toString()).communityId(communityMsId)
                    .communityName(asset.getHouseDTO() == null ? asset.getParkingSpaceDTO().getCommunityName() : asset.getHouseDTO().getCommunityName())
                    .houseCode(asset.getHouseDTO() == null ? asset.getParkingSpaceDTO().getParkingCode() : asset.getHouseDTO().getHouseCode())
                    .houseName(asset.getHouseDTO() == null ? asset.getParkingSpaceDTO().getParkingSpaceName() : asset.getHouseDTO().getHouseName())
                    .houseMsId(asset.getHouseDTO() == null ? asset.getParkingSpaceDTO().getMsId() : asset.getHouseDTO().getMsId())
                    .houseId(asset.getHouseDTO() == null ? Long.toString(asset.getParkingSpaceDTO().getId()) : Long.toString(asset.getHouseDTO().getId()))
                    .arrearsAge(arrearsAge).monthCount(ownerMonthCount).ownerMonthCount(ownerMonthCount).developerMonthCount(developerMonthCount)
                    .wholeMonthCount(monthCount).chargeObjectType(chargeObjectType)
                    .houseType(type)
                    .build();
            resp.add(arrears);
        });
        return resp;
    }

    /**
     * 悦心-分页查询缴费记录列表
     *
     * @param reqCondition 请求参数
     * @return success
     */
    @PostMapping(value = "/billList")
    public ChargeResponse<List<BillRecordVO>> billList(@Valid @RequestBody BillListReq reqCondition) throws ChargeBusinessException{
        return new ChargeResponse<>(yueXinAppService.getBillList(reqCondition));
    }

    /**
     * 悦心-查询缴费记录详情
     *
     * @param reqCondition 请求参数
     * @return success
     */
    @PostMapping(value = "/billDetail")
    public ChargeResponse<BillRecordDetailVO> billDetail(@Valid @RequestBody BillDetailReq reqCondition) throws ChargeBusinessException {
        return new ChargeResponse<>(yueXinAppService.getBillDetail(reqCondition));
    }

}
