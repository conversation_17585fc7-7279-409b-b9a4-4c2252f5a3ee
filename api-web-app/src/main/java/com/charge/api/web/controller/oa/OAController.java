package com.charge.api.web.controller.oa;

import com.charge.apicloud.client.OAClient;
import com.charge.apicloud.dto.oa.request.OAFlowCallbackRequestDTO;
import com.charge.apicloud.dto.oa.response.OAFlowCallbackDTO;
import com.charge.apicloud.dto.oa.response.OAFlowCallbackResponseDTO;
import com.charge.apicloud.enums.OAApproveStatusEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.StringUtil;
import com.charge.general.client.audit.AuditFlowClient;
import com.charge.general.dto.AuditFlowDTO;
import com.charge.general.dto.condition.AuditFlowBatchQueryConditionDTO;
import com.charge.general.dto.condition.AuditFlowConfirmConditionDTO;
import com.charge.general.enums.AuditFlowConfigStatusEnum;
import com.charge.general.enums.AuditFlowTypeEnum;
import com.charge.general.enums.AuditStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping(value = "/oa")
public class OAController {

    private final OAClient oaClient;

    private final AuditFlowClient auditFlowClient;

    @RequestMapping(value = "/flow/callback", method = {RequestMethod.POST, RequestMethod.GET})
    public OAFlowCallbackResponseDTO callback(@RequestBody OAFlowCallbackRequestDTO requestDTO) throws ChargeBusinessException {
        OAFlowCallbackResponseDTO responseDTO = AppInterfaceUtil.getResponseDataThrowException(oaClient.flowCallback(requestDTO));
        OAFlowCallbackDTO callbackDTO = responseDTO.getDto();
        if (StringUtil.isEmpty(callbackDTO.getBusinessId())) {
            log.error("业务id为空");
            return responseDTO;
        }
        AuditFlowBatchQueryConditionDTO queryConditionDTO = new AuditFlowBatchQueryConditionDTO();
        queryConditionDTO.setFlowNo(callbackDTO.getBusinessId());
        List<AuditFlowDTO> list = AppInterfaceUtil.getResponseDataThrowException(auditFlowClient.listByCondition(queryConditionDTO));
        if (CollectionUtil.isEmpty(list)) {
            log.error("找不到对应的审核记录");
            return responseDTO;
        }
        // 调用general confirm
        AuditFlowConfirmConditionDTO condition = new AuditFlowConfirmConditionDTO();
        condition.setAuditType(AuditFlowTypeEnum.OA.getCode());
        condition.setAuditStatus(convertStatus(callbackDTO.getApproveStatus()));
        condition.setId(list.get(0).getId());
        condition.setCurrentUserId(-1L);
        condition.setCurrentUserName("OA-system");
        AppInterfaceUtil.getResponseDataThrowException(auditFlowClient.auditConfirm(condition));
        return responseDTO;
    }

    private Integer convertStatus(Integer approveStatus) {
        if (OAApproveStatusEnum.PASS.getCode().equals(approveStatus)) {
            return AuditStatusEnum.PASS.getCode();
        } else if (OAApproveStatusEnum.ABOLISH.getCode().equals(approveStatus)) {
            return AuditStatusEnum.CANCEL.getCode();
        } else if (OAApproveStatusEnum.REJECT.getCode().equals(approveStatus)) {
            return AuditStatusEnum.REJECT.getCode();
        }
        return AuditStatusEnum.REJECT.getCode();
    }
}
