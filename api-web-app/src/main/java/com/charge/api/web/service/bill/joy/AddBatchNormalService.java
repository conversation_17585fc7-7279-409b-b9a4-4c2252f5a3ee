package com.charge.api.web.service.bill.joy;

import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.vo.joylife.request.BatchPayDataRequest;
import com.charge.bill.client.flow.AssetPaymentClient;
import com.charge.bill.dto.BillAssetInfoDTO;
import com.charge.bill.dto.domain.*;
import com.charge.bill.dto.domain.response.CreateBillsResponse;
import com.charge.bill.dto.income.AssetArrearsListDTO;
import com.charge.bill.dto.income.AssetPayReceivableBillDTO;
import com.charge.bill.dto.income.AssetReceivalbeBillListDTO;
import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentMethodEnum;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.PaymentTypeEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 朝昔生活缴费# 只能改造的是billPayManager.createPayIncomeBills
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddBatchNormalService {

    private final AssetPaymentClient assetPaymentClient;

    /**
     *
     * @param pay           Request
     * @param communityId   项目ID
     * @param penaltyMoney  违约金金额
     * @param principalMoney 本金金额
     * @param assetArrearsLists 计算后的抵扣和缴费账单
     * @param deadLine      截止年月
     * @param houseCount    房间数量
     * @param orderNum
     * @param disAllFlag    是否为全额积分抵扣
     * @param pointsTransRecordId 积分交易记录ID
     * @return
     */
    public CreateBillsResponse createBills(BatchPayDataRequest pay, Long communityId, BigDecimal penaltyMoney, BigDecimal principalMoney,
                                           List<AssetArrearsListDTO> assetArrearsLists,
                                           String deadLine, int houseCount, String orderNum, boolean disAllFlag, Long pointsTransRecordId) throws ChargeBusinessException {

        AssetsPayDTO assetsPayDTO = new AssetsPayDTO();
        assetsPayDTO.setAssetPayBaseDTO(buildAssetPayBaseDTO(pay, communityId, penaltyMoney, principalMoney, deadLine, houseCount, orderNum, disAllFlag,pointsTransRecordId));
        assetsPayDTO.setAssetPaymentDetailDTOS(buildDetails(assetArrearsLists,assetsPayDTO.getAssetPayBaseDTO()));
        return AppInterfaceUtil.getResponseDataThrowException(assetPaymentClient.createBills(assetsPayDTO));
    }

    private AssetPayBaseDTO buildAssetPayBaseDTO(BatchPayDataRequest pay, Long communityId,
                                                BigDecimal penaltyMoney, BigDecimal principalMoney, String deadLine,
                                                int houseCount, String orderNum,boolean disAllFlag,
                                                Long pointsTransRecordId) {
        AssetPayBaseDTO assetPayBaseDTO = new AssetPayBaseDTO();
        assetPayBaseDTO.setOrderNum(orderNum);
        assetPayBaseDTO.setPayHouseCount(houseCount);
        assetPayBaseDTO.setActualPrice(new BigDecimal(pay.getActualPrice()));
        assetPayBaseDTO.setCommunityId(communityId);
        assetPayBaseDTO.setCommunityName(pay.getCommunityName());
        assetPayBaseDTO.setMemo(pay.getMemo());
        assetPayBaseDTO.setEndYear(deadLine);
//        assetPayBaseDTO.setTotalPrice(new BigDecimal(pay.getTotalPrice()));  // 实际无使用场景
        assetPayBaseDTO.setPrincipalMoney(principalMoney);
        assetPayBaseDTO.setPenaltyMoney(penaltyMoney);
        assetPayBaseDTO.setEquityMoney(new BigDecimal(StringUtils.isEmpty(pay.getPointsMoney())?"0":pay.getPointsMoney())); // 权益调整金额（含积分抵扣金额）
        assetPayBaseDTO.setEquityAccount(pay.getEquityAccount());
        String paymentChannel = Objects.equals(PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel(),pay.getPaymentSource())?PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel():fillPaymentChannel(pay.getPaymentMethod());
        assetPayBaseDTO.setPaymentChannel(disAllFlag? "":paymentChannel);
        assetPayBaseDTO.setPaymentMethod(disAllFlag? PaymentMethodEnum.EQUITY.getPaymentCode():fillPaymentMethod(pay.getPaymentMethod()));
        assetPayBaseDTO.setPaymentType(disAllFlag? PaymentTypeEnum.POINTS.getCode():PaymentTypeEnum.CASH.getCode());
        assetPayBaseDTO.setPaymentTerminal(PaymentTerminalEnum.handleJoyLifeWechatApplet(pay.getPaymentSource()));
        assetPayBaseDTO.setIsInvoiced(0);
        assetPayBaseDTO.setPayMember(pay.getUserName());
        assetPayBaseDTO.setPayMemberId(pay.getUserId());
        assetPayBaseDTO.setPoints(pay.getPoints() == null ? null : Integer.parseInt(pay.getPoints()));
        assetPayBaseDTO.setGoodsName(PayRelatedConstants.GOODSNAME_FOR_PROPERTY);
        assetPayBaseDTO.setPayMemberMobile(pay.getPhone());
        assetPayBaseDTO.setReceiptType(0);
        assetPayBaseDTO.setMsAssetOwnerId(pay.getCustomId());
        assetPayBaseDTO.setPointsTransRecordId(pointsTransRecordId);
        assetPayBaseDTO.setClientSourceEnum(ClientSourceEnum.JOY_BATCH_NORMAL_PAY);
        assetPayBaseDTO.setCreateUser(pay.getUserName());
        return assetPayBaseDTO;
    }


    /**
     * 构建缴费详情：由于一个资产并存现金缴和积分缴，因此资产:AssetPaymentDetailDTO  = 1:2(至多)
     * @param assetArrearsLists
     * @return
     */
    private List<AssetPaymentDetailDTO> buildDetails(List<AssetArrearsListDTO> assetArrearsLists,AssetPayBaseDTO assetPayBaseDTO) {
        return assetArrearsLists.stream().map(assetArrearsListDTO -> buildAssetPaymentDetailDTO(assetArrearsListDTO,assetPayBaseDTO)).collect(Collectors.toList());
    }

    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(AssetArrearsListDTO assetArrearsListDTO,AssetPayBaseDTO assetPayBaseDTO) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 构建资产信息
        assetPaymentDetailDTO.setBillAssetInfoDTO(buildBillAssetInfoDTO(assetArrearsListDTO));
        // 缴费的应收单信息
        assetPaymentDetailDTO.setReceivableBillDTOS(buildReceivableBillDTOs(assetArrearsListDTO.getDetailList()));
        // 基础信息
        assetPaymentDetailDTO.setAssetPayBusinessDTO(buildAssetPayCommonDTO(assetArrearsListDTO,assetPayBaseDTO));
        return assetPaymentDetailDTO;
    }

    /**
     * 构建资产信息
     * @param assetArrearsListDTO
     * @return
     */
    private BillAssetInfoDTO buildBillAssetInfoDTO(AssetArrearsListDTO assetArrearsListDTO) {
        BillAssetInfoDTO billAssetInfoDTO = new BillAssetInfoDTO();
        billAssetInfoDTO.setAssetId(assetArrearsListDTO.getAssetId());
        return billAssetInfoDTO;
    }
    private AssetPayBusinessDTO buildAssetPayCommonDTO(AssetArrearsListDTO assetArrearsListDTO,AssetPayBaseDTO assetPayBaseDTO) {
        AssetPayBusinessDTO assetPayBusinessDTO = new AssetPayBusinessDTO();
        assetPayBusinessDTO.setAssetTotalAmount(assetArrearsListDTO.getAssetTotalAmount());
        assetPayBusinessDTO.setPaymentMethod(assetArrearsListDTO.getPaymentMethod());
        assetPayBusinessDTO.setPaymentType(assetArrearsListDTO.getPaymentType());
        String paymentChannel = Objects.equals(assetArrearsListDTO.getPaymentMethod(),PaymentMethodEnum.EQUITY.getPaymentCode())?"":assetPayBaseDTO.getPaymentChannel();
        assetPayBusinessDTO.setPaymentChannel(paymentChannel);
        return assetPayBusinessDTO;
    }
    /**
     * 构建缴欠费应收单信息
     * @param assetReceivalbeBillListDTOS
     * @return
     */
    private List<AssetPayReceivableBillDTO> buildReceivableBillDTOs(List<AssetReceivalbeBillListDTO> assetReceivalbeBillListDTOS) {
        if (CollectionUtils.isEmpty(assetReceivalbeBillListDTOS)) {
            return Arrays.asList();
        }
        List<AssetPayReceivableBillDTO> payRecs = new ArrayList<>();
        assetReceivalbeBillListDTOS.forEach(receivableBill -> {
            // 违约金
            if (receivableBill.getItemPenaltyAmount().compareTo(BigDecimal.ZERO) > 0) {
                AssetPayReceivableBillDTO payRec = new AssetPayReceivableBillDTO();
                fillCommonParam(payRec, receivableBill);
                payRec.setPenaltyArrearsAmount(receivableBill.getItemPenaltyAmount());
                payRecs.add(payRec);
            }
            // 本金
            if (receivableBill.getItemArrearsAmount().compareTo(BigDecimal.ZERO) > 0) {
                AssetPayReceivableBillDTO payRec = new AssetPayReceivableBillDTO();
                fillCommonParam(payRec, receivableBill);
                payRec.setArrearsAmount(receivableBill.getItemArrearsAmount());
                payRecs.add(payRec);
            }
        });
        return payRecs;
    }
    private void fillCommonParam(AssetPayReceivableBillDTO payRec, AssetReceivalbeBillListDTO receivableBill) {
        payRec.setItemId(receivableBill.getItemId());
        payRec.setBelongYears(receivableBill.getBelongYears());
        payRec.setReceivableBillId(receivableBill.getBillId());
        payRec.setChargeObject(receivableBill.getChargeObject());
        payRec.setItemName(receivableBill.getItemName());
        payRec.setAssetUseStatus(receivableBill.getAssetUseStatus());
    }

    private Integer fillPaymentMethod(String paymentMethod){
        if (Objects.equals("3",paymentMethod)) {
            return PaymentMethodEnum.ALIPAY.getPaymentCode();
        }
        return PaymentMethodEnum.WECHAT.getPaymentCode();
    }
    private String fillPaymentChannel(String paymentMethod){
        if (Objects.equals("3",paymentMethod)) {
            return  PaymentChannelEnum.ALI_PAY.getPaymentChannel();
        }
        return PaymentChannelEnum.WECHAT_PAY.getPaymentChannel();
    }
}
