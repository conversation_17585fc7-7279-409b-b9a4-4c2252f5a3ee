package com.charge.api.web.vo.pos;

import com.charge.bill.dto.predeposit.ItemAccountDetailDTO;
import com.charge.bill.dto.predeposit.pos.PredepositAccountTotalDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PreDepositAccountPosDTOV1 {
    private PredepositAccountTotalDTO monty;
    private ItemAccountDetailDTO common;
    private List<ItemAccountDetailDTOV1> prestore;
}


