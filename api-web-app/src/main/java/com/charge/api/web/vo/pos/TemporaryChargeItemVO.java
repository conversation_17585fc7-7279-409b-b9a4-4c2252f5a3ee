package com.charge.api.web.vo.pos;


import lombok.Data;

import java.io.Serializable;


@Data
public class TemporaryChargeItemVO implements Serializable {
    private static final long serialVersionUID = 2307268334997226840L;

    /**
     * 收费项Id
     */
    private String itemUuid;
    /**
     * 收费项名称
     */
    private String itemName;
    /**
     * 已选 1/待选 0
     */
    private Integer selected;
    /**
     * 是否开启收费细项(0表示开启,1表示关闭)
     */
    private String detailStatus;
    /**
     * 小区id
     */
    private String communityUuid;
}
