package com.charge.api.web.service.impl.parking;

import com.charge.api.web.dto.parking.*;
import com.charge.api.web.enums.ParkingInvoiceModeEnum;
import com.charge.api.web.service.parking.PlatformBankBusinessFlowService;
import com.charge.api.web.service.parking.PlatformBankFlowService;
import com.charge.api.web.support.ChargeItemSupport;
import com.charge.api.web.util.EcsbResponseUtils;
import com.charge.api.web.vo.CheckParamFailedInfo;
import com.charge.api.web.vo.parking.InvoiceCommunityConfigVO;
import com.charge.api.web.vo.parking.InvoiceCommunityItemTaxConfigVO;
import com.charge.api.web.vo.parking.InvoiceSellerVO;
import com.charge.api.web.vo.parking.InvoiceTaxDeviceVO;
import com.charge.bill.client.*;
import com.charge.bill.dto.income.AssetBillInfoDTO;
import com.charge.bill.dto.income.AssetTransactionDTO;
import com.charge.bill.dto.income.IncomeBillDTO;
import com.charge.bill.dto.income.OrderBillDetailDTO;
import com.charge.bill.dto.tempark.ParkingSyncRecordConditionDTO;
import com.charge.bill.dto.tempark.ParkingSyncRecordDTO;
import com.charge.bill.dto.tempark.TemparkPayDetailDTO;
import com.charge.bill.enums.BalanceStatusEnum;
import com.charge.common.constant.CommonConstant;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeStatusEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.enums.redis.RedisKeyCommonEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.config.client.item.CommunityChargeItemClient;
import com.charge.config.dto.item.CommunityChargeItemDTO;
import com.charge.config.dto.item.condition.CommunityChargeItemQueryConditionDTO;
import com.charge.config.dto.tax.TaxDetailDTO;
import com.charge.config.enums.DeletedEnum;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.StringUtil;
import com.charge.core.util.TraceContextUtil;
import com.charge.invoice.client.InvoiceSellerConfigClient;
import com.charge.invoice.client.TaxCateClient;
import com.charge.invoice.dto.PageTaxDeviceRequestDTO;
import com.charge.invoice.dto.SellerDTO;
import com.charge.invoice.dto.TaxCateDTO;
import com.charge.invoice.dto.TaxDeviceDTO;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.order.client.OrderClient;
import com.charge.order.client.OrderRuleClient;
import com.charge.order.dto.ChargeItem;
import com.charge.order.dto.OrderDTO;
import com.charge.order.dto.rule.BaseOrderRuleDTO;
import com.charge.order.dto.rule.CommunityBaseOrderRuleDTO;
import com.charge.order.dto.rule.OrderRuleEnableDTO;
import com.charge.order.dto.rule.OrderRuleQueryDTO;
import com.charge.order.enums.EnableEnum;
import com.charge.order.enums.OrderRuleCategoryEnum;
import com.charge.starter.jedis.JedisManager;
import com.charge.user.dto.common.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.converters.BigDecimalConverter;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 获取云平台银行收款业务流水接口实现类
 * Author: yjw
 * Date: 2023/5/11 14:46
 */

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PlatformBankFlowServiceImpl implements PlatformBankFlowService {

    private final JedisManager jedisManager;

    private final CommunityClient communityClient;

    private final OrderRuleClient orderRuleClient;

    private final CommunityChargeItemClient communityChargeItemClient;

    private final PlatformBankBusinessFlowService platformBankBusinessFlowServiceImpl;

    private final ParkingSyncRecordClient parkingSyncRecordClient;

    private final IncomeBillClient incomeBillClient;

    private final AssetTransactionClient assetTransactionClient;

    private final AssetBillInfoClient assetBillInfoClient;

    private final OrderClient orderClient;

    private final OrderBillDetailClient orderBillDetailClient;

    private final InvoiceSellerConfigClient invoiceSellerConfigClient;

    private final ChargeItemSupport chargeItemSupport;

    private final  TaxCateClient  taxCateClient;

    private final static String INVOICE_APPLY_PARAM_TAX_PRE="1";
    private final static String INVOICE_APPLY_ZERO_TAX_PRE="免税";
    private final static String INVOICE_APPLY_PARAM_TAX_PRE_CON="按%s简易征收";
    /**
     * 简易征收
     */
    private final static Integer TAX_CALCULATE_METHOD_SIMPLE =1;
    @Override
    public ResultResponse getPlatformBankBusinessFlow(GetPlatformBankFlowParam getPlatformBankFlowParam) throws ChargeBusinessException {

        log.info("停车场传入参数：{}", getPlatformBankFlowParam);

        CheckParamFailedInfo checkParamFailedInfo = new CheckParamFailedInfo("", "");
        List<ParkingSyncRecordDTO> parkingRecordList = new ArrayList<>();
        if(!checkPlatformBankBusinessInfo(getPlatformBankFlowParam, parkingRecordList, checkParamFailedInfo)){
            return EcsbResponseUtils.generatorResponseError("", new SsdpResultContent(Result.CODE_FAILED, checkParamFailedInfo.getCheckFailedMsg(), checkParamFailedInfo.getCheckFailedContent()));
        }

        // 数据入库
        try {
            ChargeResponse chargeResponse = platformBankBusinessFlowServiceImpl.insertPlatformBankBusinessFlow(parkingRecordList);
            if (!chargeResponse.isSuccess()) {
                return EcsbResponseUtils.generatorResponseError("", new SsdpResultContent(Result.CODE_FAILED, "数据入库失败！", chargeResponse.getMessage()));
            }

        } catch (ChargeBusinessException e) {
            log.warn("{}|写入车辆临停订单表业务异常|{}", LogCategoryEnum.BUSSINESS, parkingRecordList, e);
            return EcsbResponseUtils.generatorResponseError("", new SsdpResultContent(Result.CODE_FAILED, e.getMessage(), e.getMessage()));
        } catch (Exception e) {
            log.error("{}|写入车辆临停订单表未知异常|{}", LogCategoryEnum.BUSSINESS, parkingRecordList, e);
            return EcsbResponseUtils.generatorResponseError("", new SsdpResultContent(Result.CODE_FAILED, e.getMessage(), e.getMessage()));
        }

        return EcsbResponseUtils.generatorResponseSuccess("", new SsdpResultContent(Result.CODE_SUCCESS, "调用成功！", "success"));
    }

    private String getCommunityIdName(String communityCode) throws ChargeBusinessException {
        String cacheCommunityCodeKey = RedisKeyCommonEnum.PARKING_COMMUNITY_CODE_NAME_MAPPING.key(communityCode);
        String communityIdOrgIdName = jedisManager.getString(cacheCommunityCodeKey);

        if (StringUtil.isEmpty(communityIdOrgIdName)) {
            log.info("redis中小区id机构id名称数据已过期，重新进行查询！");
            CommunityDTO communityDTO =AppInterfaceUtil.getResponseDataThrowException(communityClient.oneByCondition(CommunityCondition.builder().communityCode(communityCode).build())) ;
           if(Objects.isNull(communityDTO)){
               return null;
           }
            communityIdOrgIdName = String.join(",", String.valueOf(communityDTO.getId()), communityDTO.getName());
            log.info("向redis中储存的数据为：{}", communityIdOrgIdName);
            jedisManager.setString(cacheCommunityCodeKey, communityIdOrgIdName, DateUtils.DataTimeSec.TEN_MIN);

        }
        return communityIdOrgIdName;
    }

    /**
     * 获取订单规则-停车场
     *
     * @return
     */
    private List<BaseOrderRuleDTO> fetchOrderRule() {
        OrderRuleQueryDTO orderRuleQueryDTO = new OrderRuleQueryDTO();
        orderRuleQueryDTO.setEnable(EnableEnum.ENABLE.getCode());
        ChargeResponse<PagingDTO<BaseOrderRuleDTO>> baseOrderRuleDTOResponse = orderRuleClient.getPage(orderRuleQueryDTO);
        PagingDTO<BaseOrderRuleDTO> pagingDTO = AppInterfaceUtil.getResponseData(baseOrderRuleDTOResponse);
        if (Objects.isNull(pagingDTO) || CollectionUtil.isEmpty(pagingDTO.getList())) {
            log.error("{}|查询该小区收费项配置失败-无法获取车辆临停订单规则", LogCategoryEnum.BUSSINESS);
            return Collections.emptyList();
        }
        return pagingDTO.getList().stream().filter(item -> Integer.parseInt(item.getSecondClassificationId()) == OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_PARK_400.getCode())
                .collect(Collectors.toList());
    }

    public ParkingSyncRecordDTO copyToParkingSyncRecordDTO(PlatformBankFlowRecord platformBankFlowRecord, Long communityId, Long chargeItemId, String chargeItemName) throws InvocationTargetException, IllegalAccessException {

        ParkingSyncRecordDTO parkingSyncRecordDTO = BeanCopierWrapper.copy(platformBankFlowRecord, ParkingSyncRecordDTO.class);
        ConvertUtils.register(new DateConverter(null), java.util.Date.class);

        BigDecimalConverter bd = new BigDecimalConverter(BigDecimal.ZERO);
        ConvertUtils.register(bd, java.math.BigDecimal.class);


        //交易日期 日期处理
        Date chargeTime = platformBankFlowRecord.getChargeTime();
        Calendar cal = Calendar.getInstance();
        cal.setTime(chargeTime);
        // 24小时制
        cal.add(Calendar.MINUTE, 1439);
        chargeTime = cal.getTime();
        parkingSyncRecordDTO.setChargeTime(chargeTime);
        parkingSyncRecordDTO.setCommunityId(communityId);
        parkingSyncRecordDTO.setItemId(chargeItemId);
        parkingSyncRecordDTO.setItemName(chargeItemName);

        return parkingSyncRecordDTO;
    }

    @Override
    public ResultResponse historyDataDelete(DeleteHistoryDataParam deleteHistoryDataParam) {
        if(!checkParam(deleteHistoryDataParam)){
            log.info("{}|参数校验失败，参数缺失", LogCategoryEnum.BUSSINESS);
            return EcsbResponseUtils.generatorResponseError("", new SsdpResultContent(Result.CODE_FAILED, "参数校验失败", "参数校验失败"));
        }
        try {
            /*校验是否存在同一天同一车场同一支付渠道的数据，有则进行软删，无则直接返回*/
            ParkingSyncRecordConditionDTO parkingSyncRecordConditionDTO = ParkingSyncRecordConditionDTO.builder()
                    .parkingId(deleteHistoryDataParam.getParkingId()).chargeDate(DateUtils.format(deleteHistoryDataParam.getChargeTime(), DateUtils.FORMAT_1))
                    .paymentChannel(deleteHistoryDataParam.getPaymentChannel()).build();
            ChargeResponse<TemparkPayDetailDTO> parkingRecordResponse = parkingSyncRecordClient.getRecord(parkingSyncRecordConditionDTO);
            if(!parkingRecordResponse.isSuccess()){
                throw new ChargeBusinessException("101", "该条数据软删失败（查询临停订单记录失败）:" + parkingRecordResponse.getMessage());
            }
            if(Objects.isNull(parkingRecordResponse.getContent())){
                return EcsbResponseUtils.generatorResponseSuccess("", new SsdpResultContent(Result.CODE_SUCCESS, "调用成功！", "success"));
            }
            TemparkPayDetailDTO temparkPayDetailDTO = parkingRecordResponse.getContent();
            String modifyUser = StringUtils.isNotBlank(deleteHistoryDataParam.getModifyUser()) ? deleteHistoryDataParam.getModifyUser() : "parking";
            TraceContextUtil.setCommunityId(temparkPayDetailDTO.getCommunityId());
            AssetTransactionDTO assetTransactionDTO = AppInterfaceUtil.getResponseDataThrowException(assetTransactionClient.getById(temparkPayDetailDTO.getAssetTransactionId()));

            if(Objects.isNull(assetTransactionDTO)){
                log.error("{}|查询交易流水为空，返回删除失败，assetTransactionId:{}", LogCategoryEnum.BUSSINESS, temparkPayDetailDTO.getAssetTransactionId());
                throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "交易流水查询结果为空");
            }
            //校验对账状态，已对账返回报错提示
            checkIncomeBillBalanceStatus(assetTransactionDTO.getIncomeId());

            deleteParkingBill(temparkPayDetailDTO, assetTransactionDTO, modifyUser);
        } catch (Exception e){
            log.error("{}|数据删除失败", LogCategoryEnum.BUSSINESS, e);
            return EcsbResponseUtils.generatorResponseError("", new SsdpResultContent(Result.CODE_FAILED, e.getMessage(), e.getMessage()));
        }
        return EcsbResponseUtils.generatorResponseSuccess("", new SsdpResultContent(Result.CODE_SUCCESS, "调用成功！", "success"));
    }

    @Override
    public   ChargeResponse<InvoiceCommunityConfigVO> getInvoiceCommunityConfig(String communityCode) throws ChargeBusinessException {
        ChargeResponse response=new ChargeResponse();
        Long communityId=getCommunityIdFromRedisValue(communityCode);
        log.info("{}|【查询小区发票配置】根据小区code={},查询到数据为：", LogCategoryEnum.BUSSINESS, communityCode, communityId);
        InvoiceCommunityConfigVO result = new InvoiceCommunityConfigVO();
        SellerDTO sellerDTO = AppInterfaceUtil.getResponseDataThrowException(invoiceSellerConfigClient.getSellerByCommunity(communityId));
        if(Objects.nonNull(sellerDTO)){
            result.setInvoiceSellerVO(BeanCopierWrapper.copy(sellerDTO, InvoiceSellerVO.class));
        }
        PageTaxDeviceRequestDTO request=new PageTaxDeviceRequestDTO();
        request.setCommunityId(communityId);
        request.setPageNum(CommonConstant.PAGE_NO);
        request.setPageSize(CommonConstant.PAGE_SIZE);
        PagingDTO<TaxDeviceDTO> taxDeviceSimpleDTOS = AppInterfaceUtil.getResponseDataThrowException(invoiceSellerConfigClient.pageTaxDevice(request));
        /**一个项目只能有一个税控设别*/
        List<Integer> allowInvoiceTypes=null;
        if(Objects.nonNull(taxDeviceSimpleDTOS) && CollectionUtil.isNotEmpty(taxDeviceSimpleDTOS.getList())){
            result.setInvoiceTaxDeviceVO(BeanCopierWrapper.copy(taxDeviceSimpleDTOS.getList().get(0), InvoiceTaxDeviceVO.class));
            allowInvoiceTypes= Arrays.stream(taxDeviceSimpleDTOS.getList().get(0).getAllowInvoiceType().split(CommonConstant.COMMA)).map(Integer::valueOf).collect(Collectors.toList());
        }
        ParkingInvoiceModeEnum invoiceMode = ParkingInvoiceModeEnum.fromInvoiceTypeCode(allowInvoiceTypes);
        result.setInvoiceMode(invoiceMode.getCode());
        result.setMessage(invoiceMode.getValue());
        result.setInvoiceTypeList(allowInvoiceTypes);
        response.setContent(result);
        response.setResponseStatus(ChargeStatusEnum.SUCCESS);
        return response;
    }

    @Override
    public ChargeResponse<InvoiceCommunityItemTaxConfigVO> getInvoiceChargeItemConfig(String communityCode, String itemCode) throws ChargeBusinessException {
        ChargeResponse response=new ChargeResponse();
        Long communityId=getCommunityIdFromRedisValue(communityCode);
        CommunityChargeItemQueryConditionDTO chargeItemQueryConditionDTO = new CommunityChargeItemQueryConditionDTO();
        chargeItemQueryConditionDTO.setItemCodes(Arrays.asList(itemCode));
        chargeItemQueryConditionDTO.setCommunityId(communityId);
        CommunityChargeItemDTO chargeItemDTO =AppInterfaceUtil.getResponseDataThrowException(communityChargeItemClient.queryChargeItemDetail(chargeItemQueryConditionDTO));
        Assert.notNull(chargeItemDTO, " 收费项为空");
        Assert.notNull(chargeItemDTO.getTaxCode(), "收费项配置税为空");
        Assert.notNull(chargeItemDTO.getTaxId(), "项目收费项没有设置税率");
        List<TaxDetailDTO> taxDetailDTOList = chargeItemDTO.getTaxRates();
        Assert.notEmpty(taxDetailDTOList, "项目收费项没有设置税率");
        TaxDetailDTO taxDetailDTO = chargeItemSupport.getTaxDetailDTO(chargeItemDTO.getTaxId(), taxDetailDTOList);
        Assert.notNull(taxDetailDTO, "项目收费项没有设置税率");
        //这里获取对应税务编码的信息
        TaxCateDTO taxCateDTO = AppInterfaceUtil.getResponseDataThrowException(taxCateClient.getTaxCateById(Long.parseLong(chargeItemDTO.getTaxCode())));
        Assert.notNull(taxCateDTO, "项目收费项未设置税收分类编码");
        InvoiceCommunityItemTaxConfigVO taxDO = new InvoiceCommunityItemTaxConfigVO();
        taxDO.setCode(taxCateDTO.getCode());
        taxDO.setGoodsName(taxCateDTO.getGoodsName());
        taxDO.setGoodsServiceCate(taxCateDTO.getGoodsServiceCate());
        taxDO.setTaxRate(taxDetailDTO.getTaxPoint());
        if(Objects.equals(TAX_CALCULATE_METHOD_SIMPLE,taxDetailDTO.getTaxCalculateMethod())){
            String taxPercentage=taxDO.getTaxRate().setScale(2, RoundingMode.HALF_UP).setScale(0, RoundingMode.HALF_UP) +  "%";
            taxDO.setTaxPre(INVOICE_APPLY_PARAM_TAX_PRE);
            if(taxDO.getTaxRate().compareTo(BigDecimal.ZERO)<=0){
                taxDO.setTaxPreCon(INVOICE_APPLY_ZERO_TAX_PRE);
            }else{
                taxDO.setTaxPreCon(String.format(INVOICE_APPLY_PARAM_TAX_PRE_CON,taxPercentage));
            }
        }
        taxDO.setItemName(chargeItemDTO.getItemName());
        taxDO.setItemCode(chargeItemDTO.getItemCode());
        response.setContent(taxDO);
        response.setCode(ChargeStatusEnum.SUCCESS.getCode());
        return response;
    }

    private Long getCommunityIdFromRedisValue(String communityCode) throws ChargeBusinessException {
        String communityIdOrgIdName = getCommunityIdName(communityCode);
        if (StringUtils.isEmpty(communityIdOrgIdName)) {
            throw new ChargeBusinessException("无法查询到所有小区id请检查！");
        }
        log.info("{}|【查询小区发票配置】根据小区code={},查询到数据为：{}", LogCategoryEnum.BUSSINESS, communityCode, communityIdOrgIdName);
        String[] split = communityIdOrgIdName.split(CommonConstant.COMMA);
        String communityId = split[0];
        return Long.parseLong(communityId);
    }

    private void deleteParkingBill(TemparkPayDetailDTO temparkPayDetailDTO, AssetTransactionDTO assetTransactionDTO, String modifyUser) throws ChargeBusinessException {

        assetTransactionDTO.setModifyUser(modifyUser);
        //删除实收单[income_bill]
        deleteIncomeBill(assetTransactionDTO);
        //删除流水信息表数据[asset_bill_info]
        deleteAssetBillInfo(assetTransactionDTO);
        //删除订单&订单明细[base_order & order_bill_detail]
        deleteOrderRelated(temparkPayDetailDTO, modifyUser);
        //删除临停订单业务流水[parking_sync_record]
        deleteParkingSyncRecord(temparkPayDetailDTO, modifyUser);
        //删除资产流水单[asset_transaction]
        deleteAssetTransaction(assetTransactionDTO);

    }

    /**
     * 删除实收单
     * @param assetTransactionDTO
     * @throws ChargeBusinessException
     */
    private void deleteIncomeBill(AssetTransactionDTO assetTransactionDTO) throws ChargeBusinessException {
        IncomeBillDTO incomeBillDTO = new IncomeBillDTO();
        incomeBillDTO.setId(assetTransactionDTO.getIncomeId());
        incomeBillDTO.setDeleted(DeletedEnum.DELETE.getCode());
        incomeBillDTO.setCommunityId(assetTransactionDTO.getCommunityId());
        incomeBillDTO.setModifyTime(DateUtils.getCurrentTimestamp());
        incomeBillDTO.setModifyUser(assetTransactionDTO.getModifyUser());
        ChargeResponse chargeResponse = incomeBillClient.updateByIdAndCommunityId(incomeBillDTO);
        if(!chargeResponse.isSuccess()){
            throw new ChargeBusinessException("101", "该条实收单删除失败:" + chargeResponse.getMessage());
        }
        log.info("{}|车辆临停删除实收单，incomeBillDTO：{}", LogCategoryEnum.BUSSINESS, incomeBillDTO);
    }

    /**
     *  删除资产流水
     * @param assetTransactionDTO
     * @return
     * @throws ChargeBusinessException
     */
    private void deleteAssetTransaction(AssetTransactionDTO assetTransactionDTO) throws ChargeBusinessException {
        AssetTransactionDTO transactionDTO = new AssetTransactionDTO();
        transactionDTO.setId(assetTransactionDTO.getId());
        transactionDTO.setDeleted(DeletedEnum.DELETE.getCode());
        transactionDTO.setModifyUser(assetTransactionDTO.getModifyUser());
        transactionDTO.setModifyTime(DateUtils.getCurrentTimestamp());
        transactionDTO.setCommunityId(assetTransactionDTO.getCommunityId());
        ChargeResponse chargeResponse = assetTransactionClient.updateByIdAndCommunityId(transactionDTO);
        if(!chargeResponse.isSuccess()){
            throw new ChargeBusinessException("101", "该条资产交易流水信息删除失败:" + chargeResponse.getMessage());
        }
        log.info("{}|车辆临停删除资产交易流水，transactionDTO：{}", LogCategoryEnum.BUSSINESS, transactionDTO);
    }

    /**
     * 删除流水信息表数据
     * @param assetTransactionDTO
     * @throws ChargeBusinessException
     */
    private void deleteAssetBillInfo(AssetTransactionDTO assetTransactionDTO) throws ChargeBusinessException {
        AssetBillInfoDTO assetBillInfoDTO = AssetBillInfoDTO.builder().assetTransactionId(assetTransactionDTO.getId()).deleted(DeletedEnum.DELETE.getCode()).communityId(assetTransactionDTO.getCommunityId()).build();
        assetBillInfoDTO.setModifyUser(assetTransactionDTO.getModifyUser());
        assetBillInfoDTO.setModifyTime(DateUtils.getCurrentTimestamp());
        ChargeResponse chargeResponse = assetBillInfoClient.updateByTransactionId(assetBillInfoDTO);
        if(!chargeResponse.isSuccess()){
            throw new ChargeBusinessException("101", "该条资产订单信息删除失败:" + chargeResponse.getMessage());
        }
        log.info("{}|车辆临停删除资产订单信息，assetBillInfoDTO：{}", LogCategoryEnum.BUSSINESS, assetBillInfoDTO);
    }

    private void deleteOrderRelated(TemparkPayDetailDTO temparkPayDetailDTO, String modifyUser) throws ChargeBusinessException {

        //删除订单表[base_order]
        deleteOrder(temparkPayDetailDTO, modifyUser);

        //删除订单明细信息order_bill_detail
        deleteOrderBillDetail(temparkPayDetailDTO, modifyUser);
    }

    private void deleteOrder(TemparkPayDetailDTO temparkPayDetailDTO, String modifyUser) throws ChargeBusinessException {
        OrderDTO orderDTO = OrderDTO.builder().id(temparkPayDetailDTO.getOrderId()).deleted(true).build();
        orderDTO.setUpdateAt(DateUtils.getCurrentTimestamp());
        orderDTO.setUpdateBy(modifyUser);
        orderDTO.setCommunityId(temparkPayDetailDTO.getCommunityId());
        ChargeResponse chargeResponse = orderClient.update(orderDTO);
        if(!chargeResponse.isSuccess()){
            throw new ChargeBusinessException("101", "该条订单删除失败:" + chargeResponse.getMessage());
        }
        log.info("{}|车辆临停订单base_order删除，orderDTO：{}", LogCategoryEnum.BUSSINESS, orderDTO);
    }

    private void deleteOrderBillDetail(TemparkPayDetailDTO temparkPayDetailDTO, String modifyUser) throws ChargeBusinessException {
        OrderBillDetailDTO billDetailDTO = OrderBillDetailDTO.builder().orderId(temparkPayDetailDTO.getOrderId())
                .deleted(DeletedEnum.DELETE.getCode()).communityId(temparkPayDetailDTO.getCommunityId())
                .build();
        billDetailDTO.setModifyUser(modifyUser);
        billDetailDTO.setModifyTime(DateUtils.getCurrentTimestamp());
        ChargeResponse chargeResponse = orderBillDetailClient.updateByOrderId(billDetailDTO);
        if(!chargeResponse.isSuccess()){
            throw new ChargeBusinessException("101", "该条订单明细数据删除失败:" + chargeResponse.getMessage());
        }
        log.info("{}|订单明细数据删除，billDetailDTO：{}", LogCategoryEnum.BUSSINESS, billDetailDTO);
    }

    private void deleteParkingSyncRecord(TemparkPayDetailDTO temparkPayDetailDTO, String modifyUser) throws ChargeBusinessException {
        ParkingSyncRecordDTO parkingSyncRecordDTO = new ParkingSyncRecordDTO();
        parkingSyncRecordDTO.setId(temparkPayDetailDTO.getId());
        parkingSyncRecordDTO.setDeleted(DeletedEnum.DELETE.getCode());
        parkingSyncRecordDTO.setModifyUser(modifyUser);
        parkingSyncRecordDTO.setModifyTime(DateUtils.getCurrentTimestamp());
        ChargeResponse<Long> parkingSyncRecordResponse = parkingSyncRecordClient.update(parkingSyncRecordDTO);
        if(!parkingSyncRecordResponse.isSuccess()){
            throw new ChargeBusinessException("101", "该条临停交易流水数据删除失败:" + parkingSyncRecordResponse.getMessage());
        }
        log.info("{}|车辆临停删除业务流水，parkingSyncRecordDTO：{}", LogCategoryEnum.BUSSINESS, parkingSyncRecordDTO);
    }

    private boolean checkParam(DeleteHistoryDataParam deleteHistoryDataParam){
        if(Objects.isNull(deleteHistoryDataParam.getChargeTime()) || StringUtils.isBlank(deleteHistoryDataParam.getParkingId())
            || StringUtils.isBlank(deleteHistoryDataParam.getPaymentChannel())){
            return false;
        }
        return true;
    }

    private boolean checkPlatformBankBusinessInfo(GetPlatformBankFlowParam getPlatformBankFlowParam, List<ParkingSyncRecordDTO> parkingRecordList,
                                                  CheckParamFailedInfo checkParamFailedInfo){
        //项目编码
        String communityCode = getPlatformBankFlowParam.getRecordList().get(0).getCommunityId();
        // 根据小区code查询小区id，再根据小区id查询机构id和机构name进行补全
        if (StringUtils.isEmpty(communityCode)) {
            checkParamFailedInfo.setCheckFailedMsg("小区id查询失败！");
            checkParamFailedInfo.setCheckFailedContent("输入小区code不能为空");
            return false;
        }
        String cacheCommunityCodeKey = RedisKeyCommonEnum.PARKING_COMMUNITY_CODE_NAME_MAPPING.key(communityCode);
        String communityIdOrgIdName = jedisManager.getString(cacheCommunityCodeKey);

        if (StringUtil.isEmpty(communityIdOrgIdName)) {

            log.info("redis中小区id机构id名称数据已过期，重新进行查询！");

            ChargeResponse<CommunityDTO> communityResponse = communityClient.oneByCondition(CommunityCondition.builder().communityCode(communityCode).build());
            if (!communityResponse.isSuccess() || communityResponse.getContent() == null) {
                checkParamFailedInfo.setCheckFailedMsg("无法查询到所有小区id请检查！");
                checkParamFailedInfo.setCheckFailedContent("查询小区信息失败");
                return false;
            }

            CommunityDTO communityDTO = communityResponse.getContent();
            communityIdOrgIdName = String.join(",", String.valueOf(communityDTO.getId()), communityDTO.getName());
            log.info("向redis中储存的数据为：{}", communityIdOrgIdName);
            jedisManager.setString(cacheCommunityCodeKey, communityIdOrgIdName, DateUtils.DataTimeSec.TEN_MIN);

        }

        // 业务流水list
        List<PlatformBankFlowRecord> recordList = getPlatformBankFlowParam.getRecordList();

        if (CollectionUtil.isEmpty(recordList)) {
            checkParamFailedInfo.setCheckFailedMsg("停车场收费业务流水list为空，请检查后重新传输！");
            return false;
        }
        // 本批次数据总条数
        int currentNum = getPlatformBankFlowParam.getCurrentNum();
        if (recordList.size() != currentNum) {
            checkParamFailedInfo.setCheckFailedMsg("本批次数据总条数currentNum与list数量不一致请检查！");
            return false;
        }

        // 获取收费项配置(先获取车辆临停订单规则ID，再获取该项目配置的车辆临停收费项）
        List<BaseOrderRuleDTO> orderRuleList = fetchOrderRule();
        if (CollectionUtil.isEmpty(orderRuleList)) {
            checkParamFailedInfo.setCheckFailedMsg("查询该小区收费项配置失败！");
            checkParamFailedInfo.setCheckFailedContent("获取车辆临停订单规则为空");
            return false;
        } else {
            List<ChargeItem> items = orderRuleList.get(0).getChargeItemIds();
            List<String> itemIds = items.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());
            if(!itemIds.contains(getPlatformBankFlowParam.getRecordList().get(0).getItemId())){
                checkParamFailedInfo.setCheckFailedMsg("收费项未在总部启用，请核实");
                checkParamFailedInfo.setCheckFailedContent("收费项未在总部启用，请核实");
                return false;
            }
        }

        if(!checkPlatformBankBusinessData(parkingRecordList, recordList, orderRuleList, communityIdOrgIdName, checkParamFailedInfo)){
            return false;
        }

        return true;
    }

    private boolean checkPlatformBankBusinessData(List<ParkingSyncRecordDTO> parkingRecordList, List<PlatformBankFlowRecord> recordList,
                                                  List<BaseOrderRuleDTO> orderRuleList, String communityIdOrgIdName,
                                                  CheckParamFailedInfo checkParamFailedInfo){
        for (PlatformBankFlowRecord platformBankFlowRecord : recordList) {
            try {
                // 收费项id
                String itemIdV2 = platformBankFlowRecord.getItemId();
                String itemNameV2 = platformBankFlowRecord.getItemName();
                // 小区id
                String communityCode = platformBankFlowRecord.getCommunityId();

                if (StringUtils.isEmpty(communityIdOrgIdName)) {
                    checkParamFailedInfo.setCheckFailedMsg("根据输入小区code:" + communityCode + "，无法查询到小区id及机构id，name请检查！");
                    return false;
                }

                log.info("根据小区code={},查询到数据为：", communityCode, communityIdOrgIdName);
                String[] split = communityIdOrgIdName.split(",");
                String communityId = split[0];
                String communityName = split[1];
                platformBankFlowRecord.setCommunityCode(communityCode);
                platformBankFlowRecord.setCommunityId(communityId);

                if(!checkChargeItemStatus(communityId, communityName, itemIdV2, itemNameV2, orderRuleList, checkParamFailedInfo)){
                    return false;
                }

                // 属性转换
                ParkingSyncRecordDTO parkingSyncRecordDTO = copyToParkingSyncRecordDTO(platformBankFlowRecord, Long.parseLong(communityId), Long.parseLong(itemIdV2), itemNameV2);
                parkingRecordList.add(parkingSyncRecordDTO);

            } catch (Exception e) {
                log.error("获取云平台银行收款业务流水失败|{}", platformBankFlowRecord, e);
                checkParamFailedInfo.setCheckFailedMsg("接口内部异常！");
                checkParamFailedInfo.setCheckFailedContent(e.getMessage());
                return false;
            }

        }
        return true;
    }

    private boolean checkChargeItemStatus(String communityId, String communityName, String itemIdV2, String itemNameV2,
                                          List<BaseOrderRuleDTO> orderRuleList, CheckParamFailedInfo checkParamFailedInfo){

        Long baseOrderRuleId = orderRuleList.get(0).getId();
        OrderRuleEnableDTO orderRuleEnableDTO = new OrderRuleEnableDTO();
        orderRuleEnableDTO.setCommunityId(Long.parseLong(communityId));
        orderRuleEnableDTO.setBaseOrderRuleId(baseOrderRuleId);
        ChargeResponse<CommunityBaseOrderRuleDTO> communityBaseOrderRuleResponse = orderRuleClient.communityDetail(orderRuleEnableDTO);
        if (!communityBaseOrderRuleResponse.isSuccess() || Objects.isNull(communityBaseOrderRuleResponse.getContent())) {
            checkParamFailedInfo.setCheckFailedMsg("查询该小区收费项配置失败！");
            checkParamFailedInfo.setCheckFailedContent("输入小区code没有配置收费项");
            return false;
        }

        //校验传入的收费项
        CommunityChargeItemQueryConditionDTO itemConditionDTO = new CommunityChargeItemQueryConditionDTO();
        itemConditionDTO.setItemId(Long.parseLong(itemIdV2));
        itemConditionDTO.setCommunityId(Long.parseLong(communityId));
        ChargeResponse<CommunityChargeItemDTO> communityItemResponse = communityChargeItemClient.queryChargeItemDetail(itemConditionDTO);
        if (!communityItemResponse.isSuccess() || communityItemResponse.getContent() == null) {
            checkParamFailedInfo.setCheckFailedMsg("查询该收费项配置失败！");
            checkParamFailedInfo.setCheckFailedContent("查询该收费项配置失败！");
            return false;
        }
        // 禁用则报错
        if (1L == communityItemResponse.getContent().getStatus()) {
            checkParamFailedInfo.setCheckFailedMsg("小区：" + communityName + " 该收费项：" + itemNameV2 + " 配置状态为禁用！");
            checkParamFailedInfo.setCheckFailedContent("小区：" + communityName + " 该收费项：" + itemNameV2 + " 配置状态为禁用！");
            return false;
        }
        return true;
    }

    /**
     * 校验实收单是否已完成对账
     * @param incomeId
     * @throws ChargeBusinessException
     */
    private void checkIncomeBillBalanceStatus(Long incomeId) throws ChargeBusinessException{
        IncomeBillDTO incomeBillDTO = AppInterfaceUtil.getResponseDataThrowException(incomeBillClient.getIncomeBillById(incomeId));
        if(Objects.isNull(incomeBillDTO)){
            log.warn("{}|查询交易实收单为空，返回删除失败，incomeId:{}", LogCategoryEnum.BUSSINESS, incomeId);
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "交易实收单查询结果为空");
        }
        if(BalanceStatusEnum.RECONCILED.getCode().equals(incomeBillDTO.getBalanceStatus())){
            log.warn("{}|交易实收单已对账，不支持删除，incomeId:{}", LogCategoryEnum.BUSSINESS, incomeId);
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "交易实收单已对账，不支持删除");
        }
    }

}
