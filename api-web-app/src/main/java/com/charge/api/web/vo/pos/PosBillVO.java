package com.charge.api.web.vo.pos;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/9/11
 */
@NoArgsConstructor
@Data
public class PosBillVO implements Serializable {

    private String chargeTypeId;
   
    private String unitName;
   
    private String subjectType;
   
    private String subjectId;
   
    private String createMonth;
   
    private String buildingName;
   
    private String itemName;
   
    private Integer month;
   
    private Integer printTip;
   
    private String createTime;
   
    private BigDecimal price;
   
    private String receiptNo;
   
    private String billStatus;
   
    private String paymentMethod;
   
    private String id;
   
    private String payId;
   
    private String consumeType;
   
    private String paymentResource;
   
    private String subjectName;
}


