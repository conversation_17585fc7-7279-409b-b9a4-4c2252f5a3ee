package com.charge.api.web.vo.joylife.response;

import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.predeposit.AsssetPredepositCountDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.config.dto.meter.MeterBookAssetDetailDTO;
import com.charge.config.dto.standard.StandardConfigDTO;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import com.charge.maindata.pojo.dto.ParkingSpaceDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class HouseOrderItemArrears {
	@ApiModelProperty(value = "房屋运营平台ID")
	private String houseMsId;
	@ApiModelProperty(value = "房屋ID")
	private String houseId;
	@ApiModelProperty(value = "房屋名称")
	private String houseName;
	@ApiModelProperty(value = "房屋编码")
	private String houseCode;
	@ApiModelProperty(value = "房屋类型")
	private String houseType;
	@ApiModelProperty(value = "房屋欠费金额")
	private String houseTotalArrearsAmount;
	/**
	 * 押金余额
	 */
	private String depositMoney;

	/**
	 * 房屋欠费金额-业主
	 */
	private String houseOwnerTotalArrearsAmount;
	/**
	 * 房屋欠费金额-开发商
	 */
	private String houseDeveloperTotalArrearsAmount;
	/**
	 * 房屋欠费金额-业主+开发商
	 */
	private String houseWholeTotalArrearsAmount;
	@ApiModelProperty(value = "房屋托收中金额(业主维度)")
	private String houseTotalCollectAmount;
	@ApiModelProperty(value = "房屋预存余额(业主维度)")
	private String housePrestoreBalance;
	/**
	 * 房屋预存余额-业主
	 */
	private String houseOwnerPrestoreBalance;
	/**
	 * 房屋预存余额-开发商
	 */
	private String houseDeveloperPrestoreBalance;
	/**
	 * 房屋预存余额-业主+开发商
	 */
	private String houseWholePrestoreBalance;
	@ApiModelProperty(value = "房屋冻结余额(业主维度)")
	private String houseHangUpAmount;
	@ApiModelProperty(value = "账龄")
	private int arrearsAge;
	private List<MonthOrderItemArrears> arrearsBillList;
	private List<MonthOrderItemArrears> hangUpBillList;

	/**
	 * 房屋欠费金额-业主（含托收中、已冻结）
	 */
	private String houseOwnerTotalArrearsAmountAll;
	/**
	 * 房屋欠费金额-开发商（含托收中、已冻结)
	 */
	private String houseDeveloperTotalArrearsAmountAll;
	/**
	 * 房屋欠费金额-业主+开发商（含托收中、已冻结)
	 */
	private String houseWholeTotalArrearsAmountAll;

	public static HouseOrderItemArrears fromV2(AssetDTO assetDTO, List<ReceivableBillDTO> receivableBillDTOList,
											   Map<Long, MeterBookAssetDetailDTO> meterMap,Map<Long, AsssetPredepositCountDTO> accountMap,
											   List<StandardConfigDTO> assetStandardsList, Integer chargeObject) {
		HouseOrderItemArrears houseOrderItemArrears=new HouseOrderItemArrears();
		HouseDTO houseDTO = assetDTO.getHouseDTO();
		ParkingSpaceDTO parkingDTO = assetDTO.getParkingSpaceDTO();
		String assetName = houseDTO!=null?houseDTO.getHouseName():parkingDTO.getParkingSpaceName();
		String type = AssetTypeEnum.HOUSE.getCode().equals(assetDTO.getType())?"house":"position";
		String msId = houseDTO!=null?houseDTO.getMsId():parkingDTO.getMsId();
		String code = houseDTO!=null?houseDTO.getHouseCode():parkingDTO.getParkingCode();
		StringBuilder houseName = new StringBuilder();
		String buildingName = houseDTO==null?"":houseDTO.getBuildingName()==null?"":houseDTO.getBuildingName();
		String unitName = houseDTO==null?"":houseDTO.getUnitName()==null?"":houseDTO.getUnitName();
		houseName.append(buildingName).append(unitName).append(assetName);
		List<MonthOrderItemArrears> billList=new ArrayList<>();
		List<MonthOrderItemArrears> hangupBillList=new ArrayList<>();
		BigDecimal amount=BigDecimal.ZERO;
		BigDecimal ownerAmount=BigDecimal.ZERO;
		BigDecimal developerAmount=BigDecimal.ZERO;
		BigDecimal collectAmount=BigDecimal.ZERO;
		BigDecimal handUpAmount=BigDecimal.ZERO;
		BigDecimal ownerAmountAll=BigDecimal.ZERO;
		BigDecimal developerAmountAll=BigDecimal.ZERO;
		BigDecimal amountAll=BigDecimal.ZERO;
		BigDecimal housePrestoreBalance = Objects.isNull(accountMap.get(assetDTO.getId()))?BigDecimal.ZERO:accountMap.get(assetDTO.getId()).getPreDeposit();
		BigDecimal houseOwnerPrestoreBalance = Objects.isNull(accountMap.get(assetDTO.getId()))?BigDecimal.ZERO:accountMap.get(assetDTO.getId()).getOwnerPreDeposit();
		BigDecimal houseDeveloperPrestoreBalance = Objects.isNull(accountMap.get(assetDTO.getId()))?BigDecimal.ZERO:accountMap.get(assetDTO.getId()).getDeveloperPreDeposit();
		int arrearsAge=0;
		Map<Long, List<StandardConfigDTO>> itemStandardsMap =CollectionUtils.isEmpty(assetStandardsList)?new HashMap<>():assetStandardsList.stream().collect(Collectors.groupingBy(s -> s.getItemId()));
		if(!CollectionUtils.isEmpty(receivableBillDTOList)){
			Optional<BigDecimal> collectAmountOptional=receivableBillDTOList.stream()
					.filter(receivableBillDTO->receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.COLLECTION.getCode())
							&& ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus())
							&& ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(receivableBillDTO.getChargeObject()))
					.map(receivableBillDTO->(receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
					.reduce(BigDecimal::add);
			collectAmount=collectAmountOptional.isPresent()?collectAmountOptional.get():BigDecimal.ZERO;
			Optional<BigDecimal> amountOptional=receivableBillDTOList.stream()
					.filter(receivableBillDTO->((receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
							|| receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
							&& ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus())))
					.map(receivableBillDTO->(receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
					.reduce(BigDecimal::add);
			amount=amountOptional.isPresent()?amountOptional.get():BigDecimal.ZERO;
			Optional<BigDecimal> ownerAmountOptional=receivableBillDTOList.stream()
					.filter(receivableBillDTO->((receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
							|| receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
							&& ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus()))
							&& ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(receivableBillDTO.getChargeObject()))
					.map(receivableBillDTO->(receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
					.reduce(BigDecimal::add);
			ownerAmount=ownerAmountOptional.isPresent()?ownerAmountOptional.get():BigDecimal.ZERO;
			Optional<BigDecimal> developerAmountOptional=receivableBillDTOList.stream()
					.filter(receivableBillDTO->effectiveArrears(receivableBillDTO)&& ChargeObjEnum.CHARGE_OBJ_DEVELOPER.getCode().equals(receivableBillDTO.getChargeObject()))
					.map(receivableBillDTO->(receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
					.reduce(BigDecimal::add);
			developerAmount=developerAmountOptional.isPresent()?developerAmountOptional.get():BigDecimal.ZERO;
			Optional<BigDecimal> hangupAmountOptional=receivableBillDTOList.stream()
					.filter(receivableBillDTO->receivableBillDTO.getBillStatus().equals(ReceivableBillStatusEnum.BILL_HOLD.getCode())
							&& ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(receivableBillDTO.getChargeObject()))
					.map(receivableBillDTO->(receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
					.reduce(BigDecimal::add);
			handUpAmount=hangupAmountOptional.isPresent()?hangupAmountOptional.get():BigDecimal.ZERO;

			Optional<BigDecimal> ownerAmountAllOptional=receivableBillDTOList.stream()
					.filter(receivableBillDTO-> effectiveArrears(receivableBillDTO) && ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(receivableBillDTO.getChargeObject()))
					.map(receivableBillDTO->(receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
					.reduce(BigDecimal::add);
			ownerAmountAll=ownerAmountAllOptional.isPresent()?ownerAmountAllOptional.get():BigDecimal.ZERO;
			Optional<BigDecimal> developerAmountAllOptional=receivableBillDTOList.stream()
					.filter(receivableBillDTO->(receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
							|| receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
							&& ChargeObjEnum.CHARGE_OBJ_DEVELOPER.getCode().equals(receivableBillDTO.getChargeObject()))
					.map(receivableBillDTO->(receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
					.reduce(BigDecimal::add);
			developerAmountAll=developerAmountAllOptional.isPresent()?developerAmountAllOptional.get():BigDecimal.ZERO;
			Optional<BigDecimal> amountAllOptional=receivableBillDTOList.stream()
					.filter(receivableBillDTO->(receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
							|| receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode())))
					.map(receivableBillDTO->(receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount()).add(receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount()))
					.reduce(BigDecimal::add);
			amountAll=amountAllOptional.isPresent()?amountAllOptional.get():BigDecimal.ZERO;
			List<ReceivableBillDTO> arrearsOrderList=receivableBillDTOList.stream()
					.filter(receivableBillDTO->effectiveArrears(receivableBillDTO)
							&& chargeObject.equals(receivableBillDTO.getChargeObject()))
					.collect(Collectors.toList());
			if(!CollectionUtils.isEmpty(receivableBillDTOList)){
				 List<String> arrearsAges = arrearsOrderList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(receivableBillDTO ->receivableBillDTO.getBelongYears()))), ArrayList::new)).stream().map(ReceivableBillDTO::getBelongYears).collect(Collectors.toList());
				 arrearsAge = Optional.ofNullable(arrearsAges).isPresent() ? arrearsAges.size() : 0;
			 }
			List<ReceivableBillDTO> hangupOrderList=receivableBillDTOList.stream()
					.filter(receivableBillDTO->receivableBillDTO.getBillStatus().equals(ReceivableBillStatusEnum.BILL_HOLD.getCode())
							&& chargeObject.equals(receivableBillDTO.getChargeObject()))
					.collect(Collectors.toList());
			billList = MonthOrderItemArrears.fromV2(arrearsOrderList,meterMap,itemStandardsMap,chargeObject);
			hangupBillList=MonthOrderItemArrears.fromHangupV2(hangupOrderList,meterMap,itemStandardsMap,chargeObject);
		}
		billList.sort(Comparator.comparing(MonthOrderItemArrears::getMonth).reversed());
		hangupBillList.sort(Comparator.comparing(MonthOrderItemArrears::getMonth).reversed());
	    houseOrderItemArrears.setHouseId(assetDTO.getId().toString());
		houseOrderItemArrears.setHouseCode(code);
		houseOrderItemArrears.setHouseMsId(msId);
		houseOrderItemArrears.setHouseName(assetName);
		houseOrderItemArrears.setArrearsBillList(billList);
		houseOrderItemArrears.setHangUpBillList(hangupBillList);
		houseOrderItemArrears.setHouseTotalArrearsAmount(ownerAmount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseOwnerTotalArrearsAmount(ownerAmount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseDeveloperTotalArrearsAmount(developerAmount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseWholeTotalArrearsAmount(amount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseTotalCollectAmount(collectAmount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseHangUpAmount(handUpAmount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseType(type);
		houseOrderItemArrears.setArrearsAge(arrearsAge);
		houseOrderItemArrears.setHousePrestoreBalance(houseOwnerPrestoreBalance.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseOwnerPrestoreBalance(houseOwnerPrestoreBalance.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseDeveloperPrestoreBalance(houseDeveloperPrestoreBalance.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseWholePrestoreBalance(housePrestoreBalance.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseOwnerTotalArrearsAmountAll(ownerAmountAll.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseDeveloperTotalArrearsAmountAll(developerAmountAll.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		houseOrderItemArrears.setHouseWholeTotalArrearsAmountAll(amountAll.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		return houseOrderItemArrears;
	}

	private static boolean effectiveArrears(ReceivableBillDTO receivableBillDTO) {
		return (receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
				|| receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode())
				|| receivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.COLLECTION.getCode()))
				&& ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(receivableBillDTO.getBillStatus());
	}

}
