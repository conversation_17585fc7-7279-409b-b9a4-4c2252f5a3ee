package com.charge.api.web.vo.pos;

import com.charge.common.serializer.DesensitizeSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/15
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class UserVO {
    @JsonProperty("houseOwnerName")
    @JsonSerialize(using = DesensitizeSerializer.class)
    private String houseOwnerName;
    @JsonProperty("houseOwnerCode")
    private String houseOwnerCode;
    @JsonProperty("houseCount")
    private Integer houseCount;
    @JsonProperty("houseOwnerMobile")
    @JsonSerialize(using = DesensitizeSerializer.class)
    private String houseOwnerMobile;
    @JsonProperty("houseOwnerId")
    private String houseOwnerId;
}


