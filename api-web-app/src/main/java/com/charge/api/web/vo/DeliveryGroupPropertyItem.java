package com.charge.api.web.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class DeliveryGroupPropertyItem {
    private Long id;

    private Long groupId;

    private String propertyId;

    private String propertyType;

    private Long groupItemId;

    private String standardConfigUuid;

    private String standardConfigName;

    private BigDecimal price;

    private Integer month;

    private BigDecimal money;

    private Date createTime;

    private Date modifyTime;


}