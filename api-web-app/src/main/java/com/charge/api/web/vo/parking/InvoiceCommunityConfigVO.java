package com.charge.api.web.vo.parking;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class InvoiceCommunityConfigVO {
    /**
     * 销方信息
     */
    InvoiceSellerVO invoiceSellerVO;
    /**
     * 税控信息
     */
    InvoiceTaxDeviceVO invoiceTaxDeviceVO;
    /**
     * 开票模式
     */
    Integer  invoiceMode;
    /**
     * 开票类型
     */
    List<Integer> invoiceTypeList;
    /**
     * 信息
     */
    String message;
}
