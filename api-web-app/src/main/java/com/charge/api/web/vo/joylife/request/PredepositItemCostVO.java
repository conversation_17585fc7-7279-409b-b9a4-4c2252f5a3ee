package com.charge.api.web.vo.joylife.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description:
 * @date 2023/2/1513:59
 */
@Data
public class PredepositItemCostVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 收费系统小区id
     */
    @NotBlank(message = "小区ID不能为空")
    private String communityId;

    /**
     * 收费系统房间id
     */
    @NotBlank(message = "收费系统房间id不能为空")
    private String houseId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户类型：0 业主
     */
    private Integer userType;

    /**
     * 支付终端信息
     */
    @NotBlank(message = "支付终端不能为空")
    private String paymentSource;
}
