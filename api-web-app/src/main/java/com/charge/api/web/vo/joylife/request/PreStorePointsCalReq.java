package com.charge.api.web.vo.joylife.request;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 预存积分计算请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class PreStorePointsCalReq extends ZhaoXiAssertRequest {

    private static final long serialVersionUID = 2975728929297694877L;
    /**
     * 费项金额
     */
    @NotNull(message = "费项金额不能为空")
    @Valid
    private ChargeItemAmount   chargeItemAmount;

}
