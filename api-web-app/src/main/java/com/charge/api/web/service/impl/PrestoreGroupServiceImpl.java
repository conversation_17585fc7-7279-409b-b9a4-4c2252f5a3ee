package com.charge.api.web.service.impl;

import com.charge.api.web.adapter.PayOrderAdapter;
import com.charge.api.web.constants.DeliveryConstants;
import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.constants.YueConstants;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.service.joylife.PrestoreGroupService;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.support.BankCollectionCheckSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.support.FeeCalculateSupport;
import com.charge.api.web.util.ShardingUtil;
import com.charge.api.web.vo.*;
import com.charge.api.web.vo.joylife.deposit.PrestoreChargeInfoVO;
import com.charge.bill.client.*;
import com.charge.bill.dto.ReceivableAmountTotalDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.income.*;
import com.charge.bill.dto.predeposit.PredepositAccountConditionDTO;
import com.charge.bill.dto.predeposit.PredepositAccountDTO;
import com.charge.bill.enums.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.config.client.item.CommunityPreStoreItemClient;
import com.charge.config.client.item.CommunitySpecialPreStoreGroupClient;
import com.charge.config.client.prestoreGroup.PrestoreGroupClient;
import com.charge.config.client.standard.StandardConfigClient;
import com.charge.config.dto.item.CommunitySpecialPrestoreGroupDTO;
import com.charge.config.dto.item.CommunitySpecialPrestoreGroupItemDTO;
import com.charge.config.dto.item.ItemInfo;
import com.charge.config.dto.item.PreStoreItemConfigDTO;
import com.charge.config.dto.item.condition.PreStoreItemConfigQueryConditionDTO;
import com.charge.config.dto.prestoreGroup.DeliveryGroupPropertyItemDTO;
import com.charge.config.dto.prestoreGroup.DeliveryTransactionDTO;
import com.charge.config.dto.prestoreGroup.condition.*;
import com.charge.config.dto.standard.StandardConfigDTO;
import com.charge.config.enums.SpecialPrestoreGroupStatusEnum;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.feecalculte.dto.ReceivableBillDTO;
import com.charge.joylife.dto.DeliveryItemDetail;
import com.charge.joylife.dto.DeliveryPayDataRequest;
import com.charge.joylife.dto.PayInfoDTO;
import com.charge.joylife.util.PaymentMethodSupport;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.enums.CustomerTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.pay.client.PayClient;
import com.charge.pay.dto.pay.PayOrderSubmitResponseDTO;
import com.charge.pay.enums.ChannelPayTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author: yjw
 * Date: 2023/3/9 11:32
 */

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PrestoreGroupServiceImpl implements PrestoreGroupService {

    private final CommunitySupport communitySupport;

    private final AssetSupport assetSupport;

    private final PredepositAccountClient predepositAccountClient;

    private final ReceivableBillClient receivableBillClient;

    private final FeeCalculateSupport calculateSupport;

    private final PrestoreGroupClient prestoreGroupClient;

    private final AssetTransactionClient assetTransactionClient;

    private final CommunitySpecialPreStoreGroupClient communitySpecialPreStoreGroupClient;

    private final CommunityPreStoreItemClient communityPreStoreItemClient;

    private final AssetClient assetClient;

    private final BillNumGeneratorClient billNumGeneratorClient;

    private final IncomeBillClient incomeBillClient;

    private final AssetBillInfoClient assetBillInfoClient;

    private final WriteOffBillClient writeOffBillClient;

    private final TransactionRelationClient transactionRelationClient;

    private final PredepositBillClient predepositBillClient;


    private final PayClient payClient;

    private final StandardConfigClient standardConfigClient;

    private final BankCollectionCheckSupport bankCollectionCheckSupport;

    @Override
    public ChargeResponse<Map<String, Object>> getOrderDetail(String communityMsId, String houseMsId, String propertyType) throws ChargeBusinessException{
        log.info("用于获取云交付订单详情: communityMsId={}, houseMsId={}, propertyType={}, appToken={}, cropId={}", communityMsId, houseMsId, propertyType);
        if (StringUtils.isEmpty(communityMsId)) {
            throw new ChargeBusinessException("-1", "项目参数为空");
        }
        if (StringUtils.isEmpty(houseMsId)) {
            throw new ChargeBusinessException("-1", "房屋参数为空");
        }
        if (StringUtils.isEmpty(propertyType)) {
            throw new ChargeBusinessException("-1", "资产类型参数为空");
        }
        Long communityId = communitySupport.getCommunityIdByMsId(communityMsId);
        if (communityId == null) {
            return new ChargeResponse(DeliveryConstants.RST_CODE_FOUNDNODATA_ERROR, "当前项目不存在");
        }
        Long houseId = assetSupport.getV2AssetId(houseMsId);
        if (houseId == null) {
            return new ChargeResponse(DeliveryConstants.RST_CODE_FOUNDNODATA_ERROR, "当前房屋不存在");
        }

        //支付状态
        int payStatus;
        //插入关联记录标识
        boolean insertRecordFlag = false;
        //获取当前房屋是否存在云交付资产组合关联记录
        DeliveryTransactionCondition deliveryTransactionCondition = new DeliveryTransactionCondition();
        deliveryTransactionCondition.setPropertyId(houseId);
        ChargeResponse<DeliveryTransactionDTO> detailDTOChargeResponse = prestoreGroupClient.queryAssetDeliveryTransaction(deliveryTransactionCondition);
        DeliveryTransactionDTO deliveryTransactionDTO = AppInterfaceUtil.getResponseData(detailDTOChargeResponse);
        AssetTransactionDTO assetTransactionDTO = new AssetTransactionDTO();
        if(deliveryTransactionDTO == null || deliveryTransactionDTO.getTransactionId() == null) {
            //若不存在关联记录，则为未支付状态，同时新增关联记录。
            payStatus = 0;
            insertRecordFlag = true;
        } else {
            ShardingUtil.addCommunityId2ThreadContext(communityId);
            ChargeResponse<AssetTransactionDTO> assetTransactionDTOResponse = assetTransactionClient.getById(deliveryTransactionDTO.getTransactionId());
            assetTransactionDTO = AppInterfaceUtil.getResponseData(assetTransactionDTOResponse);
            if(assetTransactionDTO == null || assetTransactionDTO.getPayStatus() == null){
                payStatus = 0;
                insertRecordFlag = true;
            } else {
                payStatus = assetTransactionDTO.getPayStatus();
            }
        }
        //若为已支付，则直接返回缴费明细记录
        if(payStatus == BillPayStatusEnum.SUCCESS.getCode()){
            return payedBillDetail(houseId, deliveryTransactionDTO, assetTransactionDTO);
        }

        String nowDateStr = DateUtils.getDateStr();
        DeliveryPrestoreGroupCondition deliveryPrestoreGroupCondition = DeliveryPrestoreGroupCondition.builder()
                        .communityId(communityId).date(nowDateStr).build();
        ChargeResponse<CommunitySpecialPrestoreGroupDTO> groupResponse = communitySpecialPreStoreGroupClient.getDeliveryPrestoreGroup(deliveryPrestoreGroupCondition);
        if(!groupResponse.isSuccess() || groupResponse.getContent() == null){
            return new ChargeResponse(DeliveryConstants.ERROR_CODE_GROUP_INVALID, "当前项目未配置组合定价");
        }

        //当前生效的云交付资产组合ID
        long groupId = groupResponse.getContent().getId();
        if (deliveryTransactionDTO != null && deliveryTransactionDTO.getGroupId() != groupId) {
            insertRecordFlag = true;
        }

        /*判断当前房屋是否有关联有效组合费用记录*/
        DeliveryPropertyItemQueryCondition deliveryPropertyItemQueryCondition = DeliveryPropertyItemQueryCondition.builder()
                .propertyId(houseId).groupId(groupId).build();
        ChargeResponse<List<DeliveryGroupPropertyItemDTO>> deliveryPropertyItemResponse = prestoreGroupClient.queryAssetDeliveryItem(deliveryPropertyItemQueryCondition);
        /*没有费用记录则新增,有记录则更新金额*/
        boolean insertPropertyItemFlag = false;
        if(!deliveryPropertyItemResponse.isSuccess() || deliveryPropertyItemResponse.getContent().size() == 0){
            insertPropertyItemFlag = true;
        }

        List billDetailVOS = new ArrayList();
        BigDecimal totalMoney = new BigDecimal(0);
        ChargeResponse<List<CommunitySpecialPrestoreGroupItemDTO>> itemDTOResponse = communitySpecialPreStoreGroupClient.getPrestoreGroupItemList(groupId);
        if(itemDTOResponse.isSuccess()) {
            List<CommunitySpecialPrestoreGroupItemDTO> deliveryGroupItemList = itemDTOResponse.getContent();
            for (int i = 0; i < deliveryGroupItemList.size(); i++) {
                CommunitySpecialPrestoreGroupItemDTO groupItem = deliveryGroupItemList.get(i);
                Long groupItemId = groupItem.getId();
                Map<String, Object> billDetailVO = new HashMap<>();
                billDetailVO.put("itemId", groupItem.getItemId());
                billDetailVO.put("itemName", groupItem.getItemName());
                billDetailVO.put("monthNum", groupItem.getMonth());

                BigDecimal resultMoney = groupItem.getMoney();
                BigDecimal resultPrice = new BigDecimal(0);
                String calculateFormula = "";
                String calculateDetail = "";
                String chargeType = groupItem.getChargeType();
                Long itemId = groupItem.getItemId();
                String itemName = groupItem.getItemName();
                ItemInfo itemInfo = new ItemInfo();
                itemInfo.setItemId(itemId);
                itemInfo.setItemName(itemName);
                if ("general".equals(chargeType)) {
                    List<PrestoreChargeInfoVO> prestoreDetailVOList = getSpecialChargeInfo(houseId, communityId, Lists.newArrayList(itemInfo));
                    if (CollectionUtils.isEmpty(prestoreDetailVOList)) {
                        String errorMsg = "当前房屋预存收费项[" + itemName + "]计费配置异常";
                        return new ChargeResponse(DeliveryConstants.ERROR_CODE_GROUP_INVALID, errorMsg);
                    }

                    PrestoreChargeInfoVO prestoreChargeInfoVO = prestoreDetailVOList.get(0);
                    if (prestoreChargeInfoVO == null || prestoreChargeInfoVO.getChargeMoneyPerMonth() == null) {
                        String errorMsg = "当前房屋预存收费项[" + itemName + "]计费配置单价异常";
                        return new ChargeResponse(DeliveryConstants.ERROR_CODE_GROUP_INVALID, errorMsg);
                    }

                    resultPrice = prestoreChargeInfoVO.getChargeMoneyPerMonth();
                    resultPrice = resultPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
                    int month = groupItem.getMonth();
                    resultMoney = resultPrice.multiply(new BigDecimal(month)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    Map<String, String> calculateInfo = convertCalculateInfo(prestoreChargeInfoVO, resultPrice, month, resultMoney);
                    calculateFormula = calculateInfo.get("calculateFormula");
                    calculateDetail = calculateInfo.get("calculateDetail");
                } else {
                    resultPrice = null;
                    resultMoney = resultMoney.setScale(2, BigDecimal.ROUND_HALF_UP);
                }

                totalMoney = totalMoney.add(resultMoney);
                if (insertPropertyItemFlag) {
                    //生成房屋实时费用
                    DeliveryPropertyItemCondition condition = DeliveryPropertyItemCondition.builder()
                            .groupId(groupId).groupItemId(groupItemId)
                            .propertyId(houseId).propertyType(propertyType)
                            .price(resultPrice).month(groupItem.getMonth())
                            .money(resultMoney).build();
                    prestoreGroupClient.saveOrUpdate(condition);
                } else {
                    //更新房屋实时费用
                    DeliveryPropertyItemCondition condition = DeliveryPropertyItemCondition.builder()
                            .groupId(groupId).groupItemId(groupItemId)
                            .propertyId(houseId).propertyType(propertyType)
                            .price(resultPrice).month(groupItem.getMonth())
                            .money(resultMoney).build();
                    prestoreGroupClient.updateByParam(condition);
                }

                billDetailVO.put("price", resultMoney);
                billDetailVO.put("pricePerUnit", resultPrice);
                billDetailVO.put("calculateFormula", calculateFormula);
                billDetailVO.put("calculateDetail", calculateDetail);
                billDetailVOS.add(billDetailVO);

            }
        }

        DeliveryTransactionCondition deliveryCondition = new DeliveryTransactionCondition();
        Long insertDeliveryTransactionId = 0L;
        if(insertRecordFlag){
            deliveryCondition.setGroupId(groupId);
            deliveryCondition.setPropertyId(houseId);
            deliveryCondition.setPropertyType(propertyType);
            ChargeResponse<Long> insertDeliveryTransactionRes = prestoreGroupClient.saveOrUpdateTransaction(deliveryCondition);
            insertDeliveryTransactionId = AppInterfaceUtil.getDataThrowException(insertDeliveryTransactionRes);
        } else {
            deliveryCondition.setId(deliveryTransactionDTO.getId());
            deliveryCondition.setGroupId(groupId);
            deliveryCondition.setPropertyId(houseId);
            deliveryCondition.setPropertyType(propertyType);
            prestoreGroupClient.saveOrUpdateTransaction(deliveryCondition);
        }


        Map<String, Object> deliveryOrder = new HashMap<>();
        if(insertRecordFlag) {
            deliveryOrder.put("prestoreFormId", groupId);
            deliveryOrder.put("recordId", insertDeliveryTransactionId);
        } else {
            deliveryOrder.put("prestoreFormId", groupId);
            deliveryOrder.put("recordId", deliveryTransactionDTO.getId());
        }
        deliveryOrder.put("money", totalMoney);
        deliveryOrder.put("payStatus", payStatus);
        deliveryOrder.put("billDetailVOS", billDetailVOS);
        deliveryOrder.put("communityId", communityId);
        deliveryOrder.put("houseId", houseId);

        return new ChargeResponse(deliveryOrder);
    }

    @Override
    public ChargeResponse createPay(DeliveryPayDataRequest payRequest) throws ChargeBusinessException{
        //先进行云交付-专项预存组合校验
        String communityMsId = payRequest.getCommunityMsId();
        CommunityDTO communityDTO = communitySupport.getCommunityByMsId(communityMsId);
        if (communityDTO == null || communityDTO.getId() == null) {
            return new ChargeResponse<>(DeliveryConstants.RST_CODE_FOUNDNODATA_ERROR, "当前项目不存在");
        }
        Long communityId = communityDTO.getId();
        String houseMsId = payRequest.getHouseMsId();
        Long houseId = assetSupport.getV2AssetId(houseMsId);
        if (houseId == null) {
            return new ChargeResponse<>(DeliveryConstants.RST_CODE_FOUNDNODATA_ERROR, "当前房屋不存在");
        }
        // 校验资产是否在托收中
        if (bankCollectionCheckSupport.bankCollectingByAssetId(houseId)) {
            return new ChargeResponse<>(DeliveryConstants.RST_CODE_FOUNDNODATA_ERROR, ErrorInfoEnum.E3042.getValue());
        }

        ChargeResponse checkRes = checkDeliveryInfo(payRequest, communityId, houseId);
        if(!checkRes.isSuccess()){
            return checkRes;
        }

        return addV2NewDeliveryPrestoreInfo(payRequest, communityDTO, communityId.toString(), houseId.toString());
    }

    private ChargeResponse addV2NewDeliveryPrestoreInfo(DeliveryPayDataRequest request, CommunityDTO communityDTO, String comId, String assetId) throws ChargeBusinessException {

        AssetAdapter assetInfo = assetSupport.getAssetInfoById(assetId);
        // 进入2.0灰度小区
        checkParam(request);
        List<DeliveryItemDetail> deliveryItemDetailList = request.getBillDetailVOS();
        List<DeliveryItemDetail> commonPrestore = deliveryItemDetailList.stream().filter(item -> PayRelatedConstants.PREDEPOSIT_GENERAL_ID_V2.equals(Long.valueOf(item.getItemId()))).collect(Collectors.toList());
        List<DeliveryItemDetail> specialPrestore = deliveryItemDetailList.stream().filter(item -> !PayRelatedConstants.PREDEPOSIT_GENERAL_ID_V2.equals(Long.valueOf(item.getItemId()))).collect(Collectors.toList());
        log.info("{}|【云交付预存缴费】生成预支付订单开始：通用预存个数:{}，{}",LogCategoryEnum.BUSSINESS, commonPrestore.size(),commonPrestore);
        log.info("{}|【云交付预存缴费】生成预支付订单开始：专项预存个数:{}，{}",LogCategoryEnum.BUSSINESS, specialPrestore.size(),specialPrestore);
        //校验通用预存项只能有一个
        if (!CollectionUtils.isEmpty(commonPrestore) && commonPrestore.size() > 1) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "【云交付预存缴费】通用预存缴费项为多个，请重新输入");
        }
        //TODO 判断交易状态-托收、其他业务锁等

        //支付下单
        String orderNum = IdGeneratorSupport.getIstance().nextId();
        ChargeResponse<PayOrderSubmitResponseDTO> payReturnChargeResponse = payClient.tradeCreate(PayOrderAdapter.requestDeliveryPayParamConvert(request, comId, orderNum));

        if (!payReturnChargeResponse.isSuccess() || payReturnChargeResponse.getContent() == null) {
            log.error("{}|专项预存组合支付预下单异常|{}", LogCategoryEnum.BUSSINESS, payReturnChargeResponse);
            return new ChargeResponse<>(payReturnChargeResponse.getCode(), payReturnChargeResponse.getMessage());
        }
        PayOrderSubmitResponseDTO payReturnDTO = payReturnChargeResponse.getContent();

        //收费下单，优先缴费，余额充值
        return addPredeposit(commonPrestore,specialPrestore,request,communityDTO,assetInfo,payReturnDTO,orderNum,Long.valueOf(comId),Long.valueOf(assetId));

    }

    private ChargeResponse addPredeposit(List<DeliveryItemDetail> commonPrestore,List<DeliveryItemDetail> specialPrestore,
                                         DeliveryPayDataRequest request, CommunityDTO communityDTO, AssetAdapter assetInfo,
                                         PayOrderSubmitResponseDTO payReturnDTO,String orderNum,Long comId,Long assetId) throws ChargeBusinessException {
        //查询通用预存可抵扣费项列表
        PreStoreItemConfigQueryConditionDTO query = new PreStoreItemConfigQueryConditionDTO();
        query.setCommunityIds(Lists.newArrayList(comId));
        ChargeResponse<List<PreStoreItemConfigDTO>> preStoreItemConfigList = communityPreStoreItemClient.getPreStoreItemConfig(query);
        if (preStoreItemConfigList == null || YueConstants.CODE_SUCCESS != preStoreItemConfigList.getCode() || CollectionUtils.isEmpty(preStoreItemConfigList.getContent())) {
            log.error("{}|查询小区预存配置信息异常|{}", LogCategoryEnum.BUSSINESS,preStoreItemConfigList);
            return new ChargeResponse<>(YueConstants.CODE_FAILED,"当前小区不能预存，请联系管家");
        }
        PreStoreItemConfigDTO preStoreItemConfig = preStoreItemConfigList.getContent().get(0);
        List<Long> genaralList = preStoreItemConfig.getCommonPreStoreItems();
        List<Long> deductList = request.getBillDetailVOS().stream().map(t -> Long.valueOf(t.getItemId())).collect(Collectors.toList());
        deductList.addAll(genaralList);
        //查询房间欠费列表
        List<com.charge.bill.dto.ReceivableBillDTO> recBillList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deductList)) {
            // 只拉当前月份欠费
            ReceivableConditionDTO recCondition = ReceivableConditionDTO.builder().communityId(comId).assetId(assetId).billStatus(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode())
                    .payStatuses(Lists.newArrayList(ReceivableBillPayStatusEnum.NOT_PAY.getCode(), ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                    .itemIdList(deductList)
                    .belongYearsEnd(DateUtils.format(new Date(), DateUtils.FORMAT_14))
                    .build();

            ChargeResponse<List<com.charge.bill.dto.ReceivableBillDTO>> listChargeResponse = receivableBillClient.queryList(recCondition);
            recBillList = listChargeResponse.getContent();
        }
        log.info("{}|【朝昔】云交付预存充值2.0，房间欠费|{}",LogCategoryEnum.BUSSINESS,recBillList);

        //查询业主信息 TODO
        ChargeResponse<List<AssetDTO>> listChargeResponse = assetClient.listAsset(AssetCondition.builder().ids(Lists.newArrayList(assetId)).build());
        log.info("{}|【朝昔】云交付预存充值2.0，业主信息|{}",LogCategoryEnum.BUSSINESS,listChargeResponse);
        AssetDTO assetDTO = listChargeResponse ==null || CollectionUtils.isEmpty(listChargeResponse.getContent())? new AssetDTO():listChargeResponse.getContent().get(0);
        List<CustomerDTO> customerDTOList= assetDTO.getHouseDTO() ==null? assetDTO.getParkingSpaceDTO().getListCustomer() : assetDTO.getHouseDTO().getListCustomer();

        // 查询业主信息
        List<CustomerDTO> ownerDTOList = CollectionUtils.isEmpty(customerDTOList) ? null :
                customerDTOList.stream().filter(item -> CustomerTypeEnum.OWNER.getCode().equals(item.getCustomerType())).collect(Collectors.toList());

        // 不存在业主信息则直接返回错误
        if (org.apache.commons.collections.CollectionUtils.isEmpty(ownerDTOList)) {
            return new ChargeResponse<>(YueConstants.CODE_FAILED, "房间不存在业主信息，有疑问请联系管家");
        }
        Map<String, CustomerDTO> msIdKeyCustomerMap =
                ownerDTOList.stream().filter(customerDTO -> org.apache.commons.lang.StringUtils.isNotBlank(customerDTO.getMsId()))
                        .collect(Collectors.toMap(CustomerDTO::getMsId, Function.identity()));

        // 校验是否是业主
        CustomerDTO ownerDTO = msIdKeyCustomerMap.get(request.getCustomId());
        Long userId;
        Long ownerId;
        String ownerName;
        if (Objects.nonNull(ownerDTO)) {
            ownerId = ownerDTO.getId();
            userId = ownerDTO.getId();
            ownerName = ownerDTO.getCustomerName();
        } else {
            // 非业主缴费时， 取第一个业主
            ownerDTO = ownerDTOList.get(0);
            ownerId = ownerDTO.getId();
            ownerName = ownerDTO.getCustomerName();

            // 非业主需找到对应那个用户进行充值
            CustomerDTO customerDTO = customerDTOList.stream().filter(item -> Objects.equals(item.getMsId(),
                    request.getCustomId()) && (!Objects.equals(item.getCustomerType(),
                    CustomerTypeEnum.OWNER.getCode()))).findFirst().orElse(null);
            userId = Objects.isNull(customerDTO)?ownerId:customerDTO.getId();
        }

        //保存实收单
        BigDecimal totalMoney = new BigDecimal(request.getMoney());
        String billNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.INCOME_BILL.getCode()).getContent();
        Integer points = org.apache.commons.lang.StringUtils.isEmpty(request.getTotalPoints())?0:Integer.parseInt(request.getTotalPoints());
        IncomeBillDTO income = IncomeBillDTO.builder().orderNum(orderNum).billNum(billNum).outTransactionNo(payReturnDTO.getOutTradeNo())
                .communityId(comId).communityName(communityDTO.getName()).incomeMoney(totalMoney).points(points)
                .equityAccount(request.getEquityAccount()).goodsName(PayRelatedConstants.GOODSNAME_FOR_PRESTORE)
                .paymentTerminal(PaymentTerminalEnum.handleJoyLifeWechatApplet(request.getPaymentSource()))
                .paymentTime(new Date()).payMemberId(request.getPayMemberId()).payMember(request.getPayMember()).payMemberMobile(request.getPhone())
                .payHouseCount(1).balanceStatus(BalanceStatusEnum.UNRECONCILED.getCode())
                .memo(request.getMemo())
                .msAssetOwnerId(request.getCustomId())
                .isMergeBill(payReturnDTO.getIsMergeBill())
                .subBills(PayOrderAdapter.fillSubBills(payReturnDTO.getSubItems()))
                .build();

        PayOrderAdapter.fillPaymentMethod(income,request.getPaymentMethod());
        if (Objects.equals(PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel(),request.getPaymentSource())) {
            income.setPaymentChannel(request.getPaymentSource());
        }
        income.setCreateUser(request.getPayMember());
        income.setCreateTime(DateUtils.getCurrentTimestamp());
        income = incomeBillClient.create(income).getContent();
        log.info("{}|【朝昔】云交付预存充值2.0，成功新增实收单|{}",LogCategoryEnum.BUSSINESS,income);

        //保存资产流水单
        AssetTransactionDTO asTr = AssetTransactionDTO.builder().incomeId(income.getId()).money(totalMoney).goodsName(income.getGoodsName())
                .assetOrderNum(orderNum).communityId(income.getCommunityId()).communityName(income.getCommunityName()).buildingId(assetInfo.getBuildingId())
                .buildingName(assetInfo.getBuildingName()).unitId(assetInfo.getUnitId()).unitName(assetInfo.getUnitName())
                .assetId(assetId).assetName(assetInfo.getSubName()).assetCode(assetInfo.getSubCode()).paymentMethod(income.getPaymentMethod())
                .paymentChannel(income.getPaymentChannel()).paymentTerminal(income.getPaymentTerminal()).paymentTime(new Date())
                .assetUseStatus(assetInfo.getAssetUseStatus())
                .ownerId(ownerId).ownerName(ownerName).build();
        asTr.setCreateUser(request.getPayMember());
        asTr.setCreateTime(DateUtils.getCurrentTimestamp());
        asTr = assetTransactionClient.create(asTr).getContent();
        log.info("{}|【朝昔】云交付预存充值2.0，成功新增资产流水|{}",LogCategoryEnum.BUSSINESS,asTr);

        //创建流水信息表数据
        AssetBillInfoDTO assetBillInfoDO = AssetBillInfoDTO.builder().assetTransactionId(asTr.getId())
                .orderNum(income.getOrderNum()).payMemberId(income.getPayMemberId()).payMember(income.getPayMember()).communityId(income.getCommunityId()).build();
        assetBillInfoClient.create(assetBillInfoDO);

        List<DeliveryItemDetail> preInfoList = new ArrayList<>();
        BigDecimal actualAmount = BigDecimal.ZERO;
        BigDecimal penaltyAmount = BigDecimal.ZERO;
        List<WriteOffBillDTO> writeOffList = new ArrayList<>();
        List<TransactionRelationDTO> relationList = new ArrayList<>();
        Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> recBillMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(recBillList)) {
            recBillMap = recBillList.stream().collect(Collectors.groupingBy(com.charge.bill.dto.ReceivableBillDTO::getItemId));//升序
            log.info("{}|【朝昔】云交付预存充值2.0，欠费信息map|{}",LogCategoryEnum.BUSSINESS,recBillMap);
        }
        if (recBillMap.isEmpty()) {
            preInfoList = request.getBillDetailVOS();
        } else {
            //缴欠费
            deduction(commonPrestore, specialPrestore, request.getPayMember(), comId, assetId, genaralList,
                    ownerId, income, asTr, preInfoList, actualAmount, penaltyAmount, writeOffList, recBillMap);
        }

        log.info("预存：{}，抵扣费用：{}",preInfoList,writeOffList);
        if (CollectionUtils.isNotEmpty(writeOffList)) {
            writeOffList = writeOffBillClient.batchCreate(writeOffList).getContent();
            log.info("{}|【朝昔】云交付预存充值2.0，条数：{}，成功新增核销明细|{}",LogCategoryEnum.BUSSINESS,writeOffList.size(),writeOffList);
            for (WriteOffBillDTO w:writeOffList) {
                //添加关联表数据
                TransactionRelationDTO relationDTO = TransactionRelationDTO.builder().assetTransactionId(asTr.getId()).businessId(w.getId())
                        .businessType(BusinessTypeEnum.NORMAL_PAY.getCode()).communityId(income.getCommunityId()).build();
                relationDTO.setCreateTime(income.getCreateTime());
                relationList.add(relationDTO);
            }

        }

        //组装充值数据
        List<PredepositBillDTO> predepositBillList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(preInfoList)) {

            for (DeliveryItemDetail preInfo : preInfoList) {
                Integer predepositType = PayRelatedConstants.PREDEPOSIT_GENERAL_ID_V2.equals(Long.valueOf(preInfo.getItemId()))?PredepositTypeEnum.COMMON_DEPOSIT.getCode():PredepositTypeEnum.SPECIAL_DEPOSIT.getCode();

                String preBillNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.PREDEPOSIT_BILL.getCode()).getContent();
                String itemName = PayRelatedConstants.PREDEPOSIT_GENERAL_ID_V2.equals(Long.valueOf(preInfo.getItemId()))?preInfo.getItemName():PayRelatedConstants.SPECIAL_PREDEPOSIT_PREFIX+preInfo.getItemName();

                PredepositBillDTO preBill = PredepositBillDTO.builder().communityId(comId).assetId(assetId)
                        .assetTransactionId(asTr.getId()).billNum(preBillNum).predepositItemId(Long.valueOf(preInfo.getItemId()))
                        .predepositMoney(preInfo.getPrice()).memo(income.getMemo()).predepositItemName(itemName)
                        .predepositType(predepositType).isBalance(PayRelatedConstants.IS_BALANCE_YES)
                        .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode()).userId(userId).build();
                preBill.setCreateTime(DateUtils.getCurrentTimestamp());
                preBill.setCreateUser(request.getPayMember());
                predepositBillList.add(preBill);

            }
        }
        if (CollectionUtils.isNotEmpty(predepositBillList)) {
            predepositBillList = predepositBillClient.batchCreate(BatchPredepositBillDTO.builder().predepositBillDTOList(predepositBillList).build()).getContent();
            log.info("{}|【朝昔】云交付预存充值2.0，条数：{}，成功新增预收单明细|{}",LogCategoryEnum.BUSSINESS,predepositBillList.size(),predepositBillList);
            for (PredepositBillDTO preBill : predepositBillList) {
                //添加关联表数据
                TransactionRelationDTO relationDTO = TransactionRelationDTO.builder().assetTransactionId(asTr.getId()).businessId(preBill.getId())
                        .businessType(BusinessTypeEnum.PREDEPOSIT_PAY.getCode()).communityId(income.getCommunityId()).build();
                relationDTO.setCreateTime(income.getCreateTime());
                relationList.add(relationDTO);
            }
        }

        //创建关联表数据
        relationList=transactionRelationClient.batchCreate(relationList).getContent();
        log.info("{}|【朝昔】云交付预存充值2.0，条数：{}，成功新增资产流水关联信息|{}", LogCategoryEnum.BUSSINESS, relationList.size(), relationList);
       /* SerializeConfig config = new SerializeConfig();
        config.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
        com.alibaba.fastjson.JSONObject payJson = (com.alibaba.fastjson.JSONObject) JSONObject.toJSON(payReturnDTO, config);
        payJson.put("id",income.getId());
        BigDecimal totalPointsNum= org.apache.commons.lang.StringUtils.isEmpty(request.getTotalPoints())?BigDecimal.ZERO:new BigDecimal(request.getTotalPoints());
        payJson.put("points",totalPointsNum.intValue());*/

        PayInfoDTO payInfoDTO = PayOrderAdapter.responseYueCardPayOrDeliveryPayConvert(payReturnDTO, income.getId());
        log.info("{}|【朝昔】云交付预存充值2.0，返回下单信息|{}", LogCategoryEnum.BUSSINESS, payInfoDTO);

        //回传交易流水ID至云交付关联缴费记录表
        DeliveryTransactionCondition deliveryCondition = new DeliveryTransactionCondition();
        deliveryCondition.setTransactionId(asTr.getId());
        deliveryCondition.setId(request.getRecordId());
        ChargeResponse<Long> deliveryTransactResponse = prestoreGroupClient.saveOrUpdateTransaction(deliveryCondition);
        log.info("{}|【朝昔】云交付预存充值2.0，返回云交付关联缴费信息|{}",LogCategoryEnum.BUSSINESS, deliveryTransactResponse);

        return new ChargeResponse<>(payInfoDTO);
    }

    private void deduction(List<DeliveryItemDetail> commonPrestore, List<DeliveryItemDetail> specialPrestore, String userName,
                           Long comId, Long assetId, List<Long> genaralList, Long ownerId, IncomeBillDTO income,
                           AssetTransactionDTO asTr, List<DeliveryItemDetail> preInfoList, BigDecimal actualAmount,
                           BigDecimal penaltyAmount, List<WriteOffBillDTO> writeOffList,
                           Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> recBillMap) {
        if (CollectionUtils.isNotEmpty(specialPrestore)) {
            //组装缴费明细数据
            for (DeliveryItemDetail specPre : specialPrestore) {
                String itemId = specPre.getItemId();
                String itemName = specPre.getItemName();
                BigDecimal price = specPre.getPrice();
                List<com.charge.bill.dto.ReceivableBillDTO> orderList = recBillMap.get(Long.valueOf(itemId));
                if (CollectionUtils.isNotEmpty(orderList)) {
                    orderList.sort(Comparator.comparing(com.charge.bill.dto.ReceivableBillDTO::getBelongYears));
                    log.info("【云交付预存缴费】生成预支付订单--专项预存抵扣：{}，item_name:{}查询待预存冲抵的欠费{}", comId, itemName, orderList);

                    Iterator<com.charge.bill.dto.ReceivableBillDTO> iterator = orderList.iterator();
                    while (iterator.hasNext()){
                        com.charge.bill.dto.ReceivableBillDTO recBill = iterator.next();
                        if (price.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        }
                        if (BigDecimal.ZERO.compareTo(recBill.getArrearsAmount().add(recBill.getPenaltyArrearsAmount())) < 0 ) {
                            if (BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount()) < 0 && (price.compareTo(BigDecimal.ZERO) > 0 && price.compareTo(recBill.getArrearsAmount().add(recBill.getPenaltyArrearsAmount())) >= 0)) {
                                log.info("【云交付预存缴费】生成预支付订单---专项预存抵扣：{}，item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}", comId, itemName, price, recBill.getItemId(), recBill);
                                actualAmount = actualAmount.add(recBill.getArrearsAmount());
                                penaltyAmount = penaltyAmount.add(recBill.getPenaltyArrearsAmount());
                                price = price.subtract(recBill.getArrearsAmount()).subtract(recBill.getPenaltyArrearsAmount());
                                //新增核销单
                                addWriteOffBIlls(userName, assetId, ownerId, income, asTr, writeOffList, itemName, recBill, Long.valueOf(itemId));
                                iterator.remove();
                            } else if (price.compareTo(BigDecimal.ZERO) > 0 && BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount()) == 0) {
                                log.info("【云交付预存缴费】生成预支付订单---专项预存抵扣：{}，item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}", comId, itemName, price, recBill.getItemId(), recBill);
                                BigDecimal arrears = recBill.getArrearsAmount();
                                BigDecimal deductMoney = price.compareTo(arrears) < 0 ? price : arrears;
                                actualAmount = actualAmount.add(deductMoney);
                                price = price.subtract(deductMoney);
                                recBill.setArrearsAmount(deductMoney);
                                //新增核销单
                                addWriteOffBIlls(userName, assetId, ownerId, income, asTr, writeOffList, itemName, recBill, Long.valueOf(itemId));
                                if (arrears.compareTo(deductMoney) > 0) {
                                    recBill.setArrearsAmount(arrears.subtract(deductMoney));
                                } else {
                                    iterator.remove();
                                }
                            }
                        }

                    }

                }
                if (price.compareTo(BigDecimal.ZERO) > 0) {
                    DeliveryItemDetail newPre = new DeliveryItemDetail();
                    newPre.setPrice(price);
                    newPre.setItemId(itemId);
                    newPre.setItemName(itemName);
                    preInfoList.add(newPre);
                    log.info("【云交付预存缴费】生成预支付订单-专项预存抵扣：{}，item_name:{}price:{}剩余预存进行存储：{}", comId, itemName, price, preInfoList);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(commonPrestore)) {
            DeliveryItemDetail orderInfo = commonPrestore.get(0);
            BigDecimal commonAmount = orderInfo.getPrice();
            for (Long itemId : genaralList) {
                //获取指定收费项并按时间排序的欠费数据
                List<com.charge.bill.dto.ReceivableBillDTO> orderList = recBillMap.get(itemId);
                if (CollectionUtils.isNotEmpty(orderList)) {
                    orderList.sort(Comparator.comparing(com.charge.bill.dto.ReceivableBillDTO::getBelongYears));
                    for (com.charge.bill.dto.ReceivableBillDTO recBill : orderList) {
                        if (BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount())<0 && (commonAmount.compareTo(BigDecimal.ZERO) > 0 && commonAmount.compareTo(recBill.getArrearsAmount().add(recBill.getPenaltyArrearsAmount())) >= 0)){
                            log.info("【云交付预存缴费】生成预支付订单---通用预存抵扣：{}，item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}", comId, recBill.getItemName(), commonAmount, recBill.getItemId(),recBill);
                            actualAmount = actualAmount.add(recBill.getArrearsAmount());
                            penaltyAmount = penaltyAmount.add(recBill.getPenaltyArrearsAmount());
                            commonAmount = commonAmount.subtract(recBill.getArrearsAmount()).subtract(recBill.getPenaltyArrearsAmount());
                            //新增核销单
                            addWriteOffBIlls(userName, assetId, ownerId, income, asTr, writeOffList, recBill.getItemName(), recBill, itemId);
                        } else if (commonAmount.compareTo(BigDecimal.ZERO) > 0 && BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount())==0){
                            log.info("【云交付预存缴费】生成预支付订单---通用预存抵扣：{}，item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}", comId, recBill.getItemName(), commonAmount, recBill.getItemId(),recBill);
                            BigDecimal arrears = recBill.getArrearsAmount();
                            BigDecimal deductMoney = commonAmount.compareTo(arrears)<0? commonAmount:arrears;
                            actualAmount = actualAmount.add(deductMoney);
                            commonAmount = commonAmount.subtract(deductMoney);
                            recBill.setArrearsAmount(deductMoney);
                            //新增核销单
                            addWriteOffBIlls(userName, assetId, ownerId, income, asTr, writeOffList, recBill.getItemName(), recBill, itemId);
                        } else {
                            break;
                        }
                    }
                }
            }

            if (commonAmount.compareTo(BigDecimal.ZERO) > 0) {
                log.info("【云交付预存缴费】生成预支付订单-通用预存抵扣：{}，item_name:{}price:{}剩余预存进行存储", comId, orderInfo.getItemName(), commonAmount);
                DeliveryItemDetail newPre = new DeliveryItemDetail();
                newPre.setPrice(commonAmount);
                newPre.setItemId(PayRelatedConstants.PREDEPOSIT_GENERAL_ID_V2.toString());
                newPre.setItemName(PayRelatedConstants.PREDEPOSIT_GENERAL_NAME);
                preInfoList.add(newPre);
            }

        }
    }

    /**添加核销单信息
     * @param userName
     * @param assetId
     * @param ownerId
     * @param income
     * @param asTr
     * @param writeOffList
     * @param itemName
     * @param recBill
     * @param aLong
     */
    private void addWriteOffBIlls(String userName, Long assetId, Long ownerId, IncomeBillDTO income, AssetTransactionDTO asTr, List<WriteOffBillDTO> writeOffList, String itemName, com.charge.bill.dto.ReceivableBillDTO recBill, Long aLong) {
        if (BigDecimal.ZERO.compareTo(recBill.getArrearsAmount()) < 0) {
            String writeBillNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.WRITE_OFF_BILL.getCode()).getContent();
            WriteOffBillDTO writeOff = WriteOffBillDTO.builder().assetId(assetId).userId(ownerId)
                    .assetTransactionId(asTr.getId()).receivableBillId(recBill.getId()).billNum(writeBillNum).itemId(aLong)
                    .itemName(itemName).memo(recBill.getMemo()).actualAmount(recBill.getArrearsAmount()).chargeType(ChargeTypeEnum.PRINCIPAL.getCode())
                    .writeOffType(WriteOffTypeEnum.PAY.getCode()).belongYears(recBill.getBelongYears()).isBalance(PayRelatedConstants.IS_BALANCE_YES)
                    .chargeObject(recBill.getChargeObject()).communityId(income.getCommunityId()).build();
            writeOff.setCreateUser(userName);
            writeOff.setCreateTime(income.getCreateTime());
            writeOff.setAssetUseStatus(recBill.getAssetUseStatus());
            writeOffList.add(writeOff);
        }
        if (BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount()) < 0) {
            String writeBillNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.WRITE_OFF_BILL.getCode()).getContent();
            WriteOffBillDTO writeOff = WriteOffBillDTO.builder().assetId(assetId).userId(ownerId)
                    .assetTransactionId(asTr.getId()).receivableBillId(recBill.getId()).billNum(writeBillNum).itemId(aLong)
                    .itemName(itemName).memo(recBill.getMemo()).actualAmount(recBill.getPenaltyArrearsAmount()).chargeType(ChargeTypeEnum.PENALTY.getCode())
                    .writeOffType(WriteOffTypeEnum.PAY.getCode()).belongYears(recBill.getBelongYears()).isBalance(PayRelatedConstants.IS_BALANCE_YES)
                    .chargeObject(recBill.getChargeObject()).communityId(income.getCommunityId()).build();
            writeOff.setCreateUser(userName);
            writeOff.setCreateTime(income.getCreateTime());
            writeOff.setAssetUseStatus(recBill.getAssetUseStatus());
            writeOffList.add(writeOff);
        }
    }

    /**
     * 获取专项预存项月度金额
     * @param houseId
     * @param comId
     * @param
     * @return
     */
    private List<PrestoreChargeInfoVO> getSpecialChargeInfo(Long houseId, Long comId, List<ItemInfo> itemInfoList) throws ChargeBusinessException {

        List<PrestoreChargeInfoVO> list = new ArrayList<>();
        for (int i = 0,size=itemInfoList.size(); i < size; i++) {
            ItemInfo itemInfo = itemInfoList.get(i);
            Long itemId = itemInfo.getItemId();
            //查询费项欠费
            List<Integer> payStatusList = Lists.newArrayList(ReceivableBillPayStatusEnum.NOT_PAY.getCode(),ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode(),ReceivableBillPayStatusEnum.PAY_WAIT.getCode());
            ChargeResponse<ReceivableAmountTotalDTO> totalResponse = receivableBillClient.amountTotal(ReceivableConditionDTO.builder().communityId(comId).assetId(houseId)
                    .itemId(itemId).payStatuses(payStatusList).build());
            ReceivableAmountTotalDTO amountTotalDTO = null;
            if (totalResponse != null && totalResponse.getContent()!=null) {
                amountTotalDTO = totalResponse.getContent();
            }

            //查询房间预存信息
            ChargeResponse<List<PredepositAccountDTO>> specListResult = predepositAccountClient.listPredepositAccount(PredepositAccountConditionDTO.builder().communityId(comId)
                    .assetIds(Lists.newArrayList(houseId)).billType(PredepositBillTypeEnum.PRE_DEPOSIT.getCode())
                    .chargeObj(PayRelatedConstants.PREDEPOSIT_CHARGE_OBJ_OWNER).itemId(itemId).build());

            PredepositAccountDTO preAccDTO=null;
            if (specListResult != null && specListResult.getContent()!=null && !specListResult.getContent().isEmpty()) {
                preAccDTO = specListResult.getContent().get(0);
            }
            //计算费用
            ReceivableBillDTO reDTO = calculateSupport.calculatePreStorePerMonth(comId,houseId,itemId);
            String info ="";
            if (reDTO != null && reDTO.getChargeArea()!=null && BigDecimal.ZERO.compareTo(reDTO.getChargeArea()) <0) {
                info = "每月"+itemInfo.getItemName()+" "+reDTO.getStandardStr()+"*" + reDTO.getChargeArea() + "平米 = " + reDTO.getReceivableAmount() + "元";
            } else if (reDTO != null && (reDTO.getChargeArea()==null || BigDecimal.ZERO.compareTo(reDTO.getChargeArea())==0)) {
                info= "每月" + itemInfo.getItemName() + " " + reDTO.getReceivableAmount() + "元";
            }
            /*获取计费标准信息*/
            String chargeModel = "";
            int amountType = 0;
            if(reDTO != null && reDTO.getChargeStandardId()!=null) {
                ChargeResponse<StandardConfigDTO> standardConfigDTOChargeResponse = standardConfigClient.getDetailById(reDTO.getChargeStandardId());
                StandardConfigDTO standardConfigDTO = AppInterfaceUtil.getResponseData(standardConfigDTOChargeResponse);
                if (!Objects.isNull(standardConfigDTO)) {
                    chargeModel = standardConfigDTO.getChargeModel();
                    if (com.charge.config.enums.ChargeTypeEnum.GENERAL_CYCLE_FIXED.getModel().equals(chargeModel)) {
                        Object chargeRule = standardConfigDTO.getChargeRule();
                        if (!Objects.isNull(chargeRule)) {
                            Map<String, Object> chargeRuleJSON = (Map<String, Object>) chargeRule;
                            amountType = (Integer) chargeRuleJSON.get("amountType");
                        }
                    }
                }
            }

            list.add(PrestoreChargeInfoVO.fromSpecialPrestore(amountTotalDTO,preAccDTO,reDTO,info,itemInfo,chargeModel,amountType));
        }

        return list;
    }

    /**
     * //1.校验金额和当前生效预存组合金额，有差异时，返回对应code（-6），用于接口调用侧业务逻辑；
     * //2.校验预存组合支付状态，已支付返回相应code（-7），用于接口调用侧业务逻辑；
     * //3.预存组合已作废，或者已过有效期，或金额有变化，均提示“费用已更新”,相应code（-6）
     * //4.房屋计费配置本身已修改，导致金额差异，也提示“费用已更新”,相应code（-6）
     * @param payRequest
     * @return
     */
    @Override
    public ChargeResponse checkDeliveryInfo(DeliveryPayDataRequest payRequest, Long communityId, Long houseId) throws ChargeBusinessException {
        log.info("校验云交付资产组合信息: payRequest={}", com.alibaba.fastjson.JSONObject.toJSONString(payRequest));

        long groupId = payRequest.getPrestoreFormId();
        String money = payRequest.getMoney();
        if(StringUtils.isEmpty(money)){
            return new ChargeResponse<>(-1, "交易金额不能为空");
        }

        ChargeResponse<CommunitySpecialPrestoreGroupDTO> deliveryGroupResponse = communitySpecialPreStoreGroupClient.getDeliveryPrestoreGroupInfo(groupId);
        CommunitySpecialPrestoreGroupDTO deliveryPropertyGroup = AppInterfaceUtil.getResponseData(deliveryGroupResponse);
        if(deliveryPropertyGroup == null) {
            return new ChargeResponse<>(DeliveryConstants.ERROR_CODE_GROUP_MONEY_WRONG, "找不到该资产组合");
        }
        if(!deliveryPropertyGroup.getStatus().equals(SpecialPrestoreGroupStatusEnum.ENABLE.getCode())){
            return new ChargeResponse<>(DeliveryConstants.ERROR_CODE_GROUP_MONEY_WRONG, "该资产组合已作废或失效");
        }
        Date validStartTime = deliveryPropertyGroup.getValidStartTime();
        Date validEndTime = deliveryPropertyGroup.getValidEndTime();
        Date now = new Date();
        if(!belongCalendar(now, validStartTime, validEndTime)){
            return new ChargeResponse<>(DeliveryConstants.ERROR_CODE_GROUP_MONEY_WRONG, "该资产组合已过有效期");
        }

        //获取当前房屋是否存在云交付资产组合关联记录
        DeliveryTransactionCondition deliveryTransactionCondition = new DeliveryTransactionCondition();
        Long recordId = payRequest.getRecordId();
        deliveryTransactionCondition.setPropertyId(houseId);
        deliveryTransactionCondition.setRecordId(recordId);
        ChargeResponse<DeliveryTransactionDTO> deliveryTransactionResponse = prestoreGroupClient.queryAssetDeliveryTransaction(deliveryTransactionCondition);
        DeliveryTransactionDTO deliveryTransactionDTO = AppInterfaceUtil.getResponseData(deliveryTransactionResponse);
        if(deliveryTransactionDTO == null){
            return new ChargeResponse<>(DeliveryConstants.ERROR_CODE_GROUP_MONEY_WRONG, "交易数据异常");
        }

        BigDecimal totalMoney = new BigDecimal(0);
        Map<String, Object> queryItemMap = new HashMap<>();
        queryItemMap.put("groupId", groupId);
        queryItemMap.put("propertyId", houseId);
        DeliveryPropertyItemQueryCondition deliveryPropertyItemQueryCondition = DeliveryPropertyItemQueryCondition.builder()
                .propertyId(houseId).groupId(groupId).build();
        ChargeResponse<List<DeliveryGroupPropertyItemDTO>> deliveryPropertyItemResponse = prestoreGroupClient.queryAssetDeliveryItem(deliveryPropertyItemQueryCondition);
        if(deliveryPropertyItemResponse.isSuccess() && deliveryPropertyItemResponse.getContent().size() > 0){
            List<DeliveryGroupPropertyItemDTO> deliveryGroupPropertyItems = deliveryPropertyItemResponse.getContent();
            for (int i = 0; i < deliveryGroupPropertyItems.size(); i++) {
                DeliveryGroupPropertyItemDTO deliveryGroupPropertyItem =  deliveryGroupPropertyItems.get(i);
                totalMoney = totalMoney.add(deliveryGroupPropertyItem.getMoney());
            }
        }

        log.info("{}|查询云交付资产及收费项关系表获取的总金额：{}", LogCategoryEnum.BUSSINESS, totalMoney);
        BigDecimal payMoney = new BigDecimal(money).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (totalMoney.compareTo(payMoney) != 0) {
            return new ChargeResponse<>(DeliveryConstants.ERROR_CODE_GROUP_MONEY_WRONG, "费用已更新");
        }

        List<DeliveryItemDetail> deliveryItemDetails = payRequest.getBillDetailVOS();
        for (int i = 0; i < deliveryItemDetails.size(); i++) {
            DeliveryItemDetail item =  deliveryItemDetails.get(i);
            //根据传参判断是否为常规类收费项，从而进行房屋计费配置金额校验
            BigDecimal pricePerUnit = item.getPricePerUnit();
            String itemId = item.getItemId();
            String itemName = item.getItemName();
            ItemInfo itemInfo = new ItemInfo();
            itemInfo.setItemId(Long.valueOf(itemId));
            itemInfo.setItemName(itemName);
            if(pricePerUnit != null){
                List<PrestoreChargeInfoVO> prestoreDetailVOList = getSpecialChargeInfo(houseId, communityId, Lists.newArrayList(itemInfo));
                if (CollectionUtils.isEmpty(prestoreDetailVOList)) {
                    String errorMsg = "当前房屋预存收费项[" + itemName + "]计费配置异常";
                    return new ChargeResponse<>(DeliveryConstants.ERROR_CODE_GROUP_MONEY_WRONG, errorMsg);
                }

                PrestoreChargeInfoVO prestoreChargeInfoVO = prestoreDetailVOList.get(0);
                if (prestoreChargeInfoVO == null || prestoreChargeInfoVO.getChargeMoneyPerMonth() == null) {
                    String errorMsg = "当前房屋预存收费项[" + itemName + "]计费配置单价异常";
                    return new ChargeResponse<>(DeliveryConstants.ERROR_CODE_GROUP_MONEY_WRONG, errorMsg);
                }

                BigDecimal resultPrice = prestoreChargeInfoVO.getChargeMoneyPerMonth();
                resultPrice = resultPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
                if(pricePerUnit.compareTo(resultPrice) != 0){
                    String errorMsg = "当前房屋收费项[" + itemName + "]计费配置已修改";
                    return new ChargeResponse<>(DeliveryConstants.ERROR_CODE_GROUP_MONEY_WRONG, errorMsg);
                }
            }

        }

        if(deliveryTransactionDTO.getTransactionId() != null) {
            ShardingUtil.addCommunityId2ThreadContext(communityId);
            ChargeResponse<AssetTransactionDTO> assetTransactionDTOResponse = assetTransactionClient.getById(deliveryTransactionDTO.getTransactionId());
            AssetTransactionDTO assetTransactionDTO = AppInterfaceUtil.getResponseData(assetTransactionDTOResponse);
            if (assetTransactionDTO != null && assetTransactionDTO.getPayStatus() != null && assetTransactionDTO.getPayStatus().equals(BillPayStatusEnum.SUCCESS.getCode())) {
                return new ChargeResponse<>(DeliveryConstants.ERROR_CODE_GROUP_PAYED, "该预存组合已支付");
            }
        }

        return new ChargeResponse<>("校验通过");
    }

    /**
     * 判断时间是否在时间段内
     * @param nowTime
     * @param beginTime
     * @param endTime
     * @return
     */
    public  boolean belongCalendar(Date nowTime, Date beginTime, Date endTime) {
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);
        Calendar begin = Calendar.getInstance();
        begin.setTime(beginTime);
        Calendar end = Calendar.getInstance();
        end.setTime(endTime);
        if (date.after(begin) && date.before(end)) {
            return true;
        }else if(nowTime.compareTo(beginTime)==0 || nowTime.compareTo(endTime) == 0 ){
            return true;
        }else {
            return false;
        }
    }

    private void checkParam(DeliveryPayDataRequest request) throws ChargeBusinessException {

        if (!(YueConstants.SOURCE_YUEHOME_PAY.equals(request.getPaymentSource())
                || YueConstants.SOURCE_WECHAT_APPLET.equals(request.getPaymentSource())
                || Objects.equals(ChannelPayTypeEnum.CMB_ALI_APP.getCode(),request.getPaymentSource())
                || Objects.equals(ChannelPayTypeEnum.CMB_ALI_JSPAY.getCode(),request.getPaymentSource())
                || YueConstants.SOURCE_CRTHOME_PAY.equals(request.getPaymentSource()))) {
            throw new ChargeBusinessException("支付渠道不支持");
        }

        if (CollectionUtils.isEmpty(request.getBillDetailVOS())) {
            throw new ChargeBusinessException("【云交付预存组合缴费】充值无收费项，请重新输入");
        }
        List<DeliveryItemDetail> deliveryItemDetails = request.getBillDetailVOS();

        BigDecimal totalMoney = new BigDecimal(request.getMoney());
        BigDecimal amount = BigDecimal.ZERO;
        for (DeliveryItemDetail info :deliveryItemDetails) {
            if (BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(info.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP)) > 0) {
                throw new ChargeBusinessException("【云交付预存组合缴费】充值金额不符合要求，请重新输入");
            }
            amount=amount.add(info.getPrice());
        }
        if (totalMoney.compareTo(amount) != 0) {
            throw new ChargeBusinessException("总金额与预存明细金额不对应，请重试");
        }

        if (BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(totalMoney.setScale(2, BigDecimal.ROUND_HALF_UP)) > 0) {
            throw new ChargeBusinessException("【云交付预存组合缴费】充值金额不符合要求，请重新输入");
        }

        if (amount.compareTo(new BigDecimal(10000000)) >= 0) {
            throw new ChargeBusinessException("【云交付预存组合缴费】充值金额超出数据库存储范围，请重新输入");
        }
    }

    /**
     * 获取计算公式和计算明细
     * @param prestoreChargeInfoVO
     * @param resultPrice
     * @param month
     * @param resultMoney
     * @return
     */
    private Map<String, String> convertCalculateInfo(PrestoreChargeInfoVO prestoreChargeInfoVO, BigDecimal resultPrice, int month, BigDecimal resultMoney){
        Map<String, String> map = new HashMap<>();
        switch (prestoreChargeInfoVO.getChargeModel()){
            case "normal":
                map.put("calculateFormula", "单价 * 面积 * 月份 = 总金额");
                map.put("calculateDetail",  prestoreChargeInfoVO.getStandardStr() + " X " + prestoreChargeInfoVO.getChargeArea() + " X " + month + " = " + resultMoney);
                break;
            case "fixed":
                map.put("calculateFormula", "金额 * 月份 = 总金额");
                map.put("calculateDetail", prestoreChargeInfoVO.getStandardStr() + " X " + month + " = " + resultMoney);
                break;
            case "cyclePeriod":
                map.put("calculateFormula", "金额 * 月份 = 总金额");
                map.put("calculateDetail", prestoreChargeInfoVO.getStandardStr() + " X " + month + " = " + resultMoney);
                break;
            case "cycleFixed":
                map = handleCycleFixed(prestoreChargeInfoVO, month, resultMoney);
                break;
            default:
                map.put("calculateFormula", "");
                map.put("calculateDetail", "");
                break;
        }
        return map;
    }

    /**
     * 返回已支付云交付的订单详情
     * @param houseId
     * @param deliveryTransactionDTO
     * @param assetTransactionDTO
     * @return
     */
    private ChargeResponse<Map<String, Object>> payedBillDetail(Long houseId, DeliveryTransactionDTO deliveryTransactionDTO, AssetTransactionDTO assetTransactionDTO){
        Map<String, Object> deliveryOrder = new HashMap<>();

        //获取历史快照数据
        DeliveryPropertyItemQueryCondition deliveryPropertyItemQueryCondition1 = DeliveryPropertyItemQueryCondition.builder()
                .propertyId(houseId).groupId(deliveryTransactionDTO.getGroupId()).build();
        ChargeResponse<List<DeliveryGroupPropertyItemDTO>> deliveryPropertyItemResponse1 = prestoreGroupClient.queryAssetDeliveryItem(deliveryPropertyItemQueryCondition1);
        List<DeliveryGroupPropertyItemDTO> dtos = AppInterfaceUtil.getResponseData(deliveryPropertyItemResponse1);
        List billList = new ArrayList();
        if(dtos != null && dtos.size() > 0) {
            ChargeResponse<List<CommunitySpecialPrestoreGroupItemDTO>> itemResponse = communitySpecialPreStoreGroupClient.getPrestoreGroupItemList(dtos.get(0).getGroupId());
            List<CommunitySpecialPrestoreGroupItemDTO> itemDTOS = AppInterfaceUtil.getResponseData(itemResponse);
            final Map<Long, CommunitySpecialPrestoreGroupItemDTO> itemMap = Optional.ofNullable(itemDTOS).isPresent() ?
                    itemDTOS.stream().collect(Collectors.toMap(CommunitySpecialPrestoreGroupItemDTO::getId, item -> item)) : new HashMap<>();
            billList = dtos.stream().map(item -> {
                Map<String, Object> map = new HashMap();
                CommunitySpecialPrestoreGroupItemDTO itemDTO = itemMap.get(item.getGroupItemId());
                map.put("itemId", Optional.ofNullable(itemDTO).isPresent() ? itemDTO.getItemId() : "0");
                map.put("itemName", Optional.ofNullable(itemDTO).isPresent() ? itemDTO.getItemName() : "云交付收费项");
                map.put("monthNum", item.getMonth());
                map.put("price", item.getMoney());
                map.put("pricePerUnit", item.getPrice());
                if(Objects.nonNull(item.getPrice()) && Objects.nonNull(item.getMonth()) && Objects.nonNull(item.getMoney()) ) {
                    map.put("calculateFormula", "金额 * 月份 = 总金额");
                    map.put("calculateDetail", item.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP) + " X " + item.getMonth() + " = " + item.getMoney().setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                return map;
            }).collect(Collectors.toList());
        }
        deliveryOrder.put("billDetailVOS", billList);
        deliveryOrder.put("money", assetTransactionDTO.getMoney());
        deliveryOrder.put("payMember", assetTransactionDTO.getOwnerName());
        String paymentMethodForV1 = PaymentMethodSupport.convertTOPaymentMethodV1(assetTransactionDTO.getPaymentMethod());
        deliveryOrder.put("paymentMethod", paymentMethodForV1);
        deliveryOrder.put("orderNum", assetTransactionDTO.getAssetOrderNum());
        deliveryOrder.put("transactionNo", assetTransactionDTO.getBankTransactionNo());
        deliveryOrder.put("payTime", DateUtils.format(assetTransactionDTO.getPaymentTime(), DateUtils.FORMAT_0));
        deliveryOrder.put("address", assetTransactionDTO.getCommunityName()+assetTransactionDTO.getBuildingName()
                +assetTransactionDTO.getUnitName()+assetTransactionDTO.getAssetName());
        deliveryOrder.put("payStatus", BillPayStatusEnum.SUCCESS.getCode());
        return new ChargeResponse(deliveryOrder);
    }

    private Map handleCycleFixed(PrestoreChargeInfoVO prestoreChargeInfoVO, int month, BigDecimal resultMoney){
        Map<String, String> map = new HashMap<>();
        if(prestoreChargeInfoVO.getAmountType()==1) {
            map.put("calculateFormula", "单价 * 面积 * 月份 = 总金额");
            map.put("calculateDetail", prestoreChargeInfoVO.getStandardStr() + " X " + prestoreChargeInfoVO.getChargeArea() + " X " + month + " = " + resultMoney);
        } else if(prestoreChargeInfoVO.getAmountType()==2) {
            map.put("calculateFormula", "金额 * 月份 = 总金额");
            map.put("calculateDetail", prestoreChargeInfoVO.getStandardStr() + " X " + month + " = " + resultMoney);
        }
        return map;
    }

    @Override
    public ChargeResponse<DeliveryGroupResponse> batchQueryGroupByCommunity(DeliveryGroupCommunityCondition deliveryGroupCommunityCondition) throws ChargeBusinessException {

        List<String> communityMsIds = deliveryGroupCommunityCondition.getCommunityMsIds();
        List<CommunityDTO> communityDTOList= communitySupport.getV2CommunityByIds(communityMsIds);
        List<Long> communityIds = communityDTOList.stream().map(CommunityDTO::getId).collect(Collectors.toList());
        Map<Long, String> communityId2MsId = communityDTOList.stream().collect(Collectors.toMap(CommunityDTO::getId, CommunityDTO::getMsId, (a,b) -> b));
        if (CollectionUtil.isEmpty(communityIds)) {
            return new ChargeResponse(DeliveryConstants.RST_CODE_FOUNDNODATA_ERROR, "项目不存在");
        }

        String nowDateStr = DateUtils.getDateStr();
        DeliveryPrestoreGroupCondition deliveryPrestoreGroupCondition = DeliveryPrestoreGroupCondition.builder()
                .communityIds(communityIds).date(nowDateStr).build();
        List<CommunitySpecialPrestoreGroupDTO> groupDTOS = AppInterfaceUtil.getDataThrowException(communitySpecialPreStoreGroupClient.getDeliveryPrestoreGroupByCommmunityIds(deliveryPrestoreGroupCondition));
        if(CollectionUtil.isEmpty(groupDTOS)){
            DeliveryGroupResponse response = new DeliveryGroupResponse();
            response.setValid(Lists.emptyList());
            List<DeliveryGroupCheckResult> invalidResponse = deliveryGroupCommunityCondition.getCommunityMsIds().stream().map(e -> DeliveryGroupCheckResult.builder().msId(e)
                    .errorMsg("该项目未配置组合定价").build()).collect(Collectors.toList());
            response.setInValid(invalidResponse);
            return new ChargeResponse(response);
        }

        List<Long> validCommunityIds = groupDTOS.stream().map(CommunitySpecialPrestoreGroupDTO::getCommunityId).collect(Collectors.toList());
        List<String> validCommunityMsIds = validCommunityIds.stream().map(e -> communityId2MsId.get(e)).collect(Collectors.toList());
        List<DeliveryGroupCheckResult> validResponse = validCommunityIds.stream().map(e -> DeliveryGroupCheckResult.builder().msId(communityId2MsId.get(e)).build()).collect(Collectors.toList());
        List<String> invalidCommunityMsIds = communityMsIds.stream().filter(e -> !validCommunityMsIds.contains(e)).collect(Collectors.toList());
        List<DeliveryGroupCheckResult> invalidResponse = invalidCommunityMsIds.stream().map(e -> DeliveryGroupCheckResult.builder().msId(e)
                .errorMsg("该项目未配置组合定价").build()).collect(Collectors.toList());
        DeliveryGroupResponse response = new DeliveryGroupResponse();
        response.setValid(validResponse);
        response.setInValid(invalidResponse);
        return new ChargeResponse(response);
    }

    @Override
    public ChargeResponse<DeliveryGroupResponse> batchQueryGroupByAsset(DeliveryGroupAssetCondition deliveryGroupAssetCondition) throws ChargeBusinessException {
        List<DeliveryGroupCheckResult> invalidResponse = new ArrayList<>();
        List<String> assetMsIds = deliveryGroupAssetCondition.getAssetInfos().stream().map(DeliveryGroupAssetInfo::getAssetMsId).collect(Collectors.toList());
        List<AssetAdapter> assetAdapters = assetSupport.getAssetListByCondition(AssetCondition.builder().msIds(assetMsIds).build());
        List<Long> communityIds = assetAdapters.stream().map(AssetAdapter::getCommunityId).collect(Collectors.toList());

        String nowDateStr = DateUtils.getDateStr();
        DeliveryPrestoreGroupCondition deliveryPrestoreGroupCondition = DeliveryPrestoreGroupCondition.builder()
                .communityIds(communityIds).date(nowDateStr).build();
        List<CommunitySpecialPrestoreGroupDTO> groupDTOS = AppInterfaceUtil.getDataThrowException(communitySpecialPreStoreGroupClient.getDeliveryPrestoreGroupByCommmunityIds(deliveryPrestoreGroupCondition));
        if(CollectionUtil.isEmpty(groupDTOS)){
            DeliveryGroupResponse response = new DeliveryGroupResponse();
            response.setValid(Lists.emptyList());
            invalidResponse = deliveryGroupAssetCondition.getAssetInfos().stream().map(e -> DeliveryGroupCheckResult.builder().msId(e.getCommunityMsId())
                    .errorMsg("该项目未配置组合定价").build()).collect(Collectors.toList());
            response.setInValid(invalidResponse);
            return new ChargeResponse(response);
        }

        List<Long> groupIds = groupDTOS.stream().map(CommunitySpecialPrestoreGroupDTO::getId).collect(Collectors.toList());
        Map<Long, Long> communityId2GroupIDMap = groupDTOS.stream().collect(Collectors.toMap(CommunitySpecialPrestoreGroupDTO::getCommunityId, CommunitySpecialPrestoreGroupDTO::getId, (a, b) -> b));
        List<CommunitySpecialPrestoreGroupItemDTO> groupItemDTOS = AppInterfaceUtil.getDataThrowException(communitySpecialPreStoreGroupClient.getPrestoreGroupItemByGroupIds(DeliveryGroupItemCondition.builder().groupIds(groupIds).build()));
        if(CollectionUtil.isEmpty(groupItemDTOS)){
            DeliveryGroupResponse response = new DeliveryGroupResponse();
            response.setValid(Lists.emptyList());
            invalidResponse = deliveryGroupAssetCondition.getAssetInfos().stream().map(e -> DeliveryGroupCheckResult.builder().msId(e.getCommunityMsId())
                    .errorMsg("该项目未配置组合定价收费项").build()).collect(Collectors.toList());
            response.setInValid(invalidResponse);
            return new ChargeResponse(response);
        }
        Map<Long, List<CommunitySpecialPrestoreGroupItemDTO>> groupId2ItemMap = groupItemDTOS.stream().collect(Collectors.groupingBy(CommunitySpecialPrestoreGroupItemDTO::getGroupId));

        for(AssetAdapter assetAdapter : assetAdapters){
            checkAssetDeliveryGroup(assetAdapter, communityId2GroupIDMap, groupId2ItemMap, invalidResponse);
        }
        List<String> invalidMsIds = invalidResponse.stream().map(DeliveryGroupCheckResult::getMsId).collect(Collectors.toList());
        List<String> validMsIds = assetMsIds.stream().filter(e -> !invalidMsIds.contains(e)).collect(Collectors.toList());
        List<DeliveryGroupCheckResult> validResponse = validMsIds.stream().map(e -> DeliveryGroupCheckResult.builder().msId(e).build()).collect(Collectors.toList());
        DeliveryGroupResponse response = new DeliveryGroupResponse();
        response.setValid(validResponse);
        response.setInValid(invalidResponse);
        return new ChargeResponse(response);
    }

    private void checkAssetDeliveryGroup(AssetAdapter assetAdapter, Map<Long, Long> communityId2GroupIDMap,
                                         Map<Long, List<CommunitySpecialPrestoreGroupItemDTO>> groupId2ItemMap,
                                         List<DeliveryGroupCheckResult> invalidResponse) throws ChargeBusinessException{
        Long houseId = assetAdapter.getId();
        Long communityId = assetAdapter.getCommunityId();
        Long groupId = communityId2GroupIDMap.get(communityId);
        if(Objects.isNull(groupId)){
            String errorMsg = "当前项目:" + assetAdapter.getCommunityName() + "未配置组合定价";
            DeliveryGroupCheckResult checkResult = DeliveryGroupCheckResult.builder().msId(assetAdapter.getMsId()).errorMsg(errorMsg).build();
            invalidResponse.add(checkResult);
            return;
        }
        List<CommunitySpecialPrestoreGroupItemDTO> deliveryGroupItemList = groupId2ItemMap.get(groupId);
        if(CollectionUtil.isEmpty(deliveryGroupItemList)){
            String errorMsg = "当前项目:" + assetAdapter.getCommunityName() + "未配置组合定价收费项";
            DeliveryGroupCheckResult checkResult = DeliveryGroupCheckResult.builder().msId(assetAdapter.getMsId()).errorMsg(errorMsg).build();
            invalidResponse.add(checkResult);
            return;
        }
        for (int i = 0; i < deliveryGroupItemList.size(); i++) {
            CommunitySpecialPrestoreGroupItemDTO groupItem = deliveryGroupItemList.get(i);
            String chargeType = groupItem.getChargeType();
            Long itemId = groupItem.getItemId();
            String itemName = groupItem.getItemName();
            ItemInfo itemInfo = new ItemInfo();
            itemInfo.setItemId(itemId);
            itemInfo.setItemName(itemName);
            if ("general".equals(chargeType)) {
                List<PrestoreChargeInfoVO> prestoreDetailVOList = getSpecialChargeInfo(houseId, communityId, Lists.newArrayList(itemInfo));
                if (CollectionUtils.isEmpty(prestoreDetailVOList)) {
                    String errorMsg = "当前房屋["+ assetAdapter.getAssetName() + "]预存收费项[" + itemName + "]计费配置异常";
                    DeliveryGroupCheckResult checkResult = DeliveryGroupCheckResult.builder().msId(assetAdapter.getMsId()).errorMsg(errorMsg).build();
                    invalidResponse.add(checkResult);
                    break;
                }

                PrestoreChargeInfoVO prestoreChargeInfoVO = prestoreDetailVOList.get(0);
                if (prestoreChargeInfoVO == null || prestoreChargeInfoVO.getChargeMoneyPerMonth() == null) {
                    String errorMsg = "当前房屋["+ assetAdapter.getAssetName() + "]预存收费项[" + itemName + "]计费配置单价异常";
                    DeliveryGroupCheckResult checkResult = DeliveryGroupCheckResult.builder().msId(assetAdapter.getMsId()).errorMsg(errorMsg).build();
                    invalidResponse.add(checkResult);
                    break;
                }

            }

        }
    }


}
