package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/23 10:03
 */
@Data
public class EBusinessRefundOrderCondition {

    /**
     * 小区id
     */
    @NotNull(message = "项目id不能为空")
    private Long communityId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    /**
     * 退款单号
     */
    @NotBlank(message = "订单号不能为空")
    private String refundId;

    /**
     * 退款流水号
     */
    @NotBlank(message = "退款流水号不能为空")
    private String tradeNo;

    /**
     * 原退款金额
     */
    @NotNull(message = "原退款金额不能为空")
    private BigDecimal originPrice;
    /**
     * 实际退款金额
     */
    @NotNull(message = "实际退款金额不能为空")
    private BigDecimal actualPrice;
    /**
     * 支付方式(微信：WECHAT 支付宝：ALIPAY 线下支付：OFFLINE)
     */
    @NotBlank(message = "支付方式不能为空")
    private String payType;
    /**
     * 退款成功时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "退款成功时间不能为空")
    private String refundTime;
    /**
     * 商品明细
     */
    @Valid
    private List<EBusinessRefundItem> refundItems;

}
