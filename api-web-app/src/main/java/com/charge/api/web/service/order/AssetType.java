package com.charge.api.web.service.order;

/**
 * 资产类型
 *
 * <AUTHOR>
 * @date 2023/3/3
 */
public enum AssetType {
    HOUSE(1, "house"),

    PARKING_SPACE(2, "parking-space"),

    COMMUNITY(9, "community");

    AssetType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    int code;
    String value;
}
