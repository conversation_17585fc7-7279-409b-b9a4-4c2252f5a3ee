package com.charge.api.web.controller.pos;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.controller.FileUploadController;
import com.charge.api.web.dto.pos.AddPredepositRequestDTO;
import com.charge.api.web.service.pos.ChargeBillService;
import com.charge.api.web.service.pos.PreDepositService;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.FileUploadVO;
import com.charge.api.web.vo.joylife.request.PreStoreCalReq;
import com.charge.api.web.vo.lakala.AddDepositRequest;
import com.charge.api.web.vo.lakala.PaymentResponse;
import com.charge.api.web.vo.pos.*;
import com.charge.bill.dto.predeposit.WaitingCarryForwardDTO;
import com.charge.bill.dto.predeposit.pos.PredepositRefundDetailPosDTO;
import com.charge.bill.dto.predeposit.pos.PredepositRefundSearchDetailDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AssertUtils;
import com.charge.starter.web.annotation.Idempotent;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 *pos 预存/待结转/退款相关接口
 */

@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PreDepositController {

    private final PreDepositService depositService;

    private final ChargeBillService chargeBillService;

    private final FileUploadController fileUploadController;

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseUuid", value = "房屋uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getWaitingCarryForward", method = {RequestMethod.GET})
    public ChargeResponse<WaitingCarryForwardDTO> getWaitingCarryForward(String communityUuid, String houseUuid, String token) {
        return depositService.getWaitingCarryForward(communityUuid, houseUuid);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseUuid", value = "房屋uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getPrestoreDetail", method = {RequestMethod.GET})
    public ChargeResponse<PreDepositAccountPosDTOV1> getPrestoreDetail(String communityUuid, String houseUuid, String token) {
        return depositService.getPreStoreDetail(communityUuid, houseUuid);
    }

    @ApiOperation(value = "预存项月度费用", notes = "预存项月度费用")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "subjectId", value = "房屋Id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemUuid", value = "预存收费项Id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯秘串(来自鉴权微服务的access_token)", required = true, dataType = "String")
    })
    @RequestMapping(value = "/getPrestoreItemCostPerMonth", method = {RequestMethod.GET})
    public ChargeResponse<PredepositItemCostPerMonthVO> getPrestoreItemCostPerMonth(String communityUuid, String subjectId, String itemUuid, String token) throws ChargeBusinessException {
        Long communityId = Long.valueOf(communityUuid);
        Long assetId = Long.valueOf(subjectId);
        Long itemId = Long.valueOf(itemUuid);
        PredepositItemCostPerMonthVO result = depositService.getPredepositItemCostPerMonth(communityId, assetId, itemId);
        return new ChargeResponse<>(result);
    }


    /**
     * 计算预存项月度费用
     *
     * @param calRequest 计算预存项月度费用请求
     * @return 预存额度
     * @throws ChargeBusinessException 业务异常
     */
    @PostMapping("/new/chargeAPI/calPreStore")
    public ChargeResponse<PredepositItemCostPerMonthVO> calPreStore(@RequestBody @Validated PreStoreCalReq calRequest) throws ChargeBusinessException {
        return new ChargeResponse<>(depositService.calPreStore(calRequest));
    }

    @ApiOperation(value = "预存充值", notes = "添加预存订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "houseUuid", value = "房屋Uuid", required = true, dataType = "String"),
            @ApiImplicitParam(name = "money", value = "总金额", required = true, dataType = "String"),
            @ApiImplicitParam(name = "payMoney", value = "通用预存充值金额", required = true, dataType = "String"),
            @ApiImplicitParam(name = "paymentMethod", value = "支付方式(0表示POS,1表示转账,2表示支票,3表示支付宝,4表示微信,6员工代付)", required = true, dataType = "String"),
            @ApiImplicitParam(name = "jsonContent", value = "添加费用-临时(json数据格式)[{\"chargeType\":\"0\",\"itemID\":\"100\",\"itemName\":\"收费项名称\",\"incomeDetailUuid\":\"200\",\"incomeDetailName\":\"收费细项名称\",\"price\":\"100\"},{\"chargeType\":\"0\",\"itemID\":\"200\",\"itemName\":\"收费项名称\",\"incomeDetailUuid\":\"200\",\"incomeDetailName\":\"收费细项名称\",\"price\":\"100\"}]", required = false, dataType = "String"),
            @ApiImplicitParam(name = "prestoreJson", value = "添加专项预存充值(json数据格式)[{\"itemID\":\"100\",\"itemName\":\"收费项名称\",\"price\":\"100\"},{\"itemID\":\"200\",\"itemName\":\"收费项名称\",\"price\":\"100\"}]", required = false, dataType = "String"),
            @ApiImplicitParam(name = "collectorId", value = "收费员id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "collectorName", value = "收费员姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "payMember", value = "缴费人", required = false, dataType = "String"),
            @ApiImplicitParam(name = "bankTransactionNo", value = "银行流水号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "arrivalDate", value = "到账日期(支票/转账为yyyy-MM-dd HH:mm:ss,其余为yyyy-MM-dd)", required = false, dataType = "String"),
            @ApiImplicitParam(name = "bankAccountUuid", value = "项目银行账号配置id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "bankAccountNum", value = "银行账号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "deviceInfo", value = "设备号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "mercid", value = "商户号", required = false, dataType = "String"),
            @ApiImplicitParam(name = "memo", value = "备注信息", required = false, dataType = "String"),
            @ApiImplicitParam(name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/addNewDepositInfo", method = {RequestMethod.POST})
    @Idempotent
    public ChargeResponse<AddPredepositResultVo> addNewDepositInfo(String houseUuid, String money, String payMoney, String paymentMethod, String jsonContent,
                                                                   String prestoreJson, String collectorId, String collectorName, String payMember,
                                                                   String bankTransactionNo, String arrivalDate, String bankAccountUuid, String bankAccountNum,
                                                                   String deviceInfo, String mercid,
                                                                   String memo, String token) throws Exception {
        if (StringUtils.isBlank(jsonContent)) {
            jsonContent = "[]";
        }
        if (StringUtils.isBlank(prestoreJson)) {
            prestoreJson = "[]";
        }

        //平铺的1.0入参参数组装成DTO
        AddPredepositRequestDTO requestDTO = AddPredepositRequestDTO.builder()
                .assetId(Long.valueOf(houseUuid))
                .money(money)
                .payMoney(payMoney)
                .paymentMethod(paymentMethod)
                .tempBillJson(jsonContent)
                .specialPreStoreJson(prestoreJson)
                .collectorId(collectorId)
                .collectorName(collectorName)
                .payMember(payMember)
                .bankTransactionNo(bankTransactionNo)
                .arrivalDate(arrivalDate)
                .bankAccountUuid(bankAccountUuid)
                .bankAccountNum(bankAccountNum)
                .deviceInfo(deviceInfo)
                .mercid(mercid)
                .memo(memo)
                .build();

        AddPredepositResultVo resultVo = depositService.addNewDepositInfo4House(requestDTO);
        return new ChargeResponse<>(resultVo);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "账单列表返回的id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseId", value = "房屋id", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getCheckDetail", method = {RequestMethod.GET})
    public ChargeResponse<PredepositRefundDetailPosDTO> getCheckDetail(String id, String communityUuid, String houseId, String token) throws ChargeBusinessException {
        return depositService.getCheckDetail(id, communityUuid, houseId);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "receiptNo", value = "收据号", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "paymentName", value = "缴费人姓名", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseNo", value = "房间号", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "payStart", value = "日期开始时间（yyyy-MM-dd）", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "payEnd", value = "日期结束时间（yyyy-MM-dd）", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "payMonth", value = "按月搜索（yyyy-MM）", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "所要查询的页数,默认为1", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量，默认为10", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/refundSerach", method = {RequestMethod.GET})
    public ChargePageResponse<List<PredepositRefundSearchDetailDTO>> refundSearch(String communityUuid,
                                                                                  String receiptNo,
                                                                                  String paymentName,
                                                                                  String houseNo,
                                                                                  String payStart,
                                                                                  String payEnd,
                                                                                  String payMonth,
                                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                  @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                                  String token) {
        return depositService.refundSearch(communityUuid, receiptNo, paymentName, houseNo, payStart, payEnd, payMonth,
                pageNum, pageSize);

    }

    @ApiOperation(value = "添加房屋押金(112)", notes = "添加房屋押金(112)")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "houseUuid", value = "房屋uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemId", value = "押金项(收费项)id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemName", value = "押金项(收费项)名称", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "incomeDetailUuid", value = "收费细项uuid", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "incomeDetailName", value = "收费细项名称", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "paymentMethod", value = "支付方式（0表示POS,1表示转账,2表示支票,3表示支付宝,4表示微信,6员工代付）", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "money", value = "金额", required = true, dataType = "double"),
            @ApiImplicitParam(paramType = "query", name = "collectorId", value = "收费员id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "collectorName", value = "收费员姓名", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "payMember", value = "缴费人", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "memo", value = "备注信息", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "deviceInfo", value = "设备号", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "mercid", value = "商户号", required = false, dataType = "String"),
    })
    @PostMapping(value = "/addNewHouseDeposit")
    @Idempotent
    public ChargeResponse<PaymentResponse> addNewHouseDeposit(@RequestParam String deviceInfo, @RequestParam String mercid, @RequestParam Long houseUuid, @RequestParam String collectorId,
                                                              @RequestParam String collectorName, @RequestParam(required = false)String incomeDetailName, @RequestParam(required = false) String incomeDetailUuid,
                                                              @RequestParam BigDecimal money, @RequestParam String itemName, @RequestParam Long itemId, @RequestParam String paymentMethod, @RequestParam String token
            , @RequestParam String payMember, @RequestParam String memo) throws Exception {
        Assert.isTrue(money.compareTo(BigDecimal.ZERO)>0,"金额必须大于0");
        AddDepositRequest request = AddDepositRequest.builder()
                .deviceInfo(deviceInfo).mercid(mercid).houseId(houseUuid).collectorId(collectorId).collectorName(collectorName).incomeDetailUuid(incomeDetailUuid).money(money).memo(memo)
                .itemId(itemId).itemName(itemName).paymentMethod(paymentMethod).payMember(payMember).build();
        PaymentResponse response = chargeBillService.addDeposit(request,"/addNewHouseDeposit");
        return new ChargeResponse<>(response);
    }

    @ApiOperation(value = "添加小区押金(112)", notes = "添加小区押金(112)")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "communityName", value = "小区名称", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemId", value = "押金项(收费项)id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemName", value = "押金项(收费项)名称", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "incomeDetailUuid", value = "收费细项uuid", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "incomeDetailName", value = "收费细项名称", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "paymentMethod", value = "支付方式（0表示POS,1表示转账,2表示支票,3表示支付宝,4表示微信,6员工代付）", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "money", value = "金额", required = true, dataType = "double"),
            @ApiImplicitParam(paramType = "query", name = "collectorId", value = "收费员id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "collectorName", value = "收费员姓名", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "payMember", value = "缴费人", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "memo", value = "备注信息", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "deviceInfo", value = "设备号", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "mercid", value = "商户号", required = false, dataType = "String"),
    })
    @PostMapping(value = "/addNewCommunityDeposit")
    @Idempotent(keyAppendSecond = true)
    public ChargeResponse<PaymentResponse> addNewCommunityDeposit(@RequestParam(required = false) String deviceInfo, @RequestParam String mercid, @RequestParam Long communityUuid, @RequestParam String collectorId,
                                                                  @RequestParam String collectorName, @RequestParam(required = false)String incomeDetailName, @RequestParam(required = false) String incomeDetailUuid,
                                                                  @RequestParam  BigDecimal money, @RequestParam String itemName, @RequestParam Long itemId, @RequestParam String paymentMethod,@RequestParam String token
            , @RequestParam String payMember, @RequestParam String memo) throws Exception {
        AssertUtils.isTrue(money.compareTo(BigDecimal.ZERO)>0,"金额必须大于0");
        AddDepositRequest request = AddDepositRequest.builder()
                .deviceInfo(deviceInfo).mercid(mercid).communityUuid(communityUuid).collectorId(collectorId).collectorName(collectorName).incomeDetailUuid(incomeDetailUuid).money(money).memo(memo)
                .itemId(itemId).itemName(itemName).paymentMethod(paymentMethod).payMember(payMember).build();
        PaymentResponse response = chargeBillService.addDeposit(request, "/addNewCommunityDeposit");
        return new ChargeResponse<>(response);
    }


    @ApiOperation(value = "预存退款", notes = "预存退款")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseId", value = "房屋编号", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "money", value = "退款金额", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "refundJson", value = "添加专项预存退款(json数据格式)[{\"itemID\":\"100\",\"itemName\":\"收费项名称\",\"price\":\"100\"},{\"itemID\":\"200\",\"itemName\":\"收费项名称\",\"price\":\"100\"}]", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "operator", value = "退款操作人", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "operatorId", value = "退款操作人id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "draweeName", value = "退款人", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "advanced", value = "员工是否垫付(0:否，1：是,默认0)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "employeeName", value = "垫付人", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "remark", value = "退款说明", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "revertPoints", value = "扣除万象星数量", dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "pid", value = "大会员账号pid", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "phone", value = "会员手机号", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @PostMapping(value = "/addPresotreMsg")
    @Idempotent
    public ChargeResponse<String> addPresotreMsg(@RequestParam Long communityUuid, @RequestParam Long houseId, @RequestParam(defaultValue = "0") BigDecimal money,
                                                 @RequestParam(required = false) String refundJson, @RequestParam String operator, @RequestParam String operatorId,
                                                 @RequestParam String draweeName, @RequestParam(value = "advanced", defaultValue = "0") String advanced,
                                                 @RequestParam(required = false) Integer revertPoints, @RequestParam(required = false) String pid, @RequestParam(required = false) String phone,
                                                 @RequestParam(required = false) String employeeName, @RequestParam String remark, @RequestParam String token,
                                                 @RequestParam(required = false) String acceptBankAccount, @RequestParam(required = false) String acceptBankName,
                                                 @RequestParam(required = false) String acceptAccountName, @RequestParam(required = false) String acceptAccountOpeningBank,
                                                 @RequestParam(required = false) String acceptAccountCity,@RequestParam(required = false) String payBankAccount) throws ChargeBusinessException {
        Assert.isTrue(money.compareTo(BigDecimal.ZERO)>0||StringUtils.isNotBlank(refundJson),"通用预存和专项预存退款不能同时为空");
        PreStoreRefundRequest.PreStoreRefundRequestBuilder requestBuilder = PreStoreRefundRequest.builder().operator(operator).operatorId(operatorId).communityId(communityUuid).houseId(houseId)
                .commonRefundMoney(money).draweeName(draweeName).advancePay("1".equals(advanced)).remark(remark).acceptBankAccount(acceptBankAccount).acceptBankName(acceptBankName)
                .revertPoints(revertPoints).phone(phone).pid(pid)
                .acceptAccountName(acceptAccountName).acceptAccountOpeningBank(acceptAccountOpeningBank).acceptAccountCity(acceptAccountCity).payBankAccount(payBankAccount)
                .commonRefundMoney(money).draweeName(draweeName).advancePay("1".equals(advanced)).remark(remark)
                .revertPoints(revertPoints).phone(phone).pid(pid);
        if(StringUtils.isNotBlank(refundJson)){
            requestBuilder.specialRefundChargeItems(JSON.parseArray(refundJson, ChargeItemPriceV2.class));
        }
        chargeBillService.preStoreRefund(requestBuilder.build());
        return new ChargeResponse<>();
    }


    @ApiOperation(value = "押金退款", notes = "押金退款")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "payId", value = "押金缴费id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseId", value = "房屋编号(房屋押金请填写该参数)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemId", value = "押金项id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemName", value = "押金项名称", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "money", value = "退款金额", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "operator", value = "退款操作人", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "operatorId", value = "退款操作人id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "draweeName", value = "缴款人", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "receiptNo", value = "收据号", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "advanced", value = "员工是否垫付(0:否，1：是,默认0)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "employeeName", value = "垫付人", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "remark", value = "退款说明", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "refundStyle", value = "退款方式：默认0，银行卡，1转预存", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "counterofferFile", value = "上传图片", required = false, dataType = "file")
    })
    @PostMapping(value = "/addDepositMsg")
    @Idempotent
    public ChargeResponse<String> addDepositMsg(@RequestParam Long communityUuid, @RequestParam Long payId, @RequestParam(required = false) Long houseId,
                                                @RequestParam Long itemId, @RequestParam String itemName, @RequestParam  BigDecimal money,
                                                @RequestParam String operator, @RequestParam String operatorId,  @RequestParam String draweeName,
                                                @RequestParam String receiptNo, @RequestParam(value = "advanced", defaultValue = "0") String advanced,
                                                @RequestParam(required = false) String employeeName,  @RequestParam String remark, @RequestParam String token,
                                                @RequestParam(value = "refundStyle", defaultValue = "0") String refundStyle,
                                                @RequestParam(value = "counterofferFile", required = false) MultipartFile[] counterofferFile,
                                                @RequestParam(required = false) String acceptBankAccount, @RequestParam(required = false) String acceptBankName,
                                                @RequestParam(required = false) String acceptAccountName, @RequestParam(required = false) String acceptAccountOpeningBank,
                                                @RequestParam(required = false) String acceptAccountCity,@RequestParam(required = false) String payBankAccount) throws ChargeBusinessException, IOException {
        Assert.isTrue(money.compareTo(BigDecimal.ZERO)>0,"退款金额必须大于0");
        PreDepositRefundRequest.PreDepositRefundRequestBuilder requestBuilder = PreDepositRefundRequest.builder().communityId(communityUuid).predepositAccountId(payId)
                .houseId(houseId).itemId(itemId).itemName(itemName).money(money).operator(operator).operatorId(operatorId).draweeName(draweeName).receiptNo(receiptNo)
                .advancePay("1".equals(advanced)).employeeName(employeeName).remark(remark).toPreStore("1".equals(refundStyle)).acceptBankAccount(acceptBankAccount)
                .acceptBankName(acceptBankName).acceptAccountName(acceptAccountName).acceptAccountOpeningBank(acceptAccountOpeningBank).acceptAccountCity(acceptAccountCity)
                .payBankAccount(payBankAccount);
        if(counterofferFile!=null&&counterofferFile.length>0){
            ChargeResponse<List<FileUploadVO>> chargeResponse = fileUploadController.batchUpload(counterofferFile);
            List<FileUploadVO> fileUploads = chargeResponse.getContent();
            if(!CollectionUtils.isEmpty(fileUploads)){
                List<Long> fileIds = fileUploads.stream().map(FileUploadVO::getId).collect(Collectors.toList());
                requestBuilder.fileIds(fileIds);
            }
        }
        chargeBillService.preDepositRefund(requestBuilder.build());
        return new ChargeResponse<>();
    }

}
