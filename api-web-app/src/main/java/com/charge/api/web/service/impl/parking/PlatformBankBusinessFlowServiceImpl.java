package com.charge.api.web.service.impl.parking;

import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.service.parking.PlatformBankBusinessFlowService;
import com.charge.api.web.support.AssetSupport;
import com.charge.bill.client.*;
import com.charge.bill.dto.income.*;
import com.charge.bill.dto.tempark.ParkingSyncRecordConditionDTO;
import com.charge.bill.dto.tempark.ParkingSyncRecordDTO;
import com.charge.bill.dto.tempark.TemparkPayDetailDTO;
import com.charge.bill.enums.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.enums.redis.RedisKeyLockEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.util.RedisKeyLockUtil;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import com.charge.order.client.OrderClient;
import com.charge.order.client.OrderRuleClient;
import com.charge.order.dto.YueCardOrderCreateCmd;
import com.charge.order.dto.rule.CommunityBaseOrderRuleDTO;
import com.charge.order.dto.rule.CommunityOrderRuleQueryDTO;
import com.charge.order.enums.OrderRuleCategoryEnum;
import com.charge.starter.jedis.JedisManager;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName: PlatformBankBusinessFlowServiceImpl
 * @Author: wangle
 * @Description: 获取云平台银行收款业务流水服务接口实现类
 * @Date: 2021/11/12 16:19:55
 * @Version: 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PlatformBankBusinessFlowServiceImpl implements PlatformBankBusinessFlowService {

    private final ParkingSyncRecordClient parkingSyncRecordClient;

    private final AssetClient assetClient;
    
    private final CommunityClient communityClient;

    private final AssetSupport assetSupport;

    private final BillNumGeneratorClient billNumGeneratorClient;

    private final IncomeBillClient incomeBillClient;

    private final AssetTransactionClient assetTransactionClient;

    private final AssetBillInfoClient assetBillInfoClient;

    private final OrderBillDetailClient orderBillDetailClient;

    private final TransactionRelationClient transactionRelationClient;

    private final OrderClient orderClient;

    private final OrderRuleClient orderRuleClient;

    private final JedisManager jedisManager;

    @Override
    public ChargeResponse insertPlatformBankBusinessFlow(List<ParkingSyncRecordDTO> recordList) throws ChargeBusinessException{
        //插入 云平台银行收款业务流水
        log.info("云平台获取银行收款业务流水={}", recordList);
        for (ParkingSyncRecordDTO parkingSyncRecordDTO : recordList) {
            String transferId = parkingSyncRecordDTO.getTransferId();
            /*校验是否存在同一天同一车场同一支付渠道的数据，有则不再进行后续处理*/
            ParkingSyncRecordConditionDTO parkingSyncRecordConditionDTO = ParkingSyncRecordConditionDTO.builder()
                    .parkingId(parkingSyncRecordDTO.getParkingId()).chargeDate(DateUtils.format(parkingSyncRecordDTO.getChargeTime(), DateUtils.FORMAT_1))
                            .paymentChannel(parkingSyncRecordDTO.getPaymentChannel()).build();
            ChargeResponse<TemparkPayDetailDTO> parkingRecordResponse = parkingSyncRecordClient.getRecord(parkingSyncRecordConditionDTO);
            if(!parkingRecordResponse.isSuccess()){
                throw new ChargeBusinessException("101", "流水号：" + transferId + ",该条数据入库失败（校验临停订单记录失败）:" + parkingRecordResponse.getMessage());
            }
            if(parkingRecordResponse.getContent() != null){
                return new ChargeResponse<>();
                //return updateParkingBill(parkingSyncRecordDTO);
            }

            ChargeResponse<AssetDTO> assetResponse = assetClient.getCommunityVirtualHouse(parkingSyncRecordDTO.getCommunityId());
            if(!assetResponse.isSuccess() || assetResponse.getContent() == null){
                throw new ChargeBusinessException("101", "流水号：" + transferId + ",该条数据入库失败（查询项目虚拟资产失败）:" + assetResponse.getMessage());
            }
            HouseDTO houseInfo = assetResponse.getContent().getHouseDTO();
            Long assetId = assetResponse.getContent().getId();
            Long communityId = parkingSyncRecordDTO.getCommunityId();
            ChargeResponse<CommunityDTO> communityResponse = communityClient.oneByCondition(CommunityCondition.builder().id(communityId).build());
            if (!communityResponse.isSuccess() || communityResponse.getContent() == null) {
                throw new ChargeBusinessException("101", "流水号：" + transferId + ",该条数据入库失败（查询项目信息失败），communityId:" + communityId);
            }
            CommunityDTO communityDTO = communityResponse.getContent();
            //增加项目下临停订单规则校验
            CommunityOrderRuleQueryDTO communityOrderRuleQueryDTO = new CommunityOrderRuleQueryDTO();
            communityOrderRuleQueryDTO.setCommunityId(communityId);
            communityOrderRuleQueryDTO.setSecondClassificationIds(Lists.newArrayList(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_PARK_400.getCode().toString()));
            ChargeResponse<List<CommunityBaseOrderRuleDTO>> ruleResponse = orderRuleClient.listByCommunityIdAndSubsetId(communityOrderRuleQueryDTO);
            if(!ruleResponse.isSuccess() || ruleResponse.getContent().size() == 0
                    || Objects.equals(ruleResponse.getContent().get(0).getChargeItemId(), "[]")){
                throw new ChargeBusinessException("101", "该项目未启用车辆临停订单规则");
            }

            String orderNum = IdGeneratorSupport.getIstance().nextId();
            return createParkingBill(parkingSyncRecordDTO, communityDTO, houseInfo, orderNum, assetId);
        }

        return new ChargeResponse<>();
    }

    public ChargeResponse createParkingBill(ParkingSyncRecordDTO record, CommunityDTO communityDTO, HouseDTO houseInfo,
                                            String orderNum, Long assetId) throws ChargeBusinessException {
        // 并发控制
        String lockCacheKey = RedisKeyLockEnum.PARKING_PLATFORM_TRANSFER_ID.key(record.getTransferId());
        try {
            RedisKeyLockUtil.kickOffConcurrent(jedisManager, lockCacheKey, DateUtils.DataTimeSec.FIVE_SECOND);
        }  catch (ChargeRuntimeException e) {
            log.info("{}|【停车场推送车辆临停流水】正在处理您的请求，请稍后再试！", LogCategoryEnum.BUSSINESS, e);
            throw new ChargeBusinessException(ErrorInfoEnum.E1000.getCode(), "正在处理您的请求，请稍后再试！");
        }
        try {
            //基础数据资产
            AssetAdapter assetInfo = assetSupport.getAssetInfoById(assetId.toString());
            //创建实收单[income_bill]
            IncomeBillDTO income = createIncomeBill(record, communityDTO, orderNum);
            //创建保存资产流水单[asset_transaction]
            AssetTransactionDTO transactionDO = createAssetTransaction(record, assetInfo, income, houseInfo, assetId);
            //创建流水信息表数据[asset_bill_info]
            createAssetBillInfo(income, transactionDO.getId(), record.getTransferId());
            //创建资产关联表与账单订单明细
            Long orderId = createTransactionRelationAndOrderBillDetail(transactionDO.getId(), assetId, orderNum, communityDTO.getId(), record, assetInfo);
            //创建临停订单业务流水[parking_sync_record]
            createParkingSyncRecord(record, transactionDO.getId(), orderId, communityDTO);

            return new ChargeResponse();
        } catch (Exception e) {
            log.error("{}|车辆临停订单执行下单异常|入参={}", LogCategoryEnum.BUSSINESS, record, e);
            throw new ChargeBusinessException(ErrorInfoEnum.E1003, "车辆临停订单系统下单错误，请重试");
        } finally {
            jedisManager.del(lockCacheKey);
        }
    }

    //创建实收单
    private IncomeBillDTO createIncomeBill(ParkingSyncRecordDTO record, CommunityDTO communityDTO,
                                           String orderNum) throws ChargeBusinessException {
        String billNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.INCOME_BILL.getCode()).getContent();
        IncomeBillDTO income = IncomeBillDTO.builder().orderNum(orderNum).billNum(billNum).outTransactionNo(record.getTransferId())
                .communityId(communityDTO.getId()).communityName(communityDTO.getName()).incomeMoney(record.getMoney())
                .goodsName(PayRelatedConstants.GOODSNAME_TEMP_PARKING).paymentMethod(PaymentMethodEnum.CLOUD_PLATFORM.getPaymentCode())
                .paymentChannel(PaymentChannelEnum.PARKING.getPaymentChannel()).paymentTerminal(PaymentTerminalEnum.PARKING.getCode())
                .paymentTime(new Date()).payStatus(BillPayStatusEnum.SUCCESS.getCode())
                .payHouseCount(1).balanceStatus(BalanceStatusEnum.UNRECONCILED.getCode())
                .build();
        income.setCreateUser("parking");
        income.setCreateTime(DateUtils.getCurrentTimestamp());
        income = AppInterfaceUtil.getResponseData(incomeBillClient.create(income));
        log.info("{}|车辆临停生成实收单，incomeBillDTO：{}", LogCategoryEnum.BUSSINESS, income);
        return income;
    }

    /**
     * 创建资产交易流水
     * @param record
     * @param assetInfo
     * @param income
     * @param houseInfo
     * @param assetId
     * @return
     * @throws ChargeBusinessException
     */
    private AssetTransactionDTO createAssetTransaction(ParkingSyncRecordDTO record, AssetAdapter assetInfo, IncomeBillDTO income,
                                                       HouseDTO houseInfo, Long assetId) throws ChargeBusinessException {
        AssetTransactionDTO transactionDO = AssetTransactionDTO.builder()
                .incomeId(income.getId()).money(record.getMoney()).goodsName(PayRelatedConstants.GOODSNAME_TEMP_PARKING)
                .assetOrderNum(income.getOrderNum()).communityId(income.getCommunityId()).communityName(income.getCommunityName())
                .buildingId(houseInfo.getBuildingId()).buildingName(houseInfo.getBuildingName()).unitId(houseInfo.getUnitId())
                .unitName(houseInfo.getUnitName()).assetId(assetId).assetName(assetInfo.getSubName()).payStatus(BillPayStatusEnum.SUCCESS.getCode())
                .assetCode(assetInfo.getSubCode()).paymentMethod(income.getPaymentMethod()).paymentChannel(income.getPaymentChannel())
                .assetUseStatus(assetInfo.getAssetUseStatus()).paymentTerminal(income.getPaymentTerminal()).paymentTime(new Date()).build();
        if (CollectionUtil.isNotEmpty(assetInfo.getListCustomer())) {
            CustomerDTO customerDTO = assetInfo.getListCustomer().get(0);
            transactionDO.setOwnerId(customerDTO.getId());
            transactionDO.setOwnerName(customerDTO.getCustomerName());
        }
        transactionDO.setCreateUser("parking");
        transactionDO.setCreateTime(DateUtils.getCurrentTimestamp());
        transactionDO = AppInterfaceUtil.getResponseData(assetTransactionClient.create(transactionDO));
        log.info("{}|车辆临停生成资产流水，transactionDO：{}", LogCategoryEnum.BUSSINESS, transactionDO);
        return transactionDO;
    }

    /**
     * 创建流水信息表数据
     * @param income
     * @param assetTransactionId
     * @throws ChargeBusinessException
     */
    private void createAssetBillInfo(IncomeBillDTO income, Long assetTransactionId, String outTransactionNo) throws ChargeBusinessException {
        AssetBillInfoDTO assetBillInfoDO = AssetBillInfoDTO.builder().assetTransactionId(assetTransactionId)
                .orderNum(income.getOrderNum()).outTransactionNo(outTransactionNo).communityId(income.getCommunityId()).build();
        AppInterfaceUtil.getResponseData(assetBillInfoClient.create(assetBillInfoDO));
    }

    /**
     * 创建资产关联表与账单订单明细
     * @param assetTransactionId
     * @param assetId
     * @param orderNum
     * @param communityId
     * @param record
     * @param assetInfo
     * @throws ChargeBusinessException
     */
    private Long createTransactionRelationAndOrderBillDetail(Long assetTransactionId, Long assetId, String orderNum,
                                                             Long communityId, ParkingSyncRecordDTO record, AssetAdapter assetInfo) throws ChargeBusinessException {
        List<TransactionRelationDTO> relationList = Lists.newArrayList();

        //订单表[base_order]
        Long orderId = createOrder(record, assetId, orderNum, communityId, assetInfo);

        //增加订单明细信息order_bill_detail
        OrderBillDetailDTO billDetailDTO = OrderBillDetailDTO.builder().assetTransactionId(assetTransactionId)
                .bizType(BizTypeEnum.ORDER_TYPE.getCode()).itemId(record.getItemId()).payType(OrderPayTypeEnum.PARKING.getCode())
                .itemName(record.getItemName()).actualAmount(record.getMoney()).orderId(orderId).communityId(communityId).build();
        Long orderBillDetailId = orderBillDetailClient.create(billDetailDTO).getContent();

        //添加关联数据transaction_relation
        TransactionRelationDTO relationDO = TransactionRelationDTO.builder().assetTransactionId(assetTransactionId)
                .businessId(orderBillDetailId).businessType(BusinessTypeEnum.TEMP_PAY.getCode())
                .createTime(new Date()).communityId(communityId).build();
        relationList.add(relationDO);

        AppInterfaceUtil.getResponseData(transactionRelationClient.batchCreate(relationList));
        return orderId;
    }

    /**
     * 创建订单表
     * @param record
     * @param assetId
     * @param orderNum
     * @param communityId
     * @param assetInfo
     * @return
     * @throws ChargeBusinessException
     */
    private Long createOrder(ParkingSyncRecordDTO record, Long assetId, String orderNum, Long communityId,
                             AssetAdapter assetInfo) throws ChargeBusinessException {
        YueCardOrderCreateCmd orderCreateCmd = new YueCardOrderCreateCmd();
        orderCreateCmd.setOrderNum(orderNum);
        orderCreateCmd.setCommunityId(communityId);
        orderCreateCmd.setCustomerName("停车云平台");
        orderCreateCmd.setPayStatus(BillPayStatusEnum.SUCCESS.getCode());
        orderCreateCmd.setSubsetId(400L);
        orderCreateCmd.setAssetId(assetId);
        orderCreateCmd.setAssetCode(assetInfo.getSubCode());
        com.charge.order.dto.WorkOrderItemDTO itemDTO = new com.charge.order.dto.WorkOrderItemDTO();
        itemDTO.setItemId(record.getItemId().toString());
        itemDTO.setItemName(record.getItemName());
        itemDTO.setPrice(record.getMoney());
        orderCreateCmd.setItemVOS(Lists.newArrayList(itemDTO));
        log.info("{}|车辆临停订单base_order下单，orderCreateCmd：{}", LogCategoryEnum.BUSSINESS, orderCreateCmd);
        return AppInterfaceUtil.getResponseData(orderClient.createYueCardOrder(orderCreateCmd));
    }

    /**
     * 创建车辆临停订单业务流水快照
     * @param record
     * @throws ChargeBusinessException
     */
    private Long createParkingSyncRecord(ParkingSyncRecordDTO record, Long assetTransactionId, Long baseOrderId, CommunityDTO communityDTO) throws ChargeBusinessException {
        record.setCityCompanyName(communityDTO.getCityCompanyName());
        record.setLegalCompanyName(communityDTO.getLegalCompanyCname());
        //支付方式(8表示停车云平台)
        record.setPaymentMethod(PaymentMethodEnum.CLOUD_PLATFORM.getPaymentCode());
        record.setCreateTime(DateUtils.getCurrentTimestamp());
        record.setModifyTime(DateUtils.getCurrentTimestamp());
        record.setAssetTransactionId(assetTransactionId);
        record.setBaseOrderId(baseOrderId);
        ChargeResponse<Long> parkingSyncRecordResponse = parkingSyncRecordClient.create(record);
        if(!parkingSyncRecordResponse.isSuccess()){
            throw new ChargeBusinessException("101", "流水号：" + record.getTransferId() + ",该条数据入库失败:" + parkingSyncRecordResponse.getMessage());
        }
        Long parkingSyncRecordId = parkingSyncRecordResponse.getContent();
        log.info("{}|车辆临停生成业务流水，parkingSyncRecordDTO：{}", LogCategoryEnum.BUSSINESS, record);
        return parkingSyncRecordId;
    }

    private ChargeResponse updateParkingBill(ParkingSyncRecordDTO record) throws ChargeBusinessException {
        try {
            //更新订单及订单明细相关信息
            updateOrderAndOrderBillDetail(record);
            //更新临停订单业务流水[parking_sync_record]
            updateParkingSyncRecord(record);

            return new ChargeResponse();
        } catch (Exception e) {
            log.error("{}|车辆临停订单执行更新异常|入参={}", LogCategoryEnum.BUSSINESS, record, e);
            throw new ChargeBusinessException(ErrorInfoEnum.E1003, "车辆临停订单系统更新信息错误，请重试");
        }
    }

    private void updateOrderAndOrderBillDetail(ParkingSyncRecordDTO record) throws ChargeBusinessException{
        //更新订单信息
        updateOrder(record);

        //更新订单明细信息order_bill_detail
        OrderBillDetailDTO billDetailDTO = OrderBillDetailDTO.builder().itemId(record.getItemId())
                .itemName(record.getItemName()).orderId(record.getBaseOrderId()).build();
        //Long orderBillDetailId = orderBillDetailClient.update(billDetailDTO).getContent();
    }

    /**
     * 创建车辆临停订单业务流水快照
     * @param record
     * @throws ChargeBusinessException
     */
    private void updateParkingSyncRecord(ParkingSyncRecordDTO record) throws ChargeBusinessException {
        ChargeResponse<Long> parkingSyncRecordResponse = parkingSyncRecordClient.update(record);
        if(!parkingSyncRecordResponse.isSuccess()){
            throw new ChargeBusinessException("101", "流水号：" + record.getTransferId() + ",该条数据入库失败(更新临停订单流水异常):" + parkingSyncRecordResponse.getMessage());
        }
        log.info("{}|车辆临停更新业务流水，parkingSyncRecordDTO：{}", LogCategoryEnum.BUSSINESS, record);
    }

    /**
     * 更新订单表
     * @param record
     * @return
     * @throws ChargeBusinessException
     */
    private Long updateOrder(ParkingSyncRecordDTO record) throws ChargeBusinessException {
        //TODO:待加更新逻辑
        return 1L;
    }
}
