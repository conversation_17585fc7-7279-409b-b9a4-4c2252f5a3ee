package com.charge.api.web.vo.pos;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.List;

/**
 * 押金退款
 *
 * <AUTHOR>
 * @date 2023/7/6
 */
@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PreDepositRefundRequest {
    /**
     * 操作员
     */
    String operatorId;
    /**
     * 操作人
     */
    String operator;
    /**
     * 项目id
     */
    Long communityId;

    /**
     * 房屋id
     */
    Long houseId;
    /**
     * 备注
     */
    String remark;
    /**
     * 是否是垫付
     */
    Boolean advancePay;

    /**
     * 垫付人、可选
     */
    String employeeName;
    /**
     * 退款金额
     */
    BigDecimal money;

    /**
     * 押金id
     */
    Long predepositAccountId;

    /**
     * 收费项id
     */
    Long itemId;

    /**
     * 收费项名称
     */
    String itemName;

    /**
      收据号
     */
    String receiptNo;

    /**
     * 是否转预存
     */
    Boolean toPreStore;

    /**
     * 退款申请人
     */
    String draweeName;

    /**
     * 上传文件id
     */
    List<Long> fileIds;


    @Pattern(regexp = "^[0-9]{9,25}$", message = "银行账号必须是9-25位的数字")
    String payBankAccount;

    /**
     * 收款银行账号
     */
    @Pattern(regexp = "^[0-9]{9,25}$", message = "银行账号必须是9-25位的数字")
    String acceptBankAccount;

    /**
     * 收款银行名称
     */
    @Length(max = 25,message = "收款银行名称最大长度25")
    String acceptBankName;

    /**
     * 收款账号名
     */
    @Length(max = 25,message = "收款账号名最大长度25")
    String acceptAccountName;

    /**
     *
     * 收款开户行
     */
    @Length(max = 25,message = "收款开户行最大长度25")
    String acceptAccountOpeningBank;

    /**
     * 收款账户城市
     */
    @Length(max = 25,message = "收款账户城市最大长度25")
    String acceptAccountCity;

}


