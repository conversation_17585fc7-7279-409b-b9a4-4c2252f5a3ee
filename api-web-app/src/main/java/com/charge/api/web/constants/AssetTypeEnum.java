package com.charge.api.web.constants;

import com.charge.core.enums.BaseEnum;

public enum AssetTypeEnum implements BaseEnum<Integer> {

    HOUSE(1, "house"),
    POSITIO(2, "position"),
    CAR(3, "car"),
    CARD(4, "card"),
    <PERSON>OC<PERSON>(5, "lock"),
    SHOP(6, "shops"),
    ;

    private Integer code;
    private String name;


    AssetTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return name;
    }
}
