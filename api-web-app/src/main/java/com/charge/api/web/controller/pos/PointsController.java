package com.charge.api.web.controller.pos;

import com.charge.api.web.vo.pos.points.AccountBalanceQueryVO;
import com.charge.api.web.vo.pos.points.AccountBalanceVO;
import com.charge.api.web.vo.pos.points.PredepositDeductPointsConditionVO;
import com.charge.api.web.vo.pos.points.PredepositDeductPointsCountVO;
import com.charge.apicloud.client.CrmixcClient;
import com.charge.apicloud.dto.points.AccountBalanceConditionDTO;
import com.charge.apicloud.dto.points.AccountBalanceDTO;
import com.charge.apicloud.dto.points.MemberInfoConditionDTO;
import com.charge.apicloud.dto.points.MemberInfoDTO;
import com.charge.apicloud.enums.CrmixcChannelsEnum;
import com.charge.bill.client.PredepositAdjustClient;
import com.charge.bill.dto.predeposit.points.PredepositDeductPointsConditionDTO;
import com.charge.bill.dto.predeposit.points.PredepositDeductPointsCountDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.TraceContextUtil;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Objects;

/**
 * 积分相关
 */
@RequestMapping("/points")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class PointsController {

	private final CrmixcClient  crmixcClient;

	private final PredepositAdjustClient predepositAdjustClient;

	@ApiOperation(value= "根据手机号查询大会员积分余额", notes = "根据手机号查询大会员积分余额")
	@PostMapping(value = "/phone/balance")
	public ChargeResponse<AccountBalanceVO> getAccountBalance(@Valid @RequestBody AccountBalanceQueryVO queryVO) throws ChargeBusinessException {
		MemberInfoDTO memberInfoDTO = AppInterfaceUtil.getResponseDataThrowException(crmixcClient.checkMemberInfo(MemberInfoConditionDTO.builder()
				.mobile(queryVO.getPhone()).paymentTerminal(CrmixcChannelsEnum.WECHAT_CHANNEL.getChannelId()).build()));
		if(Objects.isNull(memberInfoDTO)){
			log.info("{}|查询大会员账号为空, phone={}", LogCategoryEnum.BUSSINESS, queryVO.getPhone());
			return new ChargeResponse<>();
		}

		AccountBalanceDTO accountBalanceDTO = AppInterfaceUtil.getResponseDataThrowException(crmixcClient.getAccountBalance(AccountBalanceConditionDTO.builder().pId(memberInfoDTO.getPId()).paymentTerminal(CrmixcChannelsEnum.WECHAT_CHANNEL.getChannelId()).build()));
		if(Objects.isNull(accountBalanceDTO)){
			throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "查询积分余额为空");
		}
		AccountBalanceVO accountBalanceVO = BeanCopierWrapper.copy(accountBalanceDTO, AccountBalanceVO.class);
		accountBalanceVO.setPid(memberInfoDTO.getPId());
		return new ChargeResponse<>(accountBalanceVO);
	}

	/**
	 * 预存退款扣除积分计算接口
	 * @param
	 * @return
	 */
	@PostMapping("/deduct")
	public ChargeResponse<PredepositDeductPointsCountVO> predepositPointsDeductCount(@Valid @RequestBody PredepositDeductPointsConditionVO conditionVO) throws ChargeBusinessException {
		PredepositDeductPointsConditionDTO conditionDTO = BeanCopierWrapper.copy(conditionVO,PredepositDeductPointsConditionDTO.class);
		ChargeResponse<PredepositDeductPointsCountDTO> countResp = predepositAdjustClient.predepositPointsDeductCount(conditionDTO);
		PredepositDeductPointsCountDTO countDTO = AppInterfaceUtil.getDataThrowException(countResp);
		return new ChargeResponse<>(BeanCopierWrapper.copy(countDTO,PredepositDeductPointsCountVO.class));
	}

}
