package com.charge.api.web.dto.ssdp;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/2/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EcsbResponseDTO<T> implements Serializable {

    private static final long serialVersionUID = 9181335170632475618L;


    @JsonProperty(value = "RETURN_CODE")
    private String returnCode;

    /**
     * 响应时间，格式 yyyy-MM-dd HH:mm:ss:sss
     */
    @JsonProperty(value = "RETURN_STAMP")
    private String returnStamp;

    @JsonProperty(value = "RETURN_DATA")
    private T returnData;

    @JsonProperty(value = "RETURN_DESC")
    private String returnDesc;

}
