package com.charge.api.web.vo.joylife;

import com.charge.api.web.vo.joylife.request.ZhaoXiAssertRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;


/**
 * 退款
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseRefundReq extends ZhaoXiAssertRequest implements Serializable {

    /**
     * 收款银行账号
     */
    @NotBlank(message = "银行卡号不能为空")
    @Pattern(regexp = "^[0-9]{9,25}$", message = "银行账号为9-25位数字")
    private String bankAccountNo;

    /**
     * 退款说明
     */
    @NotBlank(message = "退款说明不能为空")
    @Length(max = 200, message = "退款说明长度超过200字符限制，请重新输入")
    private String refundRemark;

    /**
     * 付款银行账号
     */
    @Pattern(regexp = "^[0-9]{9,25}$", message = "银行账号为9-25位数字")
    private String payBankAccount;

    /**
     * 收款银行名称
     */
    @Length(max = 25, message = "收款银行名称最大长度25")
    private String acceptBankName;

    /**
     * 收款账号名
     */
    @Length(max = 25, message = "收款账号名最大长度25")
    private String acceptAccountName;

    /**
     * 收款开户行
     */
    @Length(max = 25, message = "收款开户行最大长度25")
    private String acceptAccountOpeningBank;

    /**
     * 收款账户城市
     */
    @Length(max = 25, message = "收款账户城市最大长度25")
    private String acceptAccountCity;

}
