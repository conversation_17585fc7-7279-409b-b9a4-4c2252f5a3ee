package com.charge.api.web.controller.pos;

import com.charge.api.web.service.pos.OrderService;
import com.charge.api.web.vo.pos.EBusinessMasterOrderVO;
import com.charge.api.web.vo.pos.EBusinessMasterOrderWithItemsVO;
import com.charge.api.web.vo.pos.OrderInstallmentVO;
import com.charge.api.web.vo.pos.OrderInstallmentWithItemsVO;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.order.dto.ebuiness.EBusinessMasterOrderDetailQuery;
import com.charge.order.dto.ebuiness.EBusinessMasterOrderQuery;
import com.charge.order.dto.ebuiness.InstallmentDetailQuery;
import com.charge.order.dto.ebuiness.InstallmentQuery;
import com.charge.order.enums.EBusinessOrderPayStatusEnum;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 电商订单控制器
 *
 * <AUTHOR>
 * @date 2024/11/15
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping(value = "order")
public class OrderController {

    private final OrderService orderService;

    /**
     * 滚动查出待支付的进度单列表
     *
     * @param installmentQuery 进度单查询
     * @return 待支付的进度单列表
     */
    @PostMapping(value = "e-business/installment/to-pay/roll-list")
    public ChargeResponse<List<OrderInstallmentVO>> rollListToPayInstallment(@RequestBody @Valid InstallmentQuery installmentQuery) throws ChargeBusinessException {
        installmentQuery.setPayStatusList(Lists.newArrayList(EBusinessOrderPayStatusEnum.TO_PAY.getCode(), EBusinessOrderPayStatusEnum.PARTIAL_PAID.getCode()));
        installmentQuery.setPayTerminalList(Lists.newArrayList(PaymentTerminalEnum.POS.getCode()));
        return new ChargeResponse<>(orderService.listInstallment(installmentQuery));
    }

    /**
     * 滚动查出已支付的进度单列表
     *
     * @param installmentQuery 进度单查询
     * @return 待支付的进度单列表
     */
    @PostMapping(value = "e-business/installment/paid/roll-list")
    public ChargeResponse<List<OrderInstallmentVO>> rollListPaidInstallment(@RequestBody @Valid InstallmentQuery installmentQuery) throws ChargeBusinessException {
        installmentQuery.setPayStatusList(Lists.newArrayList(EBusinessOrderPayStatusEnum.PAID.getCode(), EBusinessOrderPayStatusEnum.PARTIAL_PAID.getCode()));
        installmentQuery.setPayTerminalList(Lists.newArrayList(PaymentTerminalEnum.POS.getCode()));
        return new ChargeResponse<>(orderService.listInstallment(installmentQuery));
    }

    /**
     * 查询分期进度详情
     *
     * @param detailQuery 查询条件
     * @return 进度单详情（即子订单列表）
     */
    @PostMapping("e-business/installment/detail")
    public ChargeResponse<OrderInstallmentWithItemsVO> installmentDetail(@Valid @RequestBody InstallmentDetailQuery detailQuery) throws ChargeBusinessException {
        return new ChargeResponse<>(orderService.installmentDetail(detailQuery));
    }


    /**
     * 滚动查出已支付的主单列表
     *
     * @param orderQuery 查询条件
     * @return 主单列表
     */
    @PostMapping(value = "e-business/master-order/paid/roll-list")
    public ChargeResponse<List<EBusinessMasterOrderVO>> rollListPaidEBusinessMasterOrder(@RequestBody @Valid EBusinessMasterOrderQuery orderQuery) throws ChargeBusinessException {
        orderQuery.setPayStatusList(Lists.newArrayList(EBusinessOrderPayStatusEnum.PAID.getCode(), EBusinessOrderPayStatusEnum.PARTIAL_PAID.getCode()));
        return new ChargeResponse<>();
    }

    /**
     * 查出主单详情
     *
     * @param detailQuery 主单id
     * @return 主单详情
     */
    @PostMapping(value = "e-business/master-order/detail")
    public ChargeResponse<EBusinessMasterOrderWithItemsVO> masterOrderDetail(@RequestBody @Valid EBusinessMasterOrderDetailQuery detailQuery) throws ChargeBusinessException {
        return new ChargeResponse<>();
    }

}