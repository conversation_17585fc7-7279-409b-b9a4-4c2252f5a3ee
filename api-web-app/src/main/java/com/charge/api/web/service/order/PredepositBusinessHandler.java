package com.charge.api.web.service.order;

import com.charge.bill.client.BillNumGeneratorClient;
import com.charge.bill.client.PredepositBillClient;
import com.charge.bill.dto.income.BatchPredepositBillDTO;
import com.charge.bill.dto.income.PredepositBillDTO;
import com.charge.bill.enums.BizTypeEnum;
import com.charge.bill.enums.BusinessTypeEnum;
import com.charge.bill.enums.OrderNumPrefixEnum;
import com.charge.common.dto.BaseDTO;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.core.enums.LogCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zheng<PERSON><PERSON>o
 */
@Component
@Slf4j
public class PredepositBusinessHandler implements BillBusinessHandler {
    @Resource
    BillNumGeneratorClient billNumGeneratorClient;

    @Resource
    PredepositBillClient predepositBillClient;

    @Override
    public BusinessTypeEnum type() {
        return BusinessTypeEnum.PREDEPOSIT_PAY;
    }

    @Override
    public void validate(BillBusiness billBusiness) {
        Assert.isTrue(billBusiness instanceof PredepositBillBusiness, "billBusiness的类型错误");
        PredepositBillBusiness predepositBillBusiness = (PredepositBillBusiness) billBusiness;
        Assert.notNull(predepositBillBusiness.getChargeItemId(), "predepositBillBusiness 收费项id不能为空");
        Assert.hasText(predepositBillBusiness.getName(), "predepositBillBusiness 收费项name不能为空");
        Assert.isTrue(predepositBillBusiness.getMoney()!=null
                &&predepositBillBusiness.getMoney().compareTo(BigDecimal.ZERO)>0, "预存金额必须大于0");
    }

    @Override
    public void handle(CreateOrderContext context, AssetBillBusiness assetBillBusiness, List<BillBusiness> billBusinesses) {
        List<PredepositBillDTO> predepositBillList = new ArrayList<>();
        Long assetTransactionId = assetBillBusiness.getAssetTransactionId();
        //保存到预收明细列表中
        Long communityId = context.getCommunity().getId();
        Long assetId = assetBillBusiness.getAsset().getId();

        for (BillBusiness billBusiness : billBusinesses) {
            // 过滤掉0元的
            if(billBusiness.getMoney().compareTo(BigDecimal.ZERO)==0){
                continue;
            }
            PredepositBillBusiness bill = (PredepositBillBusiness) billBusiness;
            String preBillNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.PREDEPOSIT_BILL.getCode()).getContent();
            PredepositBillDTO predepositBillDTO = PredepositBillDTO.builder()
                    .communityId(communityId)
                    .assetId(assetId)
                    .assetTransactionId(assetTransactionId)
                    .billNum(preBillNum)
                    .predepositItemId(bill.getChargeItemId())
                    .predepositMoney(bill.getMoney())
                    .memo(context.getMemo())
                    .predepositItemName(bill.getName())
                    .predepositType(bill.getPredepositType())
                    .isBalance(1)
                    .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode())
                    .userId(context.getPayMemberId())
                    .bizType(BizTypeEnum.getByBizType(bill.getPredepositType()))
                    .build();
            predepositBillList.add(predepositBillDTO);
        }

        if (CollectionUtils.isNotEmpty(predepositBillList)) {
            predepositBillList = predepositBillClient.batchCreate(BatchPredepositBillDTO.builder().predepositBillDTOList(predepositBillList).build()).getContent();
            log.info("{}|【pos】预存充值2.0，条数：{}，成功新增预收单明细|{}", LogCategoryEnum.BUSSINESS, predepositBillList.size(), predepositBillList);

            Map<Long, Long> itemIdMap = predepositBillList.stream().collect(Collectors.toMap(PredepositBillDTO::getPredepositItemId, BaseDTO::getId));
            for (BillBusiness billBusiness : billBusinesses) {
                PredepositBillBusiness bill = (PredepositBillBusiness) billBusiness;
                bill.setId(itemIdMap.get(bill.getChargeItemId()));
            }
        }
    }
}
