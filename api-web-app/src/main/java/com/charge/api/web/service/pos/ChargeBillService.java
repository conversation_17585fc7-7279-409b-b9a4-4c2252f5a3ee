package com.charge.api.web.service.pos;

import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.lakala.*;
import com.charge.api.web.vo.pos.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;

import java.util.List;

public interface ChargeBillService {
    ChargePageResponse<List<PosBillMonthVO>> listPosBill(PosBillQueryCondition posBillQueryCondition) throws ChargeBusinessException;

    /**
     * 缴欠费和临时订单的创建订单
     */
    PaymentResponse createBill(ReceivableAndOrderBillCreateRequest receivableAndOrderBillCreateRequest) throws ChargeBusinessException;

    /**
     * 小区临时订单的创建订单
     */
    PaymentResponse createBill(CommunityOrderBillCreateRequest communityOrderBillCreateRequest) throws ChargeBusinessException;


    /**
     * 房屋多临时订单的创建订单
     */
    PaymentResponse createBill(OrdersBillCreateRequest communityOrderBillCreateRequest) throws ChargeBusinessException;

    /**
     * 小区多临时订单的创建订单
     */
    PaymentResponse createBill(CommunityOrdersBillCreateRequest communityOrderBillCreateRequest) throws ChargeBusinessException;


    /**
     * 多房间缴费创建订单
     */
    PaymentResponse createBill(BatchBillCreateRequest communityOrderBillCreateRequest) throws ChargeBusinessException;


    /**
     * 获取掉单数量
     * @param mercid
     * @param deviceInfo
     * @param communityId
     * @return
     * @throws ChargeBusinessException
     */
    Integer getLostPayNum(String mercid, String deviceInfo, String communityId) throws ChargeBusinessException ;


    /**
     * 获取掉单
     * @param mercid
     * @param deviceInfo
     * @param communityId
     * @return
     * @throws ChargeBusinessException
     */
    ChargeResponse<List<LostPay>> queryLostPay(String mercid, String deviceInfo, String communityId) throws ChargeBusinessException ;




    /**
     * 更新收据
     * @param id
     * @param printer
     * @return
     * @throws ChargeBusinessException
     */
    ChargeResponse updateReceiptInfo(String id, String printer) throws ChargeBusinessException ;



    /**
     * 列出房屋欠费列表
     * @param communityId 项目id
     * @param assetIds 资产id列表
     * @return 欠费列表
     */
    List<ReceivableV2> listArrearsByAssets(Long communityId, List<Long> assetIds);

    /**
     * 列出房屋欠费列表
     * @param communityId 项目id
     * @param assetIds 资产id列表
     * @return 欠费列表
     */
    HouseReceivablesV3 listArrearsDetailByAssets(Long communityId, List<Long> assetIds,List<Long> chargeItemIds,String belongYearsEnd) throws ChargeBusinessException;


    OrderDetailVO getOrderDetailByIncomeBill(Long incomeBillId, Long communityId) throws ChargeBusinessException;

    List<ReceiptVO> getReceiptsByIncomeBill(Long incomeBillId, Long communityId) throws ChargeBusinessException;

    ReceiptVO getReceiptsByAssetTransactionId(Long assetTransactionId) throws ChargeBusinessException;

    PaymentResponse addDeposit(AddDepositRequest addDepositRequest, String URI) throws ChargeBusinessException;


    /**
     * 预存退款
     * @param restoreRefundRequest 预存退款请求
     * @throws ChargeBusinessException 业务异常
     */
    void preStoreRefund(PreStoreRefundRequest restoreRefundRequest) throws ChargeBusinessException;

    /**
     * 押金退款
     * @param  preDepositRefundRequest 押金退款请求
     * @throws ChargeBusinessException 业务异常
     */
    void preDepositRefund(PreDepositRefundRequest preDepositRefundRequest) throws ChargeBusinessException;
}
