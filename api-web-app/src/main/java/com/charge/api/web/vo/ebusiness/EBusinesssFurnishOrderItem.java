package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/11/21 13:47
 */
@Data
public class EBusinesssFurnishOrderItem {

    /**
     * 二级订单总金额（优惠前金额）
     */
    @NotNull(message = "二级订单总金额（优惠前金额）")
    private BigDecimal originPrice;

    /**
     * 商品明细项id
     */
    @NotBlank(message = "商品明细项id不能为空")
    private String oid;
    /**
     * 收费项id
     */
    @NotNull(message = "收费项id不能为空")
    private Long itemId;
    /**
     * 收费项名称
     */
    @NotBlank(message = "收费项名称不能为空")
    private String itemName;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String skuName;

    /**
     * 支付状态 全部支付：PAID,部分支付：PART_PAID
     */
    @NotBlank(message = "支付状态不能为空")
    private String payState;

    /**
     * 支付成功时间 格式：yyyy-MM-dd HH:mm:ss
     */
    private String payTime;
    /**
     * sku编码
     */
    @NotBlank(message = "sku编码不能为空")
    private String skuCode;

    /**
     * skuId
     */
    @NotBlank(message = "skuId不能为空")
    private String skuId;

    /**
     * 类目id
     */
    @NotBlank(message = "类目id不能为空")
    private String cateId;

    /**
     * 类目名称
     */
    @NotBlank(message = "类目名称不能为空")
    private String cateName;
    /**
     * 规格型号
     */
    private String specText;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 计量单位
     */
    private String unit;
    /**
     * 商品分摊后单价
     */
    @NotNull(message = "商品分摊后单价不能为空")
    private BigDecimal apportionedUnitPrice;
    /**
     * 二级订单单价（优惠前金额）
     * （单位元）
     */
    @NotNull(message = "二级订单单价不能为空")
    private BigDecimal originUnitPrice;
    /**
     * 优惠金额，小于等于0
     */
    @NotNull(message = "优惠金额不能为空")
    private BigDecimal discountAmount;
    /**
     * 溢价金额（运费分摊金额），大于等于0
     */
    @NotNull(message = "溢价金额（运费分摊金额）不能为空")
    private BigDecimal premiumAmount;
    /**
     * 商品数量
     */
    @NotNull(message = "商品数量不能为空")
    private Integer num;
    /**
     * 分摊后总金额（优惠分摊与运费分摊后金额）
     */
    @NotNull(message = "分摊后总金额不能为空")
    private BigDecimal apportionedAmount;
    /**
     * 分摊明细，存在营销、运费时传值
     */
    @Valid
    private List<EBusinessApportionItem> apportionItems;

    /**
     * 订单小类（1-自营线上销售服务，2-到家服务，3-装修服务）
     */
    @NotNull(message = "订单小类不能为空")
    @Min(value = 1, message = "订单小类orderSubclass的值必须为 1、2 或 3")
    @Max(value = 3, message = "订单小类orderSubclass的值必须为 1、2 或 3")
    private Integer orderSubclass;

    /**
     * 订单小类名称(自营线上销售服务，到家服务，装修服务)
     */
    private String orderSubclassName;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空")
    private BigDecimal payAmount;

    /**
     * 税收分类编码
     */
    private String taxCateCode;
}
