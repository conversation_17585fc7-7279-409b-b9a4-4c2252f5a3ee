package com.charge.api.web.controller.pos;

import com.charge.api.web.dto.pos.AssetTypeArrearsAmount;
import com.charge.api.web.service.pay.LakalaService;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.lakala.SearchPayResultRequest;
import com.charge.api.web.vo.lakala.SearchPayResultResponse;
import com.charge.api.web.vo.pos.AssetBalancesVO;
import com.charge.api.web.vo.pos.BillVO1;
import com.charge.api.web.vo.pos.HouseArrearsSV1;
import com.charge.api.web.vo.pos.PayMethodChannel;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.AliBucketEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.general.FileUtil;
import com.charge.general.client.file.FileClient;
import com.charge.general.dto.FileBusinessDetailDTO;
import com.charge.general.dto.FileDTO;
import com.charge.general.enums.FileBusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;

@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(value = "lakala支付相关接口")
@RefreshScope
public class LakalaController {

    private final LakalaService lakalaService;

    private final FileClient fileClient;

    @Value("${upload_card_log:true}")
    private Boolean uploadCardLogRecord;


    /**
     * 上传日志
     *
     * @param imei   商户号
     * @param snCode 设备号
     * @param file   文件
     */
    @PostMapping(value = "/uploadCardLogRecord")
    public ChargeResponse<Void> uploadCardLogRecord(@RequestParam String imei, @RequestParam String snCode
            , @RequestParam(value = "file", required = false) MultipartFile file,@RequestParam(required = false)  String token) throws Exception {
        if (file == null) {
            log.warn("file null ,ignore");
            return new ChargeResponse<>();
        }
        if (!StringUtils.hasText(snCode)) {
            log.warn("device null ,ignore");
            return new ChargeResponse<>();
        }
        FileDTO fileDto = FileUtil.upload(file, AliBucketEnum.POS, true);
        FileBusinessDetailDTO fileBusinessDetailDTO = FileBusinessDetailDTO.builder()
                .entityKey(snCode)
                //日志无业务id，使用当前时间戳即可
                .businessId(System.currentTimeMillis())
                .businessType(FileBusinessType.POS_LOG)
                .name(fileDto.getName())
                .ossBucket(fileDto.getOssBucket())
                .ossKey(fileDto.getOssKey())
                .temp(fileDto.getTemp())
                .build();
        ChargeResponse<Long> response = fileClient.saveFileBusinessDetail(fileBusinessDetailDTO);
        AppInterfaceUtil.getResponseDataThrowException(response);
        return new ChargeResponse<>();
    }

    @ApiOperation(value = "通知服务系统查询接口", notes = "通知服务系统查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "mercid", value = "商户号(支付分配的商户号)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "termid", value = "设备号(终端号)(支付分配的终端设备号)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "paymentMethod", value = "支付方式（0-银行卡 1-微信 2-支付宝 3-银联钱包 4-百度钱包 5-京东钱包 6-拉卡拉钱包）", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "txntim", value = "交易时间", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "orderNo", value = "商户订单号", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "tranSeqNo", value = "参考号", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "payResult", value = "交易结果(0表示成功,1表示失败)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/searchPayResult", method = {RequestMethod.POST})
    public ChargeResponse<SearchPayResultResponse> searchPayResult(@RequestParam String mercid, @RequestParam(required = false) String termid, @RequestParam String paymentMethod,
                                                                   @RequestParam(required = false) String txntim, @RequestParam @NotBlank(message = "订单号不能为空") String orderNo,@RequestParam(required = false)  String tranSeqNo,
                                                                   @RequestParam String payResult, @RequestParam String token) {
        SearchPayResultRequest searchRequest = new SearchPayResultRequest(mercid,termid, PayMethodChannel.fromV1PaymentMethodBySearchPayResult(paymentMethod),txntim,orderNo,tranSeqNo,payResult,token,true,true,null,null,null);
        return lakalaService.searchPayResult(searchRequest);
    }

    @ApiOperation(value = "模糊搜索房屋列表([101][103][104]已调通)", notes = "模糊搜索房屋列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "keyword", value = "关键字", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "status", value = "欠费状态(1-欠费，0-未欠费，默认为欠费)", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "subjectType", value = "标的物类型：house,position,shops,lock,car,card", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "currentPage", value = "当前页数（默认为1）", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量（默认为10）", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/index/getArrearsHouseList", method = {RequestMethod.GET})
    public ChargePageResponse<AssetBalancesVO> getArrearsHouseList(@RequestParam(value = "communityUuid")Long communityId, String keyword,
                                                                   @RequestParam(value = "status", defaultValue = "1") Integer status, String subjectType,
                                                                   @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, String token) throws Exception {
        return lakalaService.getArrearsHouseList(communityId, keyword, status, subjectType, currentPage, pageSize);
    }

    @ApiOperation(value = "查询房屋信息详情", notes = "查询房屋信息详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "houseUuid", value = "房屋uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getHouseInfo", method = {RequestMethod.GET})
    public ChargeResponse getHouseInfo(String houseUuid, String token) throws ChargeBusinessException {
        return lakalaService.getHouseInfo(houseUuid);
    }

    @ApiOperation(value = "查询房屋缴费项列表根据收费项区分（[107]已调通）", notes = "查询房屋缴费项列表根据收费项区分")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "houseUuid", value = "房屋Uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "currentPage", value = "当前页数（默认为1）", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量（默认为10）", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getHousePayItemList", method = {RequestMethod.GET})
    public ChargeResponse getHousePayItemList(String houseUuid, @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                              @RequestParam(value = "pageSize", defaultValue = "9999") Integer pageSize, String token) {
        return lakalaService.getHousePayItemList(houseUuid, currentPage, pageSize);
    }

    @ApiOperation(value = "查询业主房间列表", notes = "查询业主房间列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "customerId", value = "客户ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/batchPay/getCustomerHouseList", method = {RequestMethod.GET})
    public ChargeResponse<HouseArrearsSV1> getCustomerHouseList(@RequestParam(value = "communityId") String communityId, @RequestParam(value = "customerId") String customerId, String token) throws ChargeBusinessException {
        return new ChargeResponse<>(lakalaService.getCustomerHouseList(communityId, customerId));
    }

    @ApiOperation(value = "查询房屋已缴费订单信息（消单表获取）（[132]已调通）", notes = "查询房屋已缴费订单信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "payId", value = "支付订单Uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @GetMapping(value = "/getPayOrder")
    public ChargeResponse<BillVO1> getPayOrder(@RequestParam(value = "payId") Long payId, @RequestParam String token) throws ChargeBusinessException {
        return new ChargeResponse<>(lakalaService.getPayOrder(payId));
    }

    @ApiOperation(value = "查询小区缴费禁用状态", notes = "JAVA类:com.chargeProject.consumer.interfaceX.IYueResidentService " +
            "函数签名:ResultContent tradeQuery(String transactionId, String orderNum);")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区ID", required = true, dataType = "String")
    })
    @RequestMapping(value = "/getCommunityDisable", method = {RequestMethod.GET})
    public ChargeResponse getCommunityDisable(String communityUuid, String token) {
        return lakalaService.getCommunityDisable(communityUuid);
    }

    @GetMapping(value = "/index/getArrearsSubjectTypeCount")
    public ChargeResponse<AssetTypeArrearsAmount> getArrearsSubjectTypeCount(@RequestParam Long communityUuid) throws ChargeBusinessException {
        return new ChargeResponse<>(lakalaService.getArrearsSubjectTypeCount(communityUuid));
    }

    @ApiOperation(value = "查询房屋的所有住户信息", notes = "查询房屋的所有住户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseUuid", value = "房屋编号", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String")
    })
    @RequestMapping(value = "/queryHouseAllResident", method = {RequestMethod.GET})
    public ChargeResponse queryHouseAllResident(@NotBlank String communityUuid,@NotBlank String houseUuid, String token) {
        return lakalaService.queryHouseAllResident(communityUuid, houseUuid);
    }

    @ApiOperation(value = "查询个人信息", notes = "查询个人信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "userName", value = "员工账号", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getPersonData", method = {RequestMethod.GET})
    public ChargeResponse getPersonData(String userName, String token) {
        return lakalaService.getPersonData(userName);
    }

    @ApiOperation(value = "根据小区返回功能权限数据", notes = "根据小区返回功能权限数据")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区Uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getAuthByCommunityUuid", method = {RequestMethod.GET})
    public ChargeResponse getAuthByCommunityUuid(String communityId, String token) {
        return lakalaService.listAuthByCommunityUuid(communityId, token);
    }

}
