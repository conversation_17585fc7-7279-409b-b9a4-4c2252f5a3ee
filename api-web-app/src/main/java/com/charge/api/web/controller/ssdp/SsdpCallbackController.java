package com.charge.api.web.controller.ssdp;

import com.charge.api.web.constants.SsdpConstant;
import com.charge.api.web.dto.ssdp.*;
import com.charge.bill.client.CashRefundClient;
import com.charge.bill.dto.refund.CashRefundConfirmCallbackDTO;
import com.charge.bill.dto.refund.CashRefundConfirmDTO;
import com.charge.bill.dto.refund.CashRefundFinishCallbackDTO;
import com.charge.common.constant.CommonConstant;
import com.charge.common.constant.MetricConstants;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.EcsbCodeEnum;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.core.enums.StatusEnum;
import com.charge.starter.web.annotation.Idempotent;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/28
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping(value = "/reportPlatform")
public class SsdpCallbackController {

    private final CashRefundClient cashRefundClient;

    /**
     * 审批结果（多笔返回）
     *
     * @param refundAuditConditionDTO
     * @return
     */
    @PostMapping(value = "/auditStatusCallback")
    @Idempotent
    public Map auditStatusCallback(@RequestBody SsdpRefundAuditConditionDTO refundAuditConditionDTO, HttpServletResponse httpServletResponse) {

        List<CashRefundConfirmDTO> cashRefundConfirms = refundAuditConditionDTO.getRecordList().stream().map(ssdpRefundAuditDTO -> {
            return new CashRefundConfirmDTO(ssdpRefundAuditDTO.getRefundRecordId(), Integer.valueOf(ssdpRefundAuditDTO.getRefundStatus()));
        }).collect(Collectors.toList());
        CashRefundConfirmCallbackDTO cashRefundConfirmCallbackDTO = new CashRefundConfirmCallbackDTO(cashRefundConfirms, cashRefundConfirms.size());

        ChargeResponse<Boolean> response = cashRefundClient.auditCashRefundCallback(cashRefundConfirmCallbackDTO);
        Boolean result = AppInterfaceUtil.getResponseData(response);

        EcsbCodeEnum ecsbCodeResult = Objects.nonNull(result) && result ? EcsbCodeEnum.SUCCESS : EcsbCodeEnum.FAILED;
        EcsbResponseDTO ecsbResponseDTO = EcsbResponseDTO.builder().returnData(buildReturnData(ecsbCodeResult)).returnCode(ecsbCodeResult.getCode())
            .returnDesc(ecsbCodeResult.getValue()).returnStamp(DateUtils.getDateStr(DateUtils.FORMAT_16)).build();
        if(Boolean.TRUE.equals(result)){
            httpServletResponse.addHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.SUCCESS.getCode());
        } else {
            httpServletResponse.addHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.FAIL.getCode());
        }
        return ImmutableMap.of("RESPONSE", ecsbResponseDTO);

    }

    private SsdpReturnDataDTO buildReturnData(EcsbCodeEnum ecsbCodeResult) {

        if (Objects.equals(ecsbCodeResult.getCode(), EcsbCodeEnum.SUCCESS.getCode())) {
            // 返回成功
            return new SsdpReturnDataDTO(SsdpConstant.SUCCESS_CODE, SsdpConstant.SUCCESS_MSG, SsdpConstant.SUCCESS);
        }
        return new SsdpReturnDataDTO(SsdpConstant.FAIL_CODE, SsdpConstant.FAIL_MSG, SsdpConstant.FAIL);
    }

    /**
     * 每次1笔,退款付款完成返回
     *
     * @param ssdpRefundPaymentDTO
     * @return
     */
    @PostMapping(value = "/paymentDetailsCallback")
    @Idempotent
    public Map paymentDetailsCallback(@RequestBody SsdpRefundPaymentDTO ssdpRefundPaymentDTO,HttpServletResponse httpServletResponse) {

        CashRefundFinishCallbackDTO cashRefundFinishCallbackDTO = buildRefundFinish(ssdpRefundPaymentDTO);

        ChargeResponse<Boolean> response = cashRefundClient.finishCashRefundCallback(cashRefundFinishCallbackDTO);
        Boolean result = AppInterfaceUtil.getResponseData(response);

        EcsbCodeEnum ecsbCodeResult = Objects.nonNull(result) && result ? EcsbCodeEnum.SUCCESS : EcsbCodeEnum.FAILED;
        EcsbResponseDTO ecsbResponseDTO = EcsbResponseDTO.builder().returnData(buildReturnData(ecsbCodeResult)).returnCode(ecsbCodeResult.getCode())
            .returnDesc(ecsbCodeResult.getValue()).returnStamp(DateUtils.getDateStr(DateUtils.FORMAT_16)).build();
        if(Boolean.TRUE.equals(result)){
            httpServletResponse.addHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.SUCCESS.getCode());
        } else {
            httpServletResponse.addHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.FAIL.getCode());
        }
        return ImmutableMap.of("RESPONSE", ecsbResponseDTO);
    }

    /**
     * 赋值下游付款完成
     * 
     * @param ssdpRefundPaymentDTO
     * @return
     */
    public CashRefundFinishCallbackDTO buildRefundFinish(SsdpRefundPaymentDTO ssdpRefundPaymentDTO) {
        List<SsdpRefundPaymentDetailDTO> recordList = ssdpRefundPaymentDTO.getRecordList();
        SsdpRefundPaymentDetailDTO ssdpRefundPaymentDetailDTO = new SsdpRefundPaymentDetailDTO();
        BigDecimal acutalMpney = BigDecimal.ZERO;
        String paymentTime = null;
        if (ssdpRefundPaymentDTO.getRefundStatus()) {
            ssdpRefundPaymentDetailDTO = ssdpRefundPaymentDTO.getRecordList().get(CommonConstant.ZERO);
            acutalMpney = recordList.stream().map(e->{
                BigDecimal money = new BigDecimal(e.getPaymentAmount());
                return money;
            }).reduce(BigDecimal.ZERO,BigDecimal::add);
            Optional<SsdpRefundPaymentDetailDTO> last = recordList.stream().max(Comparator.comparing(SsdpRefundPaymentDetailDTO::getPaymentTime));
            paymentTime = last.get().getPaymentTime();
        }
        CashRefundFinishCallbackDTO cashRefundFinishCallbackDTO = BeanCopierWrapper.copy(ssdpRefundPaymentDTO, CashRefundFinishCallbackDTO.class);
        BeanCopierWrapper.copy(ssdpRefundPaymentDetailDTO, cashRefundFinishCallbackDTO);
        cashRefundFinishCallbackDTO.setRefundBillNo(ssdpRefundPaymentDTO.getRefundRecordId());
        cashRefundFinishCallbackDTO.setPaymentAmount(acutalMpney.toPlainString());
        cashRefundFinishCallbackDTO.setPaymentTime(paymentTime);
        return cashRefundFinishCallbackDTO;
    }

}
