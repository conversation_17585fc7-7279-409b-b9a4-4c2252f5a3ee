package com.charge.api.web.vo.joylife;

import com.charge.api.web.vo.joylife.request.ZhaoXiAssertRequest;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/6/7 17:35
 */
@Data
public class AssetBillReq extends ZhaoXiAssertRequest {

    private static final long serialVersionUID = 4657851272322833069L;

    /**
     * 缴费金额
     */
    @NotNull(message = "资产缴费金额不能为空")
    @DecimalMin(value = "0.01",message = "资产缴费金额必须大于0")
    private BigDecimal amount;

    /**
     * 应收单列表
     */
    @Valid
    private List<AssetBillItem> receivables;

    /**
     * 预存列表
     */
    @Valid
    private List<AssetBillItem> prestores;

}
