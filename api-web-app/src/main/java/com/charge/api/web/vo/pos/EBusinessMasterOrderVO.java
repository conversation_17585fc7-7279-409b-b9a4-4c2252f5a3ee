package com.charge.api.web.vo.pos;

import com.charge.common.serializer.BigDecimalSerializer;
import com.charge.common.serializer.IdSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 电商主单vo
 *
 * <AUTHOR>
 * @date 2024/11/14
 */
@Data
public class EBusinessMasterOrderVO {
    /**
     * 主键id
     */
    @JsonSerialize(using = IdSerializer.class)
    private Long id;

    /**
     * 主订单号
     */
    private String extendOrderNo;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 订单时间
     */
    private Date orderTime;
    /**
     * 总金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal amount;
    /**
     * 已支付金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal paymentAmount;
}