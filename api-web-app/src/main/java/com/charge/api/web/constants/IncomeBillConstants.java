package com.charge.api.web.constants;

import com.charge.bill.enums.BusinessTypeEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 批量缴费常量类
 *<AUTHOR>
 *@date 2018/8/24
 */
public class IncomeBillConstants {
	/**
	 *支付状态(0:未支付，1:已支付，2:支付失败,3：待支付)
	 */
	public static final int PAYMENT_STATUS_NOT_PAY = 0;

	public static final int PAYMENT_STATUS_PAY_SUCCESS = 1;

	public static final int PAYMENT_STATUS_PAY_FAILED  = 2;

	public static final int PAYMENT_STATUS_PAY_WAIT = 3;

	public static final String PAY_STATUS_NO = "未支付";

	public static final String PAY_STATUS_YES = "已支付";

	public static final String PAY_STATUS_FAILED = "支付失败";

	public static final String PAY_STATUS_PAYING = "待支付";

	/**
	 *批量账单状态(0表示已完成,1待支付，2已关闭，3表示已作废，4已转预存， 5未支付)
	 */
	public static final String BILL_STATUS_FINISHED ="0";

	public static final String BILL_STATUS_PAYING = "1";

	public static final String BILL_STATUS_CLOSED = "2";

	public static final String BILL_STATUS_CANCELED = "3";

	public static final String BILL_STATUS_PRESTORE = "4";

	public static final String BILL_STATUS_NOT_PAY = "5";

	public static final String BILL_STATUS_FINISHED_CONTENT = "已完成";

	public static final String BILL_STATUS_PAYING_CONTENT = "待支付";

	public static final String BILL_STATUS_CLOSED_CONTENT = "已关闭";

	public static final String BILL_STATUS_CANCELED_CONTENT = "已作废";

	public static final String BILL_STATUS_PRESTORE_CONTENT = "已转预存";

	public static final String BILL_STATUS_NOT_PAY_CONTENT = "未支付";

	/**
	 * 是否为待支付订单
	 */
	public static final String CREATE_BILL_TYPE_CHOOSEPAY = "0";//勾选批量账单

	public static final String CREATE_BILL_TYPE_WAITPAY = "1";//待支付订单

	/**
	 * 收据类型
	 */
	public static final String RERCEIPT_TYPE_MASS = "0";//打印批量账单收据

	public static final String RERCEIPT_TYPE_HOUSE = "1";//打印房间账单收据

	/**
	 * 操作状态
	 */
	public static final String OPERATION_STATUS_BILL_CREATING = "1";//订单生成中

	public static final String OPERATION_STATUS_BILL_CREATED = "2";//订单生成完成

	public static final String OPERATION_STATUS_BILL_PAYING = "3";//缴费中

	public static final Integer OPERATION_STATUS_BILL_PAID = 4;//缴费完成

	public static final String OPERATION_STATUS_BILL_CANCELLING = "5";//作废中

	public static final String OPERATION_STATUS_BILL_CANCELLED = "6";//作废完成

	public static final String OPERATION_STATUS_BILL_CHANGING_PRESTORE = "7";//转预存处理中

	public static final String OPERATION_STATUS_BILL_FINISHED_PRESTORE = "8";//转预存处理完成

	public static final String OPERATION_STATUS_BILL_ERROR = "9";//操作发生异常

	/**
	 * 收据数值常量
	 */
	public static final int RECEIPT_INFO_AMOUNT = 10;//目前设置10个房屋为标准

	/**
	 * 批量订单创建来源
	 */
	public static final String CHARGING_PAY_SYSTEM = "CHARGING_PAY_SYSTEM"; //收费系统支付来源值

	/**
	 * 应收订单待支付状态值
	 */
	public static final String CHARGE_ORDER_PAY_STATUS_ID_PAY_WAIT = "6";

//	/**
//	 * 支付消单表账单状态：1（已作废）
//	 */
//	public static final String PAYMENT_RECORD_BILL_STATUS_ONE = "1";
//
//	/**
//	 * 支付消单表支付状态：2（已作废）
//	 */
//	public static final String PAYMENT_RECORD_PAY_STATUS_TWO = "2";


//	/**
//	 *订单状态id(0未支付，1已支付，2支付失败,3银行托收中，4已挂起，5未生效，6待支付)
//	 */
//	public static final String 	ORDER_STATUS_ZERO = "0";
//
//	/**
//	 * 收款类型(0表示本金)
//	 */
//	public static final String CHARAGE_TYPE_ZERO = "0";
//
//	/**
//	 * 收款类型(1表示违约金)
//	 */
//	public static final String CHARAGE_TYPE_ONE = "1";

	/**
	 * 批量客户
	 */
	public static final String BATCH_CUSTOMER = "1";

	/**
	 * 一户多房
	 */
	public static final String CUSTOMER_MULTI_HOUSE = "0";

	/**
	 * 对账状态
	 */
	public static final Integer BALANCE_STATUS_NOT_DONE = 0;

	/**
	 * 转化业务交易类型为消费类型,支出为1、收入为2
	 * @param businessTypeList
	 * @return
	 */
	public static String convertToType(List<Integer> businessTypeList) {
		String consumeType = "2";
		if (CollectionUtils.isEmpty(businessTypeList)) {
			return consumeType;
		}

//		if (businessTypeList.contains(BusinessTypeEnum.PREDEPOSIT_REFUND.getCode())) {
//			return "1";
//		}

		return consumeType;
	}

	public static final String LOCK_PRE_ORDER_NUM = "lock:income:order_num:";

}
