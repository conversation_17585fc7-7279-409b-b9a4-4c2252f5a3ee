package com.charge.api.web.vo.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 基础订单请求参数
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseOrderReq {

    /**
     * 主订单号
     */
    @NotBlank(message = "主订单号不能为空")
    private String orderNo;


    /**
     * 来源
     */
    @NotBlank(message = "来源不能为空")
    private String source;


    /**
     * 收费项目id：与朝昔的项目id、主数据的项目编码三选一填
     */
    private Long communityId;

    /**
     * 朝昔的项目id：与收费的项目id、主数据的项目编码三选一填
     */
    private String zhaoXiCommunityId;

    /**
     * 主数据的项目编码：与收费的项目id、朝昔的项目id三选一填
     */
    private String communityMdmCode;
}