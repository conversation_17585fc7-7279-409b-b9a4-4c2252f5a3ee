package com.charge.api.web.vo.pos.points;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/11/3 15:31
 */
@Data
public class AccountBalanceQueryVO implements Serializable {

    /**
     * 会员手机号
     */
    @NotBlank(message = "会员手机号不能为空")
    @Pattern(regexp = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(166)|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[8|9]))\\d{8}$", message = "会员手机号格式不正确")
    private String phone;

}
