package com.charge.api.web.dto.collection.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BankCollectionSignListRequestDTO implements Serializable {
    private static final long serialVersionUID = -8360096699188199161L;

    @NotNull(message = "请输入项目ms id")
    private String communityMsId;
    @NotNull(message = "请输入资产ms id列表")
    private List<String> assetMsIdList;
}
