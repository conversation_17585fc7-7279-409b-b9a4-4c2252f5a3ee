package com.charge.api.web.adapter;

import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.CommunityDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class CommunityClientAdapter {

    private final CommunityClient communityClient;

    public List<CommunityDTO> getCommunityById(List<Long> communityIds) throws ChargeBusinessException {
        if (CollectionUtils.isEmpty(communityIds)) {
            return Collections.emptyList();
        }

        ChargeResponse<List<CommunityDTO>> response = communityClient.listCommunity(CommunityCondition.builder().ids(communityIds).build());

        if (Boolean.FALSE.equals(response.isSuccess())) {
            log.error("基础数据服务-项目查询接口 调用异常,communityIds:{}", communityIds);
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "基础数据服务 项目查询接口调用异常");
        }

        return Optional.ofNullable(response.getContent()).orElse(Collections.emptyList());
    }
}
