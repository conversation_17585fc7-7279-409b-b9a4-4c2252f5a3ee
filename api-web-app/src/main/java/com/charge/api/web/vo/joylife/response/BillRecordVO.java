package com.charge.api.web.vo.joylife.response;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/8/26 11:53
 */

@Data
@Builder
public class BillRecordVO {

    /**
     * 交易类型名称
     */
    private String transactionTypeName;

    /**
     * 收费项名称
     */
    private String itemName;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 项目或车场名称
     */
    private String communityName;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 支付金额
     */
    private BigDecimal totalAmount;

    /**
     * 交易流水id
     */
    private Long transactionId;

    /**
     * 资产类型（1房间  2车位）
     */
    private Integer assetType;

    /**
     * 资产Id
     */
    private Long assetId;

    /**
     * 资产msId
     */
    private String assetMsId;

    /**
     * 订单号
     */
    private String orderNum;

}
