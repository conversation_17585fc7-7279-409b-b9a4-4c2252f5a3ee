package com.charge.api.web.vo.joylife.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/8/26 14:07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillRecordDetailVO {

    /*
    //资产msID、项目名称、资产名称、车场名称、资产类型
    //房屋或虚拟资产：资产名称：x栋X单元194 车场名称：‘’ 项目名称：A项目
    //车位：资产名称：B103
    */

    /**
     * 资产名称（车位号 or 楼栋名+单元名+房号）
     */
    private String assetName;

    /**
     * 项目名称
     */
    private String communityName;

    /**
     * 车场名称
     */
    private String parkingName;

    /**
     * 资产类型（1房间  2车位 ）
     */
    private Integer assetType;

    /**
     * 资产Id
     */
    private Long assetId;

    /**
     * 资产msId
     */
    private String assetMsId;

    /**
     * 实付金额
     */
    private BigDecimal incomeAmount;

    /**
     * 账单明细
     */
    private List<RecordDetailBillVO> detailList;

    /**
     * 积分个数
     */
    private Integer points;

    /**
     * 收费员
     */
    private String collectorName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 备注
     */
    private String memo;

    /**
     * 交易流水号
     */
    private String transactionNo;

    /**
     * 支付人
     */
    private String payMember;

    /**
     * 支付方法
     */
    private Integer paymentMethod;

    /**
     * 支付终端
     */
    private String paymentTerminal;

    /**
     * 支付渠道
     */
    private String paymentChannel;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 订单号
     */
    private String orderNum;

}
