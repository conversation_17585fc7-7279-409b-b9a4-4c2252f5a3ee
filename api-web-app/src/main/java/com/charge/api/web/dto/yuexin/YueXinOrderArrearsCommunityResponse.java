package com.charge.api.web.dto.yuexin;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 悦心-欠费查询 返回对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class YueXinOrderArrearsCommunityResponse implements Serializable {

    private static final long serialVersionUID = -2460180471396780652L;

    /**
     * 小区运营平台Id
     */
    private String communityMsId;
    /**
     * 小区Id
     */
    private String communityId;
    /**
     * 小区名称
     */
    private String communityName;
    /**
     * 房间ID
     */
    private String houseId;
    /**
     * 房间类型
     */
    private String houseType;
    /**
     * 房间编码
     */
    private String houseCode;
    /**
     * 房间全部名称
     */
    private String houseName;
    /**
     * 房间运营平台Id
     */
    private String houseMsId;
    /**
     * 房间欠费总额(业主维度，保持不变)
     */
    private BigDecimal houseTotalArrears;
    /**
     * 房间欠费总额-业主
     */
    private BigDecimal houseOwnerTotalArrears;
    /**
     * 房间欠费总额-开发商
     */
    private BigDecimal houseDeveloperTotalArrears;
    /**
     * 房间欠费总额-业主+开发商
     */
    private BigDecimal houseWholeTotalArrears;
    /**
     * 账龄
     */
    private int arrearsAge;

    /**
     * 欠费月数
     */
    @ApiModelProperty(value = "欠费月数")
    private Integer monthCount;
    /**
     * 欠费月数-业主
     */
    private Integer ownerMonthCount;
    /**
     * 欠费月数-开发商
     */
    private Integer developerMonthCount;
    /**
     * 欠费月数-业主+开发商
     */
    private Integer wholeMonthCount;

    /**
     * 收费对象类型（0-业主，1-开发商）
     */
    private Integer chargeObjectType;

}
