package com.charge.api.web.service.joylife;

import com.charge.api.web.vo.joylife.request.BillListReq;
import com.charge.api.web.vo.joylife.response.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.joylife.dto.*;
import com.charge.joylife.vo.AssetPredepositAccountListVO;
import com.charge.joylife.vo.AssetPredepositQueryVO;

import java.util.List;

public interface YueXinAppService {
    ChargeResponse<List<HouseOrderItemArrears>> listAssetsArrearsInfo(ListAssetsArrearsReq condition) throws ChargeBusinessException;

    ChargeResponse<List<ArrearsAssetInfo>> getArrearsAssetInfoByCondition(CommunityArrearsAssetsReq condition) throws ChargeBusinessException;

    List<AssetPredepositAccountListVO> getAssetPredepositList(AssetPredepositQueryVO conditionVO) throws ChargeBusinessException;

    List<VirtualAssetDTO> listVirtualAsset(VirtualAssetReq req) throws ChargeBusinessException;

    List<VirtualAssetDTO> getVirtualAsset(VirtualAssetReq req) throws ChargeBusinessException;

    PagingDTO<VirtualAssetDTO> searchVirtualAsset(VirtualAssetReq req) throws ChargeBusinessException;

    List<BillRecordVO> getBillList(BillListReq reqCondition) throws ChargeBusinessException;

    BillRecordDetailVO getBillDetail(BillDetailReq reqCondition) throws ChargeBusinessException;

    List<DepositAdjustRecordVO> listDepositAdjustRecord(Long communityId,Long assetId, Long predepositAccountId, Integer pageNum, Integer pageSize) throws ChargeBusinessException;

    List<PredepositRefundRecordVO> listRefundRecord(Long predepositAccountId, Integer pageNum, Integer pageSize) throws ChargeBusinessException;

    PredepositBillVO queryBillList(Long assetId, Long communityId, Long predepositAccountId) throws ChargeBusinessException;

}
