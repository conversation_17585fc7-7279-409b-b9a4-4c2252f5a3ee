package com.charge.api.web.controller.joylife.yuexin;

import com.charge.api.web.service.joylife.YueXinAppService;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.joylife.dto.VirtualAssetDTO;
import com.charge.joylife.dto.VirtualAssetReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/yuexin/order")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class TempOrderController {

    private final YueXinAppService yueXinAppService;

    /**
     * 查询项目的所有虚拟房屋
     *
     * @param req 请求
     * @return 虚拟房屋
     * @throws ChargeBusinessException 异常
     */
    @PostMapping(value = "/virtualhouse")
    public ChargeResponse<List<VirtualAssetDTO>> listVirtualAsset(@Valid @RequestBody VirtualAssetReq req) throws ChargeBusinessException {
        return new ChargeResponse<>(yueXinAppService.listVirtualAsset(req));
    }

    /**
     * 查询虚拟房屋详情
     *
     * @param req 请求
     * @return 虚拟房屋
     * @throws ChargeBusinessException 异常
     */
    @PostMapping(value = "/virtualhouse/detail")
    public ChargeResponse<List<VirtualAssetDTO>> getVirtualAsset(@RequestBody VirtualAssetReq req) throws ChargeBusinessException {
        return new ChargeResponse<>(yueXinAppService.getVirtualAsset(req));
    }

    /**
     * 查询虚拟房屋详情
     *
     * @param req 请求
     * @return 虚拟房屋
     * @throws ChargeBusinessException 异常
     */
    @PostMapping(value = "/virtualhouse/search")
    public ChargeResponse<PagingDTO<VirtualAssetDTO>> searchVirtualAsset(@RequestBody VirtualAssetReq req) throws ChargeBusinessException {
        return new ChargeResponse<>(yueXinAppService.searchVirtualAsset(req));
    }

}
