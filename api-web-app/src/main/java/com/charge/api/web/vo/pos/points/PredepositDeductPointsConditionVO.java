package com.charge.api.web.vo.pos.points;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 计算预存退款、预存调整 扣除积分数量
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PredepositDeductPointsConditionVO implements Serializable {
    private static final long serialVersionUID = 2812639563743781907L;

    /**
     * 项目id
     */
    private Long communityId;

    /**
     * com.charge.bill.enums.BusinessTypeEnum
     * 业务类型：2-预存调整  6-预存退款
     */
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    /**
     * 退款/调整费项
     */
    private List<PredepositRefundItemVO> sourceItems;

}
