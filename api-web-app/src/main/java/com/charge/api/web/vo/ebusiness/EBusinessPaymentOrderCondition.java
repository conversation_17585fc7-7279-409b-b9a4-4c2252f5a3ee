package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/18 14:19
 */
@Data
public class EBusinessPaymentOrderCondition {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    /**
     * 系统来源标识(固定：ECOMMERCE)
     */
    @NotBlank(message = "系统来源标识不能为空")
    private String orderSourceCode;
    /**
     * 项目编码
     */
    @NotBlank(message = "项目编码不能为空")
    private String communityId;
    /**
     * 下单时间  格式：yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "下单时间不能为空")
    private String orderTime;
    /**
     * 支付状态(全部支付：PAID  部分支付：PART_PAID)
     */
    @NotBlank(message = "支付状态不能为空")
    private String payState;
    /**
     * 订单总金额（优惠前金额）
     */
    @NotNull(message = "订单总金额（优惠前金额）不能为空")
    private BigDecimal originPrice;
    /**
     * 优惠总金额，小于等于0
     * tradePrice.discountsPrice
     */
    @NotNull(message = "优惠总金额不能为空")
    private BigDecimal discountsTotalPrice;
    /**
     * 溢价总金额（运费金额）大于等于0
     * tradePrice.deliveryPrice
     */
    @NotNull(message = "溢价总金额（运费金额）不能为空")
    private BigDecimal premiumTotalAmount;
    /**
     * 优惠后金额
     */
    @NotNull(message = "优惠后金额不能为空")
    private BigDecimal totalPrice;
    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空")
    private String customerName;
    /**
     * 客户账号
     */
    @NotBlank(message = "客户账号不能为空")
    private String customerAccount;
    /**
     * 支付明细
     */
    @NotNull(message = "支付明细不能为空")
    @Valid
    private EBusinessPayment payment;
    /**
     * 商品明细
     */
    @NotEmpty(message = "商品明细不能为空")
    @Valid
    private List<EBusinessOrderItem> orderItems;
}
