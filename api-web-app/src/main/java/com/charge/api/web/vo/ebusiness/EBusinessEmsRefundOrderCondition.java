package com.charge.api.web.vo.ebusiness;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/11/8 13:54
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EBusinessEmsRefundOrderCondition {

    /**
     * 小区id
     */
    @NotNull(message = "项目id不能为空")
    private Long communityId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 电商退款单号
     */
    @NotBlank(message = "退款单号不能为空")
    private String refundNo;

    /**
     * 退款总金额
     */
    @NotNull(message = "退款总金额不能为空")
    private BigDecimal refundAmount;

    /**
     * 退款申请时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "退款申请时间不能为空")
    private String refundTime;

    /**
     * 收款银行账号
     */
    @Pattern(regexp = "^[0-9]{9,25}$", message = "收款银行账号为9-25位数字")
    @NotBlank(message = "收款银行账号不能为空")
    private String bankAccountNo;

    /**
     * 收款账号名
     */
    @Length(max = 25,message = "收款账号名最大长度25")
    @NotBlank(message = "收款账号名不能为空")
    private String acceptAccountName;

    /**
     *
     * 收款开户行
     */
    @Length(max = 25,message = "收款开户行最大长度25")
    @NotBlank(message = "收款开户行不能为空")
    private String acceptAccountOpeningBank;

    /**
     * 收款账户城市
     */
    @Length(max = 25,message = "收款账户城市最大长度25")
    @NotBlank(message = "收款账户城市不能为空")
    private String acceptAccountCity;

    /**
     * 退款说明
     */
    @NotBlank(message = "退款说明不能为空")
    @Length(max = 200,message = "退款说明最大长度200")
    private String refundRemark;

    private String operatorName;

    /**
     * 商品明细
     */
    @Valid
    private List<EBusinessEmsRefundItem> refundItems;

}
