package com.charge.api.web.service.order;

/**
 * CommunityBankAccount
 * <p>
 * Description:
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/29
 */
public class CommunityBankAccount {
 /**
  * 小区id
  */
 private Long communityId;

 /**
  * 交易结算账户户名
  */
 private String bankActualAccountName;
 /**
  * 实体银行卡号
  */
 String bankActualAccount;
 /**
  * 虚拟银行卡号
  */
 String bankVirtualAccount;
 /**
  * 威富通账号
  */
 String swiftAccount;
 /**
  * 招行行号
  */
 String cmbAccount;
 /**
  * 拉卡拉账号id
  */
 Long lakalaAccountId;
 /**
  * lakala账号
  */
 String lakalaAccount;
 /**
  * 状态 0 未启用 1 已启用 2 停用
  */
 Integer status;
 /**
  * 状态名称
  */
 String statusName;
 /**
  * 创建时间
  */
 String createTime;
 /**
  * 修改时间
  */
 String updateTime;

 /**
  * 账户类型：1业主共有，2物业
  */
 private Integer rewardAccountType;

 /**
  * 商户号
  */
 private String merchantNum;
}