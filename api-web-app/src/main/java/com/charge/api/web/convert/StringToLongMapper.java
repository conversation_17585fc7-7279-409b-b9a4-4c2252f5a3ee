package com.charge.api.web.convert;

import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Description:
 * @date 2023/2/1716:48
 */
@Mapper(uses = StringToLongMapper.class)
public class StringToLongMapper {

     public Long stringToLong(String var){
       return var == null? null:Long.valueOf(var);
     }
     public String longToString(Long num){
        return num==null? null:String.valueOf(num);
     }
}
