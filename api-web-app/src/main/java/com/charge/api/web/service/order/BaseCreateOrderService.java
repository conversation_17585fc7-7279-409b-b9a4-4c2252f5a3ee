package com.charge.api.web.service.order;

import com.charge.api.web.support.BankAccountSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.util.ShardingUtil;
import com.charge.bill.client.*;
import com.charge.bill.dto.income.AssetBillInfoDTO;
import com.charge.bill.dto.income.AssetTransactionDTO;
import com.charge.bill.dto.income.IncomeBillDTO;
import com.charge.bill.dto.income.TransactionRelationDTO;
import com.charge.bill.enums.BalanceStatusEnum;
import com.charge.bill.enums.BusinessTypeEnum;
import com.charge.bill.enums.OrderNumPrefixEnum;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.config.client.item.CommunityPreStoreItemClient;
import com.charge.config.constant.CommonChargeItem;
import com.charge.config.dto.item.ChargeItemSimpleDTO;
import com.charge.config.dto.item.CommonPreStoreConfigDTO;
import com.charge.core.util.CollectionUtil;
import com.charge.maindata.client.CustomerClient;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.enums.ChargeObjTypeEnum;
import com.charge.maindata.pojo.dto.*;
import com.charge.pay.client.CommunityRewardClient;
import com.charge.pay.client.LakalaMerchantClient;
import com.charge.pay.dto.*;
import com.charge.pay.dto.reward.CommunityRewardConfigDetailDTO;
import com.charge.pay.dto.reward.RewardDetailRequestDTO;
import com.charge.starter.jedis.JedisManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.charge.api.web.constants.LakalaConstans.*;
import static com.charge.api.web.service.order.PosCreateOrderService.getMercid;
import static com.charge.api.web.service.pos.impl.ChargeBillServiceImpl.nullStr;
import static com.charge.maindata.enums.AssetUseStatusEnum.getCodeByTypeAndOriginalCode;

/**
 * 基础下单服务
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
@Slf4j
public abstract class BaseCreateOrderService {

    @Resource
    private BillNumGeneratorClient billNumGeneratorClient;

    @Resource
    private AssetTransactionClient assetTransactionClient;

    @Resource
    private IncomeBillClient incomeBillClient;

    @Resource
    private TransactionRelationClient transactionRelationClient;

    @Resource
    private AssetBillInfoClient assetBillInfoClient;

    @Resource
    private BillBusinessManager businessManager;

    @Resource
    private CustomerClient customerClient;

    @Resource
    private CommunitySupport communitySupport;

    @Resource
    private  JedisManager jedisManager;

    @Resource
    private CommunityRewardClient communityRewardClient;

    @Resource
    private  CommunityPreStoreItemClient communityPreStoreItemClient;

    @Resource
    private  LakalaMerchantClient lakalaMerchantClient;

    @Resource
    private  BankAccountSupport bankAccountSupport;

    @Value("${checkLakalaMerchant:false}")
    private Boolean checkLakalaMerchant;

    @Value("${checkLakalaMerchantForReward:true}")
    private Boolean checkLakalaMerchantForReward;


    private static final String MERGE_PAY = "合并缴费";
    private static final String PRE_STORE = "预存充值";


    private static final Integer COMMON_PRE_STORE_ENABLE =0;



    public void createOrder(CreateOrderContext context) throws ChargeBusinessException {
        //校验订单创建上下文
        validateContext(context);
        // 补充上下文
        fillContext(context);
        //订单支付
        orderPay(context);
        //保存账单
        saveBill(context);
        //通用后置处理
        postBill(context);
    }

    private void postBill(CreateOrderContext context) {
        String account = ThreadContext.get(ACCOUNT);
        //设置操作员的项目
        if(StringUtils.hasText(account)){
            setLastOperateCommunityId(account,context.getCommunity().getId());
        }
    }

    private void setLastOperateCommunityId(String account,Long communityId){
        jedisManager.hset(LOGIN_POS+account, COMMUNITY_ID,String.valueOf(communityId),3600*24*14);
    }


    private void fillContext(CreateOrderContext context) {
        // 填充支付订单号
        try {
            String orderNum = context.getOrderNum();
            if (!StringUtils.hasText(orderNum)){
                orderNum = IdGeneratorSupport.getIstance().nextId();
            }
            context.setOrderNum(orderNum);
        } catch (Exception e) {
            throw new ChargeRuntimeException("调用获取支付订单号异常:" + e.getMessage(), e);
        }
        //填充goodsName
        if (context.getAssetBillBusinesses().size() == 1 && context.getAssetBillBusinesses().get(0).getBillBusinesses().size() == 1) {
            BillBusiness billBusiness = context.getAssetBillBusinesses().get(0).getBillBusinesses().get(0);
            if(billBusiness instanceof PredepositBillBusiness ){
                context.setGoodsName(PRE_STORE);
            }else {
                context.setGoodsName(billBusiness.getName());
            }
        } else {
            context.setGoodsName(MERGE_PAY);
        }

        //填充用户信息
        if (context.getPayMemberId() == null || !StringUtils.hasText(context.getPayMember())) {
            Optional<AssetDTO> assetDTOOptional = context.getAssetBillBusinesses().stream().map(assetBillBusiness -> {
                AssetDTO asset = assetBillBusiness.getAsset();
                if (asset.getHouseDTO() != null || asset.getParkingSpaceDTO() != null) {
                    return asset;
                } else {
                    return null;
                }
            }).filter(Objects::nonNull).findFirst();
            assetDTOOptional.ifPresent(assetDTO -> {
                List<CustomerDTO> customers = null;
                if (assetDTO.getHouseDTO() != null) {
                    customers = assetDTO.getHouseDTO().getListCustomer();
                } else {
                    customers = assetDTO.getParkingSpaceDTO().getListCustomer();
                }
                fillOrderCustomer(context, customers);
            });
        }
    }

    private void fillOrderCustomer(CreateOrderContext context,List<CustomerDTO> customers){
        if(CollectionUtils.isEmpty(customers)){
            return;
        }
        if(StringUtils.hasText(context.getPayMember())&&context.getPayMemberId()!=null){
            return;
        }
        if(StringUtils.hasText(context.getPayMember())&&context.getPayMemberId()==null){
            CustomerDTO customer = customers.stream().filter(customerDTO -> customerDTO.getCustomerName().equals(context.getPayMember())).findFirst()
                    .orElse(customers.get(0));
            context.setCustomerMsId(org.apache.commons.lang3.StringUtils.isNotBlank(customer.getMsId())
                    && (PaymentTerminalEnum.POS.equals(context.getPaymentTerminal()) || PaymentTerminalEnum.POS_UNIONPAY.equals(context.getPaymentTerminal())) ?
                    customer.getMsId() : org.apache.commons.lang3.StringUtils.EMPTY);
            context.setPayMemberId(customer.getId());
            context.setPayMemberMobile(customer.getPhone());
        }else if(!StringUtils.hasText(context.getPayMember())){
            CustomerDTO customer = customers.get(0);
            context.setCustomerMsId(org.apache.commons.lang3.StringUtils.isNotBlank(customer.getMsId())
                    && (PaymentTerminalEnum.POS.equals(context.getPaymentTerminal()) || PaymentTerminalEnum.POS_UNIONPAY.equals(context.getPaymentTerminal())) ?
                    customer.getMsId() : org.apache.commons.lang3.StringUtils.EMPTY);
            context.setPayMember(customer.getCustomerName());
            context.setPayMemberId(customer.getId());
            context.setPayMemberMobile(customer.getPhone());
        }
    }

    private void validateContext(CreateOrderContext context) throws ChargeBusinessException {
        Assert.isTrue(!CollectionUtils.isEmpty(context.getAssetBillBusinesses()), "账单业务列表为空");
        //校验所有账户归属相同酬金制
        checkRewardAccountAndLakalaMerchant(context);
        //校验酬金制项目与通用预存互斥
        checkCommonPreStoreRewardMutualExclusion(context);
        //checkAssetChargeObject
        Assert.isTrue( !PaymentTerminalEnum.POS.equals(context.getPaymentTerminal())||checkAssetChargeObject(context),"开发商的资产不能在pos进行缴欠费或预存");
        // 不同的业务有不同的校验
        businessManager.validateBillBusiness(context);
    }

    private boolean checkAssetChargeObject(CreateOrderContext context){
        return !context.getAssetBillBusinesses().stream().anyMatch(a->{
            boolean hasPreStoreOrNormal = a.getBillBusinesses().stream().anyMatch(billBusiness ->
                    BusinessTypeEnum.NORMAL_PAY.equals(billBusiness.getType()) || BusinessTypeEnum.PREDEPOSIT_PAY.equals(billBusiness.getType()));
            if(!hasPreStoreOrNormal){
                return false;
            }
            AssetDTO asset = a.getAsset();
            if(asset.getHouseDTO()!=null){
                return ChargeObjTypeEnum.DEVELOPER.getCode().equals(asset.getHouseDTO().getChargeObjType());
            }
            if(asset.getParkingSpaceDTO()!=null){
                return ChargeObjTypeEnum.DEVELOPER.getCode().equals(asset.getParkingSpaceDTO().getChargeObjType());
            }
            return false;
        });
    }

    private void checkCommonPreStoreRewardMutualExclusion(CreateOrderContext context) throws ChargeBusinessException {
        Set<Long> commonPreStoreItems = context.getAssetBillBusinesses().stream().flatMap(assetBillBusinesses ->
                assetBillBusinesses.getBillBusinesses().stream().map(BillBusiness::getChargeItemId).filter(CommonChargeItem.ITEM_ID::equals)).collect(Collectors.toSet());
        if(!commonPreStoreItems.isEmpty()){
            ChargeResponse<CommonPreStoreConfigDTO> commonPreStoreConfigResponse = communityPreStoreItemClient.oneCommonPreStoreConfig(context.getCommunity().getId());
            CommonPreStoreConfigDTO commonPreStoreConfigDTO = AppInterfaceUtil.getResponseDataThrowException(commonPreStoreConfigResponse);
            if(commonPreStoreConfigDTO==null||!COMMON_PRE_STORE_ENABLE.equals(commonPreStoreConfigDTO.getStatus())){
                throw new ChargeBusinessException(ErrorInfoEnum.E1011.getCode(),"该项目通用预存未启用");
            }
        }
    }

    private void checkRewardAccountAndLakalaMerchant(CreateOrderContext context) throws ChargeBusinessException {
        Set<Long> chargeItemIds = context.getAssetBillBusinesses().stream().flatMap(assetBillBusinesses ->
                assetBillBusinesses.getBillBusinesses().stream().map(BillBusiness::getChargeItemId)).filter(Objects::nonNull).collect(Collectors.toSet());
        //当只有一个费项，不用校验
        if (chargeItemIds.size() == 1&&!checkLakalaMerchantForReward&&!checkLakalaMerchant) {
            return;
        }
        fillRewardConfig(context);
        CommunityRewardConfigDetailDTO communityRewardConfigDetail = context.getCommunityRewardConfigDetail();
        if (Boolean.TRUE.equals(communityRewardConfigDetail.getRewardStatus())) {
            Set<Long> ownerShipItemIds = communityRewardConfigDetail.getOwnershipChargeItemList().stream().map(ChargeItemSimpleDTO::getItemId).collect(Collectors.toSet());
            Sets.SetView<Long> difference = Sets.difference(chargeItemIds, ownerShipItemIds);
            boolean allChargeItemSameRewardAccount = difference.isEmpty() || difference.size() == chargeItemIds.size();
            if (!allChargeItemSameRewardAccount) {
                throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "酬金制项目不同账户归属的不能一起下单");
            }
            if (checkLakalaMerchantForReward) {
                checkLakalaMerchant(context);
            }
        } else if (checkLakalaMerchant) {
            checkLakalaMerchant(context);
        }
    }

    private void checkLakalaMerchant(CreateOrderContext context) throws ChargeBusinessException {
        if(StringUtils.hasText(context.getMercid())){
            fillLakalaMerchant(context);
            if((!StringUtils.hasText(context.getLakalaMerchant())) || !context.getLakalaMerchant().equals(context.getMercid())){
                throw new ChargeBusinessException(ErrorInfoEnum.E1018.getCode(),String.format("pos商户号%s与项目%s配置的%s不一致，请检查.",context.getMercid(),context.getCommunity().getName(),context.getLakalaMerchant()));
            }
        }
    }

    public void fillRewardConfig(CreateOrderContext context) throws ChargeBusinessException {
        if(context.getCommunityRewardConfigDetail()==null){
            RewardDetailRequestDTO checkRewardStatusRequestDTO = new RewardDetailRequestDTO();
            checkRewardStatusRequestDTO.setCommunityId(context.getCommunity().getId());
            ChargeResponse<CommunityRewardConfigDetailDTO> response = communityRewardClient.getCommunityRewardConfigDetailDTO(checkRewardStatusRequestDTO);
            CommunityRewardConfigDetailDTO rewardConfigDetail = AppInterfaceUtil.getResponseDataThrowException(response);
            context.setCommunityRewardConfigDetail(rewardConfigDetail);
        }
    }

    public void fillCmbTransferCommunityBankAccount(CreateOrderContext context) throws ChargeBusinessException {
        if(context.getCommunityBankAccount()!=null){
            return;
        }
        CommunityBankAccountGroupDTO accountGroupDTO = bankAccountSupport.getByCommunityId(context.getCommunity().getId());
        Assert.isTrue(!accountGroupDTO.getRewardStatus().equals(0),"酬金制项目无法使用招行转账！");
        context.setCommunityBankAccount(accountGroupDTO);
    }

    public void fillLakalaMerchant(CreateOrderContext context) throws ChargeBusinessException {
        if(StringUtils.hasText(context.getLakalaMerchant())){
            return;
        }
        CommunityDTO community = context.getCommunity();
        LakalaMerchantConditionDTO conditionDTO = new LakalaMerchantConditionDTO();
        conditionDTO.setCommunityIdList(Lists.newArrayList(community.getId()));

        CommunityRewardConfigDetailDTO communityRewardConfigDetail = context.getCommunityRewardConfigDetail();
        if(Boolean.TRUE.equals(communityRewardConfigDetail.getRewardStatus())){
            Long chargeItemId=context.getAssetBillBusinesses().stream().flatMap(a->a.getBillBusinesses().stream()).map(BillBusiness::getChargeItemId)
                    .filter(a -> !CommonChargeItem.ITEM_ID.equals(a)).findFirst().orElse(CommonChargeItem.ITEM_ID);
            boolean propertyChargeItem = communityRewardConfigDetail.getPropertyChargeItemList().stream().anyMatch(a -> chargeItemId.equals(a.getItemId()));
            Long orgBankId;
            if(propertyChargeItem){
                orgBankId = communityRewardConfigDetail.getPropertyBankAccountDTO().getId();
            }else {
                orgBankId = communityRewardConfigDetail.getOwnershipBankAccountDTO().getId();
            }
            conditionDTO.setOrgBankInfoIds(Lists.newArrayList(orgBankId));
        }
        List<LakalaCommunityMerchantDTO> lakalaCommunityMerchants = AppInterfaceUtil.getResponseDataThrowException(lakalaMerchantClient.selectByCondition(conditionDTO));
        if (CollectionUtil.isEmpty(lakalaCommunityMerchants)) {
            log.warn("查询拉卡拉/银联商户号为空,communityId:{}", community.getId());
        }else if(lakalaCommunityMerchants.size()==1){
            context.setLakalaMerchant(lakalaCommunityMerchants.get(0).getMerchantNumber());
            context.setUnionPayMerchant(lakalaCommunityMerchants.get(0).getUnionPayMerchantNumber());
        }else {
            //取id大的，之后整理完项目商户号再删除
            lakalaCommunityMerchants.sort((a,b)->b.getId().compareTo(a.getId()));
            context.setLakalaMerchant(lakalaCommunityMerchants.get(0).getMerchantNumber());
            context.setUnionPayMerchant(lakalaCommunityMerchants.get(0).getUnionPayMerchantNumber());
        }
    }


    public abstract void orderPay(CreateOrderContext context);

    private void saveBill(CreateOrderContext context) {
        //保存实收
        saveIncomeBill(context);
        //保存资产流水
        saveTransaction(context);
        //保存账单扩展信息
        saveBillExtension(context);
        //保存账单业务，比如押金、预存、订单
        orderSaveBusiness(context);
        //保存交易关联
        saveTransactionRelation(context);
    }

    private void saveIncomeBill(CreateOrderContext context) {
        String billNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.INCOME_BILL.getCode()).getContent();
        IncomeBillDTO income = IncomeBillDTO.builder()
                .orderNum(context.getOrderNum())
                .billNum(billNum)
                .outTransactionNo(context.getOutTransactionNo())
                .communityId(context.getCommunity().getId())
                .communityName(context.getCommunity().getName())
                .incomeMoney(context.getTotalPrice())
                .points(context.getTotalPoints())
                .equityAccount(context.getEquityAccount())
                .goodsName(context.getGoodsName())
                .paymentMethod(context.getPaymentMethod().getPaymentCode())
                .paymentChannel(context.getPaymentChannel().getPaymentChannel())
                .paymentTerminal(context.getPaymentTerminal().getCode())
                .paymentTime(context.getCreateTime())
                .payMemberId(org.apache.commons.lang3.StringUtils.isBlank(context.getCustomerMsId()) ?
                        nullStr(context.getPayMemberId()) : context.getCustomerMsId())
                .payMember(context.getPayMember())
                .payMemberMobile(context.getPayMemberMobile())
                .payHouseCount(context.getPayHouseCount())
                .balanceStatus(BalanceStatusEnum.UNRECONCILED.getCode())
                .collectorId(context.getCollectorId())
                .collectorName(context.getCollectorName())
                .receiptType(0)
                .billType(context.getPayHouseCount()==1?0:1)
                .bankAccountUuid(context.getBankAccountUuid())
                .bankAccountNo(context.getBankAccountNum())
                .memo(context.getMemo()).build();
        income.setId(context.getIncomeBillId());
        income.setCreateUser(context.getCollectorName());
        income.setCreateTime(new Timestamp(context.getCreateTime().getTime()));
        try {
            ChargeResponse<IncomeBillDTO> response = incomeBillClient.create(income);
            Assert.isTrue(response.isSuccess(), "创建实收单异常" + response.getMessage());
            Assert.notNull(response.getContent(), "创建实收单返回为空");
            context.setIncomeBillId(response.getContent().getId());
        } catch (Exception e) {
            throw new ChargeRuntimeException("调用创建实收单异常:" + e.getMessage(), e);
        }


    }

    private void saveBillExtension(CreateOrderContext context) {
        ShardingUtil.addCommunityId2ThreadContext(context.getCommunity().getId());
        context.getAssetBillBusinesses().forEach(assetBillBusiness -> {
            AssetBillInfoDTO assetBillInfoDTO = AssetBillInfoDTO.builder()
                    .assetTransactionId(assetBillBusiness.getAssetTransactionId())
                    .orderNum(context.getOrderNum())
                    .deviceInfo(context.getDeviceInfo())
                    .mercid(getMercid(context))
                    .payMember(context.getPayMember())
                    .payMemberId(nullStr(context.getPayMemberId()))
                    .operatorId(context.getCollectorId())
                    .operatorName(context.getCollectorName())
                    .communityId(context.getCommunity().getId())
                    .build();
            assetBillInfoDTO.setCreateTime(new Timestamp(context.getCreateTime().getTime()));
            assetBillInfoDTO.setCreateUser(context.getCollectorId());
            try {
                ChargeResponse<AssetBillInfoDTO> response = assetBillInfoClient.create(assetBillInfoDTO);
                Assert.isTrue(response.isSuccess(), "保存账单扩展信息异常" + response.getMessage());
            } catch (ChargeBusinessException e) {
                throw new ChargeRuntimeException("调用保存账单扩展信息异常:" + e.getMessage(), e);
            }
        });

    }

    private void saveTransaction(CreateOrderContext context) {
        context.getAssetBillBusinesses().forEach(assetBillBusiness -> {
            AssetTransactionDTO transaction = AssetTransactionDTO.builder()
                    .incomeId(context.getIncomeBillId())
                    .money(assetBillBusiness.getBillBusinesses().stream().map(BillBusiness::getMoney).reduce(BigDecimal.ZERO,BigDecimal::add))
                    .goodsName(context.getGoodsName())
                    .assetOrderNum(context.getOrderNum())
                    .communityId(context.getCommunity().getId())
                    .communityName(context.getCommunity().getName())
                    .assetId(assetBillBusiness.getAsset().getId())
                    .paymentMethod(context.getPaymentMethod().getPaymentCode())
                    .paymentChannel(context.getPaymentChannel().getPaymentChannel())
                    .paymentTerminal(context.getPaymentTerminal().getCode())
                    .paymentTime(context.getCreateTime())
                    .assetBillType(context.getAssetType().getValue())
                    .bankTransactionNo(context.getOutTransactionNo())
                    .build();
            transaction.setCreateUser(context.getCollectorId());
            transaction.setCreateTime(new Timestamp(context.getCreateTime().getTime()));
            HouseDTO houseDTO = assetBillBusiness.getAsset().getHouseDTO();
            if (houseDTO != null) {
                transaction.setBuildingId(houseDTO.getBuildingId());
                transaction.setBuildingName(houseDTO.getBuildingName());
                transaction.setUnitId(houseDTO.getUnitId());
                transaction.setUnitName(houseDTO.getUnitName());
                transaction.setAssetCode(houseDTO.getHouseCode());
                transaction.setAssetName(houseDTO.getHouseName());
                transaction.setAssetUseStatus(getCodeByTypeAndOriginalCode(AssetTypeEnum.HOUSE.getCode(),houseDTO.getHouseUseStatus()));
            } else if (assetBillBusiness.getAsset().getParkingSpaceDTO() != null) {
                ParkingSpaceDTO parkingSpaceDTO = assetBillBusiness.getAsset().getParkingSpaceDTO();
                transaction.setAssetCode(parkingSpaceDTO.getParkingSpaceCode());
                transaction.setAssetName(parkingSpaceDTO.getParkingSpaceName());
                transaction.setAssetUseStatus(getCodeByTypeAndOriginalCode(AssetTypeEnum.PARKING_SPACE.getCode(),parkingSpaceDTO.getUsageState()));
            }
            transaction.setOwnerId(context.getPayMemberId());
            transaction.setOwnerName(context.getPayMember());
            try {
                ChargeResponse<AssetTransactionDTO> response = assetTransactionClient.create(transaction);
                Assert.isTrue(response.isSuccess(), "创建资产流水异常" + response.getMessage());
                Assert.notNull(response.getContent(), "创建资产流水返回为空");
                Long assetTransactionId = response.getContent().getId();
                assetBillBusiness.setAssetTransactionId(assetTransactionId);
                communitySupport.setAssetTransactionIdCommunityIdRelation(assetTransactionId, transaction.getCommunityId());
            } catch (Exception e) {
                throw new ChargeRuntimeException("调用创建资产流水异常:" + e.getMessage(), e);
            }
        });

    }

    public static CustomerDTO getCustomer(AssetDTO asset) {
        List<CustomerDTO> customers = null;
        if (asset.getHouseDTO() != null) {
            customers = asset.getHouseDTO().getListCustomer();
        } else if (asset.getParkingSpaceDTO() != null) {
            customers = asset.getParkingSpaceDTO().getListCustomer();
        }

        if (CollectionUtils.isEmpty(customers)) {
            return null;
        } else {
            return customers.get(0);
        }
    }

    private void saveTransactionRelation(CreateOrderContext context) {
        List<TransactionRelationDTO> relations = context.getAssetBillBusinesses().stream().flatMap(assetBillBusiness ->
                assetBillBusiness.getBillBusinesses().stream().map(billBusiness -> {
                    TransactionRelationDTO relationDTO = TransactionRelationDTO.builder()
                            .assetTransactionId(assetBillBusiness.getAssetTransactionId())
                            .businessId(billBusiness.getId())
                            .businessType(billBusiness.getType().getCode())
                            .communityId(context.getCommunity().getId())
                            .build();
                    relationDTO.setCreateTime(context.getCreateTime());
                    return relationDTO;
                })
        ).collect(Collectors.toList());
        try {
            ChargeResponse<List<TransactionRelationDTO>> response = transactionRelationClient.batchCreate(relations);
            Assert.isTrue(response.isSuccess(), "创建资产流水关联异常" + response.getMessage());
        } catch (Exception e) {
            throw new ChargeRuntimeException("调用创建资产流水异常:" + e.getMessage(), e);
        }
    }

    private void orderSaveBusiness(CreateOrderContext context) {
        businessManager.handleBillBusiness(context);
    }

}


