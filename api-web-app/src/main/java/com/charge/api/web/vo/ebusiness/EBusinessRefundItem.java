package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/23 10:57
 */
@Data
public class EBusinessRefundItem {

    /**
     * 商品明细项id
     */
    @NotBlank(message = "商品明细项id不能为空")
    private String oid;
    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String skuName;
    /**
     * sku编码
     */
    @NotBlank(message = "sku编码不能为空")
    private String skuCode;
    /**
     * 商品数量
     */
    @NotNull(message = "商品数量不能为空")
    private Long num;
    /**
     * 退款金额
     */
    @NotNull(message = "退款金额不能为空")
    private BigDecimal refundAmount;
}
