package com.charge.api.web.vo.pos;

import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentMethodEnum;
import lombok.Data;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/5/29
 */
@Data
public class PayMethodChannel {
    private PaymentMethodEnum paymentMethod;
    private PaymentChannelEnum paymentChannel;



    public  PayMethodChannel(PaymentMethodEnum paymentMethod, PaymentChannelEnum paymentChannel){
        this.paymentMethod = paymentMethod;
        this.paymentChannel=paymentChannel;
    }

    public static PayMethodChannel from(PaymentMethodEnum paymentMethod, PaymentChannelEnum paymentChannel){
        return new PayMethodChannel(paymentMethod, paymentChannel);
    }

    public static PayMethodChannel fromV1PaymentMethod(String paymentMethodV1){
        if (PayRelatedConstants.PAYMENT_METHOD_POS_CODE.equals(paymentMethodV1)) {
            return from(PaymentMethodEnum.CARD,PaymentChannelEnum.BANK);
        }else if (PayRelatedConstants.PAYMENT_METHOD_ALIPAY_CODE.equals(paymentMethodV1)) {
            return from(PaymentMethodEnum.ALIPAY,PaymentChannelEnum.ALI_PAY);
        } else if (PayRelatedConstants.PAYMENT_METHOD_WEIXIN_CODE.equals(paymentMethodV1)) {
            return from(PaymentMethodEnum.WECHAT,PaymentChannelEnum.WECHAT_PAY);
        } else if (PayRelatedConstants.PAYMENT_METHOD_TRANSFER_CODE.equals(paymentMethodV1)||PayRelatedConstants.PAYMENT_METHOD_CHEQUE_CODE.equals(paymentMethodV1)) {
            return from(PaymentMethodEnum.TRANSFER_OFFLINE,PaymentChannelEnum.TRANSFER_OFFLINE);
        }else {
            return from(PaymentMethodEnum.CARD,PaymentChannelEnum.BANK);
        }
    }

    public static PayMethodChannel fromV1PaymentMethodBySearchPayResult(String paymentMethodV1){
        if (PayRelatedConstants.PAYMENT_METHOD_POS_CODE_CALL_BACK.equals(paymentMethodV1)) {
            return from(PaymentMethodEnum.CARD,PaymentChannelEnum.BANK);
        }else if (PayRelatedConstants.PAYMENT_METHOD_ALIPAY_CODE_CALL_BACK.equals(paymentMethodV1)) {
            return from(PaymentMethodEnum.ALIPAY,PaymentChannelEnum.ALI_PAY);
        } else if (PayRelatedConstants.PAYMENT_METHOD_WEIXIN_CODE_CALL_BACK.equals(paymentMethodV1)) {
            return from(PaymentMethodEnum.WECHAT,PaymentChannelEnum.WECHAT_PAY);
        }else {
            return from(PaymentMethodEnum.CARD,PaymentChannelEnum.BANK);
        }
    }
}


