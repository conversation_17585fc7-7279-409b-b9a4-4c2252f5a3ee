package com.charge.api.web.service.order;

import com.charge.bill.enums.BusinessTypeEnum;

import java.util.List;

/**
 * 账单业务处理
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
public interface BillBusinessHandler {
    /**
     * 处理的类型
     */
    BusinessTypeEnum type();

    /**
     * 校验
     */
    void validate(BillBusiness billBusiness);

    /**
     * 处理业务
     */
    void handle(CreateOrderContext context, AssetBillBusiness assetBillBusiness,List<BillBusiness>billBusinesses);

}


