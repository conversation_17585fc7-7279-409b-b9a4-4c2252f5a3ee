package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/15
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
@Builder
public class LostPay {

    @JsonProperty("mercid")
    private String mercid;
    @JsonProperty("buildingName")
    private String buildingName;
    @JsonProperty("itemName")
    private String itemName;
    @JsonProperty("orderNo")
    private String orderNo;
    @JsonProperty("unitName")
    private String unitName;
    @JsonProperty("createTime")
    private String createTime;
    @JsonProperty("price")
    private BigDecimal price;
    @JsonProperty("paymentMethod")
    private Integer paymentMethod;
    @JsonProperty("payId")
    private String payId;
    @JsonProperty("type")
    private String type;
    @JsonProperty("tranSeqNo")
    private String tranSeqNo;
    @JsonProperty("subjectName")
    private String subjectName;
}


