package com.charge.api.web.controller.order;

import com.charge.api.web.service.order.OrderService;
import com.charge.api.web.vo.order.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.starter.web.annotation.Idempotent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 订单命令相关接口
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping(value = "order")
@Validated
public class OrderCommandController {

    private final OrderService orderService;
    /**
     * 添加订单
     * @param orderAddCmd 添加订单命令
     * @return 添加结果
     */
    @PostMapping(value = "/add")
    @Idempotent
    public ChargeResponse<Void> addOrder(@RequestBody @Valid OrderAddCmd orderAddCmd) throws ChargeBusinessException {
        orderService.addOrder(orderAddCmd);
        return new ChargeResponse<>();
    }

    /**
     * 添加订单支付进度单
     * @param orderAddCmd 添加订单支付进度单命令
     * @return 添加结果
     */
    @PostMapping(value = "installment/add")
    @Idempotent
    public ChargeResponse<Void> addPayInstallment(@RequestBody @Valid OrderInstallmentAddCmd orderAddCmd) {
        return new ChargeResponse<>();
    }

    /**
     * 调整新增子订单
     * @param adjustAddCmd 调整新增子订单命令
     * @return 调整新增子订单结果
     */
    @PostMapping(value = "subOrder/adjust-add")
    @Idempotent
    public ChargeResponse<Void> subOrderAdjustAdd(@RequestBody @Valid SubOrderAdjustAddCmd adjustAddCmd) throws ChargeBusinessException {
        orderService.subOrderAdjustAdd(adjustAddCmd);
        return new ChargeResponse<>();
    }

    /**
     * 调整取消子订单
     * @param adjustCancelCmd 取消命令
     * @return 取消结果
     */
    @PostMapping(value = "subOrder/adjust-cancel")
    @Idempotent
    public ChargeResponse<Void> subOrderAdjustCancel(@RequestBody @Valid SubOrderAdjustCancelCmd adjustCancelCmd) throws ChargeBusinessException {
        orderService.subOrderAdjustCancel(adjustCancelCmd);
        return new ChargeResponse<>();
    }

    /**
     * 调整已支付的子订单到其他子订单单
     * @param adjustToCmd 调整到目标命令
     * @return 调整结果
     */
    @PostMapping(value = "subOrder/adjust-to")
    @Idempotent
    public ChargeResponse<Void> subOrderAdjustTo(@RequestBody @Valid SubOrderAdjustToCmd adjustToCmd) throws ChargeBusinessException {
        orderService.subOrderAdjustTo(adjustToCmd);
        return new ChargeResponse<>();
    }

    /**
     * 订单退款
     * @param refundCmd 订单退款命令
     * @return 收费退款单号
     */
    @PostMapping(value = "refund")
    @Idempotent
    public ChargeResponse<String> orderRefund(@RequestBody @Valid OrderRefundCmd refundCmd) throws ChargeBusinessException {
        return new ChargeResponse<>(orderService.orderRefund(refundCmd));
    }
}