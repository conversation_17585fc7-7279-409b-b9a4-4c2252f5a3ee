package com.charge.api.web.controller.ebusiness;

import com.charge.api.web.service.ebusiness.EBusinessItemService;
import com.charge.api.web.support.CommunityParamSupport;
import com.charge.api.web.vo.ebusiness.EBusinessItemCondition;
import com.charge.api.web.vo.ebusiness.EBusinessItemDetail;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/13 15:55
 */
@Api(value = "电商订单规则收费项相关接口")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class EBusinessItemController {

    private final EBusinessItemService eBusinessItemService;

    private final CommunityParamSupport communitySupport;

    /**
     * 获取电商订单规则收费项
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/ebusiness/order/items")
    public ChargeResponse<List<EBusinessItemDetail>> getOrderItems(@Valid @RequestBody EBusinessItemCondition condition) throws ChargeBusinessException {
        communitySupport.fillCommunityId(condition);
        return new ChargeResponse<>(eBusinessItemService.getItemVOList(condition));
    }

}
