package com.charge.api.web.vo.joylife.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-07 14:44
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CalPreStorePointRequest implements Serializable {

    private static final long serialVersionUID = -6808359542134786260L;

    /**
     * 项目id
     */
    private String communityId;

    /**
     * 资产账单信息
     */
    private List<ReceivableAssetBillInfoDTO> assetBillInfos;




}
