package com.charge.api.web.vo.joylife.response;

import com.charge.api.web.vo.joylife.PreStorePointsVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/6/12 15:13
 */
@Data
public class AssetPreStorePointsVO implements Serializable {

    private static final long serialVersionUID = -6698287261686184503L;

    /**
     * 收费的资产id
     */
    private Long assetId;

    /**
     * 朝昔的资产id（当不传收费的资产id时需要）
     */
    private String assetMsId;

    /**
     * 资产类型：1-房间,2-车位
     */
    private Integer assetType;

    /**
     * 各收费项预存积分结果
     */
    private List<PreStorePointsVO> preStorePointsVOList;
}
