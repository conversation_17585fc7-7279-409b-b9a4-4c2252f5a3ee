package com.charge.api.web.service.impl.ebusiness;

import com.charge.api.web.enums.EBusinessOrderTypeEnum;
import com.charge.api.web.service.ebusiness.EBusinessItemService;
import com.charge.api.web.vo.ebusiness.EBusinessItemCondition;
import com.charge.api.web.vo.ebusiness.EBusinessItemDetail;
import com.charge.api.web.vo.ebusiness.EBusinessItemVO;
import com.charge.common.dto.BaseDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.config.client.item.ChargeItemClient;
import com.charge.config.client.item.CommunityChargeItemClient;
import com.charge.config.dto.item.ChargeItemDTO;
import com.charge.config.dto.item.CommunityChargeItemDTO;
import com.charge.config.dto.item.condition.ChargeItemQueryConditionDTO;
import com.charge.config.dto.item.condition.CommunityChargeItemQueryConditionDTO;
import com.charge.core.util.CollectionUtil;
import com.charge.order.client.OrderRuleClient;
import com.charge.order.dto.rule.OrderRuleItemDTO;
import com.charge.order.dto.rule.OrderRuleItemQueryDTO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/13 16:21
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EBusinessItemServiceImpl implements EBusinessItemService {

    private final OrderRuleClient orderRuleClient;

    private final ChargeItemClient chargeItemClient;

    private final CommunityChargeItemClient communityChargeItemClient;

    @Override
    public List<EBusinessItemDetail> getItemVOList(EBusinessItemCondition condition) throws ChargeBusinessException {
        if(Objects.isNull(condition.getType())){
            return queryEBusinessItems(condition.getCommunityId());
        }
        List<EBusinessItemDetail> resultList = new ArrayList<>();
        EBusinessItemDetail detail = queryEBusinessItemDetail(condition.getCommunityId(), condition.getType());
        if(Objects.nonNull(detail)) {
            resultList.add(detail);
        }
        return resultList;
    }

    private List<EBusinessItemDetail> queryEBusinessItems(Long communityId) throws ChargeBusinessException{
        List<EBusinessItemDetail> resultList = new ArrayList<>();
        EBusinessItemDetail selfSupportDetail = queryEBusinessItemDetail(communityId, 1);
        EBusinessItemDetail homeServiceDetail = queryEBusinessItemDetail(communityId, 2);
        EBusinessItemDetail furnishServiceDetail = queryEBusinessItemDetail(communityId, 3);
        if(Objects.nonNull(selfSupportDetail)) {
            resultList.add(selfSupportDetail);
        }
        if(Objects.nonNull(homeServiceDetail)) {
            resultList.add(homeServiceDetail);
        }
        if(Objects.nonNull(furnishServiceDetail)) {
            resultList.add(furnishServiceDetail);
        }
        return resultList;
    }

    private EBusinessItemDetail queryEBusinessItemDetail(Long communityId, Integer type) throws ChargeBusinessException{
        EBusinessOrderTypeEnum eBusinessOrderTypeEnum = EBusinessOrderTypeEnum.fromOrderSubClass(type);
        Assert.notNull(eBusinessOrderTypeEnum, "不支持此订单业务类型！");
        EBusinessItemDetail detail = new EBusinessItemDetail();
        detail.setOrderSubclass(type);
        detail.setOrderSubclassName(eBusinessOrderTypeEnum.getValue());
        OrderRuleItemQueryDTO queryDTO = new OrderRuleItemQueryDTO();
        queryDTO.setCommunityId(communityId);
        queryDTO.setSecondClassificationIdList(Lists.newArrayList((eBusinessOrderTypeEnum.getCode())));
        ChargeResponse<List<OrderRuleItemDTO>> chargeResponse = orderRuleClient.listOrderRuleItems(queryDTO);
        List<OrderRuleItemDTO> orderRuleItemDTOList = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        if(CollectionUtil.isEmpty(orderRuleItemDTOList)){
            detail.setItems(Lists.newArrayList());
            return detail;
        }
        List<Long> chargeItemIds = orderRuleItemDTOList.stream().map(OrderRuleItemDTO::getItemId).distinct().collect(Collectors.toList());
        //查询实际存在的收费项
        ChargeItemQueryConditionDTO chargeItemQueryConditionDTO = new ChargeItemQueryConditionDTO();
        chargeItemQueryConditionDTO.setItemIds(chargeItemIds);
        ChargeResponse<PagingDTO<ChargeItemDTO>> chargeItemResponse = chargeItemClient.queryChargeItemList(chargeItemQueryConditionDTO);
        PagingDTO<ChargeItemDTO> chargeItemDtoPagingDto = AppInterfaceUtil.getResponseDataThrowException(chargeItemResponse);
        List<ChargeItemDTO> chargeItemDtoS = chargeItemDtoPagingDto.getList();
        Assert.notEmpty(chargeItemDtoS, "费项不存在");
        //查询收费项在该项目的启用状态
        CommunityChargeItemQueryConditionDTO communityConditionDTO = new CommunityChargeItemQueryConditionDTO();
        List<Long> cItemIds = chargeItemDtoS.stream().map(BaseDTO::getId).collect(Collectors.toList());
        communityConditionDTO.setItemIds(cItemIds);
        communityConditionDTO.setCommunityId(communityId);
        ChargeResponse<PagingDTO<CommunityChargeItemDTO>> communityItemResponse = communityChargeItemClient.queryCommunityChargeItemPage(communityConditionDTO);
        PagingDTO<CommunityChargeItemDTO> communityChargeItemPagingDTO = AppInterfaceUtil.getResponseDataThrowException(communityItemResponse);
        List<CommunityChargeItemDTO> communityChargeItemDTOS = communityChargeItemPagingDTO.getList();
        Assert.notEmpty(chargeItemDtoS, "项目费项未启用");
        List<EBusinessItemVO> itemVOS = communityChargeItemDTOS.stream().map(item -> {
            EBusinessItemVO vo = new EBusinessItemVO();
            vo.setItemId(item.getItemId());
            vo.setItemName(item.getItemName());
            vo.setItemCode(item.getItemCode());
            Integer state = item.getStatus()==0L ? 1 : 0;
            vo.setState(state);
            return vo;
        }).collect(Collectors.toList());
        detail.setItems(itemVOS);
        return detail;
    }

}
