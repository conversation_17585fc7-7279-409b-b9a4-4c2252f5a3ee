package com.charge.api.web.service.joylife;

import com.charge.api.web.vo.DeliveryGroupAssetCondition;
import com.charge.api.web.vo.DeliveryGroupCommunityCondition;
import com.charge.api.web.vo.DeliveryGroupResponse;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.joylife.dto.DeliveryPayDataRequest;

/**
 * Author: yjw
 * Date: 2023/3/9 11:29
 */
public interface PrestoreGroupService {

    ChargeResponse getOrderDetail(String communityMsId, String houseMsId, String propertyType) throws ChargeBusinessException;

    ChargeResponse createPay(DeliveryPayDataRequest payRequest) throws ChargeBusinessException;

    ChargeResponse checkDeliveryInfo(DeliveryPayDataRequest payRequest, Long communityId, Long houseId) throws ChargeBusinessException;

    ChargeResponse<DeliveryGroupResponse> batchQueryGroupByCommunity(DeliveryGroupCommunityCondition deliveryGroupCommunityCondition) throws ChargeBusinessException;

    ChargeResponse<DeliveryGroupResponse> batchQueryGroupByAsset(DeliveryGroupAssetCondition deliveryGroupAssetCondition) throws ChargeBusinessException;

}
