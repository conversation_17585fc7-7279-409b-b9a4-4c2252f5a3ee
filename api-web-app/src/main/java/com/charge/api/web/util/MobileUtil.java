package com.charge.api.web.util;

import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: link
 * Date: 2019/1/15
 * Time: 下午4:09
 */
public class MobileUtil {

    /**
     * 手机号屏蔽中间
     *
     * @param mobile
     * @return
     */
    public static String getMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return "";
        }
        char[] chars = mobile.toCharArray();
        boolean[] b = new boolean[chars.length];
        int count = 0;
        for (int i = 0; i < chars.length; i++) {
            //赋值
            if (isNumeric(String.valueOf(chars[i]))) {
                b[i] = true;
            } else {
                b[i] = false;
            }
            if (count > 3 && count <= 7) {
                chars[i] = '*';
            }
            //判断
            if (b[i]) {
                count++;
            } else {
                count = 0;
            }

        }
        String newString = new String(chars);
//        System.out.println("mobile:"+newString);
        return newString;
    }

    public static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]*");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }
}
