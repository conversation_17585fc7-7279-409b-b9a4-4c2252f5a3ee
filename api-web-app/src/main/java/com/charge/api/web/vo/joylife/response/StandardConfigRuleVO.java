package com.charge.api.web.vo.joylife.response;

import com.charge.config.enums.GeneralTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/05 17:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StandardConfigRuleVO {
    /**
     * 收费项id
     */
    private Long itemId;

    /**
     * 计费标准id
     */
    private Long standardConfigId;

    /**
     * 收费项名称
     */
    private String itemName;

    /**
     * 计费模型，normal:常规类-普通、fixed:常规类-固定、cycleFixed:常规类-周期指定、cyclePeriod:常规类-周期滚动
     */
    private String chargeModel;

    /**
     * 计费子模型，1:普通 2:固定
     * @see GeneralTypeEnum
     */
    private Integer  amountType;

    /**
     * 单价/收费金额
     */
    private String priceStr;

    /**
     * 面积类型
     */
    private String areaTypeStr;

    /**
     * 面积
     */
    private String areaStr;

    /**
     * 指定月份
     */
    private String appointMonthStr;

    /**
     * 计算公式
     */
    private String calFormula;
}


