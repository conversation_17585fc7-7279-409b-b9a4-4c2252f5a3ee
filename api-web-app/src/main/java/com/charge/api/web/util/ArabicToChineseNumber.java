package com.charge.api.web.util;

/**
 * NUmUtil
 * <p>
 * Description:
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/14
 */
public class ArabicToChineseNumber {

    private static final String[] CHINESE_NUMBERS = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    private static final String[] UNITS = {"", "十", "百", "千", "万", "十万", "百万", "千万", "亿"};
    public static String convert(int num) {
        if (num < 0) {
            return "暂不支持负数转换";
        }


        StringBuilder result = new StringBuilder();
        int unitIndex = 0;
        while (num > 0) {
            int digit = num % 10;
            if (digit != 0) {
                result.insert(0, UNITS[unitIndex]);
                result.insert(0, CHINESE_NUMBERS[digit]);
            } else if (result.length() > 0 && result.charAt(0) != '零') {
                result.insert(0, CHINESE_NUMBERS[digit]);
            }
            num /= 10;
            unitIndex++;
        }

        return result.toString();
    }
}