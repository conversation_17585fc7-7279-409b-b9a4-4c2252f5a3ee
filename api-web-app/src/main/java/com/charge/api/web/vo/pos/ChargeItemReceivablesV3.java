package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public  class ChargeItemReceivablesV3 {
    @JsonProperty("arrearsAmount")
    private BigDecimal arrearsAmount;
    @JsonProperty("amountList")
    private List<ReceivableV3> amountList;
    @JsonProperty("penaltyAmount")
    private BigDecimal penaltyAmount;
    @JsonProperty("goodsName")
    private String goodsName;
}


