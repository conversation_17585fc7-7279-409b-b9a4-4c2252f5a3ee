package com.charge.api.web.util;

import com.charge.common.enums.common.AliBucketEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.OssUtil;
import com.charge.core.enums.LogCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @version 1.0
 
 * @date 2023/2/10 11:20
 */
@Slf4j
public class OssFileUtil {
    private OssFileUtil() {
        throw new IllegalStateException("OssFileUtil class");
    }

    /**
     * description:
     * author: wuChao
     * date: 2023/2/20
     * param [file=上传文件, fileName=文件名, internal=（开发、本地环境=false,测试环境、uat、生产=true）]
     * return java.lang.String
     **/
    public static String uploadOss(File file, String fileName,boolean internal) {

        String fileKey = fileName.concat(com.charge.common.util.DateUtils.get14StrCurrentTime());

        try {

            //上传oss，本地测试可以修改false
            OssUtil.putOssFile(AliBucketEnum.COMMON, file, fileKey, internal);

            //获取url地址
            return OssUtil.getOssFileUrl(AliBucketEnum.COMMON, fileKey);

        } catch ( ChargeBusinessException e) {
            log.error("{}|上传阿里云oss异常|fileName={}", LogCategoryEnum.BUSSINESS, fileName, e);
        } finally {
            FileUtils.deleteQuietly(file);
        }

        return StringUtils.EMPTY;
    }



    /**
     * 上传阿里云oss
     *
     * @param inputStream
     * @param fileKey
     * @return
     */
    public static String uploadOssByStream(AliBucketEnum aliBucket,InputStream inputStream, String fileKey,boolean internal) {
        try {
            OssUtil.putOssFile(null, aliBucket, inputStream, fileKey, internal);
            return OssUtil.getOssFileUrl( aliBucket, fileKey);
        } catch (ChargeBusinessException e) {
            log.error("{}|上传阿里云oss异常|fileKey={}", LogCategoryEnum.BUSSINESS, fileKey, e);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }

        return StringUtils.EMPTY;
    }

}
