package com.charge.api.web.controller.joylife.bill.request;

import com.charge.api.web.vo.joylife.request.ZhaoXiCommunityReq;
import com.charge.common.serializer.EmojiSupportDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class CreateOrderRequest extends ZhaoXiCommunityReq implements Serializable {

    private static final long serialVersionUID = 3685384362574233574L;

    /**
     * 资产ID
     */
    @NotNull(message = "资产ID参数缺失,请重试")
    private String assetId;

    /**
     * 资产类型
     */
    @NotNull(message = "资产类型参数缺失,请重试")
    private Integer assetType;

    /**
     * 实收金额:支付总金额
     */
    @NotNull(message = "支付总金额参数丢失,请重试")
    @DecimalMin(value = "0.00",message = "金额格式不正确")
    private String totalPrice;

    /**
     * 收费员名称
     */
    private String collectorName;

    /**
     * 收费员ID
     */
    private String collectorId;

    /**
     * 支付人(当前登录用户姓名)
     */
    @NotNull(message = "支付人参数缺失,请重试")
    private String userName;

    /**
     * 客户ID（当前登录用户唯一标识，记录用户缴费历史)
     */
    @NotBlank(message = "客户ID参数缺失,请重试")
    private String userId;

    /**
     * 电话号码
     */
    @NotBlank(message = "电话号码参数缺失,请重试")
    private String phone;

    /**
     * 备注
     */
    @JsonDeserialize(using = EmojiSupportDeserializer.class)
    private String memo;

    /**
     * 支付方式（支付宝：3,微信支付：4）
     */
    private String paymentMethod;

    @NotBlank(message = "支付来源参数缺失,请重试")
    String paymentSource;

    /**
     * 是否是app支付，使用其他渠道拉起支付宝app（非支付宝小程序）的需要传该值为true
     */
    private Boolean payNative;


    /**
     * 终端IP
     */
    @NotBlank(message = "终端IP参数缺失,请重试")
    String mchCreateIp;

    /**
     * AppID
     */
    @NotBlank(message = "AppID参数缺失,请重试")
    String subAppid;

    /**
     * 微信用户小程序关注商家的openid(小程序缴费必传)
     */
    String subOpenid;

    /**
     * 买家支付宝账号
     */
    private String buyerLogonId;

    /**
     * 朝昔用户msId
     */
    private String customId;

    /**
     * 押金列表
     */
    private List<PayOrAdjustItemVO> depositItems;

    /**
     * 临时订单列表
     */
    private List<PayOrAdjustItemVO> orderItems;

    /**
     * 支付成功后跳转地址
     */
    private String returnUrl;

}

