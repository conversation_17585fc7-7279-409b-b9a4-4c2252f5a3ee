package com.charge.api.web.vo.ebusiness;

import lombok.Data;

/**
 * @Description 电商类订单规则收费项明细
 * @Author: yjw
 * @Date: 2024/9/13 16:15
 */
@Data
public class EBusinessItemVO {

    /**
     * 收费项ID
     */
    private Long itemId;

    /**
     * 收费项名称
     */
    private String itemName;


    /**
     * 收费项编码
     */
    private String itemCode;

    /**
     * 0-未启用，1-启用
     */
    private Integer state;

}
