package com.charge.api.web.service.bill.joy.query;

import com.charge.api.web.controller.joylife.bill.response.ItemsVO;
import com.charge.api.web.support.CommunitySupport;
import com.charge.bill.client.flow.QueryBillInfoClient;
import com.charge.bill.dto.domain.response.query.BillItemResponse;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.util.TraceContextUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class BillQueryService {

    private final QueryBillInfoClient queryBillInfoClient;
    private final CommunitySupport communitySupport;
    private final static int queryMaxSize = 20;

    public ChargeResponse<List<ItemsVO>> queryItems(String communityMsId, List<Long> incomeBillIds) throws ChargeBusinessException {
        if (incomeBillIds.size() > queryMaxSize) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "超过实收单最大数量20！");
        }
        TraceContextUtil.setCommunityId(communitySupport.getCommunityIdByMsId(communityMsId));
        List<BillItemResponse> billItemResponses = AppInterfaceUtil.getResponseDataThrowException(queryBillInfoClient.queryBillItems(incomeBillIds, TraceContextUtil.getCommunityId()));
        log.info("queryItems size = {}", billItemResponses.size());
        List<ItemsVO> itemsVOS = billItemResponses.stream().map(billItemResponse -> {
            ItemsVO itemsVO = new ItemsVO();
            itemsVO.setIncomeBillId(billItemResponse.getIncomeBillId());
            itemsVO.setOrderItems(billItemResponse.getOrderItems().stream().map(itemDTO -> VOConvert.INSTANCE.convert(itemDTO)).collect(Collectors.toList()));
            itemsVO.setDepositItems(billItemResponse.getDepositItems().stream().map(itemDTO -> VOConvert.INSTANCE.convert(itemDTO)).collect(Collectors.toList()));
            return itemsVO;
        }).collect(Collectors.toList());
        return new ChargeResponse<>(itemsVOS);
    }
}
