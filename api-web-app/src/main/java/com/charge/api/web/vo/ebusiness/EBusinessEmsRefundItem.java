package com.charge.api.web.vo.ebusiness;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/11/8 15:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EBusinessEmsRefundItem {

    /**
     * 商品明细项id
     */
    @NotBlank(message = "商品明细项id不能为空")
    private String oid;

    /**
     * 分期进度id
     */
    private String installmentId;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String skuName;
    /**
     * sku编码
     */
    @NotBlank(message = "sku编码不能为空")
    private String skuCode;

    /**
     * skuId
     */
    @NotBlank(message = "skuId不能为空")
    private String skuId;

    /**
     * 商品数量
     */
    @NotNull(message = "商品数量不能为空")
    private Long num;
    /**
     * 退款金额
     */
    @NotNull(message = "退款金额不能为空")
    private BigDecimal refundAmount;

}
