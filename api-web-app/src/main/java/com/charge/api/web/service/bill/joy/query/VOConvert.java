package com.charge.api.web.service.bill.joy.query;

import com.charge.api.web.controller.joylife.bill.request.PayOrAdjustItemVO;
import com.charge.bill.dto.domain.response.query.ItemDTO;
import com.charge.common.util.DateMapper;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(uses = DateMapper.class,builder = @Builder(disableBuilder = true))
public interface VOConvert {

    VOConvert INSTANCE = Mappers.getMapper(VOConvert.class);

    PayOrAdjustItemVO convert(ItemDTO itemDTO);
}
