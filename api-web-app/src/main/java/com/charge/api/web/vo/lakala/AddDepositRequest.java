package com.charge.api.web.vo.lakala;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 添加押金请求
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@Data
@Builder
public class AddDepositRequest {
    private String deviceInfo;
    private String mercid;

    private String collectorId;
    private String collectorName;
    private String incomeDetailUuid;

    private BigDecimal money;
    private String itemName;
    private Long itemId;
    private String paymentMethod;
    private String payMember;
    private String memo;

    /**
     * 房间缴纳带有房间id参数
     */
    private Long houseId;

    /**
     * 小区缴纳有小区id参数
     */
    private Long communityUuid;

}


