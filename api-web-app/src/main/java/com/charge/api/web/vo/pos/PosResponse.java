package com.charge.api.web.vo.pos;

import lombok.Data;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/5/29
 */
@Data
public class PosResponse {

    private String return_msg;

    private String return_code;

    private PosResponse(String return_msg) {
        this.return_msg = return_msg;
    }

    public static PosResponse fail(String return_msg){
        PosResponse posResponse=new PosResponse(return_msg);
        posResponse.return_code = "FAIL";
        return posResponse;
    }

    public static PosResponse success(){
        PosResponse posResponse=new PosResponse("OK");
        posResponse.return_code = "SUCCESS";
        return posResponse;
    }
}


