package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/26 16:01
 */
@Data
public class EBusinessFinishOrderCondition {

    /**
     * 项目编码
     */
    @NotBlank(message = "项目编码不能为空")
    private String communityId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 完结时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "完结时间不能为空")
    private String statementTime;

    /**
     * 状态（1-完结，2-作废）
     */
    private Integer status;
}
