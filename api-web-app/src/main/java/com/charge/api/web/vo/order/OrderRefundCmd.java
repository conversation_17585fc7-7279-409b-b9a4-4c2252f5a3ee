package com.charge.api.web.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单退款命令
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderRefundCmd extends BaseOrderCmdReq {


    /**
     * 退款金额
     */
    @NotNull(message = "退款金额")
    private BigDecimal refundAmount ;

    /**
     * 退款号
     */
    @NotBlank(message = "退款号不能为空")
    private String refundNo;


    /**
     * 支付状态：0-待支付，1-已支付
     */
    @NotNull(message = "支付状态不能为空")
    private Integer payStatus;

    /**
     * 退款银行流水号（推送成功的退款需要传）
     */
    private String bankTradeNo;

    /**
     * 退款发起时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "退款发起时间")
    private Date refundTime;

    /**
     * 支付方式(微信：WECHAT 支付宝：ALIPAY 线下支付：OFFLINE)
     */
    @NotBlank(message = "支付方式不能为空")
    private String payType;

    /**
     * 收款银行账号-走报账退款需要传
     */
    @Pattern(regexp = "^[0-9]{9,25}$", message = "收款银行账号为9-25位数字")
    private String acceptBankAccountNo;

    /**
     * 收款账号名-走报账退款需要传
     */
    @Length(max = 25,message = "收款账号名最大长度25")
    private String acceptAccountName;

    /**
     *
     * 收款开户行-走报账退款需要传
     */
    @Length(max = 25,message = "收款开户行最大长度25")
    private String acceptAccountOpeningBank;

    /**
     * 收款账户城市-走报账退款需要传
     */
    @Length(max = 25,message = "收款账户城市最大长度25")
    private String acceptAccountCity;


    /**
     * 退款子单列表
     */
    @NotEmpty(message = "子单列表不能为空")
    private List<SubOrderAmount> subOrders;


}