package com.charge.api.web.service.pos.impl;

import com.charge.api.web.adapter.HouseClientAdapter;
import com.charge.api.web.convert.PosDepositConverter;
import com.charge.api.web.service.pos.DepositService;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.DepositDetailVO;
import com.charge.api.web.vo.pos.DepositVO;
import com.charge.bill.client.DepositPosClient;
import com.charge.bill.dto.predeposit.pos.PosDepositDetailDTO;
import com.charge.bill.dto.predeposit.pos.PosDepositDetailReqDTO;
import com.charge.bill.dto.predeposit.pos.PosDepositInfoDTO;
import com.charge.bill.dto.predeposit.pos.PosDepositReqDTO;
import com.charge.bill.enums.RefundBillStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;


/**
 * pos 押金相关接口
 * <AUTHOR>
 * @date 2023/05/15 17:52
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DepositServiceImpl implements DepositService {
    private final DepositPosClient depositPosClient;

    private final HouseClientAdapter houseClientAdapter;

    private static final String PAY_STATUS_REFUND ="4";


    @Override
    public ChargePageResponse<List<DepositVO>> getDeposits(String communityUuid, Long houseUuid, String depositStatus, Integer pageNum, Integer pageSize) throws ChargeBusinessException {
        Long assetId = (houseUuid != null ? houseUuid : houseClientAdapter.getCommunityVirtualDto(Long.parseLong(communityUuid)).getId());
        PosDepositReqDTO reqDTO = PosDepositReqDTO.builder().communityId(Long.parseLong(communityUuid))
                .assetId(assetId)
                .depositStatus(depositStatus)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .build();
        ChargeResponse<PagingDTO<PosDepositInfoDTO>> response = depositPosClient.getDepositsByPage(reqDTO);
        PagingDTO<PosDepositInfoDTO> pagingDTO = AppInterfaceUtil.getResponseDataThrowException(response);

        ChargePageResponse<List<DepositVO>> chargePageResponse = new ChargePageResponse<>(Collections.emptyList(), pagingDTO.getPageSize(), (pagingDTO.getTotalCount() + pagingDTO.getPageSize() - 1) / pagingDTO.getPageSize(), pagingDTO.getPageNum(), pagingDTO.getTotalCount());
        if (CollectionUtils.isEmpty(pagingDTO.getList())) {
            return chargePageResponse;
        }

        List<DepositVO> depositVOS = PosDepositConverter.INSTANCE.toDepositVOList(pagingDTO.getList());
        updatePayStatusByBalance(depositVOS);
        chargePageResponse.setContent(depositVOS);
        return chargePageResponse;
    }

    private void updatePayStatusByBalance(List<DepositVO> deposits){
        deposits.forEach(depositVO -> {
            if(BigDecimal.ZERO.compareTo(depositVO.getBalance())==0){
                depositVO.setPayStatus(PAY_STATUS_REFUND);
            }
        });
    }

    @Override
    public ChargePageResponse<DepositDetailVO> getDepositDetail(String depositId, String communityUuid, String houseId, String pageNum, String pageSize) throws ChargeBusinessException {
        PosDepositDetailReqDTO reqDTO = PosDepositDetailReqDTO.builder().communityId(Long.parseLong(communityUuid))
                .depositId(depositId)
                .assetId(houseId != null ? Long.parseLong(houseId) : null)
                .pageNum(Integer.parseInt(pageNum))
                .pageSize(Integer.parseInt(pageSize))
                .build();
        ChargeResponse<PagingDTO<PosDepositDetailDTO>> response = depositPosClient.getDepositDetail(reqDTO);
        PagingDTO<PosDepositDetailDTO> pagingDTO = AppInterfaceUtil.getResponseDataThrowException(response);

        ChargePageResponse<DepositDetailVO> chargePageResponse = new ChargePageResponse<>(null, pagingDTO.getPageSize(), (pagingDTO.getTotalCount() + pagingDTO.getPageSize() - 1) / pagingDTO.getPageSize(), pagingDTO.getPageNum(), pagingDTO.getTotalCount());

        DepositDetailVO detailVO = PosDepositConverter.INSTANCE.toDepositDetailVO(pagingDTO.getList().get(0));
        detailVO.setDetail(detailVO.getDetail() == null ? Collections.emptyList() : detailVO.getDetail());
        chargePageResponse.setContent(detailVO);
        return chargePageResponse;
    }

    @Override
    public ChargePageResponse<List<DepositVO>> getDepositSearch(String communityUuid, String houseId, String content, String depositStatus, Integer pageNum, Integer pageSize) throws ChargeBusinessException {
        Long assetId = (houseId != null ? Long.parseLong(houseId) : houseClientAdapter.getCommunityVirtualDto(Long.parseLong(communityUuid)).getId());
        PosDepositReqDTO reqDTO = PosDepositReqDTO.builder().communityId(Long.parseLong(communityUuid))
                .searchContent(content)
                .assetId(assetId)
                .depositStatus(depositStatus)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .build();
        ChargeResponse<PagingDTO<PosDepositInfoDTO>> response = depositPosClient.getDepositsByPage(reqDTO);
        PagingDTO<PosDepositInfoDTO> pagingDTO = AppInterfaceUtil.getResponseDataThrowException(response);

        ChargePageResponse<List<DepositVO>> chargePageResponse = new ChargePageResponse<>(Collections.emptyList(), pagingDTO.getPageSize(), (pagingDTO.getTotalCount() + pagingDTO.getPageSize() - 1) / pagingDTO.getPageSize(), pagingDTO.getPageNum(), pagingDTO.getTotalCount());
        if (CollectionUtils.isEmpty(pagingDTO.getList())) {
            return chargePageResponse;
        }

        List<DepositVO> depositVOS = PosDepositConverter.INSTANCE.toDepositVOList(pagingDTO.getList());
        chargePageResponse.setContent(depositVOS);

        return chargePageResponse;
    }

    public static String convertRefundStatus(Integer newStatus) {
        if (newStatus.equals(RefundBillStatusEnum.REFUND_SUCCESS.getCode())) {
            return "1";
        } else if (newStatus.equals(RefundBillStatusEnum.REFUND_FAIL.getCode())
                || newStatus.equals(RefundBillStatusEnum.ECSB_REJECT.getCode())) {
            return "2";
        } else {
            return "0";
        }
    }
}
