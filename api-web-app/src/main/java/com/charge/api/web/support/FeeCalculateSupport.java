package com.charge.api.web.support;

import com.charge.common.dto.BaseDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.config.client.assets.AssetsChargeConfigClient;
import com.charge.config.client.standard.StandardConfigClient;
import com.charge.config.dto.assets.AssetChargeConfigDetailDTO;
import com.charge.config.dto.assets.condition.AssetChargeConfigQueryConditionDTO;
import com.charge.config.dto.standard.StandardConfigDTO;
import com.charge.config.dto.standard.condition.StandardConfigBatchCondition;
import com.charge.config.enums.AssetChargeConfigStatusEnum;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.feecalculte.client.CalculateFeeClient;
import com.charge.feecalculte.dto.AssetChargeReqDTO;
import com.charge.feecalculte.dto.ReceivableBillDTO;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 计费工具类
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FeeCalculateSupport {

    private final CalculateFeeClient calculateFeeClient;
    private final AssetsChargeConfigClient assetsChargeConfigClient;
    private final StandardConfigClient standardConfigClient;

    public Map<Long,Map<Long,String>> getAssetBindItemsMap(Long communityId,List<Long> assetIds, List<Long> specialPreStoreItems) throws ChargeBusinessException {
        Map<Long, Map<Long,String>> assetBindItemsMap = Maps.newHashMap();
        ChargeResponse<List<AssetChargeConfigDetailDTO>> listChargeResponse = assetsChargeConfigClient.queryAssetChargeConfigList(AssetChargeConfigQueryConditionDTO.builder()
                .communityId(Lists.newArrayList(communityId)).assetIds(assetIds).status(AssetChargeConfigStatusEnum.EFFECTIVE.getCode()).build());
        List<AssetChargeConfigDetailDTO> assetChargeConfigDetails = AppInterfaceUtil.getResponseDataThrowException(listChargeResponse);
        Optional.ofNullable(assetChargeConfigDetails).orElse(Lists.newArrayList()).stream().filter(e -> specialPreStoreItems.contains(e.getItemId()))
                .collect(Collectors.groupingBy(AssetChargeConfigDetailDTO::getAssetId)).forEach((assetId, configDetails) -> {
            Map<Long,String> itemIds = assetBindItemsMap.computeIfAbsent(assetId, k -> new HashMap<>(16));
            for (AssetChargeConfigDetailDTO assetChargeConfigDetailDTO : configDetails) {
                itemIds.put(assetChargeConfigDetailDTO.getItemId(),assetChargeConfigDetailDTO.getItemName());
            }
        });
        return assetBindItemsMap;
    }

    public  ReceivableBillDTO calculatePreStorePerMonth(Long communityId,Long assetId,Long itemId) throws ChargeBusinessException {
        AssetChargeReqDTO assetDTO = new AssetChargeReqDTO();
        assetDTO.setCommunityId(communityId);
        assetDTO.setAssetIds(Lists.newArrayList(assetId));
        assetDTO.setChargeItemIds(Lists.newArrayList(itemId));
        assetDTO.setCalculateFullMonth(Boolean.TRUE);
        assetDTO.setCalculateForPreStore(Boolean.TRUE);
        ChargeResponse<List<ReceivableBillDTO>> calculate = calculateFeeClient.calculate(assetDTO);
        List<ReceivableBillDTO> receivableBills = AppInterfaceUtil.getResponseDataThrowException(calculate);
        if(CollectionUtils.isEmpty(receivableBills)){
            return null;
        }
        if(receivableBills.size()==1){
            return receivableBills.get(0);
        }
        ReceivableBillDTO receivableBillDTO = receivableBills.get(0);
        Map<Long, ReceivableBillDTO> receivableMap = receivableBills.stream().collect(Collectors.toMap(ReceivableBillDTO::getChargeStandardId, Function.identity(),(a, b)->a.getBelongYears().compareTo(b.getBelongYears())>0?b:a));
        receivableBillDTO.setReceivableAmount(receivableMap.values().stream().map(ReceivableBillDTO::getReceivableAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        return receivableBillDTO;
    }

    /**
     * 每个费项每个资产各返回1比
     */
    public  List<ReceivableBillDTO> batchCalculatePreStorePerMonth(Long communityId,List<Long> assetIds,List<Long> itemIds) throws ChargeBusinessException {
        Assert.isTrue(assetIds.size()<=100,"batchCalculatePreStorePerMonth max assetIds support 100");
        AssetChargeReqDTO assetDTO = new AssetChargeReqDTO();
        assetDTO.setCommunityId(communityId);
        assetDTO.setAssetIds(assetIds);
        assetDTO.setChargeItemIds(itemIds);
        assetDTO.setCalculateFullMonth(Boolean.TRUE);
        assetDTO.setCalculateForPreStore(Boolean.TRUE);
        ChargeResponse<List<ReceivableBillDTO>> calculate = calculateFeeClient.calculate(assetDTO);
        List<ReceivableBillDTO> receivableBills = AppInterfaceUtil.getResponseDataThrowException(calculate);
        if(CollectionUtils.isEmpty(receivableBills)){
            return Lists.newArrayList();
        }
        return receivableBills.stream().collect(Collectors.groupingBy(a->a.getAssetId()+":"+a.getChargeItemId())).values().stream().map(receivableBillDtos -> {
            ReceivableBillDTO receivableBillDTO = receivableBillDtos.get(0);
            if (receivableBillDtos.size() > 1) {
                BigDecimal total = receivableBillDtos.stream().collect(Collectors.toMap(ReceivableBillDTO::getChargeStandardId,Function.identity(),(a,b)->b))
                        .values().stream().map(ReceivableBillDTO::getReceivableAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
                receivableBillDTO.setReceivableAmount(total);
            }
            return receivableBillDTO;
        }).collect(Collectors.toList());
    }


    /**
     * 查询资产关联的生效的：计费标准
     * @param communityId
     * @param assetIds
     * @param itemIds
     * @return
     */
    public Map<Long,List<StandardConfigDTO>>  getChargeStandardList(Long communityId,List<Long> assetIds,List<Long> itemIds){

        Map<Long,List<StandardConfigDTO>> assetStandardsMap = new HashMap<>();
        try{
            ChargeResponse<List<AssetChargeConfigDetailDTO>> listChargeResponse = assetsChargeConfigClient.queryAssetChargeConfigList(AssetChargeConfigQueryConditionDTO.builder()
                    .communityId(Lists.newArrayList(communityId)).assetIds(assetIds).itemIds(itemIds).status(AssetChargeConfigStatusEnum.EFFECTIVE.getCode()).build());
            List<Long> standardIds = new ArrayList<>();
            List<AssetChargeConfigDetailDTO> configData = AppInterfaceUtil.getResponseData(listChargeResponse);
            if (CollectionUtils.isNotEmpty(configData)) {
                Map<Long, List<AssetChargeConfigDetailDTO>> assetConfigMap = configData.stream().collect(Collectors.groupingBy(c -> c.getAssetId()));
                standardIds = configData.stream().map(c->c.getStandardConfigId()).collect(Collectors.toList());
                StandardConfigBatchCondition batchCondition = new StandardConfigBatchCondition();
                batchCondition.setCommunityIds(Lists.newArrayList(communityId));
                batchCondition.setStandardConfigIds(standardIds);
                List<StandardConfigDTO> standardList = AppInterfaceUtil.getResponseData(standardConfigClient.queryBatchDetailList(batchCondition));
                if (CollectionUtils.isNotEmpty(standardList)) {
                    Map<Long, StandardConfigDTO> standardsMap = standardList.stream().collect(Collectors.toMap(BaseDTO::getId, s -> s));
                    assetConfigMap.forEach((assetId,configList)->{
                        List<StandardConfigDTO> assetStandardList = configList.stream().map(c -> standardsMap.get(c.getStandardConfigId())).filter(Objects::nonNull).collect(Collectors.toList());
                        assetStandardsMap.put(assetId,assetStandardList);
                    });
                }
            }
        } catch (Exception e){
            log.error("{}|查询资产计费标准异常：", LogCategoryEnum.BUSSINESS,e.getMessage());
        }
        return assetStandardsMap;
    }
}


