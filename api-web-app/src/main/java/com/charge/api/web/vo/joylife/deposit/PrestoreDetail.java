package com.charge.api.web.vo.joylife.deposit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PrestoreDetail {
	String info;
	String money;

	public static List<PrestoreDetail> fromDefault() {
		List<PrestoreDetail> info=new ArrayList<>();
		info.add(new PrestoreDetail("","300") );
		info.add(new PrestoreDetail("","500") );
		info.add(new PrestoreDetail("","1000") );
		return info;
	}

	public static List<PrestoreDetail> from(BigDecimal moneyMonth) {
		if(moneyMonth==null){
			moneyMonth=BigDecimal.ZERO;
		}
		List<PrestoreDetail> info=new ArrayList<>();
		info.add(new PrestoreDetail("3个月",moneyMonth.multiply(new BigDecimal(3)).setScale(2, RoundingMode.HALF_UP).toString()) );
		info.add(new PrestoreDetail("6个月",moneyMonth.multiply(new BigDecimal(6)).setScale(2, RoundingMode.HALF_UP).toString()) );
		info.add(new PrestoreDetail("12个月",moneyMonth.multiply(new BigDecimal(12)).setScale(2, RoundingMode.HALF_UP).toString())  );
		return info;
	}
}
