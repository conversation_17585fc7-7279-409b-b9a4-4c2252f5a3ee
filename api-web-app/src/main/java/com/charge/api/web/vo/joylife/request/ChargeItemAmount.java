package com.charge.api.web.vo.joylife.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 费项金额
 *
 * <AUTHOR>
 * @date 2023/11/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChargeItemAmount {
    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0.01",message = "金额必须大于0.01")
    private BigDecimal amount;
    /**
     * 费项id
     */
    @NotNull(message = "费项不能为空")
    private Long chargeItemId;
    /**
     * 费项名称
     */
    private String chargeItemName;
}


