package com.charge.api.web.service.impl.ebusiness;

import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.convert.EBusinessOrderConverter;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.enums.EBusinessOrderTypeEnum;
import com.charge.api.web.service.ebusiness.EBusinessOrderService;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.util.EmojiUtils;
import com.charge.api.web.vo.ebusiness.*;
import com.charge.bill.client.*;
import com.charge.bill.dto.RefundBillConditionDTO;
import com.charge.bill.dto.RefundBillDTO;
import com.charge.bill.dto.certificate.EBusinessOrderStageAcceptDTO;
import com.charge.bill.dto.certificate.EbusinessOrderFinishDTO;
import com.charge.bill.dto.income.*;
import com.charge.bill.dto.refund.EBusinessOrderRefundDTO;
import com.charge.bill.dto.refund.EBusinessRefundItem;
import com.charge.bill.enums.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.enums.redis.RedisKeyLockEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.AssertUtils;
import com.charge.common.util.DateUtils;
import com.charge.common.util.RedisKeyLockUtil;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.config.client.item.CommunityChargeItemClient;
import com.charge.config.dto.item.CommunityChargeItemDTO;
import com.charge.config.dto.item.condition.CommunityChargeItemQueryConditionDTO;
import com.charge.config.dto.tax.TaxDetailDTO;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.StringUtil;
import com.charge.finance.dto.BillOperation;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.order.client.*;
import com.charge.order.dto.EBusinessOrderCreateCmd;
import com.charge.order.dto.OrderDTO;
import com.charge.order.dto.OrderWriteOffConditionDTO;
import com.charge.order.dto.OrderWriteOffDTO;
import com.charge.order.dto.ebuiness.*;
import com.charge.order.dto.rule.CommunityBaseOrderRuleDTO;
import com.charge.order.dto.rule.CommunityOrderRuleQueryDTO;
import com.charge.order.enums.EBusinessOrderStatusEnum;
import com.charge.order.enums.OrderRuleCategoryEnum;
import com.charge.order.enums.OrderSourceEnum;
import com.charge.pay.client.BankAccountClient;
import com.charge.pay.dto.bank.CommunityBankAccountDTO;
import com.charge.pay.dto.bank.request.BankActualAccountCommunityDTO;
import com.charge.pay.dto.bank.request.BankActualAccountQueryDTO;
import com.charge.pay.dto.enums.RewardAccountTypeEnum;
import com.charge.pay.dto.reward.RewardDetailRequestDTO;
import com.charge.starter.jedis.JedisManager;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/18 14:24
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class EBusinessOrderServiceImpl implements EBusinessOrderService {

    private final AssetClient assetClient;

    private final CommunityClient communityClient;

    private final OrderRuleClient orderRuleClient;

    private final JedisManager jedisManager;

    private final AssetSupport assetSupport;

    private final BillNumGeneratorClient billNumGeneratorClient;

    private final IncomeBillClient incomeBillClient;

    private final AssetTransactionClient assetTransactionClient;

    private final AssetBillInfoClient assetBillInfoClient;

    private final OrderBillDetailClient orderBillDetailClient;

    private final TransactionRelationClient transactionRelationClient;

    private final OrderClient orderClient;

    private final ChargeCertificateClient chargeCertificateClient;


    private final OrderWriteOffClient orderWriteOffClient;

    private final CommunityChargeItemClient communityChargeItemClient;

    private final RefundBillClient refundBillClient;

    private final EBusinessMasterOrderClient eBusinessMasterOrderClient;

    private final EBusinessBaseOrderClient eBusinessBaseOrderClient;

    private final EBusinessOrderClient eBusinessOrderClient;

    private final OrderInstallmentClient orderInstallmentClient;

    private final BankAccountClient bankAccountClient;

    private final static String E_BUSINESS_BANK_TRANSFER = "BANK_TRANSFER";

    private final static String E_BUSINESS_POS = "POS";

    private final static Integer CANCELED = 2;
    private final static Integer FINISHED = 1;

    @Value("${ebusinessNew:false}")
    private Boolean isEBusinessVersion2;

    private void baseCheck(EBusinessPaymentOrderCondition condition) throws ChargeBusinessException {
        for (EBusinessOrderItem orderItem : condition.getOrderItems()) {
            EBusinessOrderTypeEnum eBusinessOrderTypeEnum = EBusinessOrderTypeEnum.fromOrderSubClass(orderItem.getOrderSubclass());
            if (Objects.isNull(eBusinessOrderTypeEnum)) {
                throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "不支持此订单业务类型！");
            }
        }
    }

    @Override
    public void paymentOrders(EBusinessPaymentOrderCondition condition) throws ChargeBusinessException {
        this.baseCheck(condition);
        checkOrderExist(condition);
        Long communityId = Long.parseLong(condition.getCommunityId());
        CommunityDTO communityDTO =
                AppInterfaceUtil.getResponseDataThrowException(communityClient.oneByCondition(CommunityCondition.builder().id(communityId).build()));
        if (Objects.isNull(communityDTO)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "订单号：" + condition.getOrderNo() + "," +
                    "该条数据入库失败（查询项目信息失败），communityId:" + communityId);
        }

        AssetDTO assetDTO =
                AppInterfaceUtil.getResponseDataThrowException(assetClient.getCommunityVirtualHouse(communityId));
        if (Objects.isNull(assetDTO)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "订单号：" + condition.getOrderNo() + "," +
                    "该条数据入库失败（查询项目虚拟资产为空）");
        }

        Long assetId = assetDTO.getId();

        //项目下电商订单规则校验
        checkCommunityOrderRule(condition.getOrderItems(), communityId);
        //创建电商订单
        createEBusinessOrder(condition, communityDTO, assetId);
    }

    @Override
    public void refundOrders(EBusinessRefundOrderCondition condition) throws ChargeBusinessException {
        checkRefundOrderExist(condition);
        // 并发控制
        String lockCacheKey = RedisKeyLockEnum.EBUSINESS_PLATFORM_REFUND_ID.key(condition.getOrderNo());
        try {
            RedisKeyLockUtil.kickOffConcurrent(jedisManager, lockCacheKey, DateUtils.DataTimeSec.TEN_SECOND);
        }  catch (ChargeRuntimeException e) {
            log.info("{}|【电商平台推送退款订单】正在处理您的请求，请稍后再试！", LogCategoryEnum.BUSSINESS, e);
            throw new ChargeBusinessException(ErrorInfoEnum.E1000.getCode(), "正在处理您的请求，请稍后再试！");
        }
        try {
            EBusinessOrderRefundDTO refundDTO = BeanCopierWrapper.copy(condition, EBusinessOrderRefundDTO.class);
            refundDTO.setRefundMethod(condition.getPayType());
            refundDTO.setRefundSuccessTime(condition.getRefundTime());
            List<EBusinessRefundItem> refundItemList = BeanCopierWrapper.copy(condition.getRefundItems(), EBusinessRefundItem.class);
            refundDTO.setRefundItemList(refundItemList);
            AppInterfaceUtil.getResponseDataThrowException(orderBillDetailClient.eBusinessOrderRefund(refundDTO));
            if(isEBusinessVersion2){
                //新版本需要更新主单和子单的退款金额
                EBusinessWholeOrderRefundDTO wholeOrderRefundDTO = BeanCopierWrapper.copy(refundDTO, EBusinessWholeOrderRefundDTO.class);
                List<EBusinessWholeRefundItem> wholeRefundItemList = BeanCopierWrapper.copy(refundDTO.getRefundItemList(), EBusinessWholeRefundItem.class);
                wholeOrderRefundDTO.setRefundItemList(wholeRefundItemList);
                AppInterfaceUtil.getResponseDataThrowException(eBusinessOrderClient.updateRefundInfo(wholeOrderRefundDTO));
            }
        }  finally {
            jedisManager.del(lockCacheKey);
        }
    }

    @Override
    public void writeOffOrders(EBusinessWriteOffOrderConditon condition) throws ChargeBusinessException {
        checkWriteOffOrderExist(condition);
        OrderWriteOffDTO orderWriteOffDTO = BeanCopierWrapper.copy(condition, OrderWriteOffDTO.class);
        Long communityId = Long.parseLong(condition.getCommunityId());
        orderWriteOffDTO.setCommunityId(communityId);
        OrderDTO orderDTO = getOrderIdByEBusinessOid(communityId, condition.getOid());
        orderWriteOffDTO.setOrderId(orderDTO.getId());
        orderWriteOffDTO.setOrderNum(orderDTO.getOrderNum());
        orderWriteOffDTO.setGoodsName(condition.getSkuName());
        orderWriteOffDTO.setVerificationTime(DateUtils.parse(condition.getVerificationTime(), DateUtils.FORMAT_0));
        AppInterfaceUtil.getResponseDataThrowException(orderWriteOffClient.create(orderWriteOffDTO));
    }

    @Override
    public void finishOrders(EBusinessFinishOrderCondition condition) throws ChargeBusinessException {
        checkFinishParam(condition);
        //全额支付电商订单旧版本
        if(!isEBusinessVersion2) {
            Long communityId = Long.parseLong(condition.getCommunityId());
            List<OrderDTO> orderDTOS = AppInterfaceUtil.getResponseDataThrowException(orderClient.getOrderByExtendOrderNoList(Lists.newArrayList(condition.getOrderNo()), communityId));
            if(CollectionUtil.isEmpty(orderDTOS)){
                throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "订单不存在");
            }
            OrderDTO orderDTO = orderDTOS.get(0);
            if(orderDTO.getFinishStatus() == 1){
                return;
            }

            if(Objects.isNull(orderDTO.getInstallmentId())) {
                orderDTOS.forEach(item -> item.setFinishTime(DateUtils.parseToTimeStamp(condition.getStatementTime(), DateUtils.FORMAT_0)));
                EbusinessOrderFinishDTO ebusinessOrderFinishDTO = new EbusinessOrderFinishDTO();
                ebusinessOrderFinishDTO.setOrderDTOCertificateList(EBusinessOrderConverter.INSTANCE.convertCertificateOrderDTO(orderDTOS));
                ebusinessOrderFinishDTO.setBillOperation(BillOperation.CREATE);
                ebusinessOrderFinishDTO.setCommunityId(communityId);
                chargeCertificateClient.generateEBusinessFinishOrderCertificate(ebusinessOrderFinishDTO);
                AppInterfaceUtil.getResponseDataThrowException(orderClient.finishByOrderNum(OrderDTO.builder().communityId(Long.parseLong(condition.getCommunityId()))
                        .extendOrderNo(condition.getOrderNo()).finishStatus(1)
                        .finishTime(DateUtils.parseToTimeStamp(condition.getStatementTime(), DateUtils.FORMAT_0)).build()));
            } else {
                finishFurnishOrdersForV1(condition);
            }
        } else {
            finishFurnishOrders(condition);
        }
    }

    private void finishFurnishOrders(EBusinessFinishOrderCondition condition) throws ChargeBusinessException {
        Long communityId = Long.parseLong(condition.getCommunityId());
        List<EBusinessMasterOrderDTO> eBusinessMasterOrderDTOS = AppInterfaceUtil.getResponseDataThrowException(eBusinessMasterOrderClient.list(EBusinessMasterOrderConditionDTO.builder()
                .communityId(communityId).extendOrderNo(condition.getOrderNo()).build()));
        if(CollectionUtil.isEmpty(eBusinessMasterOrderDTOS)){
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "订单不存在");
        } else {
            EBusinessMasterOrderDTO eBusinessMasterOrderDTO = eBusinessMasterOrderDTOS.get(0);
            if(EBusinessOrderStatusEnum.COMPLETED.getCode().equals(eBusinessMasterOrderDTO.getStatus())){
                return;
            }
        }
        EBusinessMasterOrderDTO eBusinessMasterOrderDTO = EBusinessMasterOrderDTO.builder().communityId(Long.parseLong(condition.getCommunityId()))
                .extendOrderNo(condition.getOrderNo()).status(EBusinessOrderStatusEnum.COMPLETED.getCode())
                .finishTime(DateUtils.parseToTimeStamp(condition.getStatementTime(), DateUtils.FORMAT_0)).build();
        if(Objects.nonNull(condition.getStatus())){
            eBusinessMasterOrderDTO.setStatus(convertFinishStatus(condition.getStatus()));
        }
        AppInterfaceUtil.getResponseDataThrowException(eBusinessMasterOrderClient.finishByOrderNo(eBusinessMasterOrderDTO));
        EbusinessOrderFinishDTO ebusinessOrderFinishDTO = new EbusinessOrderFinishDTO();
        ebusinessOrderFinishDTO.setOrderDTOCertificateList( EBusinessOrderConverter.INSTANCE.convertEBusinessMasterOrderDTO(eBusinessMasterOrderDTOS));
        ebusinessOrderFinishDTO.setBillOperation(BillOperation.CREATE);
        ebusinessOrderFinishDTO.setCommunityId(communityId);
        chargeCertificateClient.generateEBusinessFinishOrderCertificateV2(ebusinessOrderFinishDTO);
    }

    private void finishFurnishOrdersForV1(EBusinessFinishOrderCondition condition) throws ChargeBusinessException {
        Long communityId = Long.parseLong(condition.getCommunityId());
        List<EBusinessMasterOrderDTO> eBusinessMasterOrderDTOS = AppInterfaceUtil.getResponseDataThrowException(eBusinessMasterOrderClient.list(EBusinessMasterOrderConditionDTO.builder()
                .communityId(communityId).extendOrderNo(condition.getOrderNo()).build()));
        if(CollectionUtil.isEmpty(eBusinessMasterOrderDTOS)){
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "订单不存在");
        } else {
            EBusinessMasterOrderDTO eBusinessMasterOrderDTO = eBusinessMasterOrderDTOS.get(0);
            if(EBusinessOrderStatusEnum.COMPLETED.getCode().equals(eBusinessMasterOrderDTO.getStatus())){
                return;
            }
        }
        EBusinessMasterOrderDTO eBusinessMasterOrderDTO = EBusinessMasterOrderDTO.builder().communityId(Long.parseLong(condition.getCommunityId()))
                .extendOrderNo(condition.getOrderNo()).status(EBusinessOrderStatusEnum.COMPLETED.getCode())
                .finishTime(DateUtils.parseToTimeStamp(condition.getStatementTime(), DateUtils.FORMAT_0)).build();
        if(Objects.nonNull(condition.getStatus())){
            eBusinessMasterOrderDTO.setStatus(convertFinishStatus(condition.getStatus()));
        }
        AppInterfaceUtil.getResponseDataThrowException(eBusinessMasterOrderClient.finishByOrderNo(eBusinessMasterOrderDTO));
        EbusinessOrderFinishDTO ebusinessOrderFinishDTO = new EbusinessOrderFinishDTO();
        ebusinessOrderFinishDTO.setOrderDTOCertificateList(EBusinessOrderConverter.INSTANCE.convertEBusinessMasterOrderDTO(eBusinessMasterOrderDTOS));
        ebusinessOrderFinishDTO.setBillOperation(BillOperation.CREATE);
        ebusinessOrderFinishDTO.setCommunityId(communityId);
        chargeCertificateClient.generateEBusinessFinishOrderCertificateV2(ebusinessOrderFinishDTO);

    }

    @Override
    public EBusinessPayBankInfo paymentFurnishOrders(EBusinessFurnishPaymentOrderCondition condition) throws ChargeBusinessException {
        checkEBusinessOrderFinish(condition);
        checkFurnishOrderParam(condition);
        Long communityId = Long.parseLong(condition.getCommunityId());
        CommunityDTO communityDTO =
                AppInterfaceUtil.getResponseDataThrowException(communityClient.oneByCondition(CommunityCondition.builder().id(communityId).build()));
        if (Objects.isNull(communityDTO)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "订单号：" + condition.getOrderNo() + "," +
                    "该条数据入库失败（查询项目信息失败），communityId:" + communityId);
        }

        AssetDTO assetDTO =
                AppInterfaceUtil.getResponseDataThrowException(assetClient.getCommunityVirtualHouse(communityId));
        if (Objects.isNull(assetDTO)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "订单号：" + condition.getOrderNo() + "," +
                    "该条数据入库失败（查询项目虚拟资产为空）");
        }

        AssetAdapter assetAdapter = AssetSupport.buildAsserAdapter(assetDTO);
        //项目下电商订单规则校验
        checkFurnishCommunityOrderRule(condition.getInstallmentItem().getOrderItems(), communityId);

        //创建电商分期订单
        EBusinessPayBankInfo eBusinessPayBankInfo = createEBusinessFurnishOrder(condition, communityDTO, assetAdapter);
        return eBusinessPayBankInfo;
    }

    @Override
    public void acceptanceOrders(EBusinessAcceptanceOrderConditon condition) throws ChargeBusinessException {
        OrderInstallmentDTO orderInstallmentDTO = OrderInstallmentDTO.builder().extendOrderNo(condition.getOrderNo())
                .progressId(condition.getInstallmentId()).communityId(Long.parseLong(condition.getCommunityId()))
                .build();
        if(FINISHED.equals(condition.getStatus())){
            orderInstallmentDTO.setAcceptanceTime(DateUtils.parseToTimeStamp(condition.getStatementTime(), DateUtils.FORMAT_0));
            orderInstallmentDTO.setAcceptanceStatus(EBusinessOrderStatusEnum.COMPLETED.getCode());
        } else if(CANCELED.equals(condition.getStatus())){
            orderInstallmentDTO.setStatus(0);
        } else {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "状态值不合法");
        }
        AppInterfaceUtil.getResponseDataThrowException(orderInstallmentClient.acceptanceByOrderNum(orderInstallmentDTO));
        //订单完成才生成财务一体化，作废不生成
        if(FINISHED.equals(condition.getStatus())) {
            EBusinessOrderStageAcceptDTO eBusinessOrderStageAcceptDTO = new EBusinessOrderStageAcceptDTO();
            eBusinessOrderStageAcceptDTO.setProgressIds(Arrays.asList(condition.getInstallmentId()));
            eBusinessOrderStageAcceptDTO.setBillOperation(BillOperation.CREATE);
            eBusinessOrderStageAcceptDTO.setCommunityId(Long.parseLong(condition.getCommunityId()));
            chargeCertificateClient.generateEBusinessStageOrderCertificate(eBusinessOrderStageAcceptDTO);
        }
    }

    @Override
    public String emsRefundOrders(EBusinessEmsRefundOrderCondition condition) throws ChargeBusinessException {
        // 并发控制
        String lockCacheKey = RedisKeyLockEnum.EBUSINESS_PLATFORM_REFUND_ID.key(condition.getOrderNo());
        try {
            RedisKeyLockUtil.kickOffConcurrent(jedisManager, lockCacheKey, DateUtils.DataTimeSec.TEN_SECOND);
        }  catch (ChargeRuntimeException e) {
            log.info("{}|【电商平台推送银行卡转账订单退款】正在处理您的请求，请稍后再试！", LogCategoryEnum.BUSSINESS, e);
            throw new ChargeBusinessException(ErrorInfoEnum.E1000.getCode(), "正在处理您的请求，请稍后再试！");
        }
        try {
            // 获取银行卡号
            BankActualAccountCommunityDTO communityBankAccount = this.getCommunityBankAccount(condition.getCommunityId());

            // 调用退款申请
            return AppInterfaceUtil.getResponseDataThrowException(eBusinessOrderClient.ebusinessOrderEmsRefund(this.convert2EBusinessOrderEmsRefundRequestDTO(condition,
                    communityBankAccount)));
        } finally {
            jedisManager.del(lockCacheKey);
        }
    }

    private EBusinessOrderEmsRefundRequestDTO convert2EBusinessOrderEmsRefundRequestDTO(EBusinessEmsRefundOrderCondition condition, BankActualAccountCommunityDTO communityBankAccount) {
        EBusinessOrderEmsRefundRequestDTO requestDTO = EBusinessOrderEmsRefundRequestDTO.builder()
                .communityId(condition.getCommunityId())
                .orderNo(condition.getOrderNo())
                .refundAmount(condition.getRefundAmount())
                .refundTime(condition.getRefundTime())
                .bankAccountNo(condition.getBankAccountNo())
                .acceptAccountName(condition.getAcceptAccountName())
                .acceptAccountOpeningBank(condition.getAcceptAccountOpeningBank())
                .acceptAccountCity(condition.getAcceptAccountCity())
                .refundRemark(condition.getRefundRemark())
                .bankActualAccount(communityBankAccount.getBankActualAccount())
                .refundNo(condition.getRefundNo())
                .operatorName(condition.getOperatorName())
                .build();

        List<EBusinessEmsRefundItemDTO> refundItems = Lists.newArrayListWithCapacity(condition.getRefundItems().size());
        for (EBusinessEmsRefundItem refundItem : condition.getRefundItems()) {
            EBusinessEmsRefundItemDTO refundItemDTO = new EBusinessEmsRefundItemDTO();
            refundItemDTO.setOid(refundItem.getOid());
            refundItemDTO.setInstallmentId(refundItem.getInstallmentId());
            refundItemDTO.setSkuName(refundItem.getSkuName());
            refundItemDTO.setSkuCode(refundItem.getSkuCode());
            refundItemDTO.setSkuId(refundItem.getSkuId());
            refundItemDTO.setNum(refundItem.getNum());
            refundItemDTO.setRefundAmount(refundItem.getRefundAmount());
            refundItems.add(refundItemDTO);
        }
        requestDTO.setRefundItems(refundItems);
        return requestDTO;
    }

    private List<String> convertSecondClassifications(List<Integer> orderSubClassList){
        if(CollectionUtil.isEmpty(orderSubClassList)){
            return Collections.emptyList();
        }
        List<String> secondClassificationIds = new ArrayList<>(orderSubClassList.size());
        orderSubClassList.forEach(e -> secondClassificationIds.add(EBusinessOrderTypeEnum.fromOrderSubClass(e).getCode().toString()));
        return secondClassificationIds;
    }

    private void createEBusinessOrder(EBusinessPaymentOrderCondition condition, CommunityDTO communityDTO,
                                      Long assetId) throws ChargeBusinessException{
        condition.setCustomerName(this.filterEmoji(condition.getCustomerName()));
        // 并发控制
        String lockCacheKey = RedisKeyLockEnum.EBUSINESS_PLATFORM_TRADE_ORDER_NO.key(condition.getOrderNo());
        try {
            RedisKeyLockUtil.kickOffConcurrent(jedisManager, lockCacheKey, DateUtils.DataTimeSec.FIVE_SECOND);
        }  catch (ChargeRuntimeException e) {
            log.info("{}|【电商平台推送收款订单】正在处理您的请求，请稍后再试！", LogCategoryEnum.BUSSINESS, e);
            throw new ChargeBusinessException(ErrorInfoEnum.E1000.getCode(), "正在处理您的请求，请稍后再试！");
        }

        try {
            //基础数据资产
            AssetAdapter assetAdapter = assetSupport.getAssetInfoById(assetId.toString());
            AssertUtils.notNull(assetAdapter,"资产查不到:"+assetId);
            //创建实收单[income_bill]
            IncomeBillDTO income = createIncomeBill(condition, communityDTO);
            //创建保存资产流水单[asset_transaction]
            AssetTransactionDTO transactionDO = createAssetTransaction(condition, assetAdapter, income);
            //创建流水信息表数据[asset_bill_info]
            createAssetBillInfo(income, transactionDO.getId());
            if(!isEBusinessVersion2){
                createTransactionRelationAndOrderBillDetail(condition, transactionDO.getId(), communityDTO.getId(), assetAdapter);
            } else {
                //创建电商主订单和子订单
                Map<String,Long> oid2BaseOrderIdMap = createEBusinessOrderRelate(condition, assetAdapter);
                log.info("{}|新增的子单map：{}", LogCategoryEnum.BUSSINESS, oid2BaseOrderIdMap);
                //创建资产关联表与账单订单明细
                createTransactionRelationAndOrderBillDetailV2(condition, transactionDO.getId(), communityDTO.getId(), assetAdapter, oid2BaseOrderIdMap);
            }
        } catch (Exception e) {
            log.error("{}|电商平台推送收款订单异常|入参={}", LogCategoryEnum.BUSSINESS, condition, e);
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "电商平台推送收款订单发生错误，请重试");
        } finally {
            jedisManager.del(lockCacheKey);
        }

    }

    private String filterEmoji(String textWithEmoji) {
        return EmojiUtils.filterOffUtf8Mb4_2(textWithEmoji);
    }

    /**
     * 创建实收单
     * @param condition
     * @param communityDTO
     * @return
     * @throws ChargeBusinessException
     */
    private IncomeBillDTO createIncomeBill(EBusinessPaymentOrderCondition condition, CommunityDTO communityDTO) throws ChargeBusinessException {
        String billNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.INCOME_BILL.getCode()).getContent();
        IncomeBillDTO income = IncomeBillDTO.builder().orderNum(condition.getPayment().getTradeNo()).billNum(billNum)
                .communityId(communityDTO.getId()).communityName(communityDTO.getName()).incomeMoney(condition.getTotalPrice())
                .goodsName(PayRelatedConstants.GOODSNAME_EBUSINESS_ORDER).paymentMethod(PaymentMethodEnum.WECHAT.getPaymentCode())
                .paymentChannel(PaymentChannelEnum.WECHAT_PAY.getPaymentChannel()).paymentTerminal(PaymentTerminalEnum.MALL.getCode())
                .payStatus(BillPayStatusEnum.SUCCESS.getCode()).paymentTime(DateUtils.parse(condition.getOrderTime(), DateUtils.FORMAT_0))
                .payHouseCount(1).balanceStatus(BalanceStatusEnum.UNRECONCILED.getCode()).payMember(this.filterEmoji(condition.getCustomerName()))
                .build();
        income.setCreateUser("ebusiness");
        income.setCreateTime(DateUtils.getCurrentTimestamp());
        income = AppInterfaceUtil.getResponseDataThrowException(incomeBillClient.create(income));
        log.info("{}|电商平台推送收款订单生成实收单，incomeBillDTO：{}", LogCategoryEnum.BUSSINESS, income);
        return income;
    }

    /**
     * 创建资产交易流水
     * @param condition
     * @param assetAdapter
     * @param income
     * @return
     * @throws ChargeBusinessException
     */
    private AssetTransactionDTO createAssetTransaction(EBusinessPaymentOrderCondition condition, AssetAdapter assetAdapter, IncomeBillDTO income) throws ChargeBusinessException {
        AssetTransactionDTO transactionDO = AssetTransactionDTO.builder()
                .incomeId(income.getId()).money(condition.getTotalPrice()).goodsName(PayRelatedConstants.GOODSNAME_EBUSINESS_ORDER)
                .assetOrderNum(income.getOrderNum()).communityId(income.getCommunityId()).communityName(income.getCommunityName())
                .buildingId(assetAdapter.getBuildingId()).buildingName(assetAdapter.getBuildingName()).unitId(assetAdapter.getUnitId())
                .unitName(assetAdapter.getUnitName()).assetId(assetAdapter.getId()).assetName(assetAdapter.getSubName()).payStatus(BillPayStatusEnum.SUCCESS.getCode())
                .assetCode(assetAdapter.getSubCode()).paymentMethod(income.getPaymentMethod()).paymentChannel(income.getPaymentChannel())
                .assetUseStatus(assetAdapter.getAssetUseStatus()).paymentTerminal(income.getPaymentTerminal())
                .paymentTime(DateUtils.parse(condition.getOrderTime(), DateUtils.FORMAT_0)).build();
        if (CollectionUtil.isNotEmpty(assetAdapter.getListCustomer())) {
            CustomerDTO customerDTO = assetAdapter.getListCustomer().get(0);
            transactionDO.setOwnerId(customerDTO.getId());
            transactionDO.setOwnerName(customerDTO.getCustomerName());
        }
        transactionDO.setCreateUser("ebusiness");
        transactionDO.setCreateTime(DateUtils.getCurrentTimestamp());
        transactionDO = AppInterfaceUtil.getResponseDataThrowException(assetTransactionClient.create(transactionDO));
        log.info("{}|电商平台推送收款订单生成资产流水，transactionDO：{}", LogCategoryEnum.BUSSINESS, transactionDO);
        return transactionDO;
    }

    /**
     * 创建流水信息表数据
     * @param income
     * @param assetTransactionId
     * @throws ChargeBusinessException
     */
    private void createAssetBillInfo(IncomeBillDTO income, Long assetTransactionId) throws ChargeBusinessException {
        AssetBillInfoDTO assetBillInfoDO = AssetBillInfoDTO.builder().assetTransactionId(assetTransactionId)
                .orderNum(income.getOrderNum()).communityId(income.getCommunityId()).payMember(income.getPayMember()).build();
        AppInterfaceUtil.getResponseDataThrowException(assetBillInfoClient.create(assetBillInfoDO));
    }

    /**
     * 创建资产关联表与账单订单明细
     * @param condition
     * @param assetTransactionId
     * @param communityId
     * @param assetAdapter
     * @throws ChargeBusinessException
     */
    private void createTransactionRelationAndOrderBillDetail(EBusinessPaymentOrderCondition condition, Long assetTransactionId,
                                                             Long communityId, AssetAdapter assetAdapter) throws ChargeBusinessException {
        for(EBusinessOrderItem orderItem:condition.getOrderItems()) {
            List<TransactionRelationDTO> relationList = Lists.newArrayList();
            //订单表[base_order]
            Long orderId = createOrder(condition, orderItem, communityId, assetAdapter);
            //增加订单明细信息order_bill_detail
            OrderBillDetailDTO billDetailDTO = OrderBillDetailDTO.builder().assetTransactionId(assetTransactionId)
                    .bizType(BizTypeEnum.ORDER_TYPE.getCode()).itemId(orderItem.getItemId()).itemName(orderItem.getItemName())
                    .payType(OrderPayTypeEnum.ORDER_PAY.getCode()).totalAmount(orderItem.getApportionedAmount()).availableBalance(orderItem.getApportionedAmount())
                    .actualAmount(orderItem.getPayAmount()).orderId(orderId).communityId(communityId).build();
            Long orderBillDetailId = orderBillDetailClient.create(billDetailDTO).getContent();

            //添加关联数据transaction_relation
            TransactionRelationDTO relationDO = TransactionRelationDTO.builder().assetTransactionId(assetTransactionId)
                    .businessId(orderBillDetailId).businessType(BusinessTypeEnum.TEMP_PAY.getCode())
                    .createTime(new Date()).communityId(communityId).build();
            relationList.add(relationDO);

            AppInterfaceUtil.getResponseDataThrowException(transactionRelationClient.batchCreate(relationList));
        }
    }

    /**
     * 创建订单表
     * @param orderItem
     * @param communityId
     * @param assetAdapter
     * @return
     * @throws ChargeBusinessException
     */
    private Long createOrder(EBusinessPaymentOrderCondition condition, EBusinessOrderItem orderItem, Long communityId,
                             AssetAdapter assetAdapter) throws ChargeBusinessException {
        EBusinessOrderCreateCmd orderCreateCmd = new EBusinessOrderCreateCmd();
        orderCreateCmd.setOrderNum(condition.getPayment().getTradeNo());
        orderCreateCmd.setExtendOrderNo(condition.getOrderNo());
        orderCreateCmd.setExtendOrderId(orderItem.getOid());
        orderCreateCmd.setCommunityId(communityId);
        orderCreateCmd.setCustomerName(this.filterEmoji(condition.getCustomerName()));
        orderCreateCmd.setPayStatus(BillPayStatusEnum.SUCCESS.getCode());
        orderCreateCmd.setSubsetId(EBusinessOrderTypeEnum.fromOrderSubClass(orderItem.getOrderSubclass()).getCode().longValue());
        orderCreateCmd.setAssetId(assetAdapter.getId());
        orderCreateCmd.setAssetCode(assetAdapter.getSubCode());
        orderCreateCmd.setPaymentMode(2);
        orderCreateCmd.setGoodsName(orderItem.getSkuName());
        orderCreateCmd.setNum(orderItem.getNum());
        orderCreateCmd.setUnit(orderItem.getUnit());
        orderCreateCmd.setGoodsUnitPrice(orderItem.getOriginUnitPrice());
        orderCreateCmd.setSpecsType(orderItem.getSpecText());
        if(CollectionUtil.isNotEmpty(orderItem.getApportionItems())) {
            String discountType = orderItem.getApportionItems().stream().map(EBusinessApportionItem::getApportionTypeName).collect(Collectors.joining(","));
            orderCreateCmd.setDiscountType(discountType);
        }
        orderCreateCmd.setDiscountAmount(orderItem.getPremiumAmount().add(orderItem.getDiscountAmount()));
        orderCreateCmd.setPayTime(DateUtils.parseToTimeStamp(orderItem.getPayTime(), DateUtils.FORMAT_0));
        if(Objects.isNull(orderItem.getTaxRate()) || StringUtils.isBlank(orderItem.getTaxCateCode())){
            CommunityChargeItemDTO communityChargeItemDTO = getTaxInfoByItemId(communityId, orderItem.getItemId());
            orderCreateCmd.setTaxCateCode(communityChargeItemDTO.getTaxCode());
            TaxDetailDTO taxDetailDTO = getTaxDetailDTO(communityChargeItemDTO.getTaxId(), communityChargeItemDTO.getTaxRates());
            if(Objects.nonNull(taxDetailDTO)) {
                orderCreateCmd.setTaxRate(taxDetailDTO.getTaxPoint());
            }
        }
        orderCreateCmd.setOrderTime(DateUtils.parseToTimeStamp(condition.getOrderTime(), DateUtils.FORMAT_0));
        orderCreateCmd.setItemId(orderItem.getItemId());
        orderCreateCmd.setMemo(orderItem.getItemName());
        orderCreateCmd.setTotalAmount(orderItem.getOriginPrice());
        orderCreateCmd.setTotalPaidAmount(orderItem.getPayAmount());
        log.info("{}|电商平台推送收款订单base_order下单，orderCreateCmd：{}", LogCategoryEnum.BUSSINESS, orderCreateCmd);
        return AppInterfaceUtil.getResponseDataThrowException(orderClient.createEBusinessOrder(orderCreateCmd));
    }

    private void checkOrderExist(EBusinessPaymentOrderCondition condition) throws ChargeBusinessException{
        List<EBusinessMasterOrderDTO> eBusinessMasterOrderDTOS = AppInterfaceUtil.getResponseDataThrowException(eBusinessMasterOrderClient.list(EBusinessMasterOrderConditionDTO.builder()
                .communityId(Long.parseLong(condition.getCommunityId())).extendOrderNo(condition.getOrderNo()).build()));
        if(CollectionUtil.isNotEmpty(eBusinessMasterOrderDTOS)){
            throw new ChargeBusinessException(ErrorInfoEnum.E1010.getCode(), "该订单数据已存在，请不要重复推送");
        }
    }

    private OrderDTO getOrderIdByEBusinessOid(Long communityId, String oid) throws ChargeBusinessException{
        ChargeResponse<List<OrderDTO>> orderDTOResponse = orderClient.getOrderByOidList(Lists.newArrayList(oid), communityId);
        List<OrderDTO> orderDTOS = AppInterfaceUtil.getResponseDataThrowException(orderDTOResponse);
        if(CollectionUtil.isEmpty(orderDTOS) || (orderDTOS.size() != 1)){
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "电商订单数据异常");
        }
        return orderDTOS.get(0);
    }

    private CommunityChargeItemDTO getTaxInfoByItemId(Long communityId, Long chargeItemId) throws ChargeBusinessException{
        CommunityChargeItemQueryConditionDTO conditionDTO = new CommunityChargeItemQueryConditionDTO();
        conditionDTO.setItemId(chargeItemId);
        conditionDTO.setCommunityId(communityId);
        return AppInterfaceUtil.getResponseDataThrowException(communityChargeItemClient.queryChargeItemDetail(conditionDTO));
    }

    private TaxDetailDTO getTaxDetailDTO(Long taxId, List<TaxDetailDTO> taxDetailDTOList){
        for(TaxDetailDTO taxDetailDTO: taxDetailDTOList){
            if (taxDetailDTO.getId().equals(taxId)){
                return taxDetailDTO;
            }
        }
        return null;
    }

    private void checkRefundOrderExist(EBusinessRefundOrderCondition condition) throws ChargeBusinessException{
        RefundBillConditionDTO conditionDTO = new RefundBillConditionDTO();
        conditionDTO.setRefundRemark(condition.getRefundId());
        List<RefundBillDTO> refundBillDTOS = AppInterfaceUtil.getResponseDataThrowException(refundBillClient.listRefundBill(conditionDTO));
        if(CollectionUtil.isNotEmpty(refundBillDTOS)){
            throw new ChargeBusinessException(ErrorInfoEnum.E1010.getCode(), "该订单的退款数据已存在，请不要重复推送");
        }
    }

    private void checkWriteOffOrderExist(EBusinessWriteOffOrderConditon condition) throws ChargeBusinessException{
        OrderWriteOffConditionDTO conditionDTO = new OrderWriteOffConditionDTO();
        conditionDTO.setCommunityId(Long.parseLong(condition.getCommunityId()));
        conditionDTO.setVerificationId(condition.getVerificationId());
        List<OrderWriteOffDTO> orderWriteOffDTOList = AppInterfaceUtil.getResponseDataThrowException(orderWriteOffClient.listBill(conditionDTO));
        if(CollectionUtil.isNotEmpty(orderWriteOffDTOList)){
            throw new ChargeBusinessException(ErrorInfoEnum.E1010.getCode(), "该订单的核销数据已存在，请不要重复推送");
        }
    }

    private void checkEBusinessOrderFinish(EBusinessFurnishPaymentOrderCondition condition) throws ChargeBusinessException{
        List<EBusinessMasterOrderDTO> eBusinessMasterOrderDTOS = AppInterfaceUtil.getResponseDataThrowException(eBusinessMasterOrderClient.list(
                EBusinessMasterOrderConditionDTO.builder().extendOrderNo(condition.getOrderNo()).communityId(Long.parseLong(condition.getCommunityId())).build()));
        if(CollectionUtil.isNotEmpty(eBusinessMasterOrderDTOS)){
            if(eBusinessMasterOrderDTOS.get(0).getStatus().equals(EBusinessOrderStatusEnum.COMPLETED.getCode())) {
                throw new ChargeBusinessException(ErrorInfoEnum.E3050.getCode(), "该订单为已完结状态，收费侧不接收此类数据");
            }
        }

    }

    private void checkCommunityOrderRule(List<EBusinessOrderItem> eBusinessOrderItems, Long communityId) throws ChargeBusinessException{
        //项目下电商订单规则校验
        List<Integer> orderSubClassList =
                eBusinessOrderItems.stream().map(EBusinessOrderItem::getOrderSubclass).collect(Collectors.toList());
        List<String> secondClassificationIds = convertSecondClassifications(orderSubClassList);
        CommunityOrderRuleQueryDTO communityOrderRuleQueryDTO = new CommunityOrderRuleQueryDTO();
        communityOrderRuleQueryDTO.setCommunityId(communityId);
        communityOrderRuleQueryDTO.setSecondClassificationIds(secondClassificationIds);
        ChargeResponse<List<CommunityBaseOrderRuleDTO>> ruleResponse =
                orderRuleClient.listByCommunityIdAndSubsetId(communityOrderRuleQueryDTO);
        if (!ruleResponse.isSuccess() || ruleResponse.getContent().isEmpty()) {
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "该项目未启用订单规则");
        }
        List<CommunityBaseOrderRuleDTO> communityBaseOrderRuleDTOS = ruleResponse.getContent();
        for (CommunityBaseOrderRuleDTO ruleDTO : communityBaseOrderRuleDTOS) {
            if (Objects.equals(ruleDTO.getChargeItemId(), "[]")) {
                throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "该项目未启用[" + ruleDTO.getFirstClassificationName() + "]订单规则");
            }
            // 转换为 List<Long>
            List<Long> itemIdList = Arrays.stream(ruleDTO.getChargeItemId().substring(1, ruleDTO.getChargeItemId().length() - 1).split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            for(EBusinessOrderItem orderItem : eBusinessOrderItems){
                if(!itemIdList.contains(orderItem.getItemId())){
                    throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "商品收费项[" + orderItem.getItemId() + "不在[" + ruleDTO.getFirstClassificationName() + "]订单规则的有效费项列表");
                }
            }
        }
    }

    private void checkFurnishCommunityOrderRule(List<EBusinesssFurnishOrderItem> eBusinessOrderItems, Long communityId) throws ChargeBusinessException{
        //项目下电商订单规则校验
        List<Integer> orderSubClassList =
                eBusinessOrderItems.stream().map(EBusinesssFurnishOrderItem::getOrderSubclass).collect(Collectors.toList());
        List<String> secondClassificationIds = convertSecondClassifications(orderSubClassList);
        CommunityOrderRuleQueryDTO communityOrderRuleQueryDTO = new CommunityOrderRuleQueryDTO();
        communityOrderRuleQueryDTO.setCommunityId(communityId);
        communityOrderRuleQueryDTO.setSecondClassificationIds(secondClassificationIds);
        ChargeResponse<List<CommunityBaseOrderRuleDTO>> ruleResponse =
                orderRuleClient.listByCommunityIdAndSubsetId(communityOrderRuleQueryDTO);
        if (!ruleResponse.isSuccess() || ruleResponse.getContent().isEmpty()) {
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "该项目未启用订单规则");
        }
        List<CommunityBaseOrderRuleDTO> communityBaseOrderRuleDTOS = ruleResponse.getContent();
        for (CommunityBaseOrderRuleDTO ruleDTO : communityBaseOrderRuleDTOS) {
            if (Objects.equals(ruleDTO.getChargeItemId(), "[]")) {
                throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "该项目未启用[" + ruleDTO.getFirstClassificationName() + "]订单规则");
            }
            // 转换为 List<Long>
            List<Long> itemIdList = Arrays.stream(ruleDTO.getChargeItemId().substring(1, ruleDTO.getChargeItemId().length() - 1).split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            for(EBusinesssFurnishOrderItem orderItem : eBusinessOrderItems){
                if(!itemIdList.contains(orderItem.getItemId())){
                    throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "商品收费项[" + orderItem.getItemId() + "不在[" + ruleDTO.getFirstClassificationName() + "]订单规则的有效费项列表");
                }
            }
        }
    }

    private EBusinessPayBankInfo createEBusinessFurnishOrder(EBusinessFurnishPaymentOrderCondition condition, CommunityDTO communityDTO, AssetAdapter assetAdapter) throws ChargeBusinessException{
        // 并发控制
        String lockCacheKey = RedisKeyLockEnum.EBUSINESS_PLATFORM_TRADE_ORDER_NO.key(condition.getOrderNo());
        try {
            RedisKeyLockUtil.kickOffConcurrent(jedisManager, lockCacheKey, DateUtils.DataTimeSec.TEN_SECOND);
        }  catch (ChargeRuntimeException e) {
            log.info("{}|【电商平台推送分期订单】正在处理您的请求，请稍后再试！", LogCategoryEnum.BUSSINESS, e);
            throw new ChargeBusinessException(ErrorInfoEnum.E1000.getCode(), "正在处理您的请求，请稍后再试！");
        }

        EBusinessPayBankInfo eBusinessPayBankInfo = null;
        try {
            if(E_BUSINESS_BANK_TRANSFER.equals(condition.getPayment().getPayType())){
                eBusinessPayBankInfo = getEBusinessPayBankInfo(communityDTO.getId());
            }
            //创建电商主订单&子订单&进度单&进度单关联订单
            Map<String,Long> oid2BaseOrderIdMap = createOrUpdateFurnishOrder(condition, assetAdapter);
            if(PaymentMethodEnum.WECHAT.name().equals(condition.getPayment().getPayType()) || PaymentMethodEnum.ALIPAY.name().equals(condition.getPayment().getPayType())) {
                //创建实收单[income_bill]
                IncomeBillDTO income = createIncomeBillForFurnishOrder(condition, communityDTO);
                //创建保存资产流水单[asset_transaction]
                AssetTransactionDTO transactionDO = createAssetTransactionForFurnishOrder(condition, assetAdapter, income);
                //创建流水信息表数据[asset_bill_info]
                createAssetBillInfo(income, transactionDO.getId());
                //创建资产关联表与账单订单明细
                createTrAndOrderBillDetailForFurnishOrder(condition, transactionDO.getId(), communityDTO.getId(), oid2BaseOrderIdMap);
            }
        } catch (Exception e) {
            log.error("{}|电商平台推送分期订单异常|入参={}", LogCategoryEnum.BUSSINESS, condition, e);
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "电商平台推送分期订单发生错误，请重试");
        } finally {
            jedisManager.del(lockCacheKey);
        }
        return eBusinessPayBankInfo;
    }

    @Override
    public EBusinessPayBankInfo getEBusinessPayBankInfo(Long communityId) throws ChargeBusinessException{
        BankActualAccountCommunityDTO bankActualAccountCommunityDTO = this.getCommunityBankAccount(communityId);
        CommunityBankAccountDTO communityBankVirtualAccountDTO =this.getCommunityVirtualAccount(communityId);
        EBusinessPayBankInfo eBusinessPayBankInfo = new EBusinessPayBankInfo();
        eBusinessPayBankInfo.setBankAccountNo(communityBankVirtualAccountDTO.getBankVirtualAccount());
        eBusinessPayBankInfo.setAcceptAccountName(bankActualAccountCommunityDTO.getBankActualAccountName());
        return eBusinessPayBankInfo;
    }

    private BankActualAccountCommunityDTO getCommunityBankAccount(Long communityId) throws ChargeBusinessException {
        BankActualAccountQueryDTO queryDTO = new BankActualAccountQueryDTO();
        queryDTO.setCommunityId(communityId);
        List<BankActualAccountCommunityDTO> communityBankAccountDTOS = AppInterfaceUtil.getResponseDataThrowException(bankAccountClient.listCommunityBankAccountInfo(queryDTO));
        if (CollectionUtils.isEmpty(communityBankAccountDTOS)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "该项目不存在可用的银行收款账户!");
        }
        List<BankActualAccountCommunityDTO> validBankAccountDTOS = communityBankAccountDTOS.stream().filter(a -> RewardAccountTypeEnum.PROPERTY.getCode().equals(a.getRewardAccountType()) || a.getRewardAccountType() == null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validBankAccountDTOS)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "该项目不存在可用的银行收款账户!");
        }
        return validBankAccountDTOS.get(0);

    }

    private IncomeBillDTO createIncomeBillForFurnishOrder(EBusinessFurnishPaymentOrderCondition condition, CommunityDTO communityDTO) throws ChargeBusinessException{
        String billNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.INCOME_BILL.getCode()).getContent();
        IncomeBillDTO income = IncomeBillDTO.builder().orderNum(condition.getPayment().getTradeNo()).billNum(billNum)
                .communityId(communityDTO.getId()).communityName(communityDTO.getName()).incomeMoney(condition.getInstallmentItem().getInstallmentPayPrice())
                .goodsName(PayRelatedConstants.GOODSNAME_EBUSINESS_ORDER).paymentMethod(PaymentMethodEnum.WECHAT.getPaymentCode())
                .paymentChannel(PaymentChannelEnum.WECHAT_PAY.getPaymentChannel()).paymentTerminal(PaymentTerminalEnum.MALL.getCode())
                .payStatus(BillPayStatusEnum.SUCCESS.getCode())
                .payHouseCount(1).balanceStatus(BalanceStatusEnum.UNRECONCILED.getCode()).payMember(this.filterEmoji(condition.getCustomerName()))
                .build();
        if(Objects.nonNull(condition.getPayment().getPayTime())){
            income.setPaymentTime(DateUtils.parse(condition.getPayment().getPayTime(), DateUtils.FORMAT_0));
        }
        income.setCreateUser("ebusiness");
        income.setCreateTime(DateUtils.getCurrentTimestamp());
        income = AppInterfaceUtil.getResponseDataThrowException(incomeBillClient.create(income));
        log.info("{}|电商平台推送分期订单生成实收单，incomeBillDTO：{}", LogCategoryEnum.BUSSINESS, income);
        return income;
    }

    private AssetTransactionDTO createAssetTransactionForFurnishOrder(EBusinessFurnishPaymentOrderCondition condition, AssetAdapter assetAdapter,
                                                       IncomeBillDTO income) throws ChargeBusinessException{
        AssetTransactionDTO transactionDO = AssetTransactionDTO.builder()
                .incomeId(income.getId()).money(condition.getInstallmentItem().getInstallmentPayPrice()).goodsName(PayRelatedConstants.GOODSNAME_EBUSINESS_ORDER)
                .assetOrderNum(income.getOrderNum()).communityId(income.getCommunityId()).communityName(income.getCommunityName())
                .buildingId(assetAdapter.getBuildingId()).buildingName(assetAdapter.getBuildingName()).unitId(assetAdapter.getUnitId())
                .unitName(assetAdapter.getUnitName()).assetId(assetAdapter.getId()).assetName(assetAdapter.getSubName()).payStatus(BillPayStatusEnum.SUCCESS.getCode())
                .assetCode(assetAdapter.getSubCode()).paymentMethod(income.getPaymentMethod()).paymentChannel(income.getPaymentChannel())
                .assetUseStatus(assetAdapter.getAssetUseStatus()).paymentTerminal(income.getPaymentTerminal())
                .build();
        if(Objects.nonNull(condition.getPayment().getPayTime())){
            transactionDO.setPaymentTime(DateUtils.parse(condition.getPayment().getPayTime(), DateUtils.FORMAT_0));
        }
        if (CollectionUtil.isNotEmpty(assetAdapter.getListCustomer())) {
            CustomerDTO customerDTO = assetAdapter.getListCustomer().get(0);
            transactionDO.setOwnerId(customerDTO.getId());
            transactionDO.setOwnerName(customerDTO.getCustomerName());
        }
        transactionDO.setCreateUser("ebusiness");
        transactionDO.setCreateTime(DateUtils.getCurrentTimestamp());
        transactionDO = AppInterfaceUtil.getResponseDataThrowException(assetTransactionClient.create(transactionDO));
        log.info("{}|电商平台推送分期订单生成资产流水，transactionDO：{}", LogCategoryEnum.BUSSINESS, transactionDO);
        return transactionDO;
    }

    private void createTrAndOrderBillDetailForFurnishOrder(EBusinessFurnishPaymentOrderCondition condition, Long assetTransactionId,
                                                           Long communityId, Map<String, Long> oid2BaseOrderIdMap) throws ChargeBusinessException{
        for(EBusinesssFurnishOrderItem orderItem:condition.getInstallmentItem().getOrderItems()) {
            List<TransactionRelationDTO> relationList = Lists.newArrayList();
            //订单表[base_order]
            Long orderId = oid2BaseOrderIdMap.get(orderItem.getOid());
            //增加订单明细信息order_bill_detail
            OrderBillDetailDTO billDetailDTO = OrderBillDetailDTO.builder().assetTransactionId(assetTransactionId)
                    .bizType(BizTypeEnum.ORDER_TYPE.getCode()).itemId(orderItem.getItemId()).itemName(orderItem.getItemName())
                    .payType(OrderPayTypeEnum.ORDER_PAY.getCode()).totalAmount(orderItem.getPayAmount()).availableBalance(orderItem.getPayAmount())
                    .actualAmount(orderItem.getPayAmount()).orderId(orderId).communityId(communityId).build();
            Long orderBillDetailId = orderBillDetailClient.create(billDetailDTO).getContent();

            //添加关联数据transaction_relation
            TransactionRelationDTO relationDO = TransactionRelationDTO.builder().assetTransactionId(assetTransactionId)
                    .businessId(orderBillDetailId).businessType(BusinessTypeEnum.TEMP_PAY.getCode())
                    .createTime(new Date()).communityId(communityId).build();
            relationList.add(relationDO);

            AppInterfaceUtil.getResponseDataThrowException(transactionRelationClient.batchCreate(relationList));
        }
    }

    private List<EBusinessOrderCreateCmd> buildEBusinessOrderCreateCmdListForWholePay(EBusinessPaymentOrderCondition condition, AssetAdapter assetAdapter){
        List<EBusinessOrderCreateCmd> orderCreateCmdList = Lists.newArrayList();
        Long communityId = Long.parseLong(condition.getCommunityId());
        for(EBusinessOrderItem orderItem:condition.getOrderItems()) {
            EBusinessOrderCreateCmd orderCreateCmd = new EBusinessOrderCreateCmd();
            orderCreateCmd.setOrderNum(condition.getPayment().getTradeNo());
            orderCreateCmd.setCommunityId(communityId);
            orderCreateCmd.setCustomerName(this.filterEmoji(condition.getCustomerName()));
            orderCreateCmd.setPayStatus(BillPayStatusEnum.SUCCESS.getCode());
            orderCreateCmd.setSubsetId(EBusinessOrderTypeEnum.fromOrderSubClass(orderItem.getOrderSubclass()).getCode().longValue());
            orderCreateCmd.setAssetId(assetAdapter.getId());
            orderCreateCmd.setAssetCode(assetAdapter.getSubCode());
            orderCreateCmd.setNum(orderItem.getNum());
            orderCreateCmd.setUnit(orderItem.getUnit());
            orderCreateCmd.setGoodsUnitPrice(orderItem.getOriginUnitPrice());
            orderCreateCmd.setItemId(orderItem.getItemId());
            orderCreateCmd.setMemo(orderItem.getItemName());
            orderCreateCmd.setTotalAmount(orderItem.getOriginPrice());
            orderCreateCmd.setTotalPaidAmount(orderItem.getPayAmount());
            orderCreateCmd.setExtendOrderNo(condition.getOrderNo());
            orderCreateCmd.setExtendOrderId(orderItem.getOid());
            log.info("{}|电商平台v2推送订单base_order下单，orderCreateCmd：{}", LogCategoryEnum.BUSSINESS, orderCreateCmd);
            orderCreateCmdList.add(orderCreateCmd);
        }
        return orderCreateCmdList;
    }

    private List<EBusinessOrderCreateCmd> buildEBusinessOrderCreateCmdList(EBusinessFurnishPaymentOrderCondition condition, Long communityId,
                                                                 AssetAdapter assetAdapter,List<EBusinessBaseOrderDTO> eBusinessBaseOrderDTOS){
        List<EBusinessOrderCreateCmd> orderCreateCmdList = Lists.newArrayList();
        Map<String, Long> oid2EBusinessOrderIdMap = eBusinessBaseOrderDTOS.stream().collect(Collectors.toMap(EBusinessBaseOrderDTO::getExtendOrderId, EBusinessBaseOrderDTO::getId, (a,b) -> b));
        for(EBusinesssFurnishOrderItem orderItem:condition.getInstallmentItem().getOrderItems()) {
            EBusinessOrderCreateCmd orderCreateCmd = new EBusinessOrderCreateCmd();
            orderCreateCmd.setOrderNum(condition.getPayment().getTradeNo());
            orderCreateCmd.setCommunityId(communityId);
            orderCreateCmd.setCustomerName(this.filterEmoji(condition.getCustomerName()));
            orderCreateCmd.setPayStatus(BillPayStatusEnum.SUCCESS.getCode());
            orderCreateCmd.setSubsetId(EBusinessOrderTypeEnum.fromOrderSubClass(orderItem.getOrderSubclass()).getCode().longValue());
            orderCreateCmd.setAssetId(assetAdapter.getId());
            orderCreateCmd.setAssetCode(assetAdapter.getSubCode());
            orderCreateCmd.setNum(orderItem.getNum());
            orderCreateCmd.setUnit(orderItem.getUnit());
            orderCreateCmd.setGoodsUnitPrice(orderItem.getOriginUnitPrice());
            orderCreateCmd.setItemId(orderItem.getItemId());
            orderCreateCmd.setMemo(orderItem.getItemName());
            orderCreateCmd.setTotalAmount(orderItem.getOriginPrice());
            orderCreateCmd.setTotalPaidAmount(orderItem.getPayAmount());
            orderCreateCmd.setExtendOrderId(orderItem.getOid());
            orderCreateCmd.setExtendOrderNo(condition.getOrderNo());
            if(Objects.nonNull(oid2EBusinessOrderIdMap)) {
                orderCreateCmd.setEbusinessOrderId(oid2EBusinessOrderIdMap.get(orderItem.getOid()));
            }
            log.info("{}|电商平台推送订单base_order下单，orderCreateCmd：{}", LogCategoryEnum.BUSSINESS, orderCreateCmd);
            orderCreateCmdList.add(orderCreateCmd);
        }
        return orderCreateCmdList;
    }

    private Map<String,Long> createOrUpdateFurnishOrder(EBusinessFurnishPaymentOrderCondition condition, AssetAdapter assetAdapter) throws ChargeBusinessException{
        Map<String,Long> oid2BaseOrderIdMap;
        List<EBusinessMasterOrderDTO> eBusinessMasterOrderDTOS = AppInterfaceUtil.getResponseDataThrowException(eBusinessMasterOrderClient.list(EBusinessMasterOrderConditionDTO.builder().extendOrderNo(condition.getOrderNo())
                .communityId(Long.parseLong(condition.getCommunityId())).build()));
        if(CollectionUtil.isNotEmpty(eBusinessMasterOrderDTOS)){
            //更新主订单信息
            log.info("{}|电商分期主订单已存在，开始更新操作", LogCategoryEnum.BUSSINESS);
            EBusinessMasterOrderDTO masterOrderDTO = eBusinessMasterOrderDTOS.get(0);
            List<EBusinessBaseOrderDTO> eBusinessBaseOrderDTOS = AppInterfaceUtil.getResponseDataThrowException(eBusinessBaseOrderClient.list(EBusinessBaseOrderConditionDTO.builder().extendOrderNo(condition.getOrderNo())
                    .communityId(Long.parseLong(condition.getCommunityId())).build()));
            EBusinessOrderDTO eBusinessOrderDTO = buildEBusinessUpdateOrderDTO(masterOrderDTO, condition, assetAdapter, eBusinessBaseOrderDTOS);
            List<EBusinessOrderCreateCmd> eBusinessOrderCreateCmds = AppInterfaceUtil.getResponseDataThrowException(eBusinessOrderClient.update(eBusinessOrderDTO));
            oid2BaseOrderIdMap = eBusinessOrderCreateCmds.stream().collect(Collectors.toMap(EBusinessOrderCreateCmd::getExtendOrderId, EBusinessOrderCreateCmd::getId, (a,b) ->b));
            return oid2BaseOrderIdMap;
        }

        EBusinessOrderDTO eBusinessOrderDTO = buildEBusinessCreateOrderDTO(condition, assetAdapter);
        List<EBusinessOrderCreateCmd> eBusinessOrderCreateCmds = AppInterfaceUtil.getResponseDataThrowException(eBusinessOrderClient.create(eBusinessOrderDTO));
        log.info("{}|电商分期订单创建成功，response:{}", LogCategoryEnum.BUSSINESS, eBusinessOrderCreateCmds);
        oid2BaseOrderIdMap = eBusinessOrderCreateCmds.stream().collect(Collectors.toMap(EBusinessOrderCreateCmd::getExtendOrderId, EBusinessOrderCreateCmd::getId, (a,b) ->b));
        return oid2BaseOrderIdMap;
    }

    private EBusinessMasterOrderDTO buildEBusinessMasterOrder(EBusinessFurnishPaymentOrderCondition condition) {
        Integer payStatus = convertPayStatus(condition.getPayState());
        String secondClassificationId = OrderRuleCategoryEnum.ONE_LEVEL_E_BUSINESS_502.getCode().toString();
        String itemNames = condition.getInstallmentItem().getOrderItems().stream().map(EBusinesssFurnishOrderItem::getItemName).distinct().collect(Collectors.joining("，"));
        String goodsName = condition.getInstallmentItem().getOrderItems().stream().map(EBusinesssFurnishOrderItem::getSkuName).distinct().collect(Collectors.joining("，"));
        EBusinessMasterOrderDTO masterOrderDTO = EBusinessMasterOrderDTO.builder().extendOrderNo(condition.getOrderNo())
                .communityId(Long.parseLong(condition.getCommunityId())).customerName(this.filterEmoji(condition.getCustomerName()))
                .goodsName(goodsName).discountAmount(condition.getDiscountsTotalPrice().add(condition.getPremiumTotalAmount())).paymentMode(condition.getPaymentMode())
                .itemNames(itemNames).secondClassificationId(secondClassificationId).originalAmount(condition.getOriginPrice())
                .totalPaidAmount(condition.getPayment().getPayPrice()).payStatus(payStatus).orderTime(DateUtils.parseToTimeStamp(condition.getOrderTime(), DateUtils.FORMAT_0))
                .build();
        if(E_BUSINESS_POS.equals(condition.getPayment().getPayType()) || E_BUSINESS_BANK_TRANSFER.equals(condition.getPayment().getPayType())){
            masterOrderDTO.setTotalPaidAmount(BigDecimal.ZERO);
        }
        Set<String> discountTypeSet = new HashSet<>();
        condition.getInstallmentItem().getOrderItems().forEach(orderItem -> {
            if(CollectionUtil.isNotEmpty(orderItem.getApportionItems())){
                orderItem.getApportionItems().forEach(e ->{
                    discountTypeSet.add(e.getApportionTypeName());
                });
            }
        });
        if(CollectionUtil.isNotEmpty(discountTypeSet)) {
            String discountType = discountTypeSet.stream().collect(Collectors.joining(","));
            masterOrderDTO.setDiscountType(discountType);
        }
        if(E_BUSINESS_POS.equals(condition.getPayment().getPayType()) || E_BUSINESS_BANK_TRANSFER.equals(condition.getPayment().getPayType())) {
            masterOrderDTO.setStatus(EBusinessOrderStatusEnum.CREATED.getCode());
        } else {
            masterOrderDTO.setStatus(EBusinessOrderStatusEnum.PROGRESSING.getCode());
        }
        masterOrderDTO.setSource(OrderSourceEnum.MALL.name());
        return masterOrderDTO;
    }

    private List<EBusinessBaseOrderDTO> buildEBusinessBaseOrder(EBusinessFurnishPaymentOrderCondition condition, Long assetId) throws ChargeBusinessException {
        Long communityId = Long.parseLong(condition.getCommunityId());
        List<EBusinessBaseOrderDTO> eBusinessBaseOrderDTOS = new ArrayList<>();
        for(EBusinesssFurnishOrderItem orderItem:condition.getInstallmentItem().getOrderItems()) {
            String discountType = "";
            if(CollectionUtil.isNotEmpty(orderItem.getApportionItems())) {
                discountType = orderItem.getApportionItems().stream().map(EBusinessApportionItem::getApportionTypeName).collect(Collectors.joining(","));
            }
            EBusinessBaseOrderDTO baseOrderDTO = EBusinessBaseOrderDTO.builder().extendOrderNo(condition.getOrderNo())
                    .communityId(Long.parseLong(condition.getCommunityId())).paymentMode(condition.getPaymentMode())
                    .assetId(assetId).goodsQuantity(orderItem.getNum()).goodsUnit(orderItem.getUnit())
                    .totalAmount(orderItem.getOriginPrice()).totalPaidAmount(orderItem.getPayAmount())
                    .specsType(orderItem.getSpecText()).discountType(discountType)
                    .itemId(orderItem.getItemId()).itemName(orderItem.getItemName()).goodsName(orderItem.getSkuName())
                    .orderTime(DateUtils.parseToTimeStamp(condition.getOrderTime(), DateUtils.FORMAT_0))
                    .extendOrderId(orderItem.getOid()).cateName(orderItem.getCateName())
                    .payStatus(convertPayStatus(orderItem.getPayState())).goodsUnitPrice(orderItem.getOriginUnitPrice())
                    .build();
            baseOrderDTO.setDiscountAmount(orderItem.getPremiumAmount().add(orderItem.getDiscountAmount()));
            if(Objects.nonNull(orderItem.getPayTime())){
                baseOrderDTO.setPaidAt(DateUtils.parseToTimeStamp(orderItem.getPayTime(), DateUtils.FORMAT_0));
            }
            if(Objects.isNull(orderItem.getTaxRate()) || StringUtils.isBlank(orderItem.getTaxCateCode())){
                CommunityChargeItemDTO communityChargeItemDTO = getTaxInfoByItemId(communityId, orderItem.getItemId());
                baseOrderDTO.setTaxCateCode(communityChargeItemDTO.getTaxCode());
                TaxDetailDTO taxDetailDTO = getTaxDetailDTO(communityChargeItemDTO.getTaxId(), communityChargeItemDTO.getTaxRates());
                if(Objects.nonNull(taxDetailDTO)) {
                    baseOrderDTO.setTaxRate(taxDetailDTO.getTaxPoint());
                }
            }
            if(E_BUSINESS_POS.equals(condition.getPayment().getPayType()) || E_BUSINESS_BANK_TRANSFER.equals(condition.getPayment().getPayType())){
                baseOrderDTO.setTotalPaidAmount(BigDecimal.ZERO);
            }
            //.customerId()
            eBusinessBaseOrderDTOS.add(baseOrderDTO);
        }
        return eBusinessBaseOrderDTOS;
    }

    private Integer convertPayStatus(String payState){
        Integer payStatus = null;
        if("UNPAID".equals(payState)){
            payStatus = 0;
        } else if("PAID".equals(payState)){
            payStatus = 1;
        } else if("PART_PAID".equals(payState)){
            payStatus = 2;
        }
        return payStatus;
    }

    private void checkFurnishOrderParam(EBusinessFurnishPaymentOrderCondition condition) throws ChargeBusinessException{
        String payType = condition.getPayment().getPayType();
        if(!(PaymentMethodEnum.WECHAT.name().equals(payType) || PaymentMethodEnum.ALIPAY.name().equals(payType)
                || E_BUSINESS_BANK_TRANSFER.equals(payType) || E_BUSINESS_POS.equals(payType))){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "不合法的支付方式");
        }

        //校验是否已存在相同的分期
        String progressId = condition.getInstallmentItem().getInstallmentId();
        List<OrderInstallmentDTO> orderInstallmentDTOS = AppInterfaceUtil.getResponseDataThrowException(orderInstallmentClient.list(OrderInstallmentConditionDTO.builder()
                .communityId(Long.parseLong(condition.getCommunityId())).progressId(progressId).build()));
        if(CollectionUtil.isNotEmpty(orderInstallmentDTOS)){
            throw new ChargeBusinessException(ErrorInfoEnum.E1010.getCode(), "已存在相同的分期,请勿重复推送");
        }

        //校验分期应付金额是否与商品应付金额合计值相等
        BigDecimal installmentTotalAmount = condition.getInstallmentItem().getInstallmentTotalPrice();
        BigDecimal goodsTotalAmount = condition.getInstallmentItem().getOrderItems().stream().map(EBusinesssFurnishOrderItem::getPayAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
        if(installmentTotalAmount.compareTo(goodsTotalAmount) != 0){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "分期应付金额与商品应付金额合计值不相等");
        }

    }

    private EBusinessOrderDTO buildEBusinessUpdateOrderDTO(EBusinessMasterOrderDTO masterOrderDTO, EBusinessFurnishPaymentOrderCondition condition,
                                                           AssetAdapter assetAdapter, List<EBusinessBaseOrderDTO> eBusinessBaseOrderDTOS)  throws ChargeBusinessException{
        EBusinessOrderDTO eBusinessOrderDTO = new EBusinessOrderDTO();

        OrderInstallmentDTO orderInstallmentDTO = buildEBusinessInstallmentItem(condition);
        buildOrderInstallmentForQuery(orderInstallmentDTO, masterOrderDTO, eBusinessBaseOrderDTOS);
        eBusinessOrderDTO.setOrderInstallmentDTO(orderInstallmentDTO);

        List<OrderInstallmentRelationDTO> orderInstallmentRelationDTOS = buildEBusinessOrderInstallmentRelationDTOS(condition, eBusinessBaseOrderDTOS);
        eBusinessOrderDTO.setOrderInstallmentRelationDTOS(orderInstallmentRelationDTOS);

        //POS支付或银行转账支付不生成base_order相关订单数据
        if(!(E_BUSINESS_POS.equals(condition.getPayment().getPayType()) || E_BUSINESS_BANK_TRANSFER.equals(condition.getPayment().getPayType()))) {
            EBusinessMasterOrderDTO newMasterOrderDTO = buildUpdateEBusinessMasterOrderDTO(masterOrderDTO, condition);
            eBusinessOrderDTO.setEBusinessMasterOrderDTO(newMasterOrderDTO);

            List<EBusinessBaseOrderDTO> newBaseOrderDTOS = new ArrayList<>();
            List<EBusinessBaseOrderDTO> newInsertBaseOrderDTOS = new ArrayList<>();
            Map<String,EBusinessBaseOrderDTO> oidMap = eBusinessBaseOrderDTOS.stream().collect(Collectors.toMap(EBusinessBaseOrderDTO::getExtendOrderId, e->e, (a,b)->b));
            for(EBusinesssFurnishOrderItem orderItem:condition.getInstallmentItem().getOrderItems()){
                EBusinessBaseOrderDTO baseOrderDTO = oidMap.get(orderItem.getOid());
                if(Objects.nonNull(baseOrderDTO)) {
                    EBusinessBaseOrderDTO newBaseOrderDTO = new EBusinessBaseOrderDTO();
                    BigDecimal totalPaidAmount = baseOrderDTO.getTotalPaidAmount().add(orderItem.getPayAmount());
                    newBaseOrderDTO.setTotalPaidAmount(totalPaidAmount);
                    newBaseOrderDTO.setPayStatus(calculateGoodsPayStatus(orderItem.getApportionedAmount(), totalPaidAmount));
                    newBaseOrderDTO.setId(baseOrderDTO.getId());
                    newBaseOrderDTO.setExtendOrderId(baseOrderDTO.getExtendOrderId());
                    newBaseOrderDTOS.add(newBaseOrderDTO);
                } else {
                    EBusinessBaseOrderDTO newInsertBaseOrderDTO = buildEBusinessInsertBaseOrder(condition, orderItem, assetAdapter.getId());
                    newInsertBaseOrderDTOS.add(newInsertBaseOrderDTO);
                }
            }
            eBusinessOrderDTO.setEBusinessBaseOrderDTOS(newBaseOrderDTOS);
            eBusinessOrderDTO.setEBusinessInsertBaseOrderDTOS(newInsertBaseOrderDTOS);

            List<EBusinessOrderCreateCmd> eBusinessOrderCreateCmdList = buildEBusinessOrderCreateCmdList(condition, Long.parseLong(condition.getCommunityId()), assetAdapter, eBusinessBaseOrderDTOS);
            eBusinessOrderDTO.setEBusinessOrderCreateCmdList(eBusinessOrderCreateCmdList);
        } else {
            //如果存在新的商品，即便是POS或银行转账支付，也需要新增至子订单
            List<EBusinessBaseOrderDTO> newInsertBaseOrderDTOS = new ArrayList<>();
            Map<String,EBusinessBaseOrderDTO> oidMap = eBusinessBaseOrderDTOS.stream().collect(Collectors.toMap(EBusinessBaseOrderDTO::getExtendOrderId, e->e, (a,b)->b));
            for(EBusinesssFurnishOrderItem orderItem:condition.getInstallmentItem().getOrderItems()){
                EBusinessBaseOrderDTO baseOrderDTO = oidMap.get(orderItem.getOid());
                if(Objects.isNull(baseOrderDTO)) {
                    EBusinessBaseOrderDTO newInsertBaseOrderDTO = buildEBusinessInsertBaseOrder(condition, orderItem, assetAdapter.getId());
                    newInsertBaseOrderDTOS.add(newInsertBaseOrderDTO);
                }
            }
            eBusinessOrderDTO.setEBusinessInsertBaseOrderDTOS(newInsertBaseOrderDTOS);
            eBusinessOrderDTO.setEBusinessOrderCreateCmdList(Collections.emptyList());
        }

        return eBusinessOrderDTO;
    }

    private OrderInstallmentDTO buildEBusinessInstallmentItem(EBusinessFurnishPaymentOrderCondition condition){
        EBusinessInstallmentItem installmentItem = condition.getInstallmentItem();
        OrderInstallmentDTO orderInstallmentDTO = OrderInstallmentDTO.builder().extendOrderNo(condition.getOrderNo())
                .communityId(Long.parseLong(condition.getCommunityId())).progressId(installmentItem.getInstallmentId())
                .sort(installmentItem.getInstallmentSort()).amount(installmentItem.getInstallmentTotalPrice())
                .payStatus(convertPayStatus(installmentItem.getPayState())).paymentMode(condition.getPaymentMode())
                .build();

        if(Objects.nonNull(condition.getPayment().getPayTime())){
            orderInstallmentDTO.setPaymentTime(DateUtils.parse(condition.getPayment().getPayTime(), DateUtils.FORMAT_0));
        }
        if(PaymentMethodEnum.WECHAT.name().equals(condition.getPayment().getPayType())){
            orderInstallmentDTO.setPaymentMethod(PaymentMethodEnum.WECHAT.getPaymentCode());
            orderInstallmentDTO.setPaymentChannel(PaymentChannelEnum.WECHAT_PAY.getPaymentChannel());
            orderInstallmentDTO.setPaymentTerminal(PaymentTerminalEnum.MALL.getCode());
            orderInstallmentDTO.setPaymentAmount(installmentItem.getInstallmentPayPrice());
        } else if(PaymentMethodEnum.ALIPAY.name().equals(condition.getPayment().getPayType())) {
            orderInstallmentDTO.setPaymentMethod(PaymentMethodEnum.ALIPAY.getPaymentCode());
            orderInstallmentDTO.setPaymentChannel(PaymentChannelEnum.ALI_PAY.getPaymentChannel());
            orderInstallmentDTO.setPaymentTerminal(PaymentTerminalEnum.MALL.getCode());
            orderInstallmentDTO.setPaymentAmount(installmentItem.getInstallmentPayPrice());
        } else if(E_BUSINESS_POS.equals(condition.getPayment().getPayType())){
            orderInstallmentDTO.setPaymentTerminal(PaymentTerminalEnum.POS.getCode());
            orderInstallmentDTO.setPaymentAmount(BigDecimal.ZERO);
        } else if(E_BUSINESS_BANK_TRANSFER.equals(condition.getPayment().getPayType())){
            orderInstallmentDTO.setPaymentMethod(PaymentMethodEnum.TRANSFER_OFFLINE.getPaymentCode());
            orderInstallmentDTO.setPaymentChannel(PaymentChannelEnum.TRANSFER_OFFLINE.getPaymentChannel());
            orderInstallmentDTO.setPaymentTerminal(PaymentTerminalEnum.MALL.getCode());
            orderInstallmentDTO.setPaymentAmount(BigDecimal.ZERO);
        }
        return orderInstallmentDTO;
    }

    private void buildOrderInstallmentForQuery(OrderInstallmentDTO orderInstallment, EBusinessMasterOrderDTO eBusinessMasterOrder, List<EBusinessBaseOrderDTO> eBusinessBaseOrders) {
        orderInstallment.setCustomerName(eBusinessMasterOrder.getCustomerName());
        orderInstallment.setMasterOrderTime(eBusinessMasterOrder.getOrderTime());
        List<Long> itemIdList = eBusinessBaseOrders.stream().map(EBusinessBaseOrderDTO::getItemId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemIdList)) {
            orderInstallment.setItemIds("," + StringUtil.join(itemIdList, ",") + ",");
        }
    }

    private List<OrderInstallmentRelationDTO> buildEBusinessOrderInstallmentRelationDTOS(EBusinessFurnishPaymentOrderCondition condition,
                                                                                         List<EBusinessBaseOrderDTO> eBusinessBaseOrderDTOS){
        List<OrderInstallmentRelationDTO> installmentRelationDTOS = new ArrayList<>();
        EBusinessInstallmentItem installmentItem = condition.getInstallmentItem();
        Map<String, Long> oid2EBusinessOrderIdMap = eBusinessBaseOrderDTOS.stream().collect(Collectors.toMap(EBusinessBaseOrderDTO::getExtendOrderId, EBusinessBaseOrderDTO::getId, (a,b) -> b));
        for(EBusinesssFurnishOrderItem orderItem:condition.getInstallmentItem().getOrderItems()) {
            OrderInstallmentRelationDTO installmentRelationDTO = OrderInstallmentRelationDTO.builder()
                    .communityId(Long.parseLong(condition.getCommunityId()))
                    .progressId(installmentItem.getInstallmentId())
                    .amount(orderItem.getPayAmount()).paymentAmount(orderItem.getPayAmount())
                    .payStatus(convertPayStatus(installmentItem.getPayState()))
                    .extendOrderId(orderItem.getOid())
                    .build();
            if(E_BUSINESS_POS.equals(condition.getPayment().getPayType()) || E_BUSINESS_BANK_TRANSFER.equals(condition.getPayment().getPayType())){
                installmentRelationDTO.setPaymentAmount(BigDecimal.ZERO);
            }
            if(Objects.nonNull(oid2EBusinessOrderIdMap)) {
                installmentRelationDTO.setEbusinessOrderId(oid2EBusinessOrderIdMap.get(orderItem.getOid()));
            }
            installmentRelationDTOS.add(installmentRelationDTO);
        }
        return installmentRelationDTOS;
    }

    private EBusinessOrderDTO buildEBusinessCreateOrderDTO(EBusinessFurnishPaymentOrderCondition condition, AssetAdapter assetAdapter) throws ChargeBusinessException{
        EBusinessOrderDTO eBusinessOrderDTO = new EBusinessOrderDTO();

        EBusinessMasterOrderDTO masterOrderDTO = buildEBusinessMasterOrder(condition);
        eBusinessOrderDTO.setEBusinessMasterOrderDTO(masterOrderDTO);

        List<EBusinessBaseOrderDTO> baseOrderDTOS = buildEBusinessBaseOrder(condition, assetAdapter.getId());
        eBusinessOrderDTO.setEBusinessBaseOrderDTOS(baseOrderDTOS);

        OrderInstallmentDTO orderInstallmentDTO = buildEBusinessInstallmentItem(condition);
        buildOrderInstallmentForQuery(orderInstallmentDTO, masterOrderDTO, baseOrderDTOS);
        eBusinessOrderDTO.setOrderInstallmentDTO(orderInstallmentDTO);

        List<OrderInstallmentRelationDTO> orderInstallmentRelationDTOS = buildEBusinessOrderInstallmentRelationDTOS(condition,Collections.emptyList());
        eBusinessOrderDTO.setOrderInstallmentRelationDTOS(orderInstallmentRelationDTOS);

        //POS支付或银行转账支付不生成base_order相关订单数据
        if(!(E_BUSINESS_POS.equals(condition.getPayment().getPayType()) || E_BUSINESS_BANK_TRANSFER.equals(condition.getPayment().getPayType()))) {
            List<EBusinessOrderCreateCmd> eBusinessOrderCreateCmdList = buildEBusinessOrderCreateCmdList(condition, Long.parseLong(condition.getCommunityId()), assetAdapter, Collections.emptyList());
            eBusinessOrderDTO.setEBusinessOrderCreateCmdList(eBusinessOrderCreateCmdList);
        } else {
            eBusinessOrderDTO.setEBusinessOrderCreateCmdList(Collections.emptyList());
        }

        return eBusinessOrderDTO;
    }

    private Map<String,Long> createEBusinessOrderRelate(EBusinessPaymentOrderCondition condition, AssetAdapter assetAdapter) throws ChargeBusinessException{
        EBusinessOrderDTO eBusinessOrderDTO = new EBusinessOrderDTO();
        EBusinessMasterOrderDTO masterOrderDTO = buildEBusinessMasterOrderForWholePay(condition);
        eBusinessOrderDTO.setEBusinessMasterOrderDTO(masterOrderDTO);

        List<EBusinessBaseOrderDTO> baseOrderDTOS = buildEBusinessBaseOrderForWholePay(condition, assetAdapter.getId());
        eBusinessOrderDTO.setEBusinessBaseOrderDTOS(baseOrderDTOS);

        List<EBusinessOrderCreateCmd> baseOrderCreateCmdList = buildEBusinessOrderCreateCmdListForWholePay(condition, assetAdapter);
        eBusinessOrderDTO.setEBusinessOrderCreateCmdList(baseOrderCreateCmdList);

        List<EBusinessOrderCreateCmd> eBusinessOrderCreateCmds = AppInterfaceUtil.getResponseDataThrowException(eBusinessOrderClient.createWholePayOrder(eBusinessOrderDTO));
        log.info("{}|新增订单相关信息成功，orderDTOS：{}", LogCategoryEnum.BUSSINESS, eBusinessOrderCreateCmds);
        Map<String,Long> oid2BaseOrderIdMap = eBusinessOrderCreateCmds.stream().collect(Collectors.toMap(EBusinessOrderCreateCmd::getExtendOrderId, EBusinessOrderCreateCmd::getId, (a,b) ->b));
        return oid2BaseOrderIdMap;
    }

    private EBusinessMasterOrderDTO buildEBusinessMasterOrderForWholePay(EBusinessPaymentOrderCondition condition){
        Integer payStatus = convertPayStatus(condition.getPayState());
        String secondClassificationId = EBusinessOrderTypeEnum.fromOrderSubClass(condition.getOrderItems().get(0).getOrderSubclass()).getCode().toString();
        String itemNames = condition.getOrderItems().stream().map(EBusinessOrderItem::getItemName).distinct().collect(Collectors.joining("，"));
        String goodsName = condition.getOrderItems().stream().map(EBusinessOrderItem::getSkuName).distinct().collect(Collectors.joining("，"));
        EBusinessMasterOrderDTO masterOrderDTO = EBusinessMasterOrderDTO.builder().extendOrderNo(condition.getOrderNo())
                .communityId(Long.parseLong(condition.getCommunityId())).customerName(this.filterEmoji(condition.getCustomerName()))
                .goodsName(goodsName).paymentMode(2)
                .itemNames(itemNames).secondClassificationId(secondClassificationId).originalAmount(condition.getOriginPrice())
                .totalPaidAmount(condition.getTotalPrice()).payStatus(payStatus).status(EBusinessOrderStatusEnum.PROGRESSING.getCode())
                .orderTime(DateUtils.parseToTimeStamp(condition.getOrderTime(), DateUtils.FORMAT_0))
                .build();
        Set<String> discountTypeSet = new HashSet<>();
        condition.getOrderItems().forEach(orderItem -> {
            if(CollectionUtil.isNotEmpty(orderItem.getApportionItems())){
                orderItem.getApportionItems().forEach(e ->{
                    discountTypeSet.add(e.getApportionTypeName());
                });
            }
        });
        if(CollectionUtil.isNotEmpty(discountTypeSet)) {
            String discountType = discountTypeSet.stream().collect(Collectors.joining(","));
            masterOrderDTO.setDiscountType(discountType);
        }
        masterOrderDTO.setDiscountAmount(condition.getPremiumTotalAmount().add(condition.getDiscountsTotalPrice()));
        return masterOrderDTO;
    }

    private List<EBusinessBaseOrderDTO> buildEBusinessBaseOrderForWholePay(EBusinessPaymentOrderCondition condition, Long assetId) throws ChargeBusinessException {
        Long communityId = Long.parseLong(condition.getCommunityId());
        List<EBusinessBaseOrderDTO> eBusinessBaseOrderDTOS = new ArrayList<>();
        for(EBusinessOrderItem orderItem:condition.getOrderItems()) {
            String discountType = "";
            if(CollectionUtil.isNotEmpty(orderItem.getApportionItems())) {
                discountType = orderItem.getApportionItems().stream().map(EBusinessApportionItem::getApportionTypeName).collect(Collectors.joining(","));
            }
            EBusinessBaseOrderDTO baseOrderDTO = EBusinessBaseOrderDTO.builder().extendOrderNo(condition.getOrderNo())
                    .communityId(Long.parseLong(condition.getCommunityId())).paymentMode(2)
                    .assetId(assetId).goodsQuantity(orderItem.getNum()).goodsUnit(orderItem.getUnit())
                    .totalAmount(orderItem.getOriginPrice()).totalPaidAmount(orderItem.getApportionedAmount())
                    .paidAt(DateUtils.parseToTimeStamp(orderItem.getPayTime(), DateUtils.FORMAT_0))
                    .specsType(orderItem.getSpecText()).discountType(discountType)
                    .itemId(orderItem.getItemId()).itemName(orderItem.getItemName()).goodsName(orderItem.getSkuName())
                    .orderTime(DateUtils.parseToTimeStamp(condition.getOrderTime(), DateUtils.FORMAT_0))
                    .cateName(orderItem.getCateName()).payStatus(convertPayStatus(orderItem.getPayState()))
                    .extendOrderId(orderItem.getOid()).goodsUnitPrice(orderItem.getOriginUnitPrice())
                    .build();
            baseOrderDTO.setDiscountAmount(orderItem.getPremiumAmount().add(orderItem.getDiscountAmount()));
            if(Objects.isNull(orderItem.getTaxRate()) || StringUtils.isBlank(orderItem.getTaxCateCode())){
                CommunityChargeItemDTO communityChargeItemDTO = getTaxInfoByItemId(communityId, orderItem.getItemId());
                baseOrderDTO.setTaxCateCode(communityChargeItemDTO.getTaxCode());
                TaxDetailDTO taxDetailDTO = getTaxDetailDTO(communityChargeItemDTO.getTaxId(), communityChargeItemDTO.getTaxRates());
                if(Objects.nonNull(taxDetailDTO)) {
                    baseOrderDTO.setTaxRate(taxDetailDTO.getTaxPoint());
                }
            }
            //.customerId()
            eBusinessBaseOrderDTOS.add(baseOrderDTO);
        }
        return eBusinessBaseOrderDTOS;
    }

    /**
     * 创建资产关联表与账单订单明细
     * @param condition
     * @param assetTransactionId
     * @param communityId
     * @param assetAdapter
     * @throws ChargeBusinessException
     */
    private void createTransactionRelationAndOrderBillDetailV2(EBusinessPaymentOrderCondition condition, Long assetTransactionId,
                                                             Long communityId, AssetAdapter assetAdapter, Map<String, Long> oid2BaseOrderIdMap) throws ChargeBusinessException {
        for(EBusinessOrderItem orderItem:condition.getOrderItems()) {
            List<TransactionRelationDTO> relationList = Lists.newArrayList();
            //订单表[base_order]
            Long orderId = oid2BaseOrderIdMap.get(orderItem.getOid());
            //增加订单明细信息order_bill_detail
            OrderBillDetailDTO billDetailDTO = OrderBillDetailDTO.builder().assetTransactionId(assetTransactionId)
                    .bizType(BizTypeEnum.ORDER_TYPE.getCode()).itemId(orderItem.getItemId()).itemName(orderItem.getItemName())
                    .payType(OrderPayTypeEnum.ORDER_PAY.getCode()).totalAmount(orderItem.getApportionedAmount()).availableBalance(orderItem.getApportionedAmount())
                    .actualAmount(orderItem.getPayAmount()).orderId(orderId).communityId(communityId).build();
            Long orderBillDetailId = orderBillDetailClient.create(billDetailDTO).getContent();

            //添加关联数据transaction_relation
            TransactionRelationDTO relationDO = TransactionRelationDTO.builder().assetTransactionId(assetTransactionId)
                    .businessId(orderBillDetailId).businessType(BusinessTypeEnum.TEMP_PAY.getCode())
                    .createTime(new Date()).communityId(communityId).build();
            relationList.add(relationDO);

            AppInterfaceUtil.getResponseData(transactionRelationClient.batchCreate(relationList));
        }
    }

    private Integer convertFinishStatus(Integer status){
        if(status == 1){
            return EBusinessOrderStatusEnum.COMPLETED.getCode();
        }else if(status == 2){
            return EBusinessOrderStatusEnum.CANCELED.getCode();
        }
        return null;
    }

    private boolean checkFinishParam(EBusinessFinishOrderCondition condition) throws ChargeBusinessException{
        if(Objects.nonNull(condition.getStatus())) {
            if (condition.getStatus() != 1 && condition.getStatus() != 2) {
                throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "状态值只能为1和2");
            }
        }
        return true;
    }

    private CommunityBankAccountDTO getCommunityVirtualAccount(Long communityId) throws ChargeBusinessException{
        List<CommunityBankAccountDTO> communityBankAccountDTOList = AppInterfaceUtil.getResponseDataThrowException(bankAccountClient.getInfoRewardList(RewardDetailRequestDTO.builder().communityId(communityId).build()));
        if (CollectionUtils.isEmpty(communityBankAccountDTOList)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "该项目不存在可用的虚拟银行收款账户信息!");
        }
        return communityBankAccountDTOList.get(0);
    }

    /**
     * 根据商品应付金额和实付金额判断是已支付-1还是部分支付-2
     * @param apportionedAmount
     * @param totalPaidAmount
     * @return
     */
    private Integer calculateGoodsPayStatus(BigDecimal apportionedAmount, BigDecimal totalPaidAmount){
        if(apportionedAmount.compareTo(totalPaidAmount) == 0){
            return 1;
        }
        return 2;
    }

    private EBusinessBaseOrderDTO buildEBusinessInsertBaseOrder(EBusinessFurnishPaymentOrderCondition condition, EBusinesssFurnishOrderItem orderItem,
                                                                Long assetId) throws ChargeBusinessException{
        Long communityId = Long.parseLong(condition.getCommunityId());
        String discountType = "";
        if(CollectionUtil.isNotEmpty(orderItem.getApportionItems())) {
            discountType = orderItem.getApportionItems().stream().map(EBusinessApportionItem::getApportionTypeName).collect(Collectors.joining(","));
        }
        EBusinessBaseOrderDTO baseOrderDTO = EBusinessBaseOrderDTO.builder().extendOrderNo(condition.getOrderNo())
                .communityId(Long.parseLong(condition.getCommunityId())).paymentMode(condition.getPaymentMode())
                .assetId(assetId).goodsQuantity(orderItem.getNum()).goodsUnit(orderItem.getUnit())
                .totalAmount(orderItem.getOriginPrice()).totalPaidAmount(orderItem.getPayAmount())
                .specsType(orderItem.getSpecText()).discountType(discountType)
                .itemId(orderItem.getItemId()).itemName(orderItem.getItemName()).goodsName(orderItem.getSkuName())
                .orderTime(DateUtils.parseToTimeStamp(condition.getOrderTime(), DateUtils.FORMAT_0))
                .extendOrderId(orderItem.getOid()).cateName(orderItem.getCateName())
                .payStatus(convertPayStatus(orderItem.getPayState())).goodsUnitPrice(orderItem.getOriginUnitPrice())
                .build();
        baseOrderDTO.setDiscountAmount(orderItem.getPremiumAmount().add(orderItem.getDiscountAmount()));
        if(Objects.nonNull(orderItem.getPayTime())){
            baseOrderDTO.setPaidAt(DateUtils.parseToTimeStamp(orderItem.getPayTime(), DateUtils.FORMAT_0));
        }
        if(Objects.isNull(orderItem.getTaxRate()) || StringUtils.isBlank(orderItem.getTaxCateCode())){
            CommunityChargeItemDTO communityChargeItemDTO = getTaxInfoByItemId(communityId, orderItem.getItemId());
            baseOrderDTO.setTaxCateCode(communityChargeItemDTO.getTaxCode());
            TaxDetailDTO taxDetailDTO = getTaxDetailDTO(communityChargeItemDTO.getTaxId(), communityChargeItemDTO.getTaxRates());
            if(Objects.nonNull(taxDetailDTO)) {
                baseOrderDTO.setTaxRate(taxDetailDTO.getTaxPoint());
            }
        }
        if(E_BUSINESS_POS.equals(condition.getPayment().getPayType()) || E_BUSINESS_BANK_TRANSFER.equals(condition.getPayment().getPayType())){
            baseOrderDTO.setTotalPaidAmount(BigDecimal.ZERO);
        }
        //.customerId()
        return baseOrderDTO;
    }

    private EBusinessMasterOrderDTO buildUpdateEBusinessMasterOrderDTO(EBusinessMasterOrderDTO masterOrderDTO,EBusinessFurnishPaymentOrderCondition condition){
        EBusinessMasterOrderDTO newMasterOrderDTO = new EBusinessMasterOrderDTO();
        newMasterOrderDTO.setId(masterOrderDTO.getId());
        newMasterOrderDTO.setPayStatus(convertPayStatus(condition.getPayState()));
        newMasterOrderDTO.setTotalPaidAmount(masterOrderDTO.getTotalPaidAmount().add(condition.getPayment().getPayPrice()));
        if(masterOrderDTO.getStatus().equals(EBusinessOrderStatusEnum.CREATED.getCode())){
            newMasterOrderDTO.setStatus(EBusinessOrderStatusEnum.PROGRESSING.getCode());
        }
        return newMasterOrderDTO;
    }

}
