package com.charge.api.web.dto.parking;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/12/18 17:28
 */
@Data
public class DeleteHistoryDataParam implements Serializable {

    private static final long serialVersionUID = -7768972737931243459L;

    /**
     * 小区id
     */
    private String communityId;

    /**
     * 小区code
     */
    private String communityCode;

    /**
     * 小区名称
     */
    private String communityName;

    /**
     * 停车场id
     */
    @NotBlank(message = "停车场ID为空")
    private String parkingId;

    /**
     * 停车场名称
     */
    private String parkingName;

    /**
     * 交易日期
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    @NotNull(message = "交易日期不能为空")
    private Date chargeTime;

    /**
     * 支付渠道
     */
    @NotBlank(message = "支付渠道为空")
    private String paymentChannel;

    /**
     * 数据更新人
     */
    private String modifyUser;

}
