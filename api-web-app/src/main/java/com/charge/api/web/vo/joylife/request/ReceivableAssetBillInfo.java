package com.charge.api.web.vo.joylife.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-07 18:20
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReceivableAssetBillInfo implements Serializable {

    private static final long serialVersionUID = -5850606725088722648L;

    /**
     * 资产id
     */
    private String assetId;

    /**
     * 应收单信息
     */
    private List<ReceivableBillInfo> receivableBillInfos;

}
