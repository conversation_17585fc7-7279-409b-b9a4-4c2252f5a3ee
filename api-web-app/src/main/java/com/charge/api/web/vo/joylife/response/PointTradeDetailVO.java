package com.charge.api.web.vo.joylife.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/26 10:03
 */
@Data
public class PointTradeDetailVO implements Serializable {
    private static final long serialVersionUID = 6523113292522878638L;

    // 大会员ID（权益账号）
    private String equityAccount;
    // 积分数
    private Integer points;
    // 1005积分抵扣，1015积分抵扣撤销，1001积分赠送，1016积分赠送撤销
    private String type;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateTs;
}
