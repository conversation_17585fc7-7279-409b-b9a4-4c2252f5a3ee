package com.charge.api.web.support;

import com.charge.api.web.config.PrePayLockConfig;
import com.charge.bill.client.LockClient;
import com.charge.bill.dto.LockObjectDTO;
import com.charge.bill.enums.BillPrePaySceneEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.enums.common.PaymentMethodEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.util.EnumUtil;
import com.charge.core.util.CollectionUtil;
import com.charge.pay.client.PayClient;
import com.charge.pay.dto.pay.TradeCloseRequestDTO;
import com.charge.pay.dto.pay.TradeCloseResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 预下单加锁检测
 * <AUTHOR>
 * @date 2023/12/29
 */
@Component
@Slf4j
public class PrePayLockSupport {

    @Resource
    private LockClient lockClient;

    @Resource
    private PrePayLockConfig prePayLockConfig;

    @Resource
    private PayClient payClient;

    /**
     * 检测加锁
     * 同时根据设置关闭之前的单和锁
     * @param lockObjectDTO
     * @throws ChargeBusinessException
     */
    public void lockAndClose(LockObjectDTO lockObjectDTO) throws ChargeBusinessException {
        if (!enableLock(prePayLockConfig, lockObjectDTO)){
            return;
        }
        LockObjectDTO existLockObjectDTO = toLockIfFailReturnExist(lockObjectDTO);
        if (Objects.isNull(existLockObjectDTO)) {
            if (isOnlyCheck(prePayLockConfig, lockObjectDTO)){
                //如果是仅仅检测则释放掉
                unlock(lockObjectDTO);
            }
            return;
        }
        if (!allowClosePrevious(prePayLockConfig, existLockObjectDTO, lockObjectDTO)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1019, getSceneDesc(existLockObjectDTO.getBusinessScene()));
        }
        closePreviousBill(existLockObjectDTO);
        //再加锁
        existLockObjectDTO = toLockIfFailReturnExist(lockObjectDTO);
        if (Objects.nonNull(existLockObjectDTO)) {
            //说明又被占用了，则不在重试了
            throw new ChargeBusinessException(ErrorInfoEnum.E1019, getSceneDesc(existLockObjectDTO.getBusinessScene()));
        }
        if (isOnlyCheck(prePayLockConfig, lockObjectDTO)){
            //如果是仅仅检测则释放掉
            unlock(lockObjectDTO);
        }
    }

    public void unlock(LockObjectDTO lockObjectDTO) {
        if (!enableLock(prePayLockConfig, lockObjectDTO)){
            return;
        }
        if (Objects.isNull(lockObjectDTO)){
            return;
        }
        try {
           AppInterfaceUtil.successOrThrowException(lockClient.billPayUnlock(lockObjectDTO));
        } catch (ChargeBusinessException ex) {
            log.warn("解锁失败!{}-{}", lockObjectDTO, ex.getMessage(), ex);
        }
    }

    public void setPrePayLockConfig(PrePayLockConfig prePayLockConfig) {
        this.prePayLockConfig = prePayLockConfig;
    }

    public void setLockClient(LockClient lockClient) {
        this.lockClient = lockClient;
    }

    private LockObjectDTO toLockIfFailReturnExist(LockObjectDTO lockObjectDTO) throws ChargeBusinessException {
        ChargeResponse<LockObjectDTO> chargeResponse = lockClient.billPayLock(lockObjectDTO);
        if (chargeResponse.isSuccess()) {
            return null;
        }
        if (ErrorInfoEnum.E1019.getCode().equals(chargeResponse.getCode().toString())) {
            return chargeResponse.getContent();
        }
        throw new ChargeBusinessException(String.valueOf(chargeResponse.getCode()), chargeResponse.getMessage());
    }

    private static boolean enableLock(PrePayLockConfig prePayLockConfig, LockObjectDTO lockObjectDTO){
        if (!prePayLockConfig.isEnableLock()){
            return false;
        }
        if (CollectionUtil.isNotEmpty(prePayLockConfig.getForbidLockScenes()) &&
                prePayLockConfig.getForbidLockScenes().contains(lockObjectDTO.getBusinessScene())){
            return false;
        }
        return true;
    }

    /**
     * 是否允许关闭前一个单（涉及到交集）
     * @param prePayLockConfig
     * @param existLockObjectDTO
     * @param lockObjectDTO
     * @return
     */
    private static boolean allowClosePrevious(PrePayLockConfig prePayLockConfig, LockObjectDTO existLockObjectDTO
            , LockObjectDTO lockObjectDTO) {

        if (CollectionUtil.isEmpty(prePayLockConfig.getAllowClosePreviousScenes())) {
            return false;
        }
        if (!prePayLockConfig.getAllowClosePreviousScenes().contains(existLockObjectDTO.getBusinessScene())) {
            return false;
        }

        if (isRepeatDeny(prePayLockConfig, existLockObjectDTO, lockObjectDTO)) {
            return false;
        }
        if (lockObjectDTO.getMinObjectMark().equals(existLockObjectDTO.getMinObjectMark()) &&
                Objects.equals(existLockObjectDTO.getPayMember(),lockObjectDTO.getPayMember()) &&
                !Objects.equals(existLockObjectDTO.getPaymentMethod(),lockObjectDTO.getPaymentMethod())) {
            //说明是切换支付方式，则不需要检测间隔
            return true;
        }

        //检测时间
        if (DateUtils.getCurrentTimestamp().compareTo(DateUtils.addSeconds(existLockObjectDTO.getPayTime()
                        , prePayLockConfig.getAllowClosePreviousAfterSec())) < 0) {
            return false;
        }
        return true;
    }

    /**
     * 是否符合重复禁止下单的规则
     * @param prePayLockConfig
     * @param existLockObjectDTO
     * @param lockObjectDTO
     * @return
     */
    private static boolean isRepeatDeny(PrePayLockConfig prePayLockConfig, LockObjectDTO existLockObjectDTO
            , LockObjectDTO lockObjectDTO) {
        if (!prePayLockConfig.isCheckRepeat()) {
            return false;
        }
        if (!lockObjectDTO.getObjectMark().equals(existLockObjectDTO.getObjectMark())) {
            return false;
        }
        if (prePayLockConfig.getDenyRepeatSecs() < 1) {
            return true;
        }
        if (DateUtils.getCurrentTimestamp().compareTo(DateUtils.addSeconds(existLockObjectDTO.getPayTime()
                        , prePayLockConfig.getDenyRepeatSecs())) < 1) {
            return true;
        }
        return false;
    }

    private static String getSceneDesc(String businessScene){
        BillPrePaySceneEnum sceneEnum = EnumUtil.getEnumByCodeWithNull(BillPrePaySceneEnum.values(), businessScene);
        if (Objects.isNull(sceneEnum)){
            return "";
        }
        return sceneEnum.getValue();
    }

    private static boolean isOnlyCheck(PrePayLockConfig prePayLockConfig, LockObjectDTO lockObjectDTO){

        if (CollectionUtil.isEmpty(prePayLockConfig.getOnlyCheckPaymentTerminals())){
            return false;
        }
        BillPrePaySceneEnum sceneEnum = EnumUtil.getEnumByCodeWithNull(BillPrePaySceneEnum.values()
                , lockObjectDTO.getBusinessScene());
        if (Objects.isNull(sceneEnum)){
            return false;
        }
        return prePayLockConfig.getOnlyCheckPaymentTerminals().contains(sceneEnum.getPaymentTerminal());
    }

    /**
     * 关单处理
     * 目前这里没关应收单
     * @param existLockObjectDTO
     */
    private void closePreviousBill(LockObjectDTO existLockObjectDTO) throws ChargeBusinessException {
        //关闭三方订单
        TradeCloseRequestDTO tradeCloseRequestDTO = TradeCloseRequestDTO.builder()
                .orderNum(existLockObjectDTO.getMark()).paymentTerminal(existLockObjectDTO.getPaymentSource()).build();
        ChargeResponse<TradeCloseResponseDTO> response = payClient.tradeOrderClose(tradeCloseRequestDTO);
        if (response.isSuccess() || response.getCode().toString().equals(ErrorInfoEnum.E2003.getCode())) {
            //立刻关锁
            AppInterfaceUtil.successOrThrowException(lockClient.billPayUnlock(existLockObjectDTO));
            return;
        }
        if ((response.getMessage().contains("CMBORDERID_NOT_EXIST") &&
                String.valueOf(PaymentMethodEnum.ALIPAY.getPaymentCode()).equals(existLockObjectDTO.getPaymentMethod()))
                || response.getMessage().contains("ORDERID_NOT_EXIST")) {
            //这种也关单先, 兼容1.0值匹配
            AppInterfaceUtil.successOrThrowException(lockClient.billPayUnlock(existLockObjectDTO));
            return;
        }
        log.info("关单失败！{}", existLockObjectDTO);

        throw new ChargeBusinessException(ErrorInfoEnum.E1019, getSceneDesc(existLockObjectDTO.getBusinessScene()));
    }
}
