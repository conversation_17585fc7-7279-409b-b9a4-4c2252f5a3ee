package com.charge.api.web.vo.pos;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.List;

/**
 * 预存退款
 *
 * <AUTHOR>
 * @date 2023/7/6
 */
@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PreStoreRefundRequest {
    /**
     * 操作员
     */
    String operatorId;
    /**
     * 操作人
     */
    String operator;
    /**
     * 项目id
     */
    Long communityId;
    /**
     * 备注
     */
    String remark;
    /**
     * 是否是垫付
     */
    Boolean advancePay;
    /**
     * 通用预存退款额度，可选
     */
    BigDecimal commonRefundMoney;
    /**
     * 专项预存退款项，可选
     */
    List<ChargeItemPriceV2> specialRefundChargeItems;
    /**
     * 垫付人
     */
    String employeeName;
    /**
     * 退款申请人
     */
    String draweeName;
    /**
     * 房屋id
     */
    Long houseId;

    /**
     * 扣除万象星数量
     */
    private Integer revertPoints;

    /**
     * 大会员账号pid
     */
    private String pid;

    /**
     * 会员手机号
     */
    private String phone;
    @Pattern(regexp = "^[0-9]{9,25}$", message = "银行账号必须是9-25位的数字")
    String payBankAccount;

    /**
     * 收款银行账号
     */
    @Pattern(regexp = "^[0-9]{9,25}$", message = "银行账号必须是9-25位的数字")
    String acceptBankAccount;

    /**
     * 收款银行名称
     */
    @Length(max = 25,message = "收款银行名称最大长度25")
     String acceptBankName;

    /**
     * 收款账号名
     */
    @Length(max = 25,message = "收款账号名最大长度25")
     String acceptAccountName;

    /**
     *
     * 收款开户行
     */
    @Length(max = 25,message = "收款开户行最大长度25")
     String acceptAccountOpeningBank;

    /**
     * 收款账户城市
     */
    @Length(max = 25,message = "收款账户城市最大长度25")
     String acceptAccountCity;
}


