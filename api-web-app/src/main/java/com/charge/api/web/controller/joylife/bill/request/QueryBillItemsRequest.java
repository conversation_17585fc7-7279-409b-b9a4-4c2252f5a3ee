package com.charge.api.web.controller.joylife.bill.request;

import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class QueryBillItemsRequest implements Serializable {

    private static final long serialVersionUID = 7986952609800827486L;
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID参数缺失,请重试")
    private Long communityMsId;

    /**
     * 实收单IDS
     */
    @NotNull(message = "实收单ID参数缺失,请重试")
    private List<Long> incomeBillIds;
}
