package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/4
 */
@NoArgsConstructor
@Data
public class BuildingReceivablePayStatisticVO {


    @JsonProperty("bName")
    private String bName;

    @JsonProperty("rate")
    private Double rate;

    @JsonProperty("bId")
    private String bId;
}


