package com.charge.api.web.support;

import com.charge.api.web.constants.YueConstants;
import com.charge.api.web.convert.AssertConverter;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.vo.joylife.request.BatchAssetPreStorePointsCalReq;
import com.charge.api.web.vo.joylife.request.BatchAssetPrestore;
import com.charge.api.web.vo.joylife.request.ZhaoXiAssertRequest;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.AssertUtils;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.TraceContextUtil;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.condition.HouseCondition;
import com.charge.maindata.condition.ParkingCondition;
import com.charge.maindata.enums.*;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import com.charge.maindata.pojo.vo.ParkingVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

import static com.charge.api.web.service.pos.impl.ChargeBillServiceImpl.nullStr;
import static com.charge.maindata.enums.AssetUseStatusEnum.getCodeByTypeAndOriginalCode;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/07
 */
@Slf4j
@Component
public class AssetSupport {

    public static AssetTypeEnum ofAssetTypeEnum(Integer assetType){
        if (assetType==null){
            return null;
        }
        for (AssetTypeEnum assetTypeEnum : AssetTypeEnum.values()) {
            if (Objects.equals(assetTypeEnum.getCode(), assetType)) {
                return assetTypeEnum;
            }
        }
        throw new IllegalArgumentException("不支持的资产类型");
    }

    @Autowired
    private AssetClient assetClient;

    public static final List<Integer> ASSET_TYPES = Arrays.stream(AssetTypeEnum.values()).map(AssetTypeEnum::getCode).collect(Collectors.toList());
    public AssetDTO getCommunityVirtualHouse(Long communityId) throws ChargeBusinessException {
        ChargeResponse<List<AssetDTO>> chargeResponse = assetClient.listAsset(AssetCondition.builder().communityId(communityId).type(1)
                .houseType(HouseTypeEnum.VIRTUAL_HOUSE_COMMUNITY.getCode()).build());
        List<AssetDTO> assetDTOS = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        Assert.notEmpty(assetDTOS,"项目虚拟房间不存在");
        return assetDTOS.get(0);
    }
    public List<AssetAdapter> getAssetAdapterByIds(List<Long> assetIds) throws ChargeBusinessException {
        AssetCondition assetCondition = AssetCondition.builder().ids(assetIds).build();
        ChargeResponse<List<AssetDTO>> response = assetClient.listAsset(assetCondition);
        List<AssetDTO> assetDTOS = AppInterfaceUtil.getDataThrowException(response);

        return buildAssetAdapter(assetDTOS);
    }

    public List<AssetAdapter> buildAssetAdapter(List<AssetDTO> assetDTOS) throws ChargeBusinessException {
        List<AssetAdapter> assetAdapters = new ArrayList<>(assetDTOS.size());
        for (AssetDTO assetDTO : assetDTOS) {
            assetAdapters.add(buildAssetAdapter(assetDTO));
        }
        return assetAdapters;
    }

    private boolean invalid(AssetDTO assetDTO) {
        if (Objects.isNull(assetDTO) || Objects.isNull(assetDTO.getId())) {
            return true;
        }

        if (Objects.equals(AssetTypeEnum.HOUSE.getCode(), assetDTO.getType()) && Objects.isNull(assetDTO.getHouseDTO())) {
            return true;
        }

        if (Objects.equals(AssetTypeEnum.PARKING_SPACE.getCode(), assetDTO.getType()) && Objects.isNull(assetDTO.getParkingSpaceDTO())) {
            return true;
        }

        return false;
    }

    private AssetAdapter buildAssetAdapter(AssetDTO assetDTO) throws ChargeBusinessException {
        AssetAdapter assetAdapter = new AssetAdapter();
        if (invalid(assetDTO)) {
            log.warn("{}|资产数据无效，{}", LogCategoryEnum.BUSSINESS, assetDTO);
            throw new ChargeBusinessException(ErrorInfoEnum.E1003);
        }

        if (Objects.equals(AssetTypeEnum.HOUSE.getCode(), assetDTO.getType())) {
            BeanCopierWrapper.copy(assetDTO.getHouseDTO(), assetAdapter);
            assetAdapter.setId(assetDTO.getId());
            assetAdapter.setType(assetDTO.getType());
            assetAdapter.setAssetCode(assetDTO.getHouseDTO().getHouseCode());
            assetAdapter.setAssetName(assetDTO.getHouseDTO().getHouseName());
            assetAdapter.setAssetNum(assetDTO.getHouseDTO().getHouseNum());
            assetAdapter.setChargeObject(assetDTO.getHouseDTO().getChargeObjType());
            assetAdapter.setChargeObjectId(assetDTO.getHouseDTO().getChargeObjId());
            assetAdapter.setType(assetDTO.getType());
            assetAdapter.setListCustomer(assetDTO.getHouseDTO().getListCustomer());
            assetAdapter.setAssetUseStatus(getCodeByTypeAndOriginalCode(AssetTypeEnum.HOUSE.getCode(),assetDTO.getHouseDTO().getHouseUseStatus()));
        } else {
            BeanCopierWrapper.copy(assetDTO.getParkingSpaceDTO(), assetAdapter);
            assetAdapter.setId(assetDTO.getId());
            assetAdapter.setType(assetDTO.getType());

            assetAdapter.setBuildingCode(assetDTO.getParkingSpaceDTO().getParkingCode());
            assetAdapter.setBuildingName(assetDTO.getParkingSpaceDTO().getParkingCode());

            assetAdapter.setAssetCode(assetDTO.getParkingSpaceDTO().getParkingSpaceCode());
            assetAdapter.setAssetName(assetDTO.getParkingSpaceDTO().getParkingName()+assetDTO.getParkingSpaceDTO().getParkingSpaceNum());
            assetAdapter.setChargeObject(assetDTO.getParkingSpaceDTO().getChargeObjType());
            assetAdapter.setChargeObjectId(assetDTO.getParkingSpaceDTO().getChargeObjId());
            assetAdapter.setType(assetDTO.getType());
            assetAdapter.setListCustomer(assetDTO.getParkingSpaceDTO().getListCustomer());
            assetAdapter.setAssetUseStatus(getCodeByTypeAndOriginalCode(AssetTypeEnum.PARKING_SPACE.getCode(),assetDTO.getParkingSpaceDTO().getUsageState()));
        }

        return assetAdapter;
    }

    public HouseDTO getHouseInfo(String houseUuid) {
        HouseCondition condition = HouseCondition.builder().msIds(Lists.newArrayList(houseUuid)).pageNum(1).pageSize(1).build();
        ChargeResponse<PagingDTO<HouseDTO>> pagingDTOChargeResponse = assetClient.pageHouse(condition);
        if (!pagingDTOChargeResponse.isSuccess()) {
            return null;
        }
        if(CollectionUtils.isNotEmpty(pagingDTOChargeResponse.getContent().getList())){
            return pagingDTOChargeResponse.getContent().getList().get(0);
        }

        return null;
    }

    /**
     * 根据资产id查询
     * 
     * @param assetId
     * @return
     */
    public AssetDTO getAssetById(Long assetId) {
        AssetCondition condition = AssetCondition.builder().id(assetId).build();
        ChargeResponse<AssetDTO> assetDTOChargeResponse = assetClient.oneAsset(condition);
        AssetDTO assetDTO = AppInterfaceUtil.getResponseData(assetDTOChargeResponse);
        return assetDTO;
    }

    public List<AssetDTO> listAssetByIds(List<Long> assetIds) throws ChargeBusinessException {
        ChargeResponse<List<AssetDTO>> assetDTOChargeResponse = assetClient.listAsset(AssetCondition.builder().ids(assetIds).build());
        return AppInterfaceUtil.getResponseDataThrowException(assetDTOChargeResponse);
    }

    public Long getCommunityId(AssetDTO assetDTO) {
        if (assetDTO == null) {
            return null;
        }
        if (assetDTO.getHouseDTO() != null) {
            return assetDTO.getHouseDTO().getCommunityId();
        } else if (assetDTO.getParkingSpaceDTO() != null) {
            return assetDTO.getParkingSpaceDTO().getCommunityId();
        } else {
            return null;
        }
    }

    public Long getAssetId(String houseUuid) {
        AssetCondition assetCondition = AssetCondition.builder().build();
        return 1L;
    }

    public String getAssetNum(Long assetId) throws ChargeBusinessException {
        ChargeResponse<AssetDTO> assetResp = assetClient.oneAsset(AssetCondition.builder().id(assetId).build());
        AssetDTO assetDTO = AppInterfaceUtil.getResponseDataThrowException(assetResp);
        return getAssetNum(assetDTO);
    }

    public AssetDTO getAsset(Long assetId) throws ChargeBusinessException {
        ChargeResponse<AssetDTO> assetResp = assetClient.oneAsset(AssetCondition.builder().id(assetId).build());
        return AppInterfaceUtil.getResponseDataThrowException(assetResp);
    }

    public static String getAssetBuildingName(AssetDTO assetDTO){
        if (assetDTO == null) {
            return null;
        } else {
            return assetDTO.getHouseDTO() != null ? assetDTO.getHouseDTO().getBuildingName() : assetDTO.getParkingSpaceDTO() != null ?
                    assetDTO.getParkingSpaceDTO().getParkingName() : null;
        }
    }

    public static String getAssetUnitName(AssetDTO assetDTO){
        if (assetDTO == null) {
            return null;
        } else {
            return assetDTO.getHouseDTO() != null ? assetDTO.getHouseDTO().getUnitName() : null;
        }
    }

    public static String getAssetFullName(AssetDTO assetDTO){
       return getAssetBuildingName(assetDTO)+nullStr(getAssetUnitName(assetDTO))+getAssetNum(assetDTO);
    }

    public static String getAssetNum(AssetDTO assetDTO){
        if (assetDTO == null) {
            return null;
        } else {
            return assetDTO.getHouseDTO() != null ? assetDTO.getHouseDTO().getHouseNum() : assetDTO.getParkingSpaceDTO() != null ?
                    assetDTO.getParkingSpaceDTO().getParkingSpaceNum() : null;
        }
    }

    public Long getV2AssetId(String houseMsId) {
        ChargeResponse<List<AssetDTO>> assetDTOChargeResponse = assetClient.listAsset(AssetCondition.builder().msIds(Collections.singletonList(houseMsId)).build());
        if (!assetDTOChargeResponse.isSuccess()) {
            log.error("查询主数据服务异常");
            return null;
        }

        List<AssetDTO> assetDTOList = assetDTOChargeResponse.getContent();
        if (CollectionUtils.isEmpty(assetDTOList)) {
            log.error("通过houseMsId：{}查询不到对应资产（房屋）", houseMsId);
            return null;
        }
        Long assetId = assetDTOList.get(0).getId();
        return assetId;
    }

    public void fillAssetId(ZhaoXiAssertRequest req) throws ChargeBusinessException {
        if(req.getAssetId()==null){
            AssertUtils.isTrue(org.springframework.util.StringUtils.hasText(req.getAssetMsId())&&req.getAssetType()!=null,"朝昔的资产id和资产类型不能为空");
            AssetDTO assetDTO = getByAssetMsId(req.getAssetMsId(), req.getAssetType());
            AssertUtils.notNull(assetDTO,"朝昔的资产查不到:"+req.getAssetMsId());
            req.setAssetId(assetDTO.getId());
        }
    }

    public Map<Long,AssetAdapter> fillAssetIds(List<? extends ZhaoXiAssertRequest> assets, Long communityId, boolean isNeedCheckObject) throws ChargeBusinessException {
        Map<Long,AssetAdapter> assetAdapterMap=new HashMap<>(assets.size());
        assets.forEach(asset -> {
            if (asset.getAssetId() == null) {
                Assert.isTrue(asset.getAssetType() != null && org.springframework.util.StringUtils.hasText(asset.getAssetMsId()), "资产类型或者msId为空:" + asset);
                Assert.isTrue(ASSET_TYPES.contains(asset.getAssetType()), "资产类型错误");
            }
        });
        List<Long> assetIds = assets.stream().map(a -> a.getAssetId()).filter(Objects::nonNull).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(assetIds)){
            List<AssetAdapter> assetList = getAssetListByCondition(AssetCondition.builder().ids(assetIds).build());
            assetList.forEach(a->assetAdapterMap.put(a.getId(),a));
        }
        Map<Integer, List<ZhaoXiAssertRequest>> assetTypeMap = assets.stream().filter(a -> a.getAssetId() == null).collect(Collectors.groupingBy(ZhaoXiAssertRequest::getAssetType));

        Map<String, AssetAdapter> mergedMsidToAssetMap = fetchAssetsFromRemote(assetTypeMap, communityId);

        updateAssetsWithFetchedData(assetTypeMap, mergedMsidToAssetMap, assetAdapterMap, isNeedCheckObject);
        return assetAdapterMap;
    }

    public List<AssetAdapter> fillZhaoxiAssetIdsAndReturnAdapter(List<ZhaoXiAssertRequest> assets, Long communityId) throws ChargeBusinessException {
        assets.forEach(asset -> {
            if (asset.getAssetId() == null) {
                Assert.isTrue(asset.getAssetType() != null && org.springframework.util.StringUtils.hasText(asset.getAssetMsId()), "资产类型或者msId为空:" + asset);
                Assert.isTrue(ASSET_TYPES.contains(asset.getAssetType()), "资产类型错误");
            }
        });
        Map<Integer, List<ZhaoXiAssertRequest>> assetMap = assets.stream().filter(a -> a.getAssetId() == null).collect(Collectors.groupingBy(ZhaoXiAssertRequest::getAssetType));
        Map<String, AssetAdapter> mergedMsidToAssetMap = assetMap.entrySet().stream().flatMap((entry -> {
            AssetCondition condition = AssetCondition.builder().type(entry.getKey()).communityId(communityId)
                    .msIds(entry.getValue().stream().map(ZhaoXiAssertRequest::getAssetMsId).distinct().collect(Collectors.toList())).build();
            List<AssetDTO> assetList = null;
            try {
                assetList = AppInterfaceUtil.getResponseDataThrowException(assetClient.listAsset(condition));
            } catch (ChargeBusinessException e) {
                throw new RuntimeException(e);
            }
            return assetList.stream().map(AssetSupport::buildAsserAdapter);
        })).collect(Collectors.toMap(this::getMergeKey, a -> a, (a, b) -> b));
        assetMap.values().forEach(assetList -> assetList.forEach(asset -> {
            AssetAdapter assetAdapter = mergedMsidToAssetMap.get(getMergeKey(asset));
            if (assetAdapter == null) {
                throw new ChargeRuntimeException("资产不存在：" + asset);
            }
            asset.setAssetId(assetAdapter.getId());
        }));
        return new ArrayList<>(mergedMsidToAssetMap.values());
    }

    private Map<String, AssetAdapter> fetchAssetsFromRemote(Map<Integer, List<ZhaoXiAssertRequest>> assetTypeMap, Long communityId) {
        return assetTypeMap.entrySet().stream()
                .flatMap(entry -> {
                    AssetCondition condition = AssetCondition.builder()
                            .type(entry.getKey())
                            .communityId(communityId)
                            .msIds(entry.getValue().stream().map(ZhaoXiAssertRequest::getAssetMsId).distinct().collect(Collectors.toList()))
                            .build();
                    List<AssetDTO> assetList;
                    try {
                        assetList = AppInterfaceUtil.getResponseDataThrowException(assetClient.listAsset(condition));
                    } catch (ChargeBusinessException e) {
                        throw new RuntimeException(e);
                    }
                    return assetList.stream().map(AssetSupport::buildAsserAdapter);
                })
                .collect(Collectors.toMap(this::getMergeKey, a -> a, (a, b) -> b));
    }

    private void updateAssetsWithFetchedData(Map<Integer, List<ZhaoXiAssertRequest>> assetTypeMap, Map<String, AssetAdapter> mergedMsidToAssetMap,
                                             Map<Long, AssetAdapter> assetAdapterMap, boolean isNeedCheckObject) throws ChargeBusinessException {
        for (List<ZhaoXiAssertRequest> assetList : assetTypeMap.values()) {
            for (ZhaoXiAssertRequest asset : assetList) {
                AssetAdapter assetAdapter = mergedMsidToAssetMap.get(getMergeKey(asset));
                if (assetAdapter == null) {
                    throw new ChargeBusinessException("资产不存在：" + asset);
                }
                if(isNeedCheckObject) {
                    validateAssetChargeObject(asset, assetAdapter);
                }
                assetAdapterMap.put(assetAdapter.getId(), assetAdapter);
                asset.setAssetId(assetAdapter.getId());
            }
        }
    }

    private void validateAssetChargeObject(ZhaoXiAssertRequest assertRequest, AssetAdapter assetAdapter) throws ChargeBusinessException {
        if (Objects.nonNull(assertRequest.getChargeObjectType()) && !assertRequest.getChargeObjectType().equals(assetAdapter.getChargeObject())) {
            log.info("{}|该资产收费对象与朝昔传入收费对象类型不一致，assetId:{}", LogCategoryEnum.BUSSINESS, assetAdapter.getId());
            throw new ChargeBusinessException(YueConstants.CHARGE_OBJ_CODE_FAILED, "存在部分资产收费对象不一致，请稍后重试");
        }
    }

    private String getMergeKey(AssetAdapter a){
        return a.getMsId() + ":" + a.getType();
    }

    private String getMergeKey(ZhaoXiAssertRequest a){
        return a.getAssetMsId() + ":" + a.getAssetType();
    }

    private AssetCondition buildAssetCondition(BatchAssetPreStorePointsCalReq req, List<BatchAssetPrestore> assetPrestores, Integer assetParamFlag) {
        AssetCondition assetCondition = AssetCondition.builder()
                .communityId(req.getCommunityId())
                .build();

        if (assetParamFlag == 1) {
            List<String> assetMsIds = assetPrestores.stream()
                    .map(BatchAssetPrestore::getAssetMsId)
                    .collect(Collectors.toList());
            List<Integer> assetTypes = assetPrestores.stream()
                    .map(BatchAssetPrestore::getAssetType)
                    .distinct()
                    .collect(Collectors.toList());

            if (assetTypes.size() > 1) {
                throw new IllegalArgumentException("资产类型只允许存在一个");
            }

            assetCondition.setMsIds(assetMsIds);
            assetCondition.setType(assetTypes.get(0));
        } else {
            List<Long> assetIds = assetPrestores.stream()
                    .map(BatchAssetPrestore::getAssetId)
                    .collect(Collectors.toList());
            assetCondition.setIds(assetIds);
        }

        return assetCondition;
    }

    private void validateAssetAdapters(List<AssetAdapter> assetAdapters, List<BatchAssetPrestore> assetPrestores, Integer assetParamFlag) throws ChargeBusinessException {
        if (CollectionUtil.isEmpty(assetAdapters)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E8002);
        }

        int expectedSize = (assetParamFlag == 1)
                ? (int) assetPrestores.stream().map(BatchAssetPrestore::getAssetMsId).distinct().count()
                : (int) assetPrestores.stream().map(BatchAssetPrestore::getAssetId).distinct().count();

        if (assetAdapters.size() != expectedSize) {
            throw new ChargeBusinessException(ErrorInfoEnum.E8002.getCode(), "部分资产信息查询结果为空");
        }
    }

    public AssetDTO getByAssetMsId(String assetMsId,Integer assetType) throws ChargeBusinessException {
        ChargeResponse<List<AssetDTO>> assetDTOChargeResponse = assetClient.listAsset(AssetCondition.builder().msIds(Collections.singletonList(assetMsId)).type(assetType).build());
        List<AssetDTO> assetDTOS = AppInterfaceUtil.getResponseDataThrowException(assetDTOChargeResponse);
        if(org.springframework.util.CollectionUtils.isEmpty(assetDTOS)){
            return null;
        }
        return assetDTOS.get(0);
    }

    public AssetAdapter getAssetInfoById(String id) throws ChargeBusinessException {

        AssetCondition condition = AssetCondition.builder().ids(Collections.singletonList(Long.valueOf(id))).build();
        List<AssetDTO> assetDTOS = AppInterfaceUtil.getDataThrowException(assetClient.listAsset(condition));
        if (CollectionUtil.isNotEmpty(assetDTOS)) {
            return buildAsserAdapter(assetDTOS.get(0));
        }

        return null;
    }

    /**
     *
     * @param assetMsId 朝昔资产ID
     * @param assetType 朝昔资产类型  1房间  2车位 3虚拟资产
     * @return
     * @throws ChargeBusinessException
     */
    public AssetAdapter getAssetInfoByMdId(String assetMsId, Integer assetType) throws ChargeBusinessException {
        AssetCondition assetCondition = AssetCondition.builder()
                .communityId(TraceContextUtil.getCommunityId())
                .type(convertAssetType(assetType)).build();
        // 虚拟资产作为收费资产ID
        if (assetType.equals(3)) {
            assetCondition.setIds(Arrays.asList(Long.valueOf(assetMsId)));
        } else {
            // 非虚拟资产作为朝昔ID
            assetCondition.setMsIds(Arrays.asList(assetMsId));
        }
        List<AssetDTO> assetDTOS = AppInterfaceUtil.getDataThrowException(assetClient.listAsset(assetCondition));
        if (CollectionUtil.isNotEmpty(assetDTOS)) {
            return buildAsserAdapter(assetDTOS.get(0));
        }
        return null;
    }

    private Integer convertAssetType(Integer assetType) {
        // 房间/虚拟房间
        if (assetType.equals(1) || assetType.equals(3)) {
            return 1;
        }
        // 车位
        if (assetType.equals(2)) {
            return 2;
        }
        return assetType;
    }

    public  static AssetAdapter buildAsserAdapter(AssetDTO assetDTO) {
        AssetAdapter assetAdapter = AssertConverter.INSTANCE.map(assetDTO);
        StringBuilder assetNameBuilder=new StringBuilder();
        if (Objects.equals(AssetTypeEnum.HOUSE.getCode(), assetDTO.getType())) {
            assetAdapter.setSubId(assetDTO.getHouseDTO().getId());
            assetAdapter.setSubCode(assetDTO.getHouseDTO().getHouseCode());
            assetAdapter.setSubName(assetDTO.getHouseDTO().getHouseName());
            assetAdapter.setSubNum(assetDTO.getHouseDTO().getHouseNum());
            assetAdapter.setCommunityId(assetDTO.getHouseDTO().getCommunityId());
            assetAdapter.setCommunityName(assetDTO.getHouseDTO().getCommunityName());
            assetAdapter.setListCustomer(assetDTO.getHouseDTO().getListCustomer());
            assetAdapter.setMsId(assetDTO.getHouseDTO().getMsId());
            assetAdapter.setBuildingId(assetDTO.getHouseDTO().getBuildingId());
            assetAdapter.setBuildingName(assetDTO.getHouseDTO().getBuildingName());
            assetAdapter.setUnitName(assetDTO.getHouseDTO().getUnitName());
            assetAdapter.setAssetNum(assetDTO.getHouseDTO().getHouseNum());
            assetAdapter.setAssetCode(assetDTO.getHouseDTO().getHouseCode());
           assetAdapter.setAssetName( assetNameBuilder.append(StringUtils.defaultIfBlank(assetDTO.getHouseDTO().getBuildingName(),""))
                   .append(StringUtils.defaultIfBlank(assetDTO.getHouseDTO().getUnitName(),""))
                   .append(StringUtils.defaultIfBlank(assetDTO.getHouseDTO().getHouseNum(),"")).toString()
            );
            assetAdapter.setAssetUseStatus(getCodeByTypeAndOriginalCode(AssetTypeEnum.HOUSE.getCode(),assetDTO.getHouseDTO().getHouseUseStatus()));
            assetAdapter.setChargeObject(assetDTO.getHouseDTO().getChargeObjType());
            assetAdapter.setHouseType(assetDTO.getHouseDTO().getHouseType());
        } else if (Objects.nonNull(assetDTO.getParkingSpaceDTO())) {
            assetAdapter.setCommunityId(assetDTO.getParkingSpaceDTO().getCommunityId());
            assetAdapter.setCommunityName(assetDTO.getParkingSpaceDTO().getCommunityName());
            assetAdapter.setBuildingCode(assetDTO.getParkingSpaceDTO().getParkingCode());
            assetAdapter.setBuildingName(assetDTO.getParkingSpaceDTO().getParkingName());
            assetAdapter.setUnitName("/");
            assetAdapter.setSubId(assetDTO.getParkingSpaceDTO().getId());
            assetAdapter.setSubCode(assetDTO.getParkingSpaceDTO().getParkingSpaceCode());
            assetAdapter.setSubNum(assetDTO.getParkingSpaceDTO().getParkingSpaceNum());
            assetAdapter.setAssetCode(assetDTO.getParkingSpaceDTO().getParkingSpaceCode());
            assetAdapter.setAssetNum(assetDTO.getParkingSpaceDTO().getParkingSpaceNum());
            assetAdapter.setSubName(assetDTO.getParkingSpaceDTO().getParkingSpaceName());
            assetAdapter.setListCustomer(assetDTO.getParkingSpaceDTO().getListCustomer());
            assetAdapter.setMsId(assetDTO.getParkingSpaceDTO().getMsId());
            assetAdapter.setAssetName( assetNameBuilder.append(StringUtils.defaultIfBlank(assetDTO.getParkingSpaceDTO().getParkingName(),""))
                    .append(StringUtils.defaultIfBlank(assetDTO.getParkingSpaceDTO().getParkingSpaceNum(),"")).toString()
            );
            assetAdapter.setAssetUseStatus(getCodeByTypeAndOriginalCode(AssetTypeEnum.PARKING_SPACE.getCode(),assetDTO.getParkingSpaceDTO().getUsageState()));
            assetAdapter.setChargeObject(assetDTO.getParkingSpaceDTO().getChargeObjType());
        }
        if(assetAdapter.getListCustomer()==null){
            assetAdapter.setListCustomer(Lists.newArrayList());
        }
        return assetAdapter;
    }

    public static List<CustomerDTO> filterCustomer(AssetAdapter assetAdapter) {
        if (CollectionUtil.isEmpty(assetAdapter.getListCustomer())) {
            return Collections.emptyList();
        }
        return assetAdapter.getListCustomer().stream()
                .filter(customerDTO -> DwellStateEnum.STAY.getCode().equals(customerDTO.getDwellState())
                        || DwellStateEnum.LEAVE.getCode().equals(customerDTO.getDwellState())
                        || UseStatusEnum.USING.getCode().equals(customerDTO.getUseStatus())
                        || UseStatusEnum.UNUSED.getCode().equals(customerDTO.getUseStatus()))
                .collect(Collectors.toList());
    }

    public ParkingVO getV2ParkingInfo(Long communityId, String parkingMsId) {
        ParkingCondition parkingCondition = ParkingCondition.builder().communityId(communityId).msId(parkingMsId).build();
        ChargeResponse<List<ParkingVO>> chargeResponse = assetClient.listParking(parkingCondition);
        if (!chargeResponse.isSuccess()) {
            log.error("查询主数据服务车场信息异常");
            return null;
        }
        return chargeResponse.getContent().get(0);
    }

    public List<AssetAdapter> listAssetByIds(Long communityId, List<Long>  assetIds) throws ChargeBusinessException {
        AssetCondition condition = AssetCondition.builder().communityId(communityId).ids(assetIds).build();
        ChargeResponse<List<AssetDTO>> listChargeResponse = assetClient.listAsset(condition);
        List<AssetDTO> data = AppInterfaceUtil.getResponseDataThrowException(listChargeResponse);
        return data.stream().map(AssetSupport::buildAsserAdapter).collect(Collectors.toList());
    }

    public List<AssetAdapter> getAssetListByMsIds(Long communityId,List<String> assetMsIds) throws ChargeBusinessException {
        AssetCondition condition = AssetCondition.builder().communityId(communityId).msIds(assetMsIds).build();
        List<AssetDTO> data = AppInterfaceUtil.getDataThrowException(assetClient.listAsset(condition));
        return data.stream().map(AssetSupport::buildAsserAdapter).collect(Collectors.toList());
    }

    public List<AssetAdapter> getAssetListByCondition(AssetCondition condition) throws ChargeBusinessException {
        ChargeResponse<List<AssetDTO>> listChargeResponse = assetClient.listAsset(condition);
        List<AssetDTO> data = AppInterfaceUtil.getDataThrowException(listChargeResponse);
        List<AssetAdapter> list = data.stream().map(e->{
            return buildAsserAdapter(e);
        }).collect(Collectors.toList());
        return list;
    }

    public AssetAdapter getAssetInfoByMsId(String msId,Integer assetType) throws ChargeBusinessException {
        AssetCondition condition = AssetCondition.builder()
                .msIds(Lists.newArrayList(msId))
                .build();
        if(assetType!=null){
            condition.setType(assetType);
        }
        List<AssetDTO> assetDTOS = AppInterfaceUtil.getResponseDataThrowException(assetClient.listAsset(condition));


        return buildAsserAdapter(assetDTOS.get(0));
    }
}
