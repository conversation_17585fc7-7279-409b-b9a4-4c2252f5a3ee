package com.charge.api.web.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 产权车位可用周期返回体
 * @Author: yjw
 * @Date: 2024/3/12 17:38
 */
@Data
public class ParkingPlacePeriodVO  implements Serializable {

    private static final long serialVersionUID = -6302775957066820241L;

    private Long itemId;

    private String itemName;

    private String validStartTime;

    private String validEndTime;

    private String parkingSpaceMsId;

}
