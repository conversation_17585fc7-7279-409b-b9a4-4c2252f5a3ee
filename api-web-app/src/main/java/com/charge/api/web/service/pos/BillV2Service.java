package com.charge.api.web.service.pos;

import com.charge.api.web.vo.pos.v3.CreateBillReq;
import com.charge.api.web.vo.pos.v3.CreateBillResp;
import com.charge.api.web.vo.pos.v3.PayResultVO;
import com.charge.common.exception.ChargeBusinessException;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/10/19 9:47
 */
public interface BillV2Service {

    CreateBillResp createBill(CreateBillReq createOrderReq) throws ChargeBusinessException;

    PayResultVO getPayResult(Long communityId, String orderNum) throws ChargeBusinessException;
}
