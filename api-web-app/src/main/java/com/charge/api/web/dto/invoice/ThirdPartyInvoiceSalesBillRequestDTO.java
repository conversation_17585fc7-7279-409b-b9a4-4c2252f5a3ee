package com.charge.api.web.dto.invoice;

import com.charge.api.web.dto.ThirdPartyInvoiceSalesBillItemDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/09/20/ 12:22
 * @description
 */
@Data
public class ThirdPartyInvoiceSalesBillRequestDTO implements Serializable {
    private static final long serialVersionUID = -6352278436222345782L;

    private List<ThirdPartyInvoiceSalesBillItemDTO> salesBillItems;

    private ThirdPartyInvoiceSalesBillMainDTO salesBillMain;
}
