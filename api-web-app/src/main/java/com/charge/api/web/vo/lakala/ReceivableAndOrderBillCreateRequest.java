package com.charge.api.web.vo.lakala;

import com.charge.api.web.annotation.NormalCharacter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceivableAndOrderBillCreateRequest {

    private String deviceInfo;
    private String mercid;
    private String houseUuid;
    /**
     * 1 - 转账 2 - 刷卡 4 - 扫码  6 - 支票
     */
    private String paymentMethod;
    private String collectorId;
    private String collectorName;
    @NotEmpty(message = "欠费项ID为空")
    private String payItemIds;
    @NotEmpty(message = "欠费项金额为空")
    private String arrearsPrice;
    /**
     * "临时收费数据，json数组字符串 ([{\"tempItemId\":\"123\",\"itemName\":\"临时费用1\"," +
     * "\"incomeDetailUuid\":\"200\",\"incomeDetailName\":\"收费细项名称\",\"price\":\"12.10\"},{\"tempItemId\":\"124\",\"itemName\":\"临时费用2\"," +
     *  "\"incomeDetailUuid\":\"200\",\"incomeDetailName\":\"收费细项名称\",\"price\":\"1.01\"}])"
     */
    private String tempChargeData;
    @NotEmpty(message = "缴费总金额为空")
    private String totalPrice;
    private String payMember;
    private String bankTransactionNo;
    private String arrivalDate;
    private String bankAccountUuid;
    private String bankAccountNum;
    @NormalCharacter
    private String memo;
    private String token;
}
