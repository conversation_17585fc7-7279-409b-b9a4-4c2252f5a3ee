package com.charge.api.web.vo.pos.v3;

import com.charge.api.web.service.order.CreateOrderContext;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 创建订单的响应
 *
 * @Author: yjw
 * @Date: 2023/10/19 14:53
 */
@Data
public class CreateBillResp {

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 外部订单号（拉卡拉订单号）
     */
    private String externalOrderNum;

    /**
     * 订单号（收费系统订单号）
     */
    private String orderNum;

    /**
     * 资产流水id
     */
    private String id;

    @JsonIgnore
    private CreateOrderContext context;

}
