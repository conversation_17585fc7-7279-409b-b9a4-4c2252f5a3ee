package com.charge.api.web.service.bill.pos;


import com.charge.api.web.vo.lakala.BatchBillCreateRequest;
import com.charge.api.web.vo.pos.HouseReceivableItems;
import com.charge.bill.dto.BillAssetInfoDTO;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.domain.AssetPaymentDetailDTO;
import com.charge.bill.dto.domain.AssetsPayDTO;
import com.charge.bill.dto.domain.response.CreateBillsResponse;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * pos多房间缴费下单
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddBatchPayOrderService {

    private final CreateBillFlowService createOrderService;

    /**
     *
     * @param request      传递的Request信息
     * @param communityDTO 项目信息
     * @param assetMap  房屋资产信息MAP
     * @param houseReceivableItems 所有的缴欠费的房屋信息
     * @param receivableMap 所有的缴欠费的应收单信息
     * @return
     * @throws ChargeBusinessException
     */
    public CreateBillsResponse createBills(BatchBillCreateRequest request, CommunityDTO communityDTO,
                                          Map<Long, AssetDTO> assetMap,
                                          List<HouseReceivableItems> houseReceivableItems,
                                          Map<Long, ReceivableBillDTO> receivableMap) throws ChargeBusinessException {
        AssetsPayDTO assetsPayDTO = new AssetsPayDTO();
        assetsPayDTO.setAssetPayBaseDTO(buildAssetPayBaseDTO(request, communityDTO, houseReceivableItems.size()));
        assetsPayDTO.setAssetPaymentDetailDTOS(buildDetails(assetMap, houseReceivableItems, receivableMap, communityDTO));
        return createOrderService.createAssetsOrder(assetsPayDTO);
    }

    /**
     * 基础信息
     * @param request
     * @param communityDTO
     * @param houseSize
     * @return
     * @throws ChargeBusinessException
     */
    private AssetPayBaseDTO buildAssetPayBaseDTO(BatchBillCreateRequest request, CommunityDTO communityDTO, int houseSize) throws ChargeBusinessException {
        AssetPayBaseDTO assetPayBaseDTO = new AssetPayBaseDTO();
        assetPayBaseDTO.setCommunityId(communityDTO.getId());
        assetPayBaseDTO.setCommunityName(communityDTO.getName());
        assetPayBaseDTO.setOrderNum(IdGeneratorSupport.getIstance().nextId());
        assetPayBaseDTO.setOutTransactionNo(request.getBankTransactionNo());
        assetPayBaseDTO.setActualPrice(new BigDecimal(request.getTotalPrice()));
        assetPayBaseDTO.setPaymentMethod(FillCommonUtil.toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())).getPaymentCode());
        assetPayBaseDTO.setPayMember(request.getPayMember());
        assetPayBaseDTO.setCollectorId(request.getCollectorId());
        assetPayBaseDTO.setCollectorName(request.getCollectorName());
        assetPayBaseDTO.setMemo(request.getMemo());
        assetPayBaseDTO.setPaymentTerminal(PaymentTerminalEnum.POS.getCode());
        assetPayBaseDTO.setPayHouseCount(houseSize);
        assetPayBaseDTO.setDeviceInfo(request.getDeviceInfo());
        assetPayBaseDTO.setMercid(request.getMercid());
        assetPayBaseDTO.setClientSourceEnum(ClientSourceEnum.POS_BATCH_HOUSE_PAY);
        FillCommonUtil.fillAssetPayBaseCommon(request.getPaymentMethod(), assetPayBaseDTO);
        return assetPayBaseDTO;
    }

    /**
     * 缴费信息
     * @param assetMap
     * @param houseReceivableItems
     * @param receivableMap
     * @return
     */
    private List<AssetPaymentDetailDTO> buildDetails(Map<Long, AssetDTO> assetMap, List<HouseReceivableItems> houseReceivableItems, Map<Long, ReceivableBillDTO> receivableMap, CommunityDTO communityDTO) {
        return houseReceivableItems.stream().map(houseReceivableItem -> {
            List<ReceivableBillDTO> billList = houseReceivableItem.getItemList().stream().map(item -> receivableMap.get(Long.parseLong(item.getItemId()))).collect(Collectors.toList());
            AssetDTO assetDTO = assetMap.get(Long.parseLong(houseReceivableItem.getHouseId()));
            return buildAssetPaymentDetailDTO(assetDTO, billList, communityDTO);
        }).collect(Collectors.toList());
    }
    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(AssetDTO assetDTO, List<ReceivableBillDTO> receivableBillDTOS, CommunityDTO communityDTO) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 资产信息
        BillAssetInfoDTO billAssetInfoDTO = FillCommonUtil.buildBillAssetInfo(assetDTO);
        billAssetInfoDTO.setCommunityId(communityDTO.getId());
        billAssetInfoDTO.setCommunityName(communityDTO.getName());
        assetPaymentDetailDTO.setBillAssetInfoDTO(billAssetInfoDTO);
        // 缴欠费应收单信息
        assetPaymentDetailDTO.setReceivableBillDTOS(FillCommonUtil.buildPayRec(receivableBillDTOS));
        return assetPaymentDetailDTO;
    }
}
