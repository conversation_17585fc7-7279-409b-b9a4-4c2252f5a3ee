package com.charge.api.web.controller.joylife.bill;

import com.charge.api.web.controller.joylife.bill.request.CreateOrderRequest;
import com.charge.api.web.controller.joylife.bill.response.ItemsVO;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.dto.joylife.PayResultV1;
import com.charge.api.web.service.bill.joy.CreateOrderService;
import com.charge.api.web.service.bill.joy.query.BillQueryService;
import com.charge.api.web.service.joylife.IncomeBillService;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.support.ConverterSupport;
import com.charge.api.web.vo.joylife.CreateBatchAssetBillReq;
import com.charge.bill.client.AssetTransactionClient;
import com.charge.bill.dto.income.AssetTransactionConditionDTO;
import com.charge.bill.dto.income.AssetTransactionDTO;
import com.charge.bill.dto.income.IncomeOrderInfosDTO;
import com.charge.bill.dto.income.WorkOrderInfosDTO;
import com.charge.bill.dto.income.joylife.AssetBillConditionDTO;
import com.charge.bill.dto.income.joylife.BatchAssetBillConditionDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.TraceContextUtil;
import com.charge.joylife.dto.AssetBillInfoQueryDTO;
import com.charge.joylife.dto.AssetBillListQueryDTO;
import com.charge.joylife.dto.BatchPayInfoDTO;
import com.charge.joylife.vo.AssetBillVO;
import com.charge.joylife.vo.BillInfoVO;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.pay.client.PointClient;
import com.charge.pay.client.PointsTradeRecordClient;
import com.charge.pay.dto.point.ChargePointsTransRecordDTO;
import com.charge.pay.dto.point.PointTradeDetailDTO;
import com.charge.pay.dto.point.PointsQueryConditionDTO;
import com.charge.pay.dto.point.PointsTransRecordConditionDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 对接朝昔
 * <AUTHOR>
 * @Description:实收单
 * @date 2022/12/1416:24
 */
@Api(value = "实收单接口")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class IncomeBillController {
    private final CommunityClient communityClient;
    private final IncomeBillService incomeBillService;
    private final AssetClient assetClient;
    private final AssetSupport assetSupport;
    private final CommunitySupport communitySupport;
    private final AssetTransactionClient assetTransactionClient;
    private final PointClient pointClient;
    private final PointsTradeRecordClient pointsTradeRecordClient;
    private final CreateOrderService createOrderService;
    private final BillQueryService billQueryService;


    /**
     * 根据资产id查询缴费记录列表
     *
     */
    @PostMapping("/api/zhaoxi/asset/billList")
    public ChargeResponse<PagingDTO<AssetBillVO>> getAssetBillList(@RequestBody AssetBillListQueryDTO params,HttpServletRequest request) throws ChargeBusinessException {
        String communityMsId =Objects.nonNull(params.getCommunityMsId())? params.getCommunityMsId():request.getHeader("X-CHARGE-MSID");
        log.info("{}|查询支付结果，url= {}, X-CHARGE-MSID={}", LogCategoryEnum.BUSSINESS, request.getRequestURI(), communityMsId);
        CommunityDTO communityDTO= AppInterfaceUtil.getResponseDataThrowException(communityClient.oneByCondition(CommunityCondition.builder().msId(communityMsId).build()));
        if(Objects.isNull(communityDTO)){
            return new ChargeResponse<>(ErrorInfoEnum.E2003);
        }
        TraceContextUtil.setCommunityId(communityDTO.getId());
        List<AssetBillConditionDTO>  conditionDTOList=new ArrayList<>();
        Map<Long, AssetAdapter>  assetAdapterHashMap=new HashMap<>();
       if(CollectionUtil.isNotEmpty(params.getHouseMsIdList())){
           List<String> houseMsList=params.getHouseMsIdList().stream().map(item->item.getAssetMsId()).collect(Collectors.toList());
           List<AssetDTO> houseList = assetClient.listAssetLessInfo(AssetCondition.builder().communityId(communityDTO.getId())
                   .msIds(houseMsList).type(AssetTypeEnum.HOUSE.getCode()).build()).getContent();
           for (AssetDTO assetDTO : houseList) {
               assetAdapterHashMap.put(assetDTO.getId(),AssetSupport.buildAsserAdapter(assetDTO));
           }
           Map<String,Long>  houseMsAssetIdMap=  houseList.stream().filter(item->Objects.nonNull(item.getHouseDTO())).collect(Collectors.toMap(item->item.getHouseDTO().getMsId(),AssetDTO::getId,(v1,v2)->v1));
           params.getHouseMsIdList().forEach(item->{
               if(Objects.nonNull((houseMsAssetIdMap.get(item.getAssetMsId())))){
                   AssetBillConditionDTO conditionDTO=new  AssetBillConditionDTO();
                   conditionDTO.setBeginTime(item.getBeginTime());
                   conditionDTO.setEndTime(item.getEndTime());
                   conditionDTO.setAssetId(houseMsAssetIdMap.get(item.getAssetMsId()));
                   conditionDTOList.add(conditionDTO);
               }
           } );
       }
        if(CollectionUtil.isNotEmpty(params.getParkingMsIdList())){
            List<String> houseMsList=params.getParkingMsIdList().stream().map(item->item.getAssetMsId()).collect(Collectors.toList());
            List<AssetDTO> parkingList = assetClient.listAssetLessInfo(AssetCondition.builder().communityId(communityDTO.getId())
                    .msIds(houseMsList).type(AssetTypeEnum.PARKING_SPACE.getCode()).build()).getContent();
            for (AssetDTO assetDTO : parkingList) {
                assetAdapterHashMap.put(assetDTO.getId(),AssetSupport.buildAsserAdapter(assetDTO));
            }
            Map<String,Long>  parkingMsAssetIdMap=  parkingList.stream().filter(item->Objects.nonNull(item.getParkingSpaceDTO())).collect(Collectors.toMap(item->item.getParkingSpaceDTO().getMsId(),AssetDTO::getId,(v1, v2)->v1));
            params.getParkingMsIdList().forEach(item->{
                if(Objects.nonNull((parkingMsAssetIdMap.get(item.getAssetMsId())))){
                    AssetBillConditionDTO conditionDTO=new  AssetBillConditionDTO();
                    conditionDTO.setBeginTime(item.getBeginTime());
                    conditionDTO.setEndTime(item.getEndTime());
                    conditionDTO.setAssetId(parkingMsAssetIdMap.get(item.getAssetMsId()));
                    conditionDTOList.add(conditionDTO);
                }
            } );
        }
        if(CollectionUtil.isEmpty(conditionDTOList)){
            return new ChargeResponse<>(new PagingDTO<>(Collections.emptyList(),params.getCurrentPage(),params.getPageSize(),0));
        }
        BatchAssetBillConditionDTO conditionDTO=new BatchAssetBillConditionDTO();
        conditionDTO.setCommunityId(communityDTO.getId());
        conditionDTO.setPageNum(params.getCurrentPage());
        conditionDTO.setPageSize(params.getPageSize());
        conditionDTO.setAssetBillCondition(conditionDTOList);
        conditionDTO.setPayMemberId(params.getPayMemberId());
        PagingDTO<AssetTransactionDTO> data= AppInterfaceUtil.getResponseDataThrowException(assetTransactionClient.getAssetTransactionByAssetIdsAndTime(conditionDTO));
        if(CollectionUtil.isNotEmpty(data.getList())){
            List<AssetBillVO> assetBillVOList=new ArrayList<>(data.getList().size());
            for (AssetTransactionDTO trBill : data.getList()) {
                assetBillVOList.add(ConverterSupport.convertTrDtoToAssetBillVo(trBill,communityMsId,assetAdapterHashMap));
            }
            communitySupport.cacheCommunityIdWithAssetTrId(data.getList().stream().map(item->String.valueOf(item.getId())).collect(Collectors.toList()), communityDTO.getId());
            return new ChargeResponse<>(new PagingDTO<>(assetBillVOList,params.getCurrentPage(),params.getPageSize(),data.getTotalCount()));

        }
        return new ChargeResponse<>(new PagingDTO<>(Collections.emptyList(),params.getCurrentPage(),params.getPageSize(),data.getTotalCount()));
    }


    /**
     * 获取资产实收单详情
     * @return
     */
    @PostMapping("/api/zhaoxi/app/asset/billInfo")
    public ChargeResponse<BillInfoVO> getAssetBillInfo(@RequestBody AssetBillInfoQueryDTO conditionDTO,HttpServletRequest request) throws ChargeBusinessException {
        String communityMsId =Objects.nonNull(conditionDTO.getCommunityMsId())? conditionDTO.getCommunityMsId():request.getHeader("X-CHARGE-MSID");
        log.info("{}|查询支付结果，url= {}, X-CHARGE-MSID={}", LogCategoryEnum.BUSSINESS, request.getRequestURI(), communityMsId);
        CommunityDTO communityDTO= AppInterfaceUtil.getResponseDataThrowException(communityClient.oneByCondition(CommunityCondition.builder().msId(communityMsId).build()));
        if(Objects.isNull(communityDTO)){
            return new ChargeResponse<>(ErrorInfoEnum.E2003);
        }
        TraceContextUtil.setCommunityId(communityDTO.getId());
        List<AssetTransactionDTO> dataList=AppInterfaceUtil.getResponseDataThrowException(assetTransactionClient.selectTransactionWriteOffReceivableBillDetail(AssetTransactionConditionDTO.builder()
                .idList(Arrays.asList(conditionDTO.getTransactionId())).communityId(communityDTO.getId().toString()).build()));
        if(CollectionUtil.isEmpty(dataList)){
            return new ChargeResponse(ErrorInfoEnum.E2003);
        }
        AssetTransactionDTO assetTransactionDTO=dataList.get(0);
        List<Long> predepositBillId=new ArrayList<>();
        PointTradeDetailDTO earnPoints=null;
        if(CollectionUtil.isNotEmpty(assetTransactionDTO.getPredepositBillDTOList())){
            predepositBillId.addAll(assetTransactionDTO.getPredepositBillDTOList().stream().map(item->item.getId()).collect(Collectors.toList()));
            PointsQueryConditionDTO pointsQueryCondition=new PointsQueryConditionDTO();
            pointsQueryCondition.setPredepostIdList(predepositBillId);
            earnPoints=AppInterfaceUtil.getResponseData(pointClient.pointsEarnQueryByCondition(pointsQueryCondition));
        }

        //查询抵扣积分记录
        List<ChargePointsTransRecordDTO> pointRedeemList=new ArrayList<>();
        if(CollectionUtil.isNotEmpty(assetTransactionDTO.getEquityAdjustBillDetailDTOList())){
            List<Long> idList=assetTransactionDTO.getEquityAdjustBillDetailDTOList().stream().map(item->item.getPointsTransId()).collect(Collectors.toList());
            pointRedeemList = AppInterfaceUtil.getResponseData(pointsTradeRecordClient.getByCondition(PointsTransRecordConditionDTO.builder()
                    .communityId(communityDTO.getId()).idList(idList).build()));
        }
        AssetDTO assetDTO = assetSupport.getAsset(assetTransactionDTO.getAssetId());
        AssetAdapter adapter=null;
        if(Objects.nonNull(assetDTO)){
            adapter=AssetSupport.buildAsserAdapter(assetDTO);
        }
        BillInfoVO billInfoVO=ConverterSupport.convertTrDtoToAssetBillVoDetail(assetTransactionDTO,earnPoints,pointRedeemList,adapter);
        if(CollectionUtil.isNotEmpty(billInfoVO.getDataInfoList())){
            List<String>  trIds=billInfoVO.getDataInfoList().stream().map(item->item.getTranFlowId()).collect(Collectors.toList());
            communitySupport.cacheCommunityIdWithAssetTrId(trIds, communityDTO.getId());
        }
        return new ChargeResponse<>(billInfoVO);

    }

    /**
     * 支付下单
     * @param incomeOrderInfosDTO
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/app/income/bill/create")
    public ChargeResponse<Long> createIncomeBills(@RequestBody @Validated IncomeOrderInfosDTO incomeOrderInfosDTO) throws ChargeBusinessException {

        ChargeResponse<Long>  response= incomeBillService.createIncomeBills(incomeOrderInfosDTO);
         return new ChargeResponse(response);
    }


    /**
     * 工单下单
     * @param workOrderInfosDTO
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/app/work/order/create")
    public ChargeResponse<Long> createWorkOrderBills(@RequestBody @Validated WorkOrderInfosDTO workOrderInfosDTO) throws ChargeBusinessException {

        ChargeResponse<Long>  response= incomeBillService.createWorkOrderBills(workOrderInfosDTO);
        return new ChargeResponse(response);
    }

    @ApiOperation(value = "查询支付结果")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orderNum", value = "交易订单号", required = true, dataType = "String"),
    })
    @GetMapping("/app/getPayResult")
    public ChargeResponse<PayResultV1> queryPayResult(String orderNum) throws ChargeBusinessException{
        return incomeBillService.getPayResult(orderNum);
    }

    /**
     * 朝昔下单处理
     * @param createOrderRequest
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/app/order/create")
    public ChargeResponse<Object> createOrder(@RequestBody @Validated CreateOrderRequest createOrderRequest) throws ChargeBusinessException {
        return createOrderService.createOrder(createOrderRequest);
    }

    @ApiOperation(value = "根据实收单查询收费项列表", notes = "根据实收单查询收费项列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityMsId", value = "朝昔小区ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "incomeBillIds", value = "实收单IDS", required = true, dataType = "List"),
    })
    @GetMapping("/app/order/query/items")
    public ChargeResponse<List<ItemsVO>> queryBillItems(@RequestParam String communityMsId, @RequestParam List<Long> incomeBillIds) throws ChargeBusinessException {
        return billQueryService.queryItems(communityMsId, incomeBillIds);
    }

    /**
     * 朝昔批量资产下单（支持预存+缴费）
     * @param createBatchAssetBillReq
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/app/order/batchAsset/create")
    public ChargeResponse<BatchPayInfoDTO> batchAssetCreateOrder(@RequestBody @Valid CreateBatchAssetBillReq createBatchAssetBillReq) throws ChargeBusinessException {
        return createOrderService.batchAssetCreateOrder(createBatchAssetBillReq);
    }

}
