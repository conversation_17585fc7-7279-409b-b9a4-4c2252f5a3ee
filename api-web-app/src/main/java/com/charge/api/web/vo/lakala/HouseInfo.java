package com.charge.api.web.vo.lakala;

import com.charge.common.serializer.DesensitizeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/5/18.
 */
@Data
@Builder
public class HouseInfo {

    private String communityName;//小区名称
    private String buildingName;//楼栋名称
    private String unitName;//单元名称
    private String houseName;//房屋名称
    private String buildArea;//建筑面积
    private String insideArea;//内部面积
    private String chargeArea;//收费面积
    private String residentUuid;//住户ID
    private String residentName;//住户姓名
    @JsonSerialize(using = DesensitizeSerializer.class)
    private String mobile;//手机号码
    private int complaintNum;//投诉报事数量
    private String receiptNumber; //收据号
    private String memo; //备注
    /**
     * 欠费金额
     */
    private BigDecimal arrearAmount;
    /**
     * 押金金额
     */
    private BigDecimal depositAmount;
    /**
     * 余额
     */
    private BigDecimal balanceAmount;
    private BigDecimal waitingCarryForwardTotal;
}
