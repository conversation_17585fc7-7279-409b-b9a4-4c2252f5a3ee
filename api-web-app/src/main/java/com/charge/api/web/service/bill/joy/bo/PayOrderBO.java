package com.charge.api.web.service.bill.joy.bo;

import com.charge.api.web.vo.joylife.PrestoreChargeOrderInfo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 创建支付单数据
 */
@Data
public class PayOrderBO {

    /**
     * 小区收费系统ID
     */
    @NotBlank(message = "小区Id参数缺失,请重试")
    String communityId;

    String orderNum;

    /**
     * 支付来源(微信小程序：WECHAT_APPLET，悦家APP：YUEHOME_PAY，润钱包：CRT_PAY)
     */
    @NotBlank(message = "支付来源参数缺失,请重试")
    String paymentSource;

    /**
     * 支付人
     */
    String payMember;

    /**
     * 支付总金额
     */
    String totalPrice;

    /**
     * 终端IP
     */
    @NotBlank(message = "终端IP参数缺失,请重试")
    String mchCreateIp;

    /**
     * AppID
     */
    String subAppid;

    /**
     * 微信用户小程序关注商家的openid(小程序缴费必传)
     */
    String subOpenid;

    /**
     * 支付人(当前登录用户姓名)
     */
    private String userName;

    /**
     * 客户ID（当前登录用户唯一标识，记录用户缴费历史)
     */
    private String userId;

    /**
     * 缴费明细详情
     */
    private List<PrestoreChargeOrderInfo> chargeOrderInfoList;

    /**
     * 买家支付宝账号
     */
    private String buyerLogonId;

    /**
     * 支付方式（支付宝：2,微信支付：4）
     */
    private String paymentMethod;
}
