package com.charge.api.web.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单vo
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderVO extends BaseOrderVO {

    private static final long serialVersionUID = 8175996290471580937L;
    /**
     * 二级分类id
     */
    private String secondClassificationId;

    /**
     * 0-已下单，1-进行中，2-已完成
     */
    private Integer status;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 收费项名称
     */
    private String itemNames;

    /**
     * 货物名称
     */
    private String goodsName;

    /**
     * 初始订单金额
     */
    private BigDecimal originalAmount;

    /**
     * 订单支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 开票金额
     */
    private BigDecimal invoicedAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 支付模式：1-分期支付，2-全额支付
     */
    private Integer payMode;

    /**
     * 订单优惠类型
     */
    private String discountType;

    /**
     * 订单优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 支付状态：0-待支付，1-已支付，2-部分支付，3-全额退款，4-部分退款
     */
    private Integer payStatus;

    /**
     * 订单完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishTime;

    /**
     * 订单完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;

}
