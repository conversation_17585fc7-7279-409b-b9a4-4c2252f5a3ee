package com.charge.api.web.dto.invoice;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date: 2023/10/13/ 18:13
 * @description
 */
@Data
public class ThirdPartyInvoiceRelationRequestDTO implements Serializable {
    private static final long serialVersionUID = 8887602956181039274L;

    /**
     * 预制发票对应结算单明细含税金额
     */
    private BigDecimal amountWithTax;

    /**
     * 预制发票对应结算单明细不含税金额
     */
    private BigDecimal amountWithoutTax;

    /**
     * 预制发票对应结算单明细税额
     */
    private BigDecimal taxAmount;

    /**
     * 结算单明细号
     */
    private String salesbillItemNo;
}
