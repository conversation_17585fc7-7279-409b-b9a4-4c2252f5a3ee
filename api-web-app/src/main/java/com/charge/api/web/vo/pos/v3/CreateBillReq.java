package com.charge.api.web.vo.pos.v3;

import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentTerminalEnum;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/10/19 16:13
 */
@Data
public class CreateBillReq implements Serializable {

    private static final long serialVersionUID = 1707187054972002789L;

    /**
     * 项目ID
     */
    @NotNull(message = "项目id不能为空")
    private Long communityId;

    /**
     * 项目名称
     */
    private String communityName;

    /**
     * 收费员ID
     */
    @NotBlank(message = "收费员不能为空")
    private String collectorId;

    /**
     * 收费员姓名
     */
    @NotBlank(message = "收费员姓名为空")
    private String collectorName;

    /**
     * 缴费人
     */
    private String payMember;

    /**
     * 缴费总金额
     */
    @NotNull(message = "总收费金额不能为空")
    private BigDecimal totalAmount;

    /**
     * 支付方式,SCAN_CODE 扫码 BANK_CARD 刷卡
     */
    @NotBlank(message = "支付方式不能为空")
    private String paymentMethod;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 备注
     */
    private String memo;

    @Valid
    @NotEmpty(message = "账单不能为空")
    private List<AssetBill> bills;

    /**
     * 设备
     */
    private String device;

    /**
     * 支付终端  POS_UNIONPAY,POS
     */

    private PaymentTerminalEnum paymentTerminal;
    private PaymentChannelEnum paymentChannel;

    private Long arrearsNoticeId;
}
