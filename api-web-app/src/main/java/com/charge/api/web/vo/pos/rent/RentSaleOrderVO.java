package com.charge.api.web.vo.pos.rent;

import com.charge.common.serializer.BigDecimalSerializer;
import com.charge.common.serializer.IdSerializer;
import com.charge.common.util.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/12/11 10:50
 */

@Data
public class RentSaleOrderVO {

    /**
     * 订单号（主键id）
     */
    @JsonSerialize(using = IdSerializer.class)
    private Long id;

    /**
     * 主订单号
     */
    private String extendOrderNo;

    /**
     * 商品订单号
     */
    private String extendOrderId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 支付状态：0-待支付1-已支付，2-部分支付，3-全额退款，4-部分退款
     */
    private Integer payStatus;

    /**
     * 订单下单时间
     */
    @JsonFormat(pattern = DateUtils.FORMAT_0, timezone = "GMT+8")
    private Date orderTime;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = DateUtils.FORMAT_0, timezone = "GMT+8")
    private Date paymentTime;
    /**
     * 应收金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal amount;
    /**
     * 实收金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal paymentAmount;
    /**
     * 欠费金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal arrearsAmount;

    /**
     * 费项id
     */
    private Long itemId;
    /**
     * 费项名称
     */
    private String itemName;

    /**
     * 商品名称
     */
    private String goodsName;

}
