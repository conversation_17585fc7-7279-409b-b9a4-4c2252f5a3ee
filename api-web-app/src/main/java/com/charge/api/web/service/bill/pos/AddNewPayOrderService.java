package com.charge.api.web.service.bill.pos;

import com.charge.api.web.vo.lakala.ReceivableAndOrderBillCreateRequest;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.domain.AssetPayDTO;
import com.charge.bill.dto.domain.AssetPaymentDetailDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.maindata.pojo.dto.AssetDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.List;

/**
 * POS缴费下单
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddNewPayOrderService {

    private final CreateBillFlowService createOrderService;

    /**
     * 创建账单
     * @param request
     * @param asset
     * @param receivableBills
     * @return
     */
    public CreateBillResponse createBill(ReceivableAndOrderBillCreateRequest request, AssetDTO asset, List<ReceivableBillDTO> receivableBills) throws ChargeBusinessException {
        return createOrderService.createOrder(buildAssetPayDTO(request, asset, receivableBills));
    }

    private AssetPayDTO buildAssetPayDTO(ReceivableAndOrderBillCreateRequest request, AssetDTO asset, List<ReceivableBillDTO> receivableBills) throws ChargeBusinessException {
        AssetPayDTO assetPayDTO = new AssetPayDTO();
        assetPayDTO.setAssetPayBaseDTO(buildAssetPayBaseDTO(request, asset));
        assetPayDTO.setAssetPaymentDetailDTO(buildAssetPaymentDetailDTO(request, receivableBills, asset));
        return assetPayDTO;
    }

    private AssetPayBaseDTO buildAssetPayBaseDTO(ReceivableAndOrderBillCreateRequest request, AssetDTO asset) throws ChargeBusinessException {
        AssetPayBaseDTO assetPayBaseDTO = new AssetPayBaseDTO();
        FillCommonUtil.fillAssetPayBaseCommunityInfo(asset, assetPayBaseDTO);
        assetPayBaseDTO.setOrderNum(IdGeneratorSupport.getIstance().nextId());
        assetPayBaseDTO.setOutTransactionNo(request.getBankTransactionNo());
        assetPayBaseDTO.setActualPrice(new BigDecimal(request.getTotalPrice())); // 实收金额
        assetPayBaseDTO.setPaymentMethod(FillCommonUtil.toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())).getPaymentCode());
        assetPayBaseDTO.setPayMember(request.getPayMember());
        assetPayBaseDTO.setCollectorId(request.getCollectorId());
        assetPayBaseDTO.setCollectorName(request.getCollectorName());
        assetPayBaseDTO.setBankAccountNo(request.getBankAccountNum());
        assetPayBaseDTO.setMemo(request.getMemo());
        assetPayBaseDTO.setBankAccountUuid(request.getBankAccountUuid());
        assetPayBaseDTO.setPaymentTerminal(PaymentTerminalEnum.POS.getCode());
        assetPayBaseDTO.setClientSourceEnum(ClientSourceEnum.POS_NORMAL_PAY);
        assetPayBaseDTO.setPayHouseCount(1);
        return assetPayBaseDTO;
    }


    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(ReceivableAndOrderBillCreateRequest request, List<ReceivableBillDTO> receivableBills, AssetDTO assetDTO) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 资产信息
        assetPaymentDetailDTO.setBillAssetInfoDTO(FillCommonUtil.buildBillAssetInfo(assetDTO));
        // 缴欠费应收单信息
        assetPaymentDetailDTO.setReceivableBillDTOS(FillCommonUtil.buildPayRec(receivableBills));
        // 临时订单信息
        assetPaymentDetailDTO.setOrderItems(FillCommonUtil.buildOrderItem(request.getTempChargeData()));
        return assetPaymentDetailDTO;
    }
}
