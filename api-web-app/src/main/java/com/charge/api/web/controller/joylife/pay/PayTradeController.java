package com.charge.api.web.controller.joylife.pay;

import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.vo.joylife.request.TradeStatusRequestVO;
import com.charge.bill.enums.BillPayStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.pay.client.PayClient;
import com.charge.pay.dto.pay.PayTradeStatusResponseDTO;
import com.charge.pay.dto.pay.TradeStatusRequestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/11 10:00
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PayTradeController {

    /**
     * 查询预支付单的交易状态
     * 如果是1.0 的项目直接返回支付成功
     */

    private  final CommunitySupport communitySupport;

    private final PayClient payClient;

    /**
     * 渠道交易状态查询
     */

    @RequestMapping(value = "/pay/trade/status/query", method = RequestMethod.POST)
    public ChargeResponse<PayTradeStatusResponseDTO> tradeTradeQuery(@Valid @RequestBody TradeStatusRequestVO requestVO) throws ChargeBusinessException {
        //获取v2.0小区id
        Long communityId = communitySupport.getCommunityIdByMsId(requestVO.getCommunityMsId());
        TradeStatusRequestDTO requestDTO = TradeStatusRequestDTO.builder()
                .communityId(communityId)
                .outTradeNo(requestVO.getOrderNum())
                .syncQuery(Objects.isNull(requestVO.isSyncQuery())?Boolean.FALSE:requestVO.isSyncQuery())
                .build();
        return payClient.tradeTradeQuery(requestDTO);

    }




}
