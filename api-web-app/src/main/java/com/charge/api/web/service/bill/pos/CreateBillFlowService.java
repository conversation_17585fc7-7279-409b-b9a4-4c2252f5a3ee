package com.charge.api.web.service.bill.pos;

import com.charge.bill.client.flow.AssetPaymentClient;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.domain.AssetPayDTO;
import com.charge.bill.dto.domain.AssetsPayDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.dto.domain.response.CreateBillsResponse;
import com.charge.bill.enums.BalanceStatusEnum;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.pay.client.PayRecordClient;
import com.charge.pay.dto.PayRecord;
import com.charge.starter.jedis.JedisManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import static com.charge.api.web.constants.LakalaConstans.*;

/**
 * POS创建账单编排服务
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CreateBillFlowService {

    private final PayRecordClient payRecordClient;
    private final AssetPaymentClient assetPaymentClient;
    private final JedisManager jedisManager;

    /**
     * 多资产
     * @param assetsPayDTO
     * @return
     */
    public CreateBillsResponse createAssetsOrder(AssetsPayDTO assetsPayDTO) {
        // 支付处理
        orderPay(assetsPayDTO.getAssetPayBaseDTO());
        // 保存账单
        CreateBillsResponse response = createBills(assetsPayDTO);
        log.info("createAssetsOrder response = {}", response);
        // 通用后置处理
        postBill(assetsPayDTO.getAssetPayBaseDTO().getCommunityId());
        return response;
    }

    /**
     * 单资产
     * @param assetPayDTO
     * @return
     */
    public CreateBillResponse createOrder(AssetPayDTO assetPayDTO) {
        // 支付处理
        orderPay(assetPayDTO.getAssetPayBaseDTO());
        // 保存账单
        CreateBillResponse createBillResponse = createBill(assetPayDTO);
        log.info("createOrder response = {}", createBillResponse);
        // 通用后置处理
        postBill(assetPayDTO.getAssetPayBaseDTO().getCommunityId());
        return createBillResponse;
    }

    private void orderPay(AssetPayBaseDTO assetPayBaseDTO) {
        PayRecord payRecord = new PayRecord()
                .setOrderNum(assetPayBaseDTO.getOrderNum())
                .setPayMember(assetPayBaseDTO.getPayMember())
                .setPayMemberId(assetPayBaseDTO.getPayMember())
                .setPayStatus(0)
                .setPaymentMethod(assetPayBaseDTO.getPaymentMethod())
                .setPaymentChannel(assetPayBaseDTO.getPaymentChannel())
                .setPaymentTerminal(PaymentTerminalEnum.POS.getCode())
                .setMoney(assetPayBaseDTO.getActualPrice()) // 需再次确认
                .setMemo(assetPayBaseDTO.getMemo())
                .setCommunityId(assetPayBaseDTO.getCommunityId())
                .setCommunityName(assetPayBaseDTO.getCommunityName())
                .setGoodsName(assetPayBaseDTO.getGoodsName())
                .setBalanceStatus(BalanceStatusEnum.UNRECONCILED.getCode())
                .setBankAccountNo(assetPayBaseDTO.getBankAccountNo())
                .setBankAccountUuid(assetPayBaseDTO.getBankAccountUuid());
        try {
            ChargeResponse response = payRecordClient.insert(payRecord);
            Assert.isTrue(response.isSuccess(), "写入交易记录异常" + response.getMessage());
        } catch (Exception e) {
            throw new ChargeRuntimeException("调用创建支付记录异常:" + e.getMessage(), e);
        }
    }
    private CreateBillResponse createBill(AssetPayDTO assetPayDTO) {
        try {
            return AppInterfaceUtil.getResponseDataThrowException(assetPaymentClient.createBill(assetPayDTO));
        } catch (Exception e) {
            throw new ChargeRuntimeException("创建账单异常:" + e.getMessage(), e);
        }
    }

    private CreateBillsResponse createBills(AssetsPayDTO assetsPayDTO) {
        try {
            return AppInterfaceUtil.getResponseDataThrowException(assetPaymentClient.createBills(assetsPayDTO));
        } catch (Exception e) {
            throw new ChargeRuntimeException("创建账单异常:" + e.getMessage(), e);
        }
    }


    private void postBill(Long communityId) {
        String account = ThreadContext.get(ACCOUNT);
        //设置操作员的项目
        if(StringUtils.hasText(account)){
            jedisManager.hset(LOGIN_POS+account, COMMUNITY_ID,String.valueOf(communityId),3600*24*14);
        }
    }
}
