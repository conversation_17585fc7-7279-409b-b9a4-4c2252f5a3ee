package com.charge.api.web.service.bill.pos;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.controller.joylife.bill.request.PayOrAdjustItemVO;
import com.charge.api.web.service.pos.impl.TempChargeVO;
import com.charge.api.web.vo.joylife.AssetBillItem;
import com.charge.bill.dto.BillAssetInfoDTO;
import com.charge.bill.dto.PayOrAdjustItemDTO;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.income.AssetPayReceivableBillDTO;
import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentMethodEnum;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import com.charge.maindata.pojo.dto.ParkingSpaceDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.charge.maindata.enums.AssetUseStatusEnum.getCodeByTypeAndOriginalCode;

public class FillCommonUtil {

    /**
     * 填充
     * @param paymentMethod
     * @param assetPayBaseDTO
     * @throws ChargeBusinessException
     */
    public static void fillAssetPayBaseCommon(String paymentMethod, AssetPayBaseDTO assetPayBaseDTO) throws ChargeBusinessException {
        PaymentMethodEnum paymentMethodEnum;
        PaymentChannelEnum paymentChannel;
        if (PayRelatedConstants.PAYMENT_METHOD_POS_CODE.equals(paymentMethod)) {
            paymentMethodEnum = PaymentMethodEnum.CARD;
            paymentChannel = PaymentChannelEnum.BANK;
        } else if (PayRelatedConstants.PAYMENT_METHOD_ALIPAY_CODE.equals(paymentMethod)) {
            paymentMethodEnum = PaymentMethodEnum.ALIPAY;
            paymentChannel = PaymentChannelEnum.ALI_PAY;
        } else if (PayRelatedConstants.PAYMENT_METHOD_WEIXIN_CODE.equals(paymentMethod)) {
            paymentMethodEnum = PaymentMethodEnum.WECHAT;
            paymentChannel = PaymentChannelEnum.WECHAT_PAY;
        } else if (PayRelatedConstants.PAYMENT_METHOD_CHEQUE_CODE.equals(paymentMethod)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "已升级项目不支持POS-支票支付，请选择扫码或刷卡");
        } else {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "已升级项目不支持POS-转账支付，请选择扫码或刷卡");
        }
        assetPayBaseDTO.setPaymentMethod(paymentMethodEnum.getPaymentCode());
        assetPayBaseDTO.setPaymentChannel(paymentChannel.getPaymentChannel());
    }

    /**
     * 支付方式（0表示POS,1表示转账,2表示支票,3表示支付宝,4表示微信）
     */
    public static PaymentMethodEnum toPaymentMethodEnum(Integer v1PaymentMethod) {
        switch (v1PaymentMethod) {
            case 0:
                return PaymentMethodEnum.CARD;
            case 3:
                return PaymentMethodEnum.ALIPAY;
            case 4:
                return PaymentMethodEnum.WECHAT;
            default:
                throw new ChargeRuntimeException("只支持刷卡和扫码");
        }
    }

    /**
     * 临时订单类
     * @param tempChargeData
     * @return
     */
    public static List<PayOrAdjustItemDTO> buildOrderItem(String tempChargeData) {
        List<TempChargeVO> tempChargeVOS = JSON.parseArray(tempChargeData, TempChargeVO.class);
        return tempChargeVOS.stream().map(tempCharge -> {
            PayOrAdjustItemDTO orderItem = new PayOrAdjustItemDTO();
            orderItem.setItemId(Long.parseLong(tempCharge.getTempItemId()));
            orderItem.setItemName(tempCharge.getItemName());
            orderItem.setAmount(new BigDecimal(tempCharge.getPrice()));
            // 校验
            Assert.notNull(orderItem.getItemId(), "预收订单收费项id不能为空");
            Assert.hasText(orderItem.getItemName(), "预收订单收费项name不能为空");
            Assert.notNull(orderItem.getAmount(), "预收订单金额不能为空");
            return orderItem;
        }).collect(Collectors.toList());
    }

    /**
     * 填充AssetPayBaseInfo信息
     * @param asset
     * @throws ChargeBusinessException
     */
    public static void fillAssetPayBaseCommunityInfo(AssetDTO asset, final AssetPayBaseDTO assetPayBaseDTO) throws ChargeBusinessException {
        if(asset.getHouseDTO()!=null){
            HouseDTO houseDTO = asset.getHouseDTO();
            assetPayBaseDTO.setCommunityId(houseDTO.getCommunityId());
            assetPayBaseDTO.setCommunityName(houseDTO.getCommunityName());
        }else if(asset.getParkingSpaceDTO()!=null){
            ParkingSpaceDTO parkingSpaceDTO = asset.getParkingSpaceDTO();
            assetPayBaseDTO.setCommunityId(parkingSpaceDTO.getCommunityId());
            assetPayBaseDTO.setCommunityName(parkingSpaceDTO.getCommunityName());
        }else {
            throw new ChargeBusinessException("该资产无有效的房间或者车位信息");
        }
    }


    /**
     * 构建资产信息
     * @param assetDTO
     * @return
     */
    public static BillAssetInfoDTO buildBillAssetInfo(AssetDTO assetDTO) {
        BillAssetInfoDTO billAssetInfoDTO = new BillAssetInfoDTO();
        billAssetInfoDTO.setAssetId(assetDTO.getId());
        billAssetInfoDTO.setAssetType(assetDTO.getType());
        billAssetInfoDTO.setChargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode());
        List<CustomerDTO> customerDTOS = new ArrayList<>();
        if (assetDTO.getHouseDTO() != null) {
            HouseDTO houseDTO = assetDTO.getHouseDTO();
            billAssetInfoDTO.setCommunityId(houseDTO.getCommunityId());
            billAssetInfoDTO.setCommunityName(houseDTO.getCommunityName());
            billAssetInfoDTO.setAssetName(houseDTO.getHouseName());
            billAssetInfoDTO.setAssetCode(houseDTO.getHouseCode());
            billAssetInfoDTO.setBuildingId(houseDTO.getBuildingId());
            billAssetInfoDTO.setBuildingName(houseDTO.getBuildingName());
            billAssetInfoDTO.setUnitId(houseDTO.getUnitId());
            billAssetInfoDTO.setUnitName(houseDTO.getUnitName());
            billAssetInfoDTO.setAssetUseStatus(getCodeByTypeAndOriginalCode(AssetTypeEnum.HOUSE.getCode(),assetDTO.getHouseDTO().getHouseUseStatus()));
            customerDTOS = houseDTO.getListCustomer();
        } else if (assetDTO.getParkingSpaceDTO() != null) {
            ParkingSpaceDTO parkingSpaceDTO = assetDTO.getParkingSpaceDTO();
            billAssetInfoDTO.setCommunityId(parkingSpaceDTO.getCommunityId());
            billAssetInfoDTO.setCommunityName(parkingSpaceDTO.getCommunityName());
            billAssetInfoDTO.setAssetCode(parkingSpaceDTO.getParkingSpaceCode());
            billAssetInfoDTO.setAssetName(parkingSpaceDTO.getParkingSpaceName());
            billAssetInfoDTO.setAssetUseStatus(getCodeByTypeAndOriginalCode(AssetTypeEnum.PARKING_SPACE.getCode(),parkingSpaceDTO.getUsageState()));
            customerDTOS = parkingSpaceDTO.getListCustomer();
        }
        if (CollectionUtils.isNotEmpty(customerDTOS)) {
            CustomerDTO customerDTO = customerDTOS.get(0);
            billAssetInfoDTO.setOwnerId(customerDTO.getId());
            billAssetInfoDTO.setOwnerName(customerDTO.getCustomerName());
        }
        return billAssetInfoDTO;
    }

    /**
     * 构建缴欠费的应收单信息
     * @param receivableBills
     * @return
     */
    public static List<AssetPayReceivableBillDTO> buildPayRec(List<ReceivableBillDTO> receivableBills) {
        if (CollectionUtils.isEmpty(receivableBills)) {
            return Collections.emptyList();
        }
        return receivableBills.stream()
                .filter(receivableBillDTO -> receivableBillDTO.getPenaltyArrearsAmount().compareTo(BigDecimal.ZERO) > 0 || receivableBillDTO.getArrearsAmount().compareTo(BigDecimal.ZERO) > 0)
                .map(receivableBillDTO -> {
                    // 缴欠费数据校验
                    Assert.hasText(receivableBillDTO.getBelongYears(), "所属年月不能为空");
                    Assert.notNull(receivableBillDTO.getItemId(), "收费项id不能为空");
                    Assert.notNull(receivableBillDTO.getId(), "应收单ID不能为空");
                    Assert.notNull(receivableBillDTO.getChargeObject(), "chargeObject不能为空");
                    Assert.hasText(receivableBillDTO.getItemName(), "收费项name不能为空");
                    // 构建数据
                    AssetPayReceivableBillDTO payRec = new AssetPayReceivableBillDTO();
                    payRec.setItemId(receivableBillDTO.getItemId());
                    payRec.setBelongYears(receivableBillDTO.getBelongYears());
                    payRec.setReceivableBillId(receivableBillDTO.getId());
                    payRec.setChargeObject(chargeObjEnumOfCode(receivableBillDTO.getChargeObject()).getCode());
                    payRec.setAssetUseStatus(receivableBillDTO.getAssetUseStatus());
                    payRec.setItemName(receivableBillDTO.getItemName());
                    payRec.setPenaltyArrearsAmount(receivableBillDTO.getPenaltyArrearsAmount());
                    payRec.setArrearsAmount(receivableBillDTO.getArrearsAmount());
                    payRec.setIncomeAmount(receivableBillDTO.getIncomeAmount());
                    return payRec;
                }).collect(Collectors.toList());
    }

    private static ChargeObjEnum chargeObjEnumOfCode(Integer code) {
        return Arrays.stream(ChargeObjEnum.values()).filter(chargeObjEnum -> chargeObjEnum.getCode().equals(code))
                .findFirst().orElseThrow(() -> new ChargeRuntimeException("不支持的ChargeObject:" + code));
    }

    /**
     * 构建押金/临时订单等DTO
     * @param payOrAdjustItemVOS
     * @return
     */
    public static List<PayOrAdjustItemDTO> buildPayItemDTOS(List<PayOrAdjustItemVO> payOrAdjustItemVOS) {
        if (CollectionUtils.isEmpty(payOrAdjustItemVOS)) {
            return Collections.emptyList();
        }
        return payOrAdjustItemVOS.stream().map(tempCharge -> {
            PayOrAdjustItemDTO orderItem = new PayOrAdjustItemDTO();
            orderItem.setItemId(tempCharge.getItemId());
            orderItem.setItemName(tempCharge.getItemName());
            orderItem.setAmount(new BigDecimal(tempCharge.getAmount()));
            if (StringUtils.isNotEmpty(tempCharge.getSecondClassificationId())) {
                orderItem.setSecondClassificationId(tempCharge.getSecondClassificationId());
            }
            // 校验
            Assert.notNull(orderItem.getItemId(), "预收订单收费项id不能为空");
            Assert.hasText(orderItem.getItemName(), "预收订单收费项name不能为空");
            Assert.notNull(orderItem.getAmount(), "预收订单金额不能为空");
            return orderItem;
        }).collect(Collectors.toList());
    }

    /**
     * 构建预存DTO
     * @param assetBillItems
     * @return
     */
    public static List<PayOrAdjustItemDTO> buildBatchAssetPayItemDTOS(List<AssetBillItem> assetBillItems) {
        if (CollectionUtils.isEmpty(assetBillItems)) {
            return Collections.emptyList();
        }
        return assetBillItems.stream().map(assetBillItem -> {
            PayOrAdjustItemDTO orderItem = new PayOrAdjustItemDTO();
            orderItem.setItemId(assetBillItem.getItemId());
            orderItem.setItemName(assetBillItem.getItemName());
            orderItem.setAmount(assetBillItem.getAmount());
            orderItem.setPoints(String.valueOf(assetBillItem.getPoints()));
            return orderItem;
        }).collect(Collectors.toList());
    }

}
