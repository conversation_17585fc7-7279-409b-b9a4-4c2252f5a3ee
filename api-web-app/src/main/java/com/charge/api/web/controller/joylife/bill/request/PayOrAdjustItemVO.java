package com.charge.api.web.controller.joylife.bill.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PayOrAdjustItemVO implements Serializable {

    private static final long serialVersionUID = -8655337316016796714L;
    /**
     * 专项预存收费项id
     */
    private Long itemId;

    /**
     * 收费项名称
     */
    private String itemName;

    /**
     * 金额
     */
    private String amount;

    /**
     * 收费项二级分类ID
     */
    private String secondClassificationId;
}
