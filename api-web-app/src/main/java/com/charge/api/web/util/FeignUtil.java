package com.charge.api.web.util;

import com.charge.common.dto.ChargeResponse;

public class FeignUtil {

    public static <T> T getContent(ChargeResponse<T> response, String errorMsg) {
        if (response.isSuccess()) {
            T content = response.getContent();
            if (content == null) {
                throw new RuntimeException("查询" + errorMsg + "结果为null");
            }
            return content;
        }
        throw new RuntimeException("查询" + errorMsg + "失败");
    }
}
