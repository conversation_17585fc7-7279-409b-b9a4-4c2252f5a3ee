package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/26 16:00
 */
@Data
public class EBusinessWriteOffOrderConditon {

    /**
     * 项目编码
     */
    @NotBlank(message = "项目编码不能为空")
    private String communityId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 二级订单号
     */
    @NotBlank(message = "二级订单号不能为空")
    private String oid;

    /**
     * 核销单id
     */
    @NotBlank(message = "核销单id不能为空")
    private String verificationId;

    /**
     * 核销时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "核销时间不能为空")
    private String verificationTime;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String skuName;

    /**
     * sku编码
     */
    @NotBlank(message = "sku编码不能为空")
    private String skuCode;

    /**
     * 商品数量
     */
    @NotNull(message = "商品数量不能为空")
    private Long num;
    /**
     * 核销金额
     */
    @NotNull(message = "核销金额不能为空")
    private BigDecimal verificationAmount;
}
