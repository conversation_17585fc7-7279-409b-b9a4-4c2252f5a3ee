package com.charge.api.web.convert;

import com.charge.api.web.vo.order.SubOrderQuery;
import com.charge.api.web.vo.order.SubOrderVO;
import com.charge.common.util.DateMapper;
import com.charge.order.dto.ebuiness.EBusinessBaseOrderDTO;
import com.charge.order.dto.ebuiness.EBusinessSubOrderQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;


/**
 * 缴费通知单转换器
 * <AUTHOR>
 * @description
 * @date 2023/02/28
 */
@Mapper(uses = {DateMapper.class,OrderEnumMapper.class })
public interface EBusinessBaseOrderConverter {

    EBusinessBaseOrderConverter INSTANCE = Mappers.getMapper(EBusinessBaseOrderConverter.class);

    EBusinessSubOrderQuery map(SubOrderQuery subOrderAdd);

    @Mapping(target = "subOrderNo",source = "extendOrderId")
    @Mapping(target = "orderNo",source = "extendOrderNo")
    @Mapping(target = "originalAmount",source = "totalAmount")
    @Mapping(target = "amount",expression = "java(order.getTotalAmount().add(order.getDiscountAmount()))")
    @Mapping(target = "paidAmount",source = "totalPaidAmount")
    @Mapping(target = "payMode",source = "paymentMode")
    SubOrderVO  map(EBusinessBaseOrderDTO order);

}
