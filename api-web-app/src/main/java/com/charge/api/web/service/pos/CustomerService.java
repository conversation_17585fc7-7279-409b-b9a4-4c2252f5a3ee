package com.charge.api.web.service.pos;

import com.charge.api.web.vo.pos.points.AssetCustomerQueryVO;
import com.charge.api.web.vo.pos.points.CustomerPointsVO;
import com.charge.common.exception.ChargeBusinessException;

import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/11/3 14:11
 */
public interface CustomerService {

    List<CustomerPointsVO> getAssetCustomerList(AssetCustomerQueryVO queryVO) throws ChargeBusinessException;
}
