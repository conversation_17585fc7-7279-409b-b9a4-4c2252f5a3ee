package com.charge.api.web.dto.joylife;

import lombok.Data;

import java.io.Serializable;

/**
 * 查询支付结果返回实体
 * Author: yjw
 * Date: 2023/6/1 10:58
 */
@Data
public class PayResultV1 implements Serializable {

    private static final long serialVersionUID = -940743401932046497L;

    /**
     * 订单号
     */
    private String orderNum;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 单据类型：目前写死为"singleOrder"
     */
    private String billType;

    /**
     * 支付状态
     */
    private String payCode;

    /**
     * 交易时间
     */
    private String payTime;

    /**
     * 金额
     */
    private String payMoney;

    /**
     * 违约金金额
     */
    private String payPenalty;

    /**
     * 支付方式
     */
    private Integer paymentMethod;

    /**
     * 支付人
     */
    private String payMember;

    /**
     * 交易号
     */
    private String outTransactionNo;

    /**
     * 手机号
     */
    private String payMemberMobile;
}
