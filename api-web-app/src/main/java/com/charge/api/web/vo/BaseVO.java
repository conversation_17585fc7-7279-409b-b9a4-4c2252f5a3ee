package com.charge.api.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * BaseVO
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
public class BaseVO implements Serializable {
    private static final long serialVersionUID = -5777227033785368756L;
    /**
     * id
     */
    private Long id;
    /**
     * 修改人
     */
    private String modifyUser;
    /**
     * 创建人
     */
    private String createUser;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;
}