package com.charge.api.web.config;

import com.charge.api.web.service.order.CommunityRewardSupport;
import com.charge.common.exception.ChargeBusinessException;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "gray.pay")
@RefreshScope
public class AssetPayGrayConfig {

    @Resource
    private CommunityRewardSupport communityRewardSupport;

    /**
     * 是否开启灰度总开关
     */
    private String masterSwitch;

    /**
     * 是否灰度所有URI
     */
    private String openAllCommunity;

    /**
     * 具体的配置值
     */
    private List<URIGrayConstant> uriGrayConstants;


    public boolean isOpenGray(Long communityId, String uri) throws ChargeBusinessException {
        // 主开关是否开启
        if (!isOpenSwitch(this.masterSwitch)) {
            return false;
        }
        // 酬金制项目不执行
        if (communityRewardSupport.checkCommunityRewardStatus(communityId)) {
            return false;
        }
        // 如果开启"灰度所有项目"开关，则直接返回
        if (isOpenSwitch(this.openAllCommunity)) {
            return true;
        }
        // 如果配置不存在则过滤
        if (CollectionUtils.isEmpty(this.uriGrayConstants)) {
            return false;
        }
        // 遍历
        for (URIGrayConstant constant:this.uriGrayConstants) {
            // 如果URI相同
            if (constant.getUri().equals(uri)) {
                // 如果为空则返回False
                if (StringUtils.isEmpty(constant.getCommunityIds())) {
                    return false;
                }
                // 如果是all表示的是全部开启
                if (constant.getCommunityIds().equalsIgnoreCase("all")) {
                    return true;
                }
                // 转换为项目IDS
                for (String communityIdValue:constant.getCommunityIds().split(",")) {
                    if (Long.valueOf(communityIdValue).equals(communityId)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private boolean isOpenSwitch(String value){
        if(StringUtils.isEmpty(value)){
            return false;
        }
        // 不区分大小写
        return value.equalsIgnoreCase("true");
    }


    @Data
    public static class URIGrayConstant {
        /**
         * 请求的URL
         */
        private String uri;
        /**
         *
         */
        private String communityIds;
    }
}
