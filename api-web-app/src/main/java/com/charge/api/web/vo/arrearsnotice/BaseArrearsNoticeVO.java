package com.charge.api.web.vo.arrearsnotice;

import com.charge.common.serializer.BigDecimalSerializer;
import com.charge.common.serializer.IdSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 基础缴费通知
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@Data
public class BaseArrearsNoticeVO {

    /**
     * 自增主键
     */
    @JsonSerialize(using = IdSerializer.class)
    private Long id;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 资产名称
     */
    private String assetNames;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 欠费金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal arrearsAmount;

    /**
     * 支付状态 （1已支付  0 未支付）
     */
    private Integer payStatus;


}
