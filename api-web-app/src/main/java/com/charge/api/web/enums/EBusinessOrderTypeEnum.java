package com.charge.api.web.enums;

import java.util.Objects;

import static com.charge.order.enums.OrderRuleCategoryEnum.*;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/19 16:23
 */
public enum EBusinessOrderTypeEnum {

    SELF_SUPPORT(1, ONE_LEVEL_E_BUSINESS_500.getCode(), ONE_LEVEL_E_BUSINESS_500.getValue()),
    HOME_SERVICE(2, ONE_LEVEL_E_BUSINESS_501.getCode(), ONE_LEVEL_E_BUSINESS_501.getValue()),
    DECORATION_SERVICE(3, ONE_LEVEL_E_BUSINESS_502.getCode(), ONE_LEVEL_E_BUSINESS_502.getValue()),
    LEASE_INTENTION(4, ONE_LEVEL_LEASE_600.getCode(), ONE_LEVEL_LEASE_600.getValue()),
    LEASE_COMMISSION(5, ONE_LEVEL_LEASE_601.getCode(), ONE_LEVEL_LEASE_601.getValue()),
    PUBLIC_AREA(6, ONE_LEVEL_PUBLIC_AREA_700.getCode(), ONE_LEVEL_PUBLIC_AREA_700.getValue()),
    ;

    /**
     * 订单业务类型
     */
    private Integer orderSubClass;
    /**
     * 订单规则品类code
     */
    private Integer code;
    /**
     * 订单规则品类value
     */
    private String value;

    EBusinessOrderTypeEnum(Integer orderSubClass, Integer code, String value) {
        this.orderSubClass = orderSubClass;
        this.code = code;
        this.value = value;
    }

    public Integer getOrderSubClass() {
        return orderSubClass;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static EBusinessOrderTypeEnum fromOrderSubClass(Integer orderSubClass) {
        for (EBusinessOrderTypeEnum value : values()) {
            if (Objects.equals(value.getOrderSubClass(), orderSubClass)) {
                return value;
            }
        }
        return null;
    }
}
