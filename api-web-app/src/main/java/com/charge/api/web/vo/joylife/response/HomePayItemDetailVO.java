package com.charge.api.web.vo.joylife.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/07
 */
@Data
public class HomePayItemDetailVO implements Serializable {

    /**
     * 小区名称
     */
    private String communityName;
    /**
     * 单元名称
     */
    private String unitName;
    /**
     * 楼栋名称
     */
    private String buildingName;
    /**
     * 房间名称
     */
    private String houseName;
    /**
     * 朝夕房间Id
     */
    private String houseId;
    /**
     * 总金额（包含违约金）
     */
    private BigDecimal totalAmount;
    /**
     * 总欠费（包含违约金）
     */
    private BigDecimal totalArrears;
    /**
     * 已收金额（包含违约金）
     */
    private BigDecimal totalPay;
    /**
     * 待支付金额（包含违约金）
     */
    private BigDecimal totalToPay;
    /**
     * 挂起状态下欠费总金额（包含违约金）
     */
    private BigDecimal totalHold;
    /**
     * 托收状态下欠费总金额（含违约金）
     */
    private BigDecimal totalCollection;
    /**
     * 总违约金金额
     */
    private BigDecimal totalPenaltyAmount;

    private List<HomePayItemDetailItemVO> itemList;

}
