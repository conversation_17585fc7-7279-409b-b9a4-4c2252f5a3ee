package com.charge.api.web.vo.joylife;

import lombok.*;

import java.math.BigDecimal;

/**
 * 预存积分结果
 *
 * <AUTHOR>
 * @date 2023/11/27
 */
@Data
@NoArgsConstructor

public class PreStorePointsVO {
    /**
     * 是否赠送积分
     */
    private Boolean  rewardPoints=Boolean.FALSE;

    /**
     * 赠送的积分数量
     */
    private BigDecimal pointSAmount=BigDecimal.ZERO;

    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 费项id
     */
    private Long chargeItemId;

}


