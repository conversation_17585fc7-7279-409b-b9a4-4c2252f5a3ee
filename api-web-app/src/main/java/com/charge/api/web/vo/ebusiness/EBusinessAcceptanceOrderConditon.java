package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/11/8 13:45
 */
@Data
public class EBusinessAcceptanceOrderConditon {

    /**
     * 项目编码
     */
    @NotBlank(message = "项目编码不能为空")
    private String communityId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 完结时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "完结时间不能为空")
    private String statementTime;

    /**
     * 分期id
     */
    @NotBlank(message = "分期id不能为空")
    private String installmentId;

    /**
     * 状态，1-验收，2-作废
     */
    private Integer status;
}
