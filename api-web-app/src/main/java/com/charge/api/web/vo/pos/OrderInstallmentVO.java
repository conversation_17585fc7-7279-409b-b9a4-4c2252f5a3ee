package com.charge.api.web.vo.pos;

import com.charge.api.web.util.ArabicToChineseNumber;
import com.charge.common.serializer.BigDecimalSerializer;
import com.charge.common.serializer.IdSerializer;
import com.charge.common.util.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * OrderInstallmentVO
 * <p>
 * Description:
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/14
 */
@Data
public class OrderInstallmentVO {
    /**
     * 主键id
     */
    @JsonSerialize(using = IdSerializer.class)
    private Long id;

    /**
     * 进度id
     */
    private String progressId;
    /**
     * 主订单号
     */
    private String extendOrderNo;
    /**
     * 账单排序
     */
    private Integer sort;

    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 订单下单时间
     */
    @JsonFormat(pattern = DateUtils.FORMAT_0, timezone = "GMT+8")
    private Date orderTime;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = DateUtils.FORMAT_0, timezone = "GMT+8")
    private Date paymentTime;
    /**
     * 应收金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal amount;
    /**
     * 实收金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal paymentAmount;
    /**
     * 欠费金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal arrearsAmount;

    /**
     * 备注 ：第一阶段进度单
     */
    private String memo;

    public void fillVo() {
        this.arrearsAmount = amount.subtract(paymentAmount);
        this.memo="第"+ ArabicToChineseNumber.convert(sort)+"阶段进度单";
    }
}