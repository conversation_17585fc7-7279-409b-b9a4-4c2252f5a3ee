package com.charge.api.web.dto.joylife;

import com.charge.api.web.config.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/20
 */
@Data
public class TradeDetailMonthInfo implements Serializable {

    private static final long serialVersionUID = 7690003460872769214L;

    private String chargeMonth;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal monthPay;

    private List<TradeDetailItemInfo> itemList;
}
