package com.charge.api.web.vo.joylife.response;

import com.charge.common.serializer.IdSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BillRelateInfoVO implements Serializable {

    private static final long serialVersionUID = -7896614996398215835L;
    @ApiModelProperty("关联单据id")
    private String relateId;

    @ApiModelProperty("关联单据类型（前端跳转用）跟前端约定-常量类RelatedViewConstant")
    private String relateType;

    @ApiModelProperty("关联单据类型tab页（前端跳转用）跟前端约定-常量类RelatedViewConstant")
    private String relateTab;

    @ApiModelProperty("关联单号（应收单）")
    private String relatedNo;

    @ApiModelProperty("资产id")
    @JsonSerialize(using = IdSerializer.class)
    private Long assetId;

    @ApiModelProperty("资产类型")
    private Integer assetType;

    @ApiModelProperty("展示类型 1 跳转 2 弹窗")
    private Integer showType;

    @ApiModelProperty("红冲调整id")
    @JsonSerialize(using = IdSerializer.class)
    private Long refundId;

    @ApiModelProperty("明细单据id")
    @JsonSerialize(using = IdSerializer.class)
    private Long targetBillId;

    @ApiModelProperty("单据状态")
    private Integer billStatus;

    @ApiModelProperty("错误提示")
    private String errorMsg;

    /**
     * 订单的二级分类id
     */
    private Long secondClassificationId;

}
