package com.charge.api.web.support;

import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.Paging;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.pay.client.BankAccountClient;
import com.charge.pay.dto.CommunityBankAccountGroupDTO;
import com.charge.pay.dto.bank.OrgBankConditionDTO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * 银行账户支撑类
 *
 * <AUTHOR>
 * @date 2024/9/9
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BankAccountSupport {

    private final BankAccountClient bankAccountClient;

    public CommunityBankAccountGroupDTO getByCommunityId(Long communityId) throws ChargeBusinessException {
        OrgBankConditionDTO orgBankConditionDTO = new OrgBankConditionDTO();
        orgBankConditionDTO.setCommunityIdList(Lists.newArrayList(communityId));
        orgBankConditionDTO.setPaging(new Paging(1, 20));
        ChargeResponse<PagingDTO<CommunityBankAccountGroupDTO>> pagingDTOChargeResponse = bankAccountClient.communityGroupPaging(orgBankConditionDTO);
        PagingDTO<CommunityBankAccountGroupDTO> accountGroupPaging = AppInterfaceUtil.getResponseDataThrowException(pagingDTOChargeResponse);
        Assert.notEmpty(accountGroupPaging.getList(), "未查询到项目银行账号信息");
        CommunityBankAccountGroupDTO communityBankAccountGroupDTO = accountGroupPaging.getList().get(0);
        Assert.notEmpty(communityBankAccountGroupDTO.getBankAccounts(), "未查询到项目银行账号信息");
        return communityBankAccountGroupDTO;
    }
}