package com.charge.api.web.dto.yuexin;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ListArrearsByCommunityIdReq {

    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空")
    private String communityMsId;

    /**
     * 楼栋id
     */
    private List<String> buildingMsIdList;

    /**
     * 房号/房间编码
     */
    private String houseName;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    /**
     * 每页数量
     */
    @NotNull(message = "每页数量不能为空")
    private Integer pageSize;

    /**
     * 欠费账龄
     */
    @Pattern(regexp = "^[0-9]*[0-9]*$", message = "账龄只能为非负整数")
    private String arrearsAge;

}
