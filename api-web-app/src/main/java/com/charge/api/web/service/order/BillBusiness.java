package com.charge.api.web.service.order;

import com.charge.bill.enums.BusinessTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 账单业务
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class BillBusiness {

    /**
     * 业务类型
     */
    private BusinessTypeEnum type;

    /**
     * 业务id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 金额
     */
    private BigDecimal money;

    /**
     * 收费项
     */
    private Long chargeItemId;
}


