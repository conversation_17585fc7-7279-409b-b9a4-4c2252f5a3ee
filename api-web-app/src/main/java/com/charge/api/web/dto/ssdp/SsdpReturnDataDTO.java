package com.charge.api.web.dto.ssdp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SsdpReturnDataDTO implements Serializable {

    private static final long serialVersionUID = 7066058283970100219L;

    private Integer code;

    private String msg;

    private String content;

}
