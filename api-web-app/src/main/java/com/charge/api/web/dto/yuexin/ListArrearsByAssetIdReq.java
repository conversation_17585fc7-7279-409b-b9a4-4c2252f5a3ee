package com.charge.api.web.dto.yuexin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ListArrearsByAssetIdReq {

    /**
     * 小区主数据ID
     */
    @NotBlank(message = "项目id不能为空")
    String communityMsId;

    /**
     * 房屋主数据ID
     */
    @NotEmpty(message = "资产id不能为空")
    List<String> houseMsIdList;

    /**
     * 每页数量
     */
    Integer number;

    /**
     * 资产类型：房间 1， 车位 2
     */
    @ApiModelProperty(value = "资产类型")
    private Integer assetType;

}
