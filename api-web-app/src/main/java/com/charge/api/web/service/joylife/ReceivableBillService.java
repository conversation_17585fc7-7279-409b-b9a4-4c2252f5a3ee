package com.charge.api.web.service.joylife;

import com.charge.api.web.vo.joylife.request.HomePayItemDetailConditionVO;
import com.charge.api.web.vo.joylife.request.HousePayItemListConditionVO;
import com.charge.api.web.vo.joylife.response.HomePayItemDetailVO;
import com.charge.api.web.vo.joylife.response.HousePayItemListVO;
import com.charge.common.dto.ChargeResponse;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/15
 */
public interface ReceivableBillService {
    ChargeResponse<HousePayItemListVO> getHousePayItemList(HousePayItemListConditionVO conditionVO);

    ChargeResponse<HomePayItemDetailVO> getHomePayItemDetail(HomePayItemDetailConditionVO conditionVO);
}
