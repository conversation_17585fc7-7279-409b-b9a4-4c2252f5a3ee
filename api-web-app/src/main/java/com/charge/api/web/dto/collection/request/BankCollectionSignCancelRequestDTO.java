package com.charge.api.web.dto.collection.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BankCollectionSignCancelRequestDTO implements Serializable {
    private static final long serialVersionUID = -8360096699188199161L;

    @NotNull(message = "请输入项目ms id")
    private String communityMsId;
    @NotNull(message = "请输入要签约的资产ms id")
    private String assetMsId;
    /**
     * 项目托收配置id
     */
    @NotNull(message = "请输入配置id")
    private Long configId;

    /**
     * 银行账号
     */
    @NotNull(message = "请输入银行卡号")
    private String bankAccount;

    @NotNull(message = "请输入签约id")
    private Long agreementId;
}
