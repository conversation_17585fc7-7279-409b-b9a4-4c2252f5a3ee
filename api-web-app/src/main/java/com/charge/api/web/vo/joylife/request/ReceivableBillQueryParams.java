package com.charge.api.web.vo.joylife.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReceivableBillQueryParams implements Serializable {
	/**
	 * 电话号码
	 */
	@ApiModelProperty(value = "电话号码")
	private String phoneNo;
	/**
	 * 朝昔用户ID
	 */
	@ApiModelProperty(value = "用户运营平台ID")
	private String userMsId;
	/**
	 * 朝昔用户的小区ID和资产ID组合
	 */
	@ApiModelProperty(value = "朝昔小区和房屋ID组合")
	private List<CommunityHouseQueryParams> msCommunityInfoList;
	/**
	 * 朝昔支付渠道
	 */

	@ApiModelProperty(value = "支付渠道")
	@NotBlank(message = "支付渠道不能为空")
	private String paymentSource;

	/**
	 * 子app id 用于查询商户号
	 */
	private String subAppid;
}
