package com.charge.api.web.vo.lakala;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
public class SubjectSearch {
    private String subjectSearchId;

    private String communityId;

    private String communityName;

    private String buildingId;

    private String buildingName;

    private String unitId;

    private String unitName;

    private String subjectId;

    private String subjectName;

    private String subjectType;

    private String subjectCode;

    private String subjectRemark;

    private String houseownerId;

    private String houseownerName;

    private String houseownerCode;

    private String houseownerBirthday;

    private String houseownerMobile;

    private Integer houseownerSex;

    private BigDecimal arrearsAmount;

    private BigDecimal depositAmount;

    private BigDecimal prestoreAmount;

    private Integer configureStatus;

    private String memo;

    private Date createTs;

    private Date updateTs;

    private String cropId;

    private Integer arrearsMonth;

    private String status;

    private String buildArea;//建筑面积
    private String insideArea;//内部面积
    private String chargeArea;//收费面积

}