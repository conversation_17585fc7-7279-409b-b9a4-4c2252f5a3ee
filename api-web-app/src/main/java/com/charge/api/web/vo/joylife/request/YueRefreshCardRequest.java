package com.charge.api.web.vo.joylife.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 更新月卡ID类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/26 16:17
 */
@Data
@ApiModel("更新月卡ID请求参数")
public class YueRefreshCardRequest {

    @ApiModelProperty("交易订单号")
    @NotBlank(message = "交易订单号不能为空")
    private String orderNum;

    @ApiModelProperty("月卡id")
    @NotBlank(message = "月卡ID不能为空")
    private String cardId;
}
