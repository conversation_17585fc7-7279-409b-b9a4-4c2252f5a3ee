package com.charge.api.web.service.bill.pos;


import com.charge.api.web.vo.lakala.AddDepositRequest;
import com.charge.bill.dto.PayOrAdjustItemDTO;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.domain.AssetPayDTO;
import com.charge.bill.dto.domain.AssetPaymentDetailDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.maindata.pojo.dto.AssetDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * POS房间/小区缴押金下单
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddNewDepositService {

    private final CreateBillFlowService createOrderService;

    public CreateBillResponse createBill(AddDepositRequest request, AssetDTO asset, String uri) throws ChargeBusinessException {
        return createOrderService.createOrder(buildAssetPayDTO(request, asset, uri));
    }

    /**
     * 构建AssetPayDTO
     * @param request
     * @param asset
     * @return
     */
    private AssetPayDTO buildAssetPayDTO(AddDepositRequest request, AssetDTO asset, String uri) throws ChargeBusinessException {
        AssetPayDTO assetPayDTO = new AssetPayDTO();
        assetPayDTO.setAssetPayBaseDTO(buildAssetPayBaseDTO(request, asset, uri));
        assetPayDTO.setAssetPaymentDetailDTO(buildAssetPaymentDetailDTO(request, asset));
        return assetPayDTO;
    }

    /**
     * 构建基础信息
     * @param request
     * @param asset
     * @return
     */
    private AssetPayBaseDTO buildAssetPayBaseDTO(AddDepositRequest request, AssetDTO asset, String uri) throws ChargeBusinessException {
        AssetPayBaseDTO assetPayBaseDTO = new AssetPayBaseDTO();
        FillCommonUtil.fillAssetPayBaseCommunityInfo(asset, assetPayBaseDTO);
        assetPayBaseDTO.setOrderNum(IdGeneratorSupport.getIstance().nextId());
        assetPayBaseDTO.setActualPrice(request.getMoney());
        assetPayBaseDTO.setPaymentMethod(FillCommonUtil.toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())).getPaymentCode());
        assetPayBaseDTO.setPayMember(request.getPayMember());
        assetPayBaseDTO.setCollectorId(request.getCollectorId());
        assetPayBaseDTO.setCollectorName(request.getCollectorName());
        assetPayBaseDTO.setMemo(request.getMemo());
        if ("/addNewHouseDeposit".equals(uri)) {
            assetPayBaseDTO.setClientSourceEnum(ClientSourceEnum.POS_HOUSE_GUARANTY_PAY);
        } else {
            assetPayBaseDTO.setClientSourceEnum(ClientSourceEnum.POS_COMMUNITY_GUARANTY_PAY);
        }
        assetPayBaseDTO.setPayHouseCount(1);
        assetPayBaseDTO.setDeviceInfo(request.getDeviceInfo());
        assetPayBaseDTO.setMercid(request.getMercid());
        assetPayBaseDTO.setPaymentTerminal(PaymentTerminalEnum.POS.getCode());
        return assetPayBaseDTO;
    }
    /**
     * 构建缴费信息
     * @param request
     * @param assetDTO
     * @return
     */
    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(AddDepositRequest request, AssetDTO assetDTO) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 资产信息
        assetPaymentDetailDTO.setBillAssetInfoDTO(FillCommonUtil.buildBillAssetInfo(assetDTO));
        // 缴押金信息
        assetPaymentDetailDTO.setDepositItems(buildDepositInfo(request));
        return assetPaymentDetailDTO;
    }
    private List<PayOrAdjustItemDTO> buildDepositInfo(AddDepositRequest request) {
        PayOrAdjustItemDTO depositInfo = new PayOrAdjustItemDTO();
        depositInfo.setItemId(request.getItemId());
        depositInfo.setAmount(request.getMoney());
        depositInfo.setItemName(request.getItemName());
        return Arrays.asList(depositInfo);
    }
}
