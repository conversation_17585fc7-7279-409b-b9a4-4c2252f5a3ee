package com.charge.api.web.dto.invoice;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date: 2023/09/05/ 19:26
 * @description
 */
@Data
public class ThirdPartyInvoiceMainDTO implements Serializable {
    private static final long serialVersionUID = -440309929072234334L;

    /**
     * 业务单号
     */
    private String salesbillNo;

    /**
     * 发票类型s-增值税专用发票 ce-增值税电子普通发票
     */
    private String invoiceType;
    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 销方编号
     */
    private String sellerNo;
    /**
     * 销方税号
     */
    private String sellerTaxNo;
    /**
     * 销方名称
     */
    private String sellerName;
    /**
     * 销方地址
     */
    private String sellerAddress;
    /**
     * 销方电话
     */
    private String sellerTel;
    /**
     * 销方银行名称
     */
    private String sellerBankName;
    /**
     * 销方银行账号
     */
    private String sellerBankAccount;
    /**
     * 购方税号
     */
    private String purchaserTaxNo;
    /**
     * 购方名称
     */
    private String purchaserName;
    /**
     * 购方地址
     */
    private String purchaserAddress;
    /**
     * 购方电话
     */
    private String purchaserTel;
    /**
     * 购方银行名称
     */
    private String purchaserBankName;
    /**
     * 购方银行账号
     */
    private String purchaserBankAccount;
    /**
     * 不含税金额
     */
    private BigDecimal amountWithoutTax;
    /**
     * 税额
     */
    private BigDecimal taxAmount;
    /**
     * 含税金额
     */
    private BigDecimal amountWithTax;
    /**
     * 税率（0.06,0.13）
     */
    private String taxRate;
    /**
     * 发票开票日期（YYYYMMDD）
     */
    private String paperDrewDate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 收款人姓名
     */
    private String cashierName;
    /**
     * 复核人姓名
     */
    private String checkerName;
    /**
     * 开票人姓名
     */
    private String invoicerName;
    /**
     * 发票状态，1正常，0 已作废
     */
    private String status;

    /**
     * 红蓝标识 1-蓝票 2-红字发票 3-红冲发票
     */
    private String invoiceColor;
    /**
     * 原发票号码
     */
    private String originInvoiceNo;
    /**
     * 原发票代码
     */
    private String originInvoiceCode;

    /**
     * 红字信息表编号
     */
    private String redNotificationNo;
    /**
     * 密文
     */
    private String cipherText;
    /**
     * 系统来源（如ERP、OA）
     */
    private String systemOrig;
    /**
     * 电票PDF地址
     */
    private String pdfPath;
    /**
     * 邮箱
     */
    private String receiveUserEmail;
    /**
     * 开票时间（毫秒，如：1594023438064）
     */
    private String createTime;
    /**
     * 拓展字段1：申请部门（如北京院**部）
     */
    private String ext1;
    /**
     * 拓展字段2：合同号（如设2017-021）
     */
    private String ext2;
    /**
     * 拓展字段3：项目名称（如昆山电子艺术创意城）
     */
    private String ext3;

}
