package com.charge.api.web.dto.collection.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class BankCollectionSignListResponseDTO implements Serializable {
    private static final long serialVersionUID = -8360096699188199161L;

    private Long agreementId;
    private String assetName;
    private String msId;
    private String collectionChannel;
    private String bankName;
    private String bankAccountName;
    private String bankAccount;
    private String certificateNum;
    private String phone;
    private String entrustNo;
    private String createTime;
    private String userMsId;
}
