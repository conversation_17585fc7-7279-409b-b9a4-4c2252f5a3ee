package com.charge.api.web.vo.order;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 子单金额
 *
 * <AUTHOR>
 * @date 2024/12/3
 */
@Data
public class SubOrderAmount {
    /**
     * 子订单号
     */
    @NotNull(message = "子订单号不能为空")
    private String subOrderNo;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;


}