package com.charge.api.web.vo.lakala;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量账单创建请求
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchBillCreateRequest {
    private String paymentMethod;
    private String collectorId;
    private String collectorName;
    private String chargeData;
    private String arrearsPrice;
    private String totalPrice;
    private String penaltyMoney;
    private String principalMoney;
    private String customerId;
    private String customerName;
    private String payMember;
    private String bankTransactionNo;
    private String arrivalDate;
    private String memo;
    private String communityId;
    private String deadline;
    private String token;
    private String chargeType;
    private String deviceInfo;
    private String mercid;
}


