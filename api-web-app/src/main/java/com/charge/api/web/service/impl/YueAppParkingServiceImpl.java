package com.charge.api.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.charge.api.web.adapter.PayOrderAdapter;
import com.charge.api.web.config.AssetPayGrayConfig;
import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.service.bill.joy.AddMonthParkingPayService;
import com.charge.api.web.service.joylife.YueAppParkingService;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.api.web.support.PrePayLockSupport;
import com.charge.bill.dto.LockObjectDTO;
import com.charge.joylife.util.PaymentMethodSupport;
import com.charge.api.web.util.CancelZeroUtil;
import com.charge.api.web.util.ResultPage;
import com.charge.api.web.util.ShardingUtil;
import com.charge.api.web.vo.CardPayItemInfo;
import com.charge.api.web.vo.joylife.request.YueCardPayRequestVO;
import com.charge.api.web.vo.joylife.response.YueCardOrderInfoVO;
import com.charge.api.web.vo.joylife.response.YueCardPayDetailVO;
import com.charge.api.web.vo.joylife.response.YueCardTransactionFlowVO;
import com.charge.bill.client.*;
import com.charge.bill.dto.income.*;
import com.charge.bill.enums.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeStatusEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.TraceContextUtil;
import com.charge.maindata.condition.HouseCondition;
import com.charge.maindata.enums.HouseTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import com.charge.order.client.OrderClient;
import com.charge.order.dto.YueCardOrderCreateCmd;
import com.charge.pay.client.PayClient;
import com.charge.pay.dto.pay.PayOrderSubmitResponseDTO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static com.charge.api.web.service.pos.impl.ChargeBillServiceImpl.nullStr;

/**
 * 月卡订单服务接口
 * Author: yjw
 * Date: 2023/4/12 13:46
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class YueAppParkingServiceImpl implements YueAppParkingService {

    private final IncomeCarriedForwardClient incomeCarriedForwardClient;
    private final IncomeBillClient incomeBillClient;
    private final CommunitySupport communitySupport;
    private final AssetSupport assetSupport;
    private final BillNumGeneratorClient billNumGeneratorClient;
    private final AssetTransactionClient assetTransactionClient;
    private final AssetBillInfoClient assetBillInfoClient;
    private final OrderBillDetailClient orderBillDetailClient;
    private final TransactionRelationClient transactionRelationClient;
    private final OrderClient orderClient;

    private final PayClient payClient;
    private final AddMonthParkingPayService addMonthParkingPayService;
    private final AssetPayGrayConfig assetPayGrayConfig;

    private final PrePayLockSupport prePayLockSupport;

    /**
     * 查询用户月卡缴费记录列表
     *
     * @param cardId
     * @param currentPage
     * @param pageSize
     * @return
     */
    @Override
    public ResultPage getParkingBillList(String cardId, Integer currentPage, Integer pageSize) throws ChargeBusinessException {
        log.info("{}|查询用户月卡缴费记录列表:cardId={}, currentPage={}, pageSize={}", LogCategoryEnum.BUSSINESS, cardId, currentPage, pageSize);
        if (StringUtils.isEmpty(cardId) || "null".equals(cardId)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1006, "月卡ID参数为空");
        }
        TraceContextUtil.setCommunityId(TraceContextUtil.getCommunityId());
        ChargeResponse<PagingDTO<YueCardTransactionFlowDTO>> yueCardResponse = incomeCarriedForwardClient.billList(IncomeCarriedForwardConditionDTO.builder().
                cardId(cardId).currentPage(currentPage).pageSize(pageSize).build());
        PagingDTO<YueCardTransactionFlowDTO> yueCardTransactionFlowDTOPagingDTO = AppInterfaceUtil.getResponseDataThrowException(yueCardResponse);
        List<YueCardTransactionFlowDTO> yueCardTransactionFlowDTOS = yueCardTransactionFlowDTOPagingDTO.getList();
        List<YueCardOrderInfoVO> itemList = new ArrayList<>();
        String communityName = "";
        for (YueCardTransactionFlowDTO yueCardTransactionFlowDTO : yueCardTransactionFlowDTOS) {
            YueCardOrderInfoVO yueCardOrderInfoVO = new YueCardOrderInfoVO();
            yueCardOrderInfoVO.setOrderNum(yueCardTransactionFlowDTO.getOrderNum());
            BigDecimal price = yueCardTransactionFlowDTO.getMoney();
            yueCardOrderInfoVO.setPrice(CancelZeroUtil.precisionDecimal(price));
            yueCardOrderInfoVO.setTradeTime(DateUtils.format(yueCardTransactionFlowDTO.getChargeDealTime(), DateUtils.FORMAT_0));
            yueCardOrderInfoVO.setCardNumber(yueCardTransactionFlowDTO.getCarNum());
            itemList.add(yueCardOrderInfoVO);
            communityName = String.valueOf(yueCardTransactionFlowDTO.getCommunityName());
        }
        YueCardTransactionFlowVO yueCardTransactionFlowVO = new YueCardTransactionFlowVO();
        yueCardTransactionFlowVO.setSubjectInfo(communityName + "停车场");
        yueCardTransactionFlowVO.setItemList(itemList);
        return ResultPage.builder().code(0).content(yueCardTransactionFlowVO).pageSize(pageSize).
                totalPage(yueCardTransactionFlowDTOPagingDTO.getTotalCount()).currentPage(currentPage)
                .totalRecord(yueCardTransactionFlowDTOPagingDTO.getTotalCount()).build();
    }

    @Override
    public ChargeResponse createCardPayBills(YueCardPayRequestVO payVO) throws ChargeBusinessException {
        //校验
        CommunityDTO communityDTO = communitySupport.getCommunityByMsId(payVO.getProjectId());
        ShardingUtil.addCommunityId2ThreadContext(communityDTO.getId());
        HouseDTO houseInfo =null;
        if(StringUtils.isNotEmpty(payVO.getHouseUuid())) {
            houseInfo = assetSupport.getHouseInfo(payVO.getHouseUuid());
        }else {
            AssetDTO communityVirtualHouse = assetSupport.getCommunityVirtualHouse(communityDTO.getId());
            houseInfo = communityVirtualHouse.getHouseDTO();
        }
        if (Objects.isNull(houseInfo)) {
            return new ChargeResponse<>(ChargeStatusEnum.FAIL.getCode(), "当前房屋不存在");
        }
        payVO.setHouseUuid(houseInfo.getMsId());
//        ParkingVO parkingInfo = assetSupport.getV2ParkingInfo(communityDTO.getId(), payVO.getPropertyId());
//        if (Objects.isNull(parkingInfo)) {
//            return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), "当前车场不存在");
//        }
        //日期校验 有效时间 < 到期时间
        Date effectiveTime = payVO.getEffectiveTime();
        Date expireTime = payVO.getExpireTime();
        if (!effectiveTime.before(expireTime)) {
            return new ChargeResponse<>(ChargeStatusEnum.FAIL.getCode(), "有效时间大于到期时间");
        }
        //解析收费项
        List<CardPayItemInfo> itemInfos = analysisJSON(payVO.getCardItem());
        ChargeResponse checkParam = checkParam(itemInfos, payVO.getAmount());
        if (Objects.equals(checkParam.getCode(), ChargeStatusEnum.FAIL.getCode())) {
            return checkParam;
        }
        //下单
        String orderNum = IdGeneratorSupport.getIstance().nextId();
        LockObjectDTO lockObjectDTO = LockObjectDTO.builder().mark(orderNum)
                .paymentMethod(payVO.getPaymentMethod()).paymentSource(payVO.getPaymentSource())
                .payMember(payVO.getPayMember()).businessScene(BillPrePaySceneEnum.ZHAOXI_PARKING_CARD.getCode())
                .parkingCardIdList(new HashSet<>(Arrays.asList(payVO.getCardId())))
                .build();
        prePayLockSupport.lockAndClose(lockObjectDTO);

        ChargeResponse<PayOrderSubmitResponseDTO> response = payClient.tradeCreate(
                PayOrderAdapter.requestYueCardPayParamConvert(payVO, communityDTO.getId(), orderNum,itemInfos));
        //如果支付失败则直接返回
        if (!response.isSuccess() || Objects.isNull(response.getContent())) {
            log.error("{}|停车月卡支付预下单异常|{}", LogCategoryEnum.BUSSINESS, response);
            prePayLockSupport.unlock(lockObjectDTO);
            return new ChargeResponse<>(response.getCode(), response.getMessage());
        }
        PayOrderSubmitResponseDTO payReturnDTO = response.getContent();
        try {
            return createCardBill(payVO, communityDTO, houseInfo, payReturnDTO, itemInfos, orderNum);
        }catch (Exception ex){
            prePayLockSupport.unlock(lockObjectDTO);
            throw ex;
        }
    }

    @Override
    public ChargeResponse<YueCardPayDetailVO> cardPayDetail(String orderNum) throws ChargeBusinessException {
        IncomeCarriedForwardConditionDTO conditionDTO = new IncomeCarriedForwardConditionDTO();
        conditionDTO.setOrderNum(orderNum);
        conditionDTO.setCommunityId(TraceContextUtil.getCommunityId());
        ChargeResponse<YueCardPayDetailDTO> response = incomeCarriedForwardClient.getCardPayDetail(conditionDTO);
        YueCardPayDetailDTO cardPayDetailDTO = AppInterfaceUtil.getDataThrowException(response);
        YueCardPayDetailVO payDetailVO = new YueCardPayDetailVO();
        if (Objects.nonNull(cardPayDetailDTO)) {
            payDetailVO.setPayOrderId(cardPayDetailDTO.getAssetTransactionId().toString());
            payDetailVO.setYardName(cardPayDetailDTO.getParkingName());
            payDetailVO.setCarNo(cardPayDetailDTO.getCarNum());
            payDetailVO.setPayTime(cardPayDetailDTO.getPaymentTime());
            String paymentMethodForV1 = PaymentMethodSupport.convertTOPaymentMethodV1(cardPayDetailDTO.getPaymentMethod());
            payDetailVO.setPaymentMethod(paymentMethodForV1);
            payDetailVO.setStartDay(cardPayDetailDTO.getStartDay());
            payDetailVO.setEndDay(cardPayDetailDTO.getEndDay());
        }
        return new ChargeResponse<>(payDetailVO);
    }

    /**
     * 解析json字符串
     *
     * @param dataList
     * @return
     */
    private static List<CardPayItemInfo> analysisJSON(String dataList) {
        JSONArray ja = JSON.parseArray(dataList);
        Iterator<Object> it = ja.iterator();
        List<CardPayItemInfo> list = new ArrayList<>();
        while (it.hasNext()) {
            CardPayItemInfo itemInfo = new CardPayItemInfo();
            JSONObject ob = (JSONObject) it.next();
            if (ob.containsKey("itemId")) {
                itemInfo.setItemId(Long.parseLong((String) ob.get("itemId")));
            }
            if (ob.containsKey("itemName")) {
                itemInfo.setItemName((String) ob.get("itemName"));
            }
            if (ob.containsKey("price")) {
                String price = (String) ob.get("price");
                itemInfo.setPrice(new BigDecimal(price));
            }
            list.add(itemInfo);
        }
        return list;
    }

    private ChargeResponse createCardBill(YueCardPayRequestVO request, CommunityDTO communityDTO, HouseDTO houseInfo, PayOrderSubmitResponseDTO payReturnDTO,
                                          List<CardPayItemInfo> itemInfos, String orderNum) throws ChargeBusinessException {
        try {
            ShardingUtil.addCommunityId2ThreadContext(communityDTO.getId());
            //基础数据资产
            Long assetId = assetSupport.getV2AssetId(houseInfo.getMsId());
            AssetAdapter assetInfo = assetSupport.getAssetInfoById(assetId.toString());
            if (assetPayGrayConfig.isOpenGray(communityDTO.getId(), "/app/parking/createPayOrder")) {
                CreateBillResponse response = addMonthParkingPayService.createBill(request, communityDTO, payReturnDTO.getOutTradeNo(), orderNum, assetInfo, houseInfo, itemInfos);
                log.info("createBill response = {}", response);
                //创建收入结转[income_carried_forword]
                AssetTransactionDTO transactionDO = new AssetTransactionDTO();
                transactionDO.setAssetOrderNum(response.getOrderNum());
                transactionDO.setId(response.getAssetTransactionId());
                createIncomeCarriedForward(request, assetInfo, communityDTO, houseInfo, transactionDO, assetId, itemInfos);
                return new ChargeResponse<>(PayOrderAdapter.responseYueCardPayOrDeliveryPayConvert(payReturnDTO, response.getIncomeBillId()));
            }
            //创建实收单[income_bill]
            IncomeBillDTO income = createIncomeBill(request, communityDTO, payReturnDTO, orderNum, itemInfos);
            //创建保存资产流水单[asset_transaction]
            AssetTransactionDTO transactionDO = createAssetTransaction(request, assetInfo, income, houseInfo, assetId);
            //创建流水信息表数据[asset_bill_info]
            createAssetBillInfo(income, transactionDO.getId());
            //创建资产关联表与账单订单明细
            createTransactionRelationAndOrderBillDetail(itemInfos, transactionDO.getId(), assetId, orderNum, communityDTO.getId(), request, assetInfo);
            //创建收入结转[income_carried_forword]
            createIncomeCarriedForward(request, assetInfo, communityDTO, houseInfo, transactionDO, assetId, itemInfos);
           return new ChargeResponse<>(PayOrderAdapter.responseYueCardPayOrDeliveryPayConvert(payReturnDTO, income.getId()));
        } catch (Exception e) {
            log.error("{}|停车月卡支付,执行下单异常|入参={}", LogCategoryEnum.BUSSINESS, request, e);
            throw new ChargeBusinessException(ErrorInfoEnum.E1003, "系统下单错误，请重试");
        }
    }

    // //校验参数
    private ChargeResponse checkParam(List<CardPayItemInfo> itemInfos, BigDecimal amount) {
        if (CollectionUtil.isEmpty(itemInfos)) {
            return new ChargeResponse<>(ChargeStatusEnum.FAIL.getCode(), "收费项不能为空");
        }
        for (int i = 0; i < itemInfos.size(); i++) {
            if (Objects.isNull(itemInfos.get(i).getItemId())) {
                return new ChargeResponse<>(ChargeStatusEnum.FAIL.getCode(), "收费项id不能为空");
            }
            String tempPrice = itemInfos.get(i).getPrice().toString();
            if (StringUtils.isEmpty(itemInfos.get(i).getPrice().toString())) {
                return new ChargeResponse<>(ChargeStatusEnum.FAIL.getCode(), "收费项金额不能为空");
            }
            if (BigDecimal.valueOf(Double.parseDouble(tempPrice)).compareTo(BigDecimal.valueOf(Double.parseDouble("99999999.99"))) > 0) {
                return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), "收费项金额不能超过8位");
            }
        }
        BigDecimal itemAmountSum = itemInfos.stream().map(CardPayItemInfo::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (amount.compareTo(itemAmountSum) != 0) {
            return new ChargeResponse<>(ChargeStatusEnum.FAIL.getCode(), "金额不一致");
        }
        return new ChargeResponse<>();
    }

    //创建实收单
    private IncomeBillDTO createIncomeBill(YueCardPayRequestVO request, CommunityDTO communityDTO,
                                           PayOrderSubmitResponseDTO payReturnDTO, String orderNum, List<CardPayItemInfo> itemInfos) throws ChargeBusinessException {
        String billNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.INCOME_BILL.getCode()).getContent();
        IncomeBillDTO income = IncomeBillDTO.builder().orderNum(orderNum).billNum(billNum).outTransactionNo(payReturnDTO.getOutTradeNo())
                .communityId(communityDTO.getId()).communityName(communityDTO.getName()).incomeMoney(request.getAmount())
                .goodsName(itemInfos.size() > 1 ? PayRelatedConstants.GOODSNAME_FOR_MERGE : PayRelatedConstants.GOODSNAME_MONTH_PARKING)
                .paymentTime(new Date())
                .payMemberId(request.getPayMemberId())
                .payMember(request.getPayMember())
                .payHouseCount(1).balanceStatus(BalanceStatusEnum.UNRECONCILED.getCode())
                .paymentTerminal(PaymentTerminalEnum.handleJoyLifeWechatApplet(request.getPaymentSource()))
//                .msAssetOwnerId(request.getPayMemberId())
                .isMergeBill(payReturnDTO.getIsMergeBill())
                .subBills(PayOrderAdapter.fillSubBills(payReturnDTO.getSubItems()))
                .build();
        PayOrderAdapter.fillPaymentMethod(income,request.getPaymentMethod());
        if (Objects.equals(PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel(),request.getPaymentSource())) {
            income.setPaymentChannel(request.getPaymentSource());
        }
        income.setCreateUser(request.getPayMember());
        income.setCreateTime(DateUtils.getCurrentTimestamp());
        income = AppInterfaceUtil.getResponseData(incomeBillClient.create(income));
        log.info("{}|月卡生成实收单，incomeBillDTO：{}", LogCategoryEnum.BUSSINESS, income);
        return income;
    }

    //创建资产交易流水
    private AssetTransactionDTO createAssetTransaction(YueCardPayRequestVO request, AssetAdapter assetInfo, IncomeBillDTO income,
                                                       HouseDTO houseInfo, Long assetId) throws ChargeBusinessException {
        AssetTransactionDTO transactionDO = AssetTransactionDTO.builder()
                .incomeId(income.getId()).money(request.getAmount()).goodsName(PayRelatedConstants.GOODSNAME_MONTH_PARKING)
                .assetOrderNum(income.getOrderNum()).communityId(income.getCommunityId()).communityName(income.getCommunityName())
                .buildingId(houseInfo.getBuildingId()).buildingName(houseInfo.getBuildingName()).unitId(houseInfo.getUnitId())
                .unitName(houseInfo.getUnitName()).assetId(assetId).assetName(assetInfo.getSubName())
                .assetCode(assetInfo.getSubCode())
                .assetUseStatus(assetInfo.getAssetUseStatus())
                .paymentMethod(income.getPaymentMethod())
                .paymentChannel(income.getPaymentChannel())
                .paymentTerminal(income.getPaymentTerminal()).paymentTime(new Date()).build();
        transactionDO.setCreateUser(request.getPayMember());
        transactionDO.setCreateTime(DateUtils.getCurrentTimestamp());
        if (!CollectionUtils.isEmpty(assetInfo.getListCustomer())) {
            transactionDO.setOwnerId(assetInfo.getListCustomer().get(0).getId());
            transactionDO.setOwnerName(nullStr(assetInfo.getListCustomer().get(0).getCustomerName()));
        }
        transactionDO = AppInterfaceUtil.getResponseData(assetTransactionClient.create(transactionDO));
        log.info("{}|月卡生成资产流水，transactionDO：{}", LogCategoryEnum.BUSSINESS, transactionDO);
        return transactionDO;
    }

    //创建流水信息表数据
    private void createAssetBillInfo(IncomeBillDTO income, Long assetTransactionId) throws ChargeBusinessException {
        AssetBillInfoDTO assetBillInfoDO = AssetBillInfoDTO.builder().assetTransactionId(assetTransactionId)
                .orderNum(income.getOrderNum())
                .payMemberId(income.getPayMemberId())
                .payMember(income.getPayMember()).build();
        AppInterfaceUtil.getResponseData(assetBillInfoClient.create(assetBillInfoDO));
    }

    //创建收入结转主表
    private void createIncomeCarriedForward(YueCardPayRequestVO request, AssetAdapter assetInfo, CommunityDTO communityDTO, HouseDTO houseInfo,
                                            AssetTransactionDTO transactionDO, Long assetId, List<CardPayItemInfo> itemInfos) throws ChargeBusinessException {
        IncomeCarriedForwardDTO carriedForwardDTO = BeanCopierWrapper.copy(request, IncomeCarriedForwardDTO.class);
        carriedForwardDTO.setOrderNum(transactionDO.getAssetOrderNum());
        carriedForwardDTO.setAssetTransactionId(transactionDO.getId());
        carriedForwardDTO.setCommunityId(communityDTO.getId());
        carriedForwardDTO.setCommunityName(communityDTO.getName());
        carriedForwardDTO.setParkingId(request.getPropertyId());
        carriedForwardDTO.setParkingName(request.getPropertyName());
        carriedForwardDTO.setAssetId(assetId);
        carriedForwardDTO.setCarNum(request.getCarNo());
        carriedForwardDTO.setAssetCode(assetInfo.getSubCode());
        carriedForwardDTO.setPayMoney(request.getAmount());
        carriedForwardDTO.setStartDay(DateUtils.format(request.getEffectiveTime(), "yyyy-MM-dd"));
        carriedForwardDTO.setEndDay(DateUtils.format(request.getExpireTime(), "yyyy-MM-dd"));
        carriedForwardDTO.setStatus(IncomeCarriedForwardEnum.NO_CARRY_FORWARD.getCode());
        carriedForwardDTO.setRefundStatus(IncomeForwardRefundStatusEnum.NO.getCode());
        int totalDay = Days.daysBetween(LocalDate.parse(carriedForwardDTO.getStartDay()), LocalDate.parse(carriedForwardDTO.getEndDay())).getDays() + 1;
        carriedForwardDTO.setTotalDay(totalDay);
        carriedForwardDTO.setCardItem(getListJosn(itemInfos, totalDay));
        carriedForwardDTO.setApiType(Integer.valueOf(request.getApiType()));
        carriedForwardDTO.setParkingCardId(request.getParkingCardId());
        AppInterfaceUtil.getResponseDataThrowException(incomeCarriedForwardClient.create(carriedForwardDTO));
        log.info("{}|月卡生成收入结转单，incomeCarriedForwardDTO：{}", LogCategoryEnum.BUSSINESS, carriedForwardDTO);
    }

    //创建资产关联表与账单订单明细
    private void createTransactionRelationAndOrderBillDetail(List<CardPayItemInfo> itemInfos, Long assetTransactionId, Long assetId, String orderNum,
                                                             Long communityId, YueCardPayRequestVO request, AssetAdapter assetInfo) throws ChargeBusinessException {
        List<TransactionRelationDTO> relationList = Lists.newArrayList();
        for (int i = 0, size = itemInfos.size(); i < size; i++) {
            CardPayItemInfo cardPayItemInfo = itemInfos.get(i);

            //订单表[base_order]
            Long orderId = createOrder(cardPayItemInfo, assetId, orderNum, communityId, request, assetInfo);

            //增加订单明细信息order_bill_detail
            OrderBillDetailDTO billDetailDTO = OrderBillDetailDTO.builder().assetTransactionId(assetTransactionId)
                    .bizType(BizTypeEnum.ORDER_TYPE.getCode()).itemId(cardPayItemInfo.getItemId())
                    .itemName(cardPayItemInfo.getItemName()).actualAmount(cardPayItemInfo.getPrice()).orderId(orderId)
                    .communityId(communityId).build();
            Long orderBillDetailId = orderBillDetailClient.create(billDetailDTO).getContent();

            //添加关联数据transaction_relation
            TransactionRelationDTO relationDO = TransactionRelationDTO.builder().assetTransactionId(assetTransactionId)
                    .businessId(orderBillDetailId).businessType(BusinessTypeEnum.TEMP_PAY.getCode())
                    .createTime(new Date()).communityId(communityId).build();
            relationList.add(relationDO);
        }
        AppInterfaceUtil.getResponseData(transactionRelationClient.batchCreate(relationList));
    }

    //创建订单表
    private Long createOrder(CardPayItemInfo cardPayItemInfo, Long assetId, String orderNum, Long communityId, YueCardPayRequestVO request, AssetAdapter assetInfo) throws ChargeBusinessException {
        YueCardOrderCreateCmd orderCreateCmd = new YueCardOrderCreateCmd();
        orderCreateCmd.setOrderNum(orderNum);
        orderCreateCmd.setCommunityId(communityId);
//        orderCreateCmd.setCustomerId(Long.parseLong(request.getPayMemberId()));
        orderCreateCmd.setCustomerName(request.getPayMember());
        orderCreateCmd.setSubsetId(200L);
        orderCreateCmd.setAssetId(assetId);
        orderCreateCmd.setAssetCode(assetInfo.getSubCode());
        com.charge.order.dto.WorkOrderItemDTO itemDTO = BeanCopierWrapper.copy(cardPayItemInfo, com.charge.order.dto.WorkOrderItemDTO.class);
        itemDTO.setItemId(cardPayItemInfo.getItemId().toString());
        orderCreateCmd.setItemVOS(Lists.newArrayList(itemDTO));
        log.info("{}|订单base_order下单，orderCreateCmd：{}", LogCategoryEnum.BUSSINESS, orderCreateCmd);
        return AppInterfaceUtil.getResponseData(orderClient.createYueCardOrder(orderCreateCmd));
    }

    private String getListJosn(List<CardPayItemInfo> cardItemDTOList, int totalDay) {
        if (CollectionUtil.isNotEmpty(cardItemDTOList) && Objects.nonNull(totalDay)) {
            for (CardPayItemInfo itemInfo : cardItemDTOList) {
                //收费项日金额
                BigDecimal dayPrice = itemInfo.getPrice().divide(new BigDecimal(totalDay), 2, RoundingMode.HALF_UP);
                itemInfo.setDayPrice(dayPrice);
            }
        }
        return JSONArray.toJSONString(cardItemDTOList);
    }
}
