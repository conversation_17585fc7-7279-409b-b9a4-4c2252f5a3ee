package com.charge.api.web.service.bill.joy.query;

import com.charge.api.web.controller.joylife.bill.request.PayOrAdjustItemVO;
import com.charge.api.web.controller.joylife.bill.response.DetailVO;
import com.charge.bill.dto.domain.response.query.AssetTransactionDTO;
import com.charge.bill.dto.domain.response.query.BillDetailResponse;
import com.charge.bill.dto.domain.response.query.IncomeBillDTO;
import com.charge.bill.dto.domain.response.query.deposit.DepositDTO;
import com.charge.bill.dto.domain.response.query.order.OrderDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 将BillDetail转换为对应的VO
 */
public class BillDetailToVoUtil {

    public static DetailVO convert(BillDetailResponse response) {
        DetailVO detailVO = new DetailVO();
        if (Objects.isNull(response)) {
            return detailVO;
        }
        // 实收单信息
        buildByIncomeBill(response.getIncomeBillDTO(), detailVO);
        // 资产账单信息
        if (Objects.nonNull(response.getAssetBillDTO())) {
            // 资产流水表明细
            buildByAssetTransaction(response.getAssetBillDTO().getAssetTransactionDTO(), detailVO);
            // 订单信息
            buildOrderItem(response.getAssetBillDTO().getOrderDTO(), detailVO);
            // 押金信息
            buildDepositItem(response.getAssetBillDTO().getDepositDTO(), detailVO);
        }
        return detailVO;
    }


    /**
     * 根据实收单转换
     * @param incomeBillDTO
     * @param detailVO
     */
    private static void buildByIncomeBill(IncomeBillDTO incomeBillDTO, final DetailVO detailVO) {
        if (Objects.nonNull(incomeBillDTO)) {
            detailVO.setIncomeBillId(incomeBillDTO.getId());
            detailVO.setCommunityName(incomeBillDTO.getCommunityName());
            detailVO.setOrderNum(incomeBillDTO.getOrderNum());
            detailVO.setCollectorName(incomeBillDTO.getCollectorName());
            detailVO.setMemo(incomeBillDTO.getMemo());
            detailVO.setPayMember(incomeBillDTO.getPayMember());
            detailVO.setPayMemberMobile(incomeBillDTO.getPayMemberMobile());
            detailVO.setPaymentMethod(incomeBillDTO.getPaymentMethod());
            detailVO.setOutTransactionNo(incomeBillDTO.getOutTransactionNo());
        }
    }

    private static void buildByAssetTransaction(AssetTransactionDTO assetTransactionDTO, final DetailVO detailVO) {
        if (Objects.nonNull(assetTransactionDTO)) {
            detailVO.setTransactionId(assetTransactionDTO.getId());
            detailVO.setAssetCode(assetTransactionDTO.getAssetCode());
            detailVO.setAssetName(assetTransactionDTO.getAssetName());
            detailVO.setCreateTime(assetTransactionDTO.getCreateTime());
            detailVO.setPayTime(assetTransactionDTO.getPayTime());
        }
    }

    /**
     * 构建收费项信息
     * @param orderDTO
     * @param detailVO
     */
    private static void buildOrderItem(OrderDTO orderDTO, final DetailVO detailVO) {
        if (Objects.nonNull(orderDTO) && CollectionUtils.isNotEmpty(orderDTO.getOrderBillDetailDTOS())) {
            detailVO.setOrderItems(orderDTO.getOrderBillDetailDTOS()
                    .stream().map(orderBillDetailDTO -> {
                        PayOrAdjustItemVO itemVO = new PayOrAdjustItemVO();
                        itemVO.setItemId(orderBillDetailDTO.getItemId());
                        itemVO.setItemName(orderBillDetailDTO.getItemName());
                        itemVO.setAmount(String.valueOf(orderBillDetailDTO.getActualAmount()));
                        return itemVO;
                    }).collect(Collectors.toList()));
        }
    }

    /**
     * 构建押金收费项信息
     * @param depositDTO
     * @param detailVO
     */
    private static void buildDepositItem(DepositDTO depositDTO, final DetailVO detailVO) {
        if (Objects.nonNull(depositDTO) && CollectionUtils.isNotEmpty(depositDTO.getPredepositBillDTOS())) {
            detailVO.setOrderItems(depositDTO.getPredepositBillDTOS()
                    .stream().map(depositPredepositBillDTO -> {
                        PayOrAdjustItemVO itemVO = new PayOrAdjustItemVO();
                        itemVO.setItemId(depositPredepositBillDTO.getPredepositItemId());
                        itemVO.setItemName(depositPredepositBillDTO.getPredepositItemName());
                        itemVO.setAmount(String.valueOf(depositPredepositBillDTO.getPredepositMoney()));
                        return itemVO;
                    }).collect(Collectors.toList()));
        }
    }
}
