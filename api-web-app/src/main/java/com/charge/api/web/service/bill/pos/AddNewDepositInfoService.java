package com.charge.api.web.service.bill.pos;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.dto.pos.AddPredepositRequestDTO;
import com.charge.bill.dto.PayOrAdjustItemDTO;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.domain.AssetPayDTO;
import com.charge.bill.dto.domain.AssetPaymentDetailDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.DateUtils;
import com.charge.maindata.pojo.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * POS预存充值
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddNewDepositInfoService {


    private final CreateBillFlowService createOrderService;

    public CreateBillResponse createBill(AddPredepositRequestDTO request, AssetDTO asset) throws ChargeBusinessException {
        return createOrderService.createOrder(buildAssetPayDTO(request, asset));
    }
    private AssetPayDTO buildAssetPayDTO(AddPredepositRequestDTO request, AssetDTO assetDTO) throws ChargeBusinessException {
        AssetPayDTO assetPayDTO = new AssetPayDTO();
        assetPayDTO.setAssetPayBaseDTO(buildAssetPayBaseDTO(request, assetDTO));
        assetPayDTO.setAssetPaymentDetailDTO(buildAssetPaymentDetailDTO(request, assetDTO));
        return assetPayDTO;
    }

    /**
     * 构建基础信息
     * @param request
     * @param asset
     * @return
     * @throws ChargeBusinessException
     */
    private AssetPayBaseDTO buildAssetPayBaseDTO(AddPredepositRequestDTO request, AssetDTO asset) throws ChargeBusinessException {
        AssetPayBaseDTO assetPayBaseDTO = new AssetPayBaseDTO();
        // 填充项目信息
        FillCommonUtil.fillAssetPayBaseCommunityInfo(asset, assetPayBaseDTO);
        assetPayBaseDTO.setOrderNum(IdGeneratorSupport.getIstance().nextId());
        assetPayBaseDTO.setActualPrice(new BigDecimal(request.getMoney()));
        FillCommonUtil.fillAssetPayBaseCommon(request.getPaymentMethod(), assetPayBaseDTO);
        assetPayBaseDTO.setPayMember(request.getPayMember());
        assetPayBaseDTO.setCollectorId(request.getCollectorId());
        assetPayBaseDTO.setCollectorName(request.getCollectorName());
        assetPayBaseDTO.setReceiptType(0);
        assetPayBaseDTO.setPaymentTerminal(PaymentTerminalEnum.POS.getCode());
        Date createTime = request.getArrivalDate() != null ? DateUtils.parse(request.getArrivalDate(), DateUtils.FORMAT_0) : new Date();
        assetPayBaseDTO.setCreateTime(new Timestamp(createTime.getTime()));
        assetPayBaseDTO.setPaymentTime(String.valueOf(createTime));
        assetPayBaseDTO.setClientSourceEnum(ClientSourceEnum.POS_PRESTORE_RECHARGE);
        assetPayBaseDTO.setPayHouseCount(1);
        return assetPayBaseDTO;
    }

    /**
     * 构建收费项
      * @param request
     * @param assetDTO
     * @return
     */
    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(AddPredepositRequestDTO request, AssetDTO assetDTO) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 资产信息
        assetPaymentDetailDTO.setBillAssetInfoDTO(FillCommonUtil.buildBillAssetInfo(assetDTO));
        // 临时订单类
        assetPaymentDetailDTO.setOrderItems(buildOrderItem(request.getTempBillJson()));
        // 通用预存
        assetPaymentDetailDTO.setPrestoreItems(buildPreStoreItem(request.getSpecialPreStoreJson(), request.getPayMoney()));
        return assetPaymentDetailDTO;
    }

    /**
     * 构建临时订单
     * @param tempBillJson
     * @return
     */
    private List<PayOrAdjustItemDTO> buildOrderItem(String tempBillJson) {
        if (StringUtils.isEmpty(tempBillJson)) {
            return Arrays.asList();
        }
        List<PayOrAdjustItemDTO> orderItems = new ArrayList<>();
        List<JSONObject> tempBillList = JSONArray.parseArray(tempBillJson, JSONObject.class);
        tempBillList.stream().forEach(json -> {
            PayOrAdjustItemDTO orderItem = new PayOrAdjustItemDTO();
            orderItem.setItemId(json.getLong("itemID"));
            orderItem.setItemName(json.getString("itemName"));
            orderItem.setAmount(json.getBigDecimal("price"));
            // 校验订单
            Assert.notNull(orderItem.getItemId(), "预收订单收费项id不能为空");
            Assert.hasText(orderItem.getItemName(), "预收订单收费项name不能为空");
            Assert.notNull(orderItem.getAmount(), "预收订单金额不能为空");
            orderItems.add(orderItem);
        });
        return orderItems;
    }



    /**
     * 构建通用与专项预存信息
     * @param specialPreStoreJson
     * @param payMoney  通用预存充值金额
     * @return
     */
    private List<PayOrAdjustItemDTO> buildPreStoreItem(String specialPreStoreJson, String payMoney) {
        List<JSONObject> specialPreStoreJSON = JSONArray.parseArray(specialPreStoreJson, JSONObject.class);
        List<PayOrAdjustItemDTO> prestoreItems = new ArrayList<>();
        // 通用预存
        if(new BigDecimal(payMoney).compareTo(BigDecimal.ZERO)>0){
            PayOrAdjustItemDTO commonItem = new PayOrAdjustItemDTO();
            commonItem.setItemName(PayRelatedConstants.PREDEPOSIT_ITEM_GENERAL_NAME);
            commonItem.setItemId(PayRelatedConstants.PREDEPOSIT_ITEM_GENERAL_ID);
            commonItem.setAmount(new BigDecimal(payMoney));
            prestoreItems.add(commonItem);
        }
        // 专项预存
        for (JSONObject json : specialPreStoreJSON) {
            PayOrAdjustItemDTO specialItem = new PayOrAdjustItemDTO();
            specialItem.setItemName(json.getString("itemName"));
            specialItem.setItemId(json.getLong("itemID"));
            specialItem.setAmount(json.getBigDecimal("price"));
            // 校验
            Assert.notNull(specialItem.getItemId(), "预存收费项id不能为空");
            Assert.hasText(specialItem.getItemName(), "预存收费项name不能为空");
            Assert.notNull(specialItem.getAmount(), "预存金额不能为空");
            prestoreItems.add(specialItem);
        }
        return prestoreItems;
    }
}
