package com.charge.api.web.service.pos;

import com.charge.api.web.vo.pos.rent.RentSaleOrderDetailQuery;
import com.charge.api.web.vo.pos.rent.RentSaleOrderQuery;
import com.charge.api.web.vo.pos.rent.RentSaleOrderVO;
import com.charge.common.exception.ChargeBusinessException;

import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/12/11 14:16
 */
public interface RentSaleOrderService {

    List<RentSaleOrderVO> listOrder(RentSaleOrderQuery rentSaleOrderQuery) throws ChargeBusinessException;

    RentSaleOrderVO orderDetail(RentSaleOrderDetailQuery detailQuery) throws ChargeBusinessException;
}
