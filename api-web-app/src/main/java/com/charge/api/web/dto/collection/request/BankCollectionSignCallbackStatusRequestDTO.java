package com.charge.api.web.dto.collection.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BankCollectionSignCallbackStatusRequestDTO implements Serializable {
    private static final long serialVersionUID = -8360096699188199161L;

    @NotNull(message = "请输入签约申请号")
    private String applyNum;

    /**
     * 项目托收配置id
     */
    @NotNull(message = "请输入配置id")
    private Long configId;


}
