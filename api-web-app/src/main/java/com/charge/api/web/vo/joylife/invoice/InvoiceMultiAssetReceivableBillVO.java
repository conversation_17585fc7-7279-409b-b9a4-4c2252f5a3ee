package com.charge.api.web.vo.joylife.invoice;

import com.charge.api.web.vo.joylife.CustomerSimpleVO;
import com.charge.invoice.dto.InvoiceBillDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import lombok.Data;

import java.util.List;

@Data
public class InvoiceMultiAssetReceivableBillVO {
    /**
     * 小区id(朝昔)
     */
    String communityMsId;
    /**
     * 小区id（收费）
     */
    Long  communityId;

    /**
     * 小区名称
     */
    String communityName;
    /**
     * Long(资产id)
     */
    Long assetId;
    /**
     * 资产id（朝昔测）
     */
    String  assetMsId;
    /**
     * 资产类型资产类型：1-房间,2-车位 （当不传收费的资产id时需要）
     */
    Integer assetType;

    /**
     * 房间名称(楼栋+单元+房间名称/停车场+停车位)
     */
    String houseName;
    /**
     * 业主assetOwnerId
     */
    List<CustomerSimpleVO> customerSimpleVOS;
    /**
     * 可开票账单
     */
    List<InvoiceBillDTO>  invoiceBillDTOList;
}
