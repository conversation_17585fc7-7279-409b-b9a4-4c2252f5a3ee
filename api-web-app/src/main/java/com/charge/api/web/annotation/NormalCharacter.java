package com.charge.api.web.annotation;

import com.charge.api.web.annotation.validator.NormalCharacterValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {NormalCharacterValidator.class})
public @interface NormalCharacter {

    String message() default "收款备注请勿输入表情包及特殊符号等异常信息";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
