package com.charge.api.web.service.pos.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.charge.api.web.constants.PosPayMethodEnum;
import com.charge.api.web.service.order.*;
import com.charge.api.web.service.pay.SwiftPassService;
import com.charge.api.web.service.pos.BillV2Service;
import com.charge.api.web.service.pos.PreDepositService;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.support.BankCollectionCheckSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.support.PrePayLockSupport;
import com.charge.api.web.vo.BillItem;
import com.charge.api.web.vo.pos.v3.AssetBill;
import com.charge.api.web.vo.pos.v3.CreateBillReq;
import com.charge.api.web.vo.pos.v3.CreateBillResp;
import com.charge.api.web.vo.pos.v3.PayResultVO;
import com.charge.bill.client.IncomeBillClient;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.LockObjectDTO;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.enums.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.AssertUtils;
import com.charge.common.util.DateUtils;
import com.charge.config.constant.CommonChargeItem;
import com.charge.config.dto.item.PreStoreItemConfigDTO;
import com.charge.core.util.CollectionUtil;
import com.charge.leaf.client.IdGeneratorClient;
import com.charge.maindata.enums.ChargeObjTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.order.client.EBusinessBaseOrderClient;
import com.charge.order.client.EBusinessMasterOrderClient;
import com.charge.order.client.OrderInstallmentClient;
import com.charge.order.client.OrderRuleClient;
import com.charge.order.dto.ebuiness.*;
import com.charge.order.dto.rule.CommunityBaseOrderRuleDTO;
import com.charge.order.dto.rule.CommunityOrderRuleQueryDTO;
import com.charge.order.enums.OrderRuleCategoryEnum;
import com.charge.pay.PayBusinessCallback;
import com.charge.pay.PayFactory;
import com.charge.pay.channel.lakala.vo.LakalaCreateOrderDTO;
import com.charge.pay.channel.lakala.vo.LakalaCreateOrderResultDTO;
import com.charge.pay.channel.lakala.vo.LakalaReq;
import com.charge.pay.channel.unionpay.vo.UnionPayCreateOrderDTO;
import com.charge.pay.channel.unionpay.vo.UnionPayReq;
import com.charge.pay.client.LakalaMerchantClient;
import com.charge.pay.client.PayRecordClient;
import com.charge.pay.client.TransferPayClient;
import com.charge.pay.domain.*;
import com.charge.pay.dto.CommunityBankAccountGroupDTO;
import com.charge.pay.dto.PayRecord;
import com.charge.pay.dto.pay.TransferPayCreateReqDTO;
import com.charge.pay.dto.pay.TransferPayDTO;
import com.charge.pay.dto.reward.CommunityBankAccountPlusDTO;
import com.charge.pay.enums.PayChannelEnum;
import com.charge.pay.enums.PayTradeStateEnum;
import com.charge.pay.exception.PayException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.charge.api.web.service.pos.impl.ChargeBillServiceImpl.chargeObjEnumOfCode;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/10/19 10:53
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BillV2ServiceImpl implements BillV2Service {

    private final ReceivableBillClient receivableBillClient;

    private final AssetSupport assetSupport;

    private final IncomeBillClient incomeBillClient;

    private final LakalaMerchantClient lakalaMerchantClient;

    private final BankCollectionCheckSupport bankCollectionCheckSupport;

    private final CommunitySupport communitySupport;

    private final PreDepositService preDepositService;

    private final PosCreateOrderService posCreateOrderService;

    private final CmbTransferCreateOrderService cmbTransferCreateOrderService;

    private final OrderRuleClient orderRuleClient;

    private final PayBusinessCallback payBusinessCallback;

    public static final BigDecimal HUNDRED =new BigDecimal("100");

    private final TransferPayClient transferPayClient;

    private final IdGeneratorClient idGeneratorClient;

    private final PrePayLockSupport prePayLockSupport;

    private final OrderInstallmentClient orderInstallmentClient;

    private final EBusinessBaseOrderClient eBusinessBaseOrderClient;

    private final EBusinessMasterOrderClient eBusinessMasterOrderClient;

    private final PayRecordClient payRecordClient;

    private final SwiftPassService swiftPassService;



    @Value("${transfer.bankName:招商银行深圳分行}")
    private String bankName;

    @Value("${createBillMaxBatchSize:10}")
    private int createBillMaxBatchSize;

    @Value("${synPosPayResult:false}")
    private Boolean synPosPayResult;

    @Value("${maxPayMemberLength:64}")
    private int maxPayMemberLength;


    private void checkBusiness(CreateBillReq createOrderReq,CreateOrderContext context) throws ChargeBusinessException {
        for (AssetBill assetBill :createOrderReq.getBills()){
            AssertUtils.notNull(assetBill.getAssetId(),ErrorInfoEnum.E3042.getCode());
        }
        List<BillItem> receivables = createOrderReq.getBills().stream().filter(bill -> bill.getReceivables() != null)
                .flatMap(bill -> bill.getReceivables().stream()).collect(Collectors.toList());
        Map<Long, ReceivableBillDTO> receivableMap = context.getReceivableMap();
        //应收需要校验金额与欠费一致，
        if(!CollectionUtils.isEmpty(receivables)){
            for (Map.Entry<Long,List<ReceivableBillDTO>> entry : context.getAssertIdToEffectiveReceivablesMapByIds().entrySet()) {
                swiftPassService.checkReceivableBillStatus(entry.getValue(),context.getCheckingReceivableList(),context.getAssertIdToEffectiveReceivablesMap().get(entry.getKey()));
            }

            for (BillItem billItem:receivables){
                ReceivableBillDTO receivableBillDTO = receivableMap.get(billItem.getId());
                //校验欠费存在
                if(receivableBillDTO==null){
                    throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),billItem.getItemName()+"欠费已经核销或者不存在");
                }
                BigDecimal arrears = receivableBillDTO.getArrearsAmount().add(receivableBillDTO.getPenaltyArrearsAmount());
                //校验金额相同
                if((!context.getAllowPenaltyIncrease())&&arrears.compareTo(billItem.getAmount())!=0){
                    throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),String.format("%s欠费:%2f,缴纳%2f不一致",receivableBillDTO.getItemName(),arrears,billItem.getAmount()));
                }
            }
        }
        //预存需要校验绑定关系
        for (AssetBill assetBill:createOrderReq.getBills()){
            List<BillItem> billItems = assetBill.getPrestores();
            if(CollectionUtils.isNotEmpty(billItems)){
                // 专项预存才需要校验
                List<Long> itemIds = billItems.stream().map(BillItem::getItemId).filter(itemId ->!CommonChargeItem.ITEM_ID.equals(itemId)).collect(Collectors.toList());
                List<Long> notBindItemIds = preDepositService.listChargeItemIdsNotBind(itemIds, createOrderReq.getCommunityId(), assetBill.getAssetId());
                if(!CollectionUtils.isEmpty(notBindItemIds)){
                    String notBindItemName = billItems.stream().filter(billItem -> notBindItemIds.contains(billItem.getItemId())).map(BillItem::getItemName).distinct()
                            .collect(Collectors.joining(","));
                    if(org.springframework.util.StringUtils.hasText(notBindItemName)){
                        throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),String.format("收费项%s未绑定，无法预存",notBindItemName));
                    }
                }
            }
        }
        if(bankCollectionCheckSupport.bankCollectingByAssetIds(context.getAssetMap().keySet())){
            throw new ChargeBusinessException(ErrorInfoEnum.E3042);
        }
    }

    private CreateOrderContext buildCreateOrderContext(CreateBillReq createOrderReq)throws ChargeBusinessException{
        CommunityDTO community = communitySupport.getCommunityById(createOrderReq.getCommunityId());
        List<Long> assetIds = createOrderReq.getBills().stream().map(AssetBill::getAssetId).distinct().collect(Collectors.toList());
        Map<Long, AssetDTO> assetMap = assetSupport.listAssetByIds(assetIds).stream().collect(Collectors.toMap(AssetDTO::getId, Function.identity(), (a, b) -> b));
        //校验资产均有效
        if(assetMap.size()!=assetIds.size()){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"部分资产不存在或者不生效:"+Sets.difference(new TreeSet<>(assetIds),assetMap.keySet()));
        }
        ReceivableConditionDTO receivableCondition = ReceivableConditionDTO.builder().communityId(community.getId())
                .billStatuses(org.assertj.core.util.Lists.newArrayList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),ReceivableBillStatusEnum.BILL_CHECKING.getCode()))
                .payStatuses(org.assertj.core.util.Lists.newArrayList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(), ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()))
                .assetIdList(assetIds)
                .chargeObject(ChargeObjTypeEnum.OWNER.getCode())
                .billType(ReceivableBillTypeEnum.RECEIVABLE.getCode()).build();
        ChargeResponse<List<ReceivableBillDTO>> receivablesResp = receivableBillClient.queryList(receivableCondition);
        List<ReceivableBillDTO> receivableBillsResponseData = AppInterfaceUtil.getResponseDataThrowException(receivablesResp);
        List<ReceivableBillDTO> receivableBills = receivableBillsResponseData.stream().filter(t->ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(t.getBillStatus())).collect(Collectors.toList());

        List<ReceivableBillDTO> chackingReceivableBills = new ArrayList<>();
        Map<Long, List<ReceivableBillDTO>> effectiveByIdsReceivableGroupAssetIdMap = new HashMap<>();
        Map<Long, List<ReceivableBillDTO>> effectiveReceivableGroupAssetIdMap = new HashMap<>();
        List<AssetBill> assetBillsByReceivables = createOrderReq.getBills().stream().filter(t -> CollectionUtils.isNotEmpty(t.getReceivables())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(assetBillsByReceivables)){
            List<Long> receivables = createOrderReq.getBills().stream().map(AssetBill::getReceivables).flatMap(Collection::stream).map(BillItem::getId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(receivableBillsResponseData)){
                throw new ChargeBusinessException(ErrorInfoEnum.E2003);
            }
            Map<Long, List<String>> assetIdToItemNamesMap = createOrderReq.getBills().stream().collect(Collectors.toMap(t -> t.getAssetId(), t -> t.getReceivables().stream().map(BillItem::getItemName).distinct().collect(Collectors.toList())));

            effectiveByIdsReceivableGroupAssetIdMap = receivableBillsResponseData.stream()
                    .filter(t -> assetIdToItemNamesMap.containsKey(t.getAssetId()) && assetIdToItemNamesMap.get(t.getAssetId()).contains(t.getItemName()) && receivables.contains(t.getId()))
                    .collect(Collectors.groupingBy(
                            ReceivableBillDTO::getAssetId)
                    );

            effectiveReceivableGroupAssetIdMap = receivableBills.stream()
                    .filter(t -> assetIdToItemNamesMap.containsKey(t.getAssetId()) && assetIdToItemNamesMap.get(t.getAssetId()).contains(t.getItemName()))
                    .collect(Collectors.groupingBy(
                            ReceivableBillDTO::getAssetId)
                    );

            chackingReceivableBills = receivableBillsResponseData.stream().filter(t->ReceivableBillStatusEnum.BILL_CHECKING.getCode().equals(t.getBillStatus())).collect(Collectors.toList());

        }

        Map<Long, ReceivableBillDTO> receivableMap = receivableBills.stream().collect(Collectors.toMap(ReceivableBillDTO::getId, Function.identity(), (a, b) -> b));
        Map<Long, List<ReceivableBillDTO>> assetReceivableMap = receivableBills.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId));
        Map<Long, OrderInstallmentDetailDTO> installmentDetailMap = Maps.newHashMap();
        Map<Long, EBusinessMasterOrderDTO> ebusinessBaseIdToMasterOrderMap = Maps.newHashMap();

        CreateOrderContext context= CreateOrderContext.builder()
                .community(community)
                .paymentTerminal(createOrderReq.getPaymentTerminal())
                .paymentChannel(createOrderReq.getPaymentChannel())
                .payHouseCount(createOrderReq.getBills().size())
                .assetType(AssetType.HOUSE)
                .payMember(createOrderReq.getPayMember())
                .totalPrice(createOrderReq.getTotalAmount())
                .memo(createOrderReq.getMemo())
                .collectorId(createOrderReq.getCollectorId())
                .collectorName(createOrderReq.getCollectorName())
                .deviceInfo(createOrderReq.getDevice())
                .createTime(new Date())
                .receivableMap(receivableMap)
                .checkingReceivableList(chackingReceivableBills)
                .assertIdToEffectiveReceivablesMapByIds(effectiveByIdsReceivableGroupAssetIdMap)
                .assertIdToEffectiveReceivablesMap(effectiveReceivableGroupAssetIdMap)
                .assetReceivableMap(assetReceivableMap)
                .assetMap(assetMap)
                .assetBillBusinesses(Lists.newArrayList())
                .totalAmount(createOrderReq.getTotalAmount())
                .orderInstallmentDetailMap(installmentDetailMap)
                .ebusinessBaseIdToMasterOrderMap(ebusinessBaseIdToMasterOrderMap)
                .build();
        Long incomeBillId = AppInterfaceUtil.getResponseDataThrowException(idGeneratorClient.nextId());
        context.setIncomeBillId(incomeBillId);
        if(context.getPaymentTerminal().equals(PaymentTerminalEnum.POS) || context.getPaymentTerminal().equals(PaymentTerminalEnum.POS_UNIONPAY)){
            PosPayMethodEnum posPayMethod = PosPayMethodEnum.byCode(createOrderReq.getPaymentMethod());
            context.setPaymentMethod(posPayMethod.getPaymentMethod());
            context.setPaymentChannel(posPayMethod.getPaymentChannel());
            context.setAllowPenaltyIncrease(false);
        } else if (context.getPaymentChannel().equals(PaymentChannelEnum.CMB_TRANSFER)) {
            context.setPaymentMethod(PaymentMethodEnum.TRANSFER_OFFLINE);
            context.setPaymentTerminal(PaymentTerminalEnum.CHARGE_SYSTEM);
            context.setAllowPenaltyIncrease(true);
        }

        List<Long> installmentIds = createOrderReq.getBills().stream().map(AssetBill::getOrders).filter(Objects::nonNull)
                .flatMap(a -> a.stream().map(BillItem::getInstallmentId)).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        for (Long installId : installmentIds) {
            InstallmentDetailQuery installmentDetailQuery = new InstallmentDetailQuery();
            installmentDetailQuery.setInstallmentId(installId);
            installmentDetailQuery.setCommunityId(community.getId());
            ChargeResponse<OrderInstallmentDetailDTO> chargeResponse = orderInstallmentClient.detail(installmentDetailQuery);
            OrderInstallmentDetailDTO orderInstallmentDetailDTO = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
            Assert.notNull(orderInstallmentDetailDTO, "订单进度单不存在:" + installId);
            installmentDetailMap.put(installId, orderInstallmentDetailDTO);
        }
        if(CollectionUtils.isEmpty(installmentIds)){
            List<Long> eBusinessBaseOrderIds = createOrderReq.getBills().stream().map(AssetBill::getOrders).filter(Objects::nonNull)
                    .flatMap(a -> a.stream().map(BillItem::getEBusinessBaseOrderId)).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(eBusinessBaseOrderIds)) {
                ChargeResponse<List<EBusinessBaseOrderDTO>> listChargeResponse = eBusinessBaseOrderClient.list(EBusinessBaseOrderConditionDTO.builder()
                        .ids(eBusinessBaseOrderIds).communityId(community.getId()).build());
                List<EBusinessBaseOrderDTO> eBusinessBaseOrders =  AppInterfaceUtil.getResponseDataThrowException(listChargeResponse);
                Assert.isTrue(eBusinessBaseOrderIds.size()==eBusinessBaseOrders.size(), "部分子单不存在");
                Map<String, List<EBusinessBaseOrderDTO>> extendOrderNoToEBusinessBaseOrders = eBusinessBaseOrders.stream().collect(Collectors.groupingBy(EBusinessBaseOrderDTO::getExtendOrderNo));

                Set<String> extendOrderNos = extendOrderNoToEBusinessBaseOrders.keySet();
                extendOrderNos.forEach(extendOrderNo -> {
                    ChargeResponse<List<EBusinessMasterOrderDTO>> eBusinessMasterOrderResp = eBusinessMasterOrderClient.list(EBusinessMasterOrderConditionDTO.builder()
                            .extendOrderNo(extendOrderNo).communityId(community.getId()).build());
                    List<EBusinessMasterOrderDTO> masterOrderDTOS = null;
                    try {
                        masterOrderDTOS = AppInterfaceUtil.getResponseDataThrowException(eBusinessMasterOrderResp);
                    } catch (ChargeBusinessException e) {
                        throw new RuntimeException(e);
                    }
                    Assert.notEmpty(masterOrderDTOS, "主单不存在");
                    EBusinessMasterOrderDTO eBusinessMasterOrderDTO = masterOrderDTOS.get(0);
                    List<EBusinessBaseOrderDTO> eBusinessBaseOrderDTOS = extendOrderNoToEBusinessBaseOrders.get(extendOrderNo);
                    eBusinessBaseOrderDTOS.forEach(eBusinessBaseOrderDTO ->  ebusinessBaseIdToMasterOrderMap.put(eBusinessBaseOrderDTO.getId(), eBusinessMasterOrderDTO));
                });
            }
        }

        // 填充支付订单号
        try {
            context.setOrderNum(IdGeneratorSupport.getIstance().nextId());
        } catch (Exception e) {
            throw new ChargeRuntimeException("调用获取支付订单号异常:" + e.getMessage(), e);
        }
        posCreateOrderService.fillRewardConfig(context);
        return context;
    }

    private void fillAssetBusiness(CreateBillReq createOrderReq,CreateOrderContext context) throws ChargeBusinessException {
        for (AssetBill assetBill:createOrderReq.getBills()){
            AssetBillBusiness assetBillBusiness=new AssetBillBusiness();
            context.getAssetBillBusinesses().add(assetBillBusiness);
            assetBillBusiness.setAsset(context.getAssetMap().get(assetBill.getAssetId()));
            assetBillBusiness.setBillBusinesses(Lists.newArrayList());
            //处理欠费
            fillAssetBusinessReceivables(context,assetBill,assetBillBusiness);
            //处理订单
            fillAssetBusinessOrders(context,assetBill,assetBillBusiness);
            //处理押金
            fillAssetBusinessDeposits(context,assetBill,assetBillBusiness);
            //处理预存
            fillAssetBusinessPreStores(context,assetBill,assetBillBusiness);
        }
    }
    public void fillAssetBusinessPreStores(CreateOrderContext context, AssetBill assetBill,AssetBillBusiness assetBillBusiness) throws ChargeBusinessException {
        if(CollectionUtils.isNotEmpty(assetBill.getPrestores())){
            List<PredepositBillBusiness> commonPreStores=Lists.newArrayList();
            List<PredepositBillBusiness> specialPreStores=Lists.newArrayList();
            assetBill.getPrestores().forEach(billItem -> {
                PredepositBillBusiness billBusiness = new PredepositBillBusiness();
                billBusiness.setType(BusinessTypeEnum.PREDEPOSIT_PAY);
                billBusiness.setChargeItemId(billItem.getItemId());
                billBusiness.setMoney(billItem.getAmount());
                if(CommonChargeItem.ITEM_ID.equals(billItem.getItemId())){
                    billBusiness.setName(CommonChargeItem.ITEM_NAME);
                    billBusiness.setPredepositType(PredepositTypeEnum.COMMON_DEPOSIT.getCode());
                    commonPreStores.add(billBusiness);
                }else {
                    billBusiness.setName(billItem.getItemName());
                    billBusiness.setPredepositType(PredepositTypeEnum.SPECIAL_DEPOSIT.getCode());
                    specialPreStores.add(billBusiness);
                }
            });
            //处理预存抵扣
            preStoreDeduction(commonPreStores,specialPreStores,assetBillBusiness,context);
            //扣完的预存需要去除
            if(CollectionUtils.isNotEmpty(commonPreStores)){
                commonPreStores.removeIf(next -> next.getMoney().compareTo(BigDecimal.ZERO) == 0);
                if(CollectionUtils.isNotEmpty(commonPreStores)){
                    assetBillBusiness.getBillBusinesses().addAll(commonPreStores);
                }
            }
            if(CollectionUtils.isNotEmpty(specialPreStores)){
                specialPreStores.removeIf(next -> next.getMoney().compareTo(BigDecimal.ZERO) == 0);
                if(CollectionUtils.isNotEmpty(specialPreStores)){
                    assetBillBusiness.getBillBusinesses().addAll(specialPreStores);
                }
            }
        }
    }

    private void preStoreDeduction(List<PredepositBillBusiness> commonPreStores,List<PredepositBillBusiness> specialPreStores,
                                   AssetBillBusiness assetBillBusiness,CreateOrderContext context) throws ChargeBusinessException {
        List<ReceivableBillDTO> receivableBills = context.getAssetReceivableMap().getOrDefault(assetBillBusiness.getAsset().getId(),Lists.newArrayList());
        List<Long>  receivableBillIdsInBills= assetBillBusiness.getBillBusinesses().stream().filter(billBusiness -> billBusiness instanceof ReceivablePayBillBusiness).map(billBusiness ->
                (ReceivablePayBillBusiness) billBusiness).map(ReceivablePayBillBusiness::getReceivableBillId).collect(Collectors.toList());
        //需要对应收进行排序，先抵扣早的欠费，本次已经缴纳的不需要抵扣
        List<ReceivableBillDTO> toDeductReceivables = receivableBills.stream().filter(receivableBillDTO -> !receivableBillIdsInBills.contains(receivableBillDTO.getId()))
                .sorted(Comparator.comparing(ReceivableBillDTO::getBelongYears)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(toDeductReceivables)){
            return;
        }
        //必须专项先抵扣，再抵扣通用
        if(CollectionUtils.isNotEmpty(specialPreStores)){
            preStoreDeduction(specialPreStores,toDeductReceivables,assetBillBusiness,context.getCommunity().getId());
        }
        if(CollectionUtils.isNotEmpty(commonPreStores)){
            preStoreDeduction(commonPreStores,toDeductReceivables,assetBillBusiness,context.getCommunity().getId());
        }
    }

    private void preStoreDeduction(List<PredepositBillBusiness> preStores,List<ReceivableBillDTO> toDeductReceivables, AssetBillBusiness assetBillBusiness,Long communityId) throws ChargeBusinessException {
        for (PredepositBillBusiness predepositBillBusiness:preStores){
            List<Long> deductItems = getDeductItems(predepositBillBusiness.getChargeItemId(), communityId);
            toDeductReceivables.forEach(toDeductReceivable->{
                if(deductItems.contains(toDeductReceivable.getItemId())){
                    BigDecimal arrears = toDeductReceivable.getArrearsAmount().add(toDeductReceivable.getPenaltyArrearsAmount());
                    //已经被抵扣了，跳过
                    if(arrears.compareTo(BigDecimal.ZERO)<=0){
                        return ;
                    }
                    //欠费大于预存的跳过
                    if(arrears.compareTo(predepositBillBusiness.getMoney())>0){
                        return ;
                    }
                    //添加应收支付业务
                    addReceivablePayBillBusiness(assetBillBusiness,toDeductReceivable,null);
                    //应收欠费置为0,预存扣减
                    predepositBillBusiness.setMoney(predepositBillBusiness.getMoney().subtract(arrears));
                    toDeductReceivable.setArrearsAmount(BigDecimal.ZERO);
                    toDeductReceivable.setPenaltyArrearsAmount(BigDecimal.ZERO);
                }
            });
        }

    }

    private List<Long> getDeductItems(Long preStoreItemId,Long communityId) throws ChargeBusinessException {
        if(CommonChargeItem.ITEM_ID.equals(preStoreItemId)){
            //获取项目预存配置
            PreStoreItemConfigDTO preStoreItemConfig = preDepositService.getCommunityPreStoreItem(communityId);
            return preStoreItemConfig.getCommonPreStoreItems();
        }else {
            return Lists.newArrayList(preStoreItemId);
        }
    }

    private void fillAssetBusinessReceivables(CreateOrderContext context, AssetBill assetBill, AssetBillBusiness assetBillBusiness) {
        if (CollectionUtils.isNotEmpty(assetBill.getReceivables())) {
            Map<Long, ReceivableBillDTO> receivableMap = context.getReceivableMap();
            assetBill.getReceivables().forEach(billItem -> {
                ReceivableBillDTO receivableBill = receivableMap.get(billItem.getId());
                addReceivablePayBillBusiness(assetBillBusiness, receivableBill, billItem);
            });
        }
    }

    private void addReceivablePayBillBusiness(AssetBillBusiness assetBillBusiness, ReceivableBillDTO receivableBill, BillItem billItem) {

        if (receivableBill.getArrearsAmount().compareTo(BigDecimal.ZERO) > 0) {
            ReceivablePayBillBusiness arrears = new ReceivablePayBillBusiness()
                    .setBelongYears(receivableBill.getBelongYears())
                    .setReceivableBillId(receivableBill.getId())
                    .setChargeType(ChargeTypeEnum.PRINCIPAL)
                    .setChargeObject(chargeObjEnumOfCode(receivableBill.getChargeObject()))
                    .setAssetUseStatus(receivableBill.getAssetUseStatus());
            arrears.setName(receivableBill.getItemName())
                    .setChargeItemId(receivableBill.getItemId())
                    .setMoney(receivableBill.getArrearsAmount())
                    .setType(BusinessTypeEnum.NORMAL_PAY);
            assetBillBusiness.getBillBusinesses().add(arrears);
            if(billItem!=null){
                billItem.setAmount(billItem.getAmount().subtract(receivableBill.getArrearsAmount()));
            }
        }
        if (receivableBill.getPenaltyArrearsAmount().compareTo(BigDecimal.ZERO) > 0||
                (billItem!=null&&billItem.getAmount().compareTo(BigDecimal.ZERO)>0)) {
            ReceivablePayBillBusiness penalty = new ReceivablePayBillBusiness()
                    .setBelongYears(receivableBill.getBelongYears())
                    .setReceivableBillId(receivableBill.getId())
                    .setChargeType(ChargeTypeEnum.PENALTY)
                    .setChargeObject(chargeObjEnumOfCode(receivableBill.getChargeObject()))
                    .setAssetUseStatus(receivableBill.getAssetUseStatus());
            penalty.setName(receivableBill.getItemName())
                    .setChargeItemId(receivableBill.getItemId())
                    .setMoney(receivableBill.getPenaltyArrearsAmount())
                    .setType(BusinessTypeEnum.NORMAL_PAY);
            if(billItem!=null){
                if(billItem.getAmount().compareTo(BigDecimal.ZERO)>0){
                    penalty.setMoney(billItem.getAmount());
                }else {
                    return;
                }
            }
            assetBillBusiness.getBillBusinesses().add(penalty);
        }
    }
    public void fillAssetBusinessOrders(CreateOrderContext context, AssetBill assetBill,AssetBillBusiness assetBillBusiness) throws ChargeBusinessException {
        if(CollectionUtils.isNotEmpty(assetBill.getOrders())){
            List<Long> installIds = assetBill.getOrders().stream().map(BillItem::getInstallmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, String> installIdToSecondClassificationId=new HashMap<>();
            fillInstallIdToSecondClassificationIdMap(context, installIds, installIdToSecondClassificationId);
            //获取临停的费项列表用于判断是临停还是普通的临时通用
            Set<Long> chargeItemIdsOfTempPark = getChargeItemIdsOfTempPark(context.getCommunity().getId());
            assetBill.getOrders().forEach(billItem -> {
                OrderRuleCategoryEnum ruleCategoryEnum=chargeItemIdsOfTempPark.contains(billItem.getItemId())?OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_301: OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_300;
                OrderBillBusiness orderBillBusiness = new OrderBillBusiness()
                        .setInstallmentId(billItem.getInstallmentId())
                        .setEBusinessBaseOrderId(billItem.getEBusinessBaseOrderId())
                        .setSubsetId(ruleCategoryEnum.getCode().longValue());
                orderBillBusiness.setMoney(billItem.getAmount())
                        .setChargeItemId(billItem.getItemId())
                        .setName(billItem.getItemName())
                        .setType(BusinessTypeEnum.TEMP_PAY);
                if(billItem.getInstallmentId()!=null&&installIdToSecondClassificationId.containsKey(billItem.getInstallmentId())){
                    orderBillBusiness.setSubsetId(Long.parseLong(installIdToSecondClassificationId.get(billItem.getInstallmentId())));
                }else if (billItem.getEBusinessBaseOrderId()!=null&&context.getEbusinessBaseIdToMasterOrderMap().containsKey(billItem.getEBusinessBaseOrderId())){
                    EBusinessMasterOrderDTO eBusinessMasterOrderDTO = context.getEbusinessBaseIdToMasterOrderMap().get(billItem.getEBusinessBaseOrderId());
                    orderBillBusiness.setSubsetId(Long.parseLong(eBusinessMasterOrderDTO.getSecondClassificationId()));
                }
                assetBillBusiness.getBillBusinesses().add(orderBillBusiness);
            });
        }
    }

    private void fillInstallIdToSecondClassificationIdMap(CreateOrderContext context, List<Long> installIds, Map<Long, String> installIdToSecondClassificationIdMap) throws ChargeBusinessException {
        for (Long installId : installIds) {
            OrderInstallmentDetailDTO orderInstallmentDetailDTO = context.getOrderInstallmentDetailMap().get(installId);
            EBusinessMasterOrderDTO eBusinessMasterOrderDTO = orderInstallmentDetailDTO.getEBusinessMasterOrderDTO();
            installIdToSecondClassificationIdMap.put(installId, eBusinessMasterOrderDTO.getSecondClassificationId());
        }
    }


    private Set<Long> getChargeItemIdsOfTempPark(Long communityId) throws ChargeBusinessException {
        CommunityOrderRuleQueryDTO communityOrderRuleQueryDTO=new CommunityOrderRuleQueryDTO();
        communityOrderRuleQueryDTO.setCommunityId(communityId);
        communityOrderRuleQueryDTO.setSecondClassificationId(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_301.getCode());
        ChargeResponse<List<CommunityBaseOrderRuleDTO>> response = orderRuleClient.listByCommunityIdAndSubsetId(communityOrderRuleQueryDTO);
        List<CommunityBaseOrderRuleDTO> communityBaseOrderRuleS = AppInterfaceUtil.getResponseDataThrowException(response);
        if(CollectionUtils.isEmpty(communityBaseOrderRuleS)){
            return Sets.newHashSet();
        }else {
            String chargeItemIdStr = communityBaseOrderRuleS.get(0).getChargeItemId();
            List<Long> chargeItemIds = JSONArray.parseArray(chargeItemIdStr, Long.class);
            return Sets.newHashSet(chargeItemIds);
        }
    }

    public void fillAssetBusinessDeposits(CreateOrderContext context, AssetBill assetBill,AssetBillBusiness assetBillBusiness){
        if(CollectionUtils.isNotEmpty(assetBill.getDeposits())){
            assetBill.getDeposits().forEach(billItem -> {
                PredepositBillBusiness billBusiness=new PredepositBillBusiness();
                billBusiness.setPredepositType(PredepositTypeEnum.DEPOSIT.getCode())
                        .setMoney(billItem.getAmount())
                        .setName(billItem.getItemName())
                        .setType(BusinessTypeEnum.PREDEPOSIT_PAY)
                        .setChargeItemId(billItem.getItemId());
                assetBillBusiness.getBillBusinesses().add(billBusiness);
            });
        }
    }

    private void formatAmount(List<BillItem> billItems){
        if(billItems!=null){
            billItems.forEach(billItem -> billItem.setAmount(billItem.getAmount().setScale(2, RoundingMode.HALF_UP)));
        }
    }

    private void formatAmount(CreateBillReq createOrderReq){
        for (AssetBill assetBill:createOrderReq.getBills()){
            formatAmount(assetBill.getReceivables());
            formatAmount(assetBill.getPrestores());
            formatAmount(assetBill.getOrders());
            formatAmount(assetBill.getDeposits());
            assetBill.setAmount(assetBill.getAmount().setScale(2, RoundingMode.HALF_UP));
        }
    }

    @Override
    public CreateBillResp createBill(CreateBillReq createOrderReq) throws ChargeBusinessException{
        //校验参数合规
        formatAmount(createOrderReq);
        checkParams(createOrderReq);
        //构建上下文
        CreateOrderContext context = buildCreateOrderContext(createOrderReq);
        //校验业务合规
        checkBusiness(createOrderReq,context);
        //填充业务
        fillAssetBusiness(createOrderReq,context);
        lock(context);
        if(createOrderReq.getPaymentTerminal().equals(PaymentTerminalEnum.POS)){
            posCreateOrderService.fillLakalaMerchant(context);
            String externalOrderNum=createLakalaOrder(createOrderReq, context);
            context.setOutTransactionNo(externalOrderNum);
        }else if(createOrderReq.getPaymentTerminal().equals(PaymentTerminalEnum.POS_UNIONPAY)){
            posCreateOrderService.fillLakalaMerchant(context);
            validateMerchantNo(createOrderReq, context);
            String externalOrderNum=createUnionPayOrder(createOrderReq, context);
            context.setOutTransactionNo(externalOrderNum);
        }else if (createOrderReq.getPaymentChannel().equals(PaymentChannelEnum.CMB_TRANSFER)){
            cmbTransferCreateOrderService.fillCmbTransferCommunityBankAccount(context);
            String externalOrderNum=createCmbTransferOrder(createOrderReq,context);
            context.setOutTransactionNo(externalOrderNum);
        }else {
            throw new IllegalArgumentException("not support paymentTerminal");
        }
        posCreateOrderService.createOrder(context);
        return buildBillResp(context);
    }

    private static void validateMerchantNo(CreateBillReq createOrderReq, CreateOrderContext context) {
        if(StringUtils.hasText(createOrderReq.getMerchantNo())){
            if(!createOrderReq.getMerchantNo().equals(context.getUnionPayMerchant())){
                throw new IllegalArgumentException(String.format("当前机器绑定的商编不属于%s，请不要混刷", context.getCommunity().getName()));
            }
        }
    }

    private void lock(CreateOrderContext context) throws ChargeBusinessException {
        Set<Long> receivableIds = context.getAssetBillBusinesses().stream().flatMap(a -> a.getBillBusinesses().stream().filter(b -> b instanceof ReceivablePayBillBusiness)).map(b -> ((ReceivablePayBillBusiness) b).getReceivableBillId()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(receivableIds)) {
            return;
        }
        LockObjectDTO lockObjectDTO = LockObjectDTO.builder().mark(context.getOrderNum()).paymentSource(PaymentTerminalEnum.CHARGE_SYSTEM.getCode()).paymentMethod(context.getPaymentMethod().getPaymentMethod()).businessScene(getByPaymentChannel(context.getPaymentChannel(), context.getPaymentTerminal()).getCode()).receiveBillIdList(receivableIds).payMember(context.getPayMember()).build();
        prePayLockSupport.lockAndClose(lockObjectDTO);
    }

    private BillPrePaySceneEnum getByPaymentChannel(PaymentChannelEnum paymentChannel, PaymentTerminalEnum paymentTerminalEnum) {
        if (PaymentChannelEnum.CMB_TRANSFER.equals(paymentChannel)) {
            return BillPrePaySceneEnum.CMB_TRANSFER;
        } else if (PaymentTerminalEnum.POS.equals(paymentTerminalEnum)) {
            return BillPrePaySceneEnum.POS;
        } else {
            return BillPrePaySceneEnum.ZHAOXI_LIFE;
        }
    }

    private CreateBillResp buildBillResp(CreateOrderContext context){
        CreateBillResp resp=new CreateBillResp();
        if(PaymentTerminalEnum.POS_UNIONPAY.equals(context.getPaymentTerminal())){
            resp.setMerchantNo(context.getUnionPayMerchant());
        }else {
            resp.setMerchantNo(context.getLakalaMerchant());
        }
        resp.setOrderNum(context.getOrderNum());
        resp.setExternalOrderNum(context.getOutTransactionNo());
        resp.setId(String.valueOf(context.getAssetBillBusinesses().get(0).getAssetTransactionId()));
        resp.setContext(context);
        return resp;
    }

    /**
     * 先查询实收表（income_bill）字段pay_status，为已支付（pay_status=1）直接返回，其他状态查询拉卡拉接口
     */
    @Override
    public PayResultVO getPayResult(Long communityId, String orderNum) throws ChargeBusinessException {
        ChargeResponse<PayRecord> chargeResponse = payRecordClient.selectByOrderNum(orderNum);
        PayRecord payRecord = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        Assert.notNull(payRecord,"订单不存在");
        if(Objects.equals(BillPayStatusEnum.SUCCESS.getCode(), payRecord.getPayStatus())){
            return PayResultVO.builder()
                    .payStatus(BillPayStatusEnum.SUCCESS.getCode().toString())
                    .totalAmount(payRecord.getMoney().toString())
                    .payTime(DateUtils.format(payRecord.getPaymentTime(), DateUtils.FORMAT_3))
                    .externalOrderNum(payRecord.getReferNo())
                    .orderNum(orderNum)
                    .build();
        }
        QueryResultDO queryResultDO;
        String merchantNumber = payRecord.getMercid();
        if (PaymentTerminalEnum.POS.getCode().equals(payRecord.getPaymentTerminal())){
            queryResultDO = getPayResultFromLakala(orderNum, merchantNumber);
        }else {
            queryResultDO = getPayResultFromUnionPay(orderNum, merchantNumber);
        }
        //拉卡拉成功，我方非成功的，可选同步状态
        if(Boolean.TRUE.equals(synPosPayResult)&&queryResultDO.getTradeState().equals(PayTradeStateEnum.SUCCESS.getCode())){
            PayOrderResultDO payOrderResultDO=new PayOrderResultDO();
            payOrderResultDO.setOutTradeNo(queryResultDO.getOutTradeNo());
            payOrderResultDO.setTransactionId(queryResultDO.getTransactionId());
            //需要传入单位分
            payOrderResultDO.setTotalFee(queryResultDO.getTotalFee());
            payOrderResultDO.setTradeState(PayTradeStateEnum.SUCCESS.getCode());
            payOrderResultDO.setPaymentCode(queryResultDO.getPaymentCode());
            payOrderResultDO.setPayFinishTime(queryResultDO.getPayFinishTime());
            payOrderResultDO.setPaymentCode(queryResultDO.getPaymentCode());
            try {
                payBusinessCallback.deal(payOrderResultDO);
            } catch (Exception e) {
                throw new ChargeBusinessException(e);
            }
        }
        return PayResultVO.builder()
                .payStatus(convertToOwnPayStatus(PayTradeStateEnum.byCode(queryResultDO.getTradeState())))
                .payTime(queryResultDO.getPayFinishTime())
                .totalAmount(centToDollarForString(queryResultDO.getTotalFee()))
                .orderNum(queryResultDO.getOutTradeNo())
                .externalOrderNum(queryResultDO.getTransactionId()).build();
    }

    private QueryResultDO getPayResultFromLakala(String orderNum,String lakalaMerchantNumber){
        QueryOrderDO queryOrderDO = new QueryOrderDO();
        queryOrderDO.setTransactionId(orderNum);
        queryOrderDO.setTradeScene(lakalaMerchantNumber);
        return PayFactory.getCallbackPay(PayChannelEnum.LAKALA.getCode()).orderStatusQuery(queryOrderDO);
    }

    private QueryResultDO getPayResultFromUnionPay(String orderNum,String unionPayMerchantNumber){
        QueryOrderDO queryOrderDO = new QueryOrderDO();
        queryOrderDO.setTransactionId(orderNum);
        queryOrderDO.setMerchantNo(unionPayMerchantNumber);
        return PayFactory.getCallbackPay(PayChannelEnum.UNIONPAY.getCode()).orderStatusQuery(queryOrderDO);
    }

    private BigDecimal sum(List<BillItem> billItems){
        if(org.springframework.util.CollectionUtils.isEmpty(billItems)){
            return BigDecimal.ZERO;
        }
        return billItems.stream().map(BillItem::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
    }
    private void checkParams(CreateBillReq createOrderReq) throws ChargeBusinessException {

        AssertUtils.isTrue(createOrderReq.getPayMember() == null || createOrderReq.getPayMember().length() <= maxPayMemberLength
                ,ErrorInfoEnum.E1002.getCode(),"缴费人长度不能超过" + maxPayMemberLength+",请选择具体的缴费人");
        //校验金额
        checkItems(createOrderReq);
        //校验最大资产数量
        AssertUtils.isTrue(createOrderReq.getBills().size()<=createBillMaxBatchSize,ErrorInfoEnum.E1002.getCode(),"一次最多缴费房间数:"+createBillMaxBatchSize);
    }

    private void checkItems(CreateBillReq createOrderReq) throws ChargeBusinessException {
        BigDecimal totalMount=BigDecimal.ZERO;
        for (AssetBill assetBill: createOrderReq.getBills()){
            if(CollectionUtils.isNotEmpty(assetBill.getReceivables())){
                for (BillItem billItem:assetBill.getReceivables()){
                    //校验欠费必须有应收单id
                    AssertUtils.notNull(billItem.getId(),ErrorInfoEnum.E1002.getCode(),"应收单id为空");
                }
            }
            if(CollectionUtils.isNotEmpty(assetBill.getOrders())){
                for (BillItem billItem:assetBill.getOrders()){
                    //校验欠费必须有应收单id
                    if(billItem.getInstallmentId()!=null){
                        AssertUtils.notNull(billItem.getEBusinessBaseOrderId(),ErrorInfoEnum.E1002.getCode(),"分期账单的商品id不能为空");
                    }
                }
            }
            BigDecimal assetTotal = sum(assetBill.getReceivables()).add(sum(assetBill.getPrestores()))
                    .add(sum(assetBill.getDeposits())).add(sum(assetBill.getOrders()));
            AssertUtils.isTrue(assetTotal.compareTo(assetBill.getAmount())==0,ErrorInfoEnum.E1002.getCode(),"资产账单汇总金额与账单细项汇总不一致");
            totalMount=totalMount.add(assetTotal);
        }
    }

    private List<String> listLockKey(Collection<Long> assetIds) throws ChargeBusinessException {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(assetIds)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1013);
        }

        List<String> lockKeys = new ArrayList<>();
        assetIds.forEach(assetId -> {
            lockKeys.add("POS_COMMON_PAY_LOCK" + assetId);
        });
        return lockKeys;
    }

    /**
     * 拉卡拉支付状态转化为收费系统支付状态
     * 拉卡拉：                收费系统：
     * 0:待支付                0:未支付
     * 1:支付中                0:未支付
     * 2:支付成功              1:支付成功
     * 3:支付失败              2:支付失败
     * 4:已过期                4:已关闭
     * 5:已取消                4:已关闭
     * 6：部分退款或者全部退款   2:支付失败
     *
     * @param tradeState
     * @return
     */
    private String convertToOwnPayStatus(PayTradeStateEnum tradeState) {
        switch (tradeState) {
            case SUCCESS:
                return BillPayStatusEnum.SUCCESS.getCode().toString();
            case NOTPAY:
            case ING:
                return BillPayStatusEnum.UN_PAY.getCode().toString();
            case REVERSE:
            case CLOSED:
                return BillPayStatusEnum.CLOSE.getCode().toString();
            default:
                return BillPayStatusEnum.FAILURE.getCode().toString();
        }
    }

    //分转换为元，返回string类型
    public String centToDollarForString(String t){
        if (t == null) {
            return "0";
        } else {
            BigDecimal amount = new BigDecimal(t);
            amount = amount.divide(new BigDecimal(100));
            return amount.toString();
        }
    }

    /**
     * 将单位为元的金额转换为单位为分
     *
     * @param yuan 单位为元的字符型值
     * @return
     */
    public static String dollarToCentForString(String yuan) {
        String value;

        try {
            BigDecimal var1 = new BigDecimal(yuan);
            BigDecimal var2 = new BigDecimal(100);
            BigDecimal var3 = var1.multiply(var2);
            value = var3.stripTrailingZeros().toPlainString();
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format("非法金额[%s]", yuan));
        }

        return value;
    }

    /**
     * 创建拉卡拉v3版 订单
     * @return
     * @throws ChargeBusinessException
     */
    private String createCmbTransferOrder(CreateBillReq createOrderReq, CreateOrderContext context) throws ChargeBusinessException{
        CommunityBankAccountGroupDTO communityBankAccount = context.getCommunityBankAccount();
        List<CommunityBankAccountPlusDTO> bankAccounts = communityBankAccount.getBankAccounts();
        CommunityBankAccountPlusDTO communityBankAccountPlusDTO = bankAccounts.get(0);
        TransferPayCreateReqDTO transferPayCreateReqDTO=TransferPayCreateReqDTO.builder()
                .bizId(createOrderReq.getArrearsNoticeId())
                .orderNum(context.getOrderNum())
                .communityId(createOrderReq.getCommunityId())
                .acceptorAccount(communityBankAccount.getBankActualAccount())
                .acceptorVirtualAccount(communityBankAccountPlusDTO.getBankVirtualAccount())
                .acceptorAccountName(communityBankAccount.getBankActualAccountName())
                .acceptorAccountBank(bankName)
                .incomeBillId(context.getIncomeBillId())
                .amount(context.getTotalAmount())
                .build();
        ChargeResponse<TransferPayDTO> transferPayDTOChargeResponse = transferPayClient.create(transferPayCreateReqDTO);
        TransferPayDTO transferPayDTO = AppInterfaceUtil.getResponseDataThrowException(transferPayDTOChargeResponse);
        return  transferPayDTO.getAccountSubUnitCode();
    }

    /**
     * 创建拉卡拉v3版 订单
     * @return
     * @throws ChargeBusinessException
     */
    private String createLakalaOrder(CreateBillReq createOrderReq, CreateOrderContext context) throws ChargeBusinessException{
        PayOrderSubmitResultDO response;
        LakalaReq<LakalaCreateOrderDTO> lakalaReq = buildCreateOrderRequest(createOrderReq, context);
        PayOrderDO payOrderDO = new PayOrderDO();
        payOrderDO.setTotalFee(createOrderReq.getTotalAmount().multiply(HUNDRED).toString());
        payOrderDO.setOutTradeNo(context.getOrderNum());
        payOrderDO.setBody(JacksonUtils.toJson(lakalaReq));
        try {
            response = PayFactory.getCallbackPay(PayChannelEnum.LAKALA.getCode()).payOrder(payOrderDO);
        }catch (PayException payException){
            throw new ChargeBusinessException(payException.getCode()+"",payException.getMessage());
        }
        LakalaCreateOrderResultDTO lakalaCreateOrderResultDTO = (LakalaCreateOrderResultDTO) response.getPayInfo();
        return lakalaCreateOrderResultDTO.getPayOrderNo();
    }

    /**
     * 创建银联 订单
     * @return
     * @throws ChargeBusinessException
     */
    private String createUnionPayOrder(CreateBillReq createOrderReq, CreateOrderContext context) throws ChargeBusinessException{
        PayOrderSubmitResultDO response;
        UnionPayReq<UnionPayCreateOrderDTO> unionPayReq = buildCreateUnionPayOrderRequest(createOrderReq, context);
        PayOrderDO payOrderDO = new PayOrderDO();
        payOrderDO.setTotalFee(createOrderReq.getTotalAmount().multiply(HUNDRED).toString());
        payOrderDO.setOutTradeNo(context.getOrderNum());
        payOrderDO.setBody(JacksonUtils.toJson(unionPayReq));
        try {
            response = PayFactory.getCallbackPay(PayChannelEnum.UNIONPAY.getCode()).payOrder(payOrderDO);
        }catch (PayException payException){
            log.error("银联订单登记失败,项目id:{}:req:{},errMsg:{}",createOrderReq.getCommunityId(),unionPayReq.getReq_data(),payException.getMessage());
            throw new ChargeBusinessException(payException.getCode()+"",payException.getMessage());
        }
        return response.getOutTradeNo();
    }

    private LakalaReq<LakalaCreateOrderDTO> buildCreateOrderRequest(CreateBillReq createOrderReq, CreateOrderContext context){
        Assert.hasText(context.getLakalaMerchant(),"该项目拉卡拉商户号未配置:"+context.getCommunity().getName());
        LakalaReq<LakalaCreateOrderDTO> lakalaReq = new LakalaReq<>();
        LakalaCreateOrderDTO createOrderData = new LakalaCreateOrderDTO();
        createOrderData.setOut_order_no(context.getOrderNum());
        createOrderData.setMerchant_no(context.getLakalaMerchant());
        createOrderData.setTotal_amount(dollarToCentForString(createOrderReq.getTotalAmount().toString()));
        lakalaReq.setReq_data(createOrderData);
        return lakalaReq;
    }

    private UnionPayReq<UnionPayCreateOrderDTO> buildCreateUnionPayOrderRequest(CreateBillReq createOrderReq, CreateOrderContext context){
        Assert.hasText(context.getUnionPayMerchant(),"该项目银联商户号未配置:"+context.getCommunity().getName());
        UnionPayReq<UnionPayCreateOrderDTO> unionPayReq = new UnionPayReq<>();
        UnionPayCreateOrderDTO createOrderData = new UnionPayCreateOrderDTO();
        createOrderData.setOut_order_no(context.getOrderNum());
        createOrderData.setMerchant_no(context.getUnionPayMerchant());
        createOrderData.setTotal_amount(dollarToCentForString(createOrderReq.getTotalAmount().toString()));
        unionPayReq.setReq_data(createOrderData);
        return unionPayReq;
    }

}
