package com.charge.api.web.dto.ssdp;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/2/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SsdpRefundAuditConditionDTO implements Serializable {

    private static final long serialVersionUID = -8887131054739369015L;

    /**
     * 单据总数
     */
    private String totalCount;

    /**
     * 退款列表详情
     */
    private List<SsdpRefundAuditDTO> recordList;
}
