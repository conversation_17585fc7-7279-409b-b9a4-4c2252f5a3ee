package com.charge.api.web.dto.invoice;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date: 2023/09/05/ 19:34
 * @description
 */
@Data
public class ThirdPartyInvoiceDetailDTO implements Serializable {
    private static final long serialVersionUID = 5099819166118114687L;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 货物或应税劳务名称 ，禁止特殊字符
     */
    private String cargoName;

    /**
     * 规格型号， 禁止特殊字符
     */
    private String itemSpec;

    /**
     * 数量单位， 禁止特殊字符
     */
    private String quantityUnit;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 税收分类编码
     */
    private String goodsTaxNo;

    /**
     * 税率 小数
     */
    private BigDecimal taxRate;

    /**
     * 含税金额   数量*含税单价
     */
    private BigDecimal amountWithTax;

    /**
     * 不含税金额
     */
    private BigDecimal amountWithoutTax;

    /**
     * 含税单价
     */
    private BigDecimal unitPrice;

    /**
     *  税额
     */
    private BigDecimal taxAmount;

    /**
     *  扣除额
     */
    private BigDecimal deduction;

    /**
     * 是否享受税收优惠政策
     */
    private String taxPre;

    /**
     * 税收优惠
     */
    private String taxPreCon;

    /**
     * 零税率标志
     */
    private String zeroTax;

    /**
     * 拓展字段1：区域（如示范区）
     */
    private String ext1;

    /**
     * 拓展字段2：阶段（设计前期阶段）
     */
    private String ext2;
}
