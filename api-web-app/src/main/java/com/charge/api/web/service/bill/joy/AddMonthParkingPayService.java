package com.charge.api.web.service.bill.joy;


import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.vo.CardPayItemInfo;
import com.charge.api.web.vo.joylife.request.YueCardPayRequestVO;
import com.charge.bill.client.flow.AssetPaymentClient;
import com.charge.bill.dto.BillAssetInfoDTO;
import com.charge.bill.dto.PayOrAdjustItemDTO;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.domain.AssetPayDTO;
import com.charge.bill.dto.domain.AssetPaymentDetailDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.core.util.CollectionUtil;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 朝昔月卡缴费
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddMonthParkingPayService {

    private final AssetPaymentClient assetPaymentClient;

    /**
     *
     * @param request
     * @param communityDTO
     * @param outTradeNo
     * @param orderNum
     * @param assetAdapter
     * @param houseInfo
     * @param itemInfos
     * @throws ChargeBusinessException
     */
    public CreateBillResponse createBill(YueCardPayRequestVO request, CommunityDTO communityDTO,
                                         String outTradeNo, String orderNum, AssetAdapter assetAdapter, HouseDTO houseInfo, List<CardPayItemInfo> itemInfos) throws ChargeBusinessException {
        AssetPayDTO assetPayDTO = new AssetPayDTO();
        assetPayDTO.setAssetPayBaseDTO(createAssetOrderBaseDTO(request, communityDTO, outTradeNo, orderNum, itemInfos));
        assetPayDTO.setAssetPaymentDetailDTO(buildAssetPaymentDetailDTO(assetAdapter, houseInfo, itemInfos));
        return AppInterfaceUtil.getResponseDataThrowException(assetPaymentClient.createBill(assetPayDTO));
    }

    /**
     * 构建基础信息
     * @param request
     * @param communityDTO
     * @param outTradeNo
     * @param orderNum
     * @return
     */
    private AssetPayBaseDTO createAssetOrderBaseDTO(YueCardPayRequestVO request, CommunityDTO communityDTO,
                                                    String outTradeNo, String orderNum, List<CardPayItemInfo> itemInfos) {
        AssetPayBaseDTO assetPayBaseDTO = new AssetPayBaseDTO();
        assetPayBaseDTO.setCommunityId(communityDTO.getId());
        assetPayBaseDTO.setCommunityName(communityDTO.getName());
        assetPayBaseDTO.setOrderNum(orderNum);
        assetPayBaseDTO.setOutTransactionNo(outTradeNo);
        assetPayBaseDTO.setGoodsName(itemInfos.size() > 1 ? PayRelatedConstants.GOODSNAME_FOR_MERGE : PayRelatedConstants.GOODSNAME_MONTH_PARKING);
        assetPayBaseDTO.setActualPrice(request.getAmount());
        assetPayBaseDTO.setPaymentTerminal(PaymentTerminalEnum.handleJoyLifeWechatApplet(request.getPaymentSource()));
        assetPayBaseDTO.setPayMember(request.getPayMember());
        assetPayBaseDTO.setPayMemberId(request.getPayMemberId());
        assetPayBaseDTO.setCreateUser(request.getPayMember());
        assetPayBaseDTO.setPayHouseCount(1);
        assetPayBaseDTO.setClientSourceEnum(ClientSourceEnum.JOY_MONTH_PARKING_PAY);
        CommonUtils.fillPaymentMethodInfo(request.getPaymentMethod(), assetPayBaseDTO);
        if (Objects.equals(PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel(),request.getPaymentSource())) {
            assetPayBaseDTO.setPaymentChannel(request.getPaymentSource());
        }
        return assetPayBaseDTO;
    }

    /**
     * 构建缴费数据
     * @param assetAdapter
     * @param houseInfo
     * @param itemInfos
     * @return
     */
    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(AssetAdapter assetAdapter, HouseDTO houseInfo, List<CardPayItemInfo> itemInfos) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 资产信息
        assetPaymentDetailDTO.setBillAssetInfoDTO(createBillAssetInfoDTO(assetAdapter, houseInfo));
        // 订单信息
        assetPaymentDetailDTO.setOrderItems(buildOrderItem(itemInfos));
        return assetPaymentDetailDTO;
    }
    private BillAssetInfoDTO createBillAssetInfoDTO(AssetAdapter assetAdapter, HouseDTO houseInfo) {
        BillAssetInfoDTO billAssetInfoDTO = BeanCopierWrapper.copy(assetAdapter, BillAssetInfoDTO.class);
        billAssetInfoDTO.setAssetId(assetAdapter.getId());
        billAssetInfoDTO.setAssetType(assetAdapter.getType());
        billAssetInfoDTO.setAssetName(assetAdapter.getSubName());
        billAssetInfoDTO.setAssetCode(assetAdapter.getSubCode());
        if (CollectionUtil.isNotEmpty(assetAdapter.getListCustomer())) {
            CustomerDTO customerDTO = assetAdapter.getListCustomer().get(0);
            billAssetInfoDTO.setOwnerId(customerDTO.getId());
            billAssetInfoDTO.setOwnerName(customerDTO.getCustomerName());
        }
        billAssetInfoDTO.setBuildingId(houseInfo.getBuildingId());
        billAssetInfoDTO.setBuildingName(houseInfo.getBuildingName());
        billAssetInfoDTO.setUnitId(houseInfo.getUnitId());
        billAssetInfoDTO.setUnitName(houseInfo.getUnitName());
        return billAssetInfoDTO;
    }
    private List<PayOrAdjustItemDTO> buildOrderItem(List<CardPayItemInfo> itemInfos) {
        if (CollectionUtils.isEmpty(itemInfos)) {
            return Arrays.asList();
        }
        return itemInfos.stream().map(cardPayItemInfo -> {
            PayOrAdjustItemDTO payOrAdjustItemDTO = new PayOrAdjustItemDTO();
            payOrAdjustItemDTO.setItemId(cardPayItemInfo.getItemId());
            payOrAdjustItemDTO.setItemName(cardPayItemInfo.getItemName());
            payOrAdjustItemDTO.setAmount(cardPayItemInfo.getPrice());
            return payOrAdjustItemDTO;
        }).collect(Collectors.toList());
    }
}
