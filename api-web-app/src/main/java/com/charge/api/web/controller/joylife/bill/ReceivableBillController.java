package com.charge.api.web.controller.joylife.bill;


import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import com.charge.api.web.annotation.JoyLifeCheckParam;
import com.charge.api.web.service.joylife.ReceivableBillService;
import com.charge.api.web.support.ShardingSupport;
import com.charge.api.web.vo.joylife.request.CommunityHouseQueryParams;
import com.charge.api.web.vo.joylife.request.HomePayItemDetailConditionVO;
import com.charge.api.web.vo.joylife.request.HousePayItemListConditionVO;
import com.charge.api.web.vo.joylife.request.ReceivableBillQueryParams;
import com.charge.api.web.vo.joylife.response.CommunityReceivableBillDetailVO;
import com.charge.api.web.vo.joylife.response.CommunityReceivableBillSumVO;
import com.charge.api.web.vo.joylife.response.HomePayItemDetailVO;
import com.charge.api.web.vo.joylife.response.HousePayItemListVO;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.condition.HouseCondition;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.HouseDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 对接朝昔
 */
@Api(tags = {"zhaoxi_api"}, value = "朝昔应收表接口")
@RestController
@RequestMapping(value = "/app")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ReceivableBillController {
    private final ReceivableBillClient receivableBillClient;
    private final CommunityClient communityClient;
    private final AssetClient assetClient;

    private final ReceivableBillService receivableBillService;
    private final ShardingSupport shardingSupport;

    /**
     * 获取用户所有欠费明细和统计
     * @param params
     * @return
     */
    @ApiOperation(value = "获取用户所有欠费明细和统计", notes = "传资产信息，返回欠费明细")
    @JoyLifeCheckParam
    @PostMapping(value = "/getArrearsItemList/community")
    public ChargeResponse<CommunityReceivableBillDetailVO> getArrearsItemListAllCommunity(@RequestBody ReceivableBillQueryParams params) {
        List<String> communityIdList = params.getMsCommunityInfoList().stream().map(CommunityHouseQueryParams::getCommunityMsId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(communityIdList)) {
            return new ChargeResponse();
        }
        ChargeResponse<List<CommunityDTO>> communityDTOChargeResponse = communityClient.listCommunity(CommunityCondition.builder().msIds(communityIdList).build());
        if (!communityDTOChargeResponse.isSuccess()) {
            return new ChargeResponse();
        }
        List<CommunityDTO> communityDTOList = communityDTOChargeResponse.getContent();
        if(CollectionUtils.isEmpty(communityDTOList)){
            return new ChargeResponse();
        }
        List<String> assertIdList = params.getMsCommunityInfoList().stream().map(CommunityHouseQueryParams::getHouseMsIdList).flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assertIdList)) {
            return new ChargeResponse();
        }
        ChargeResponse<PagingDTO<HouseDTO>> houseDTOListResponse = assetClient.pageHouse(HouseCondition.builder().msIds(assertIdList)
                .pageNum(1).pageSize(assertIdList.size()).build());
        if (!houseDTOListResponse.isSuccess()) {
            return new ChargeResponse();
        }
        PagingDTO<HouseDTO> houseDTOList = houseDTOListResponse.getContent();
        if(CollectionUtils.isEmpty(houseDTOList.getList())){
            return new ChargeResponse();
        }
        Map<Long, List<HouseDTO>> houseListMap = houseDTOList.getList().stream().collect(Collectors.groupingBy(HouseDTO::getCommunityId));
        List<Long> communityIds = houseListMap.keySet().stream().collect(Collectors.toList());
        List<Long> houseUuidList = houseDTOList.getList().stream().map(HouseDTO::getId).distinct().collect(Collectors.toList());
       /* ChargeResponse<PagingDTO<ReceivableBillDTO>> pagingDTOChargeResponse = receivableBillClient.queryByPage(ReceivableConditionDTO.builder()
                .communityIds(communityIds)
                .assetIdList(houseUuidList)
                .pageNum(1)
                .pageSize(9999)
                .build());
        if (!pagingDTOChargeResponse.isSuccess()) {
            return new ChargeResponse();
        }
        PagingDTO<ReceivableBillDTO> receivableList = pagingDTOChargeResponse.getContent();*/
        ReceivableConditionDTO receivableConditionDTO = ReceivableConditionDTO.builder()
                .communityIds(communityIds)
                .assetIdList(houseUuidList)
                .pageNum(1)
                .pageSize(9999)
                .build();
        List<ReceivableBillDTO> receivableBills = shardingSupport.pageReceivableBill(receivableConditionDTO);
        //TODO  获取支付状态和预存
        return new ChargeResponse(CommunityReceivableBillDetailVO.from(receivableBills, communityDTOList, houseListMap, null));
    }

    /**
     * 获取用户的小区维度欠费统计
     * @param params
     * @return
     */
    @ApiOperation(value = "获取用户的欠费统计")
    @PostMapping(value = "/getArrearsItemAmount/community")
    public ChargeResponse<CommunityReceivableBillSumVO> getArrearsItemAmount(@RequestBody ReceivableBillQueryParams params) {
        List<CommunityHouseQueryParams> communityHouseQueryParamsList = params.getMsCommunityInfoList();
        List<String> communityIdList = communityHouseQueryParamsList.stream().map(CommunityHouseQueryParams::getCommunityMsId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(communityIdList)) {
            return new ChargeResponse();
        }
        ChargeResponse<List<CommunityDTO>> communityDTOChargeResponse = communityClient.listCommunity(CommunityCondition.builder().msIds(communityIdList).build());
        if (!communityDTOChargeResponse.isSuccess()) {
            return new ChargeResponse();
        }
        List<CommunityDTO> communityDTOList = communityDTOChargeResponse.getContent();
        List<String> assertIdList = communityHouseQueryParamsList.stream().map(CommunityHouseQueryParams::getHouseMsIdList).flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assertIdList)) {
            return new ChargeResponse();
        }
        ChargeResponse<PagingDTO<HouseDTO>> houseDTOListResponse = assetClient.pageHouse(HouseCondition.builder().msIds(assertIdList).pageNum(1).pageSize(assertIdList.size()).build());
        if (!houseDTOListResponse.isSuccess()) {
            return new ChargeResponse();
        }
        PagingDTO<HouseDTO> houseDTOList = houseDTOListResponse.getContent();
        if(CollectionUtils.isEmpty(houseDTOList.getList())){
            return new ChargeResponse();
        }
        List<Long> houseUuidList = houseDTOList.getList().stream().map(HouseDTO::getId).distinct().collect(Collectors.toList());
        List<Long> communityIds = communityDTOList.stream().map(CommunityDTO::getId).collect(Collectors.toList());
     /*   ChargeResponse<PagingDTO<ReceivableBillDTO>> pagingDTOChargeResponse = receivableBillClient.queryByPage(ReceivableConditionDTO.builder()
                .communityIds(communityIds)
                .assetIdList(houseUuidList)
                .statusIdList(Arrays.asList(ReceivableBillPayStatusEnum.NOT_PAY.getCode()
                        ,ReceivableBillPayStatusEnum.COLLECTION.getCode(),ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                .pageNum(1)
                .pageSize(9999)
                .build());
        if (!pagingDTOChargeResponse.isSuccess()) {
            return new ChargeResponse();
        }
        PagingDTO<ReceivableBillDTO> receivableList = pagingDTOChargeResponse.getContent();*/
        ReceivableConditionDTO receivableConditionDTO = ReceivableConditionDTO.builder()
                .communityIds(communityIds)
                .assetIdList(houseUuidList)
                .statusIdList(Arrays.asList(ReceivableBillPayStatusEnum.NOT_PAY.getCode()
                        ,ReceivableBillPayStatusEnum.COLLECTION.getCode(),ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                .pageNum(1)
                .pageSize(9999)
                .build();
        List<ReceivableBillDTO> receivableBils = shardingSupport.pageReceivableBill(receivableConditionDTO);
        return new ChargeResponse(CommunityReceivableBillSumVO.from(receivableBils, communityDTOList));
    }


    /**
     * 查询房屋全部账单列表
     *
     * @param conditionVO
     * @return
     */
    @JoyLifeCheckParam
    @GetMapping("/getHousePayItemList")
    public ChargeResponse<HousePayItemListVO> getHousePayItemList(HousePayItemListConditionVO conditionVO) {
        ChargeResponse<HousePayItemListVO> response = receivableBillService.getHousePayItemList(conditionVO);
        return response;
    }


    /**
     * 查询房屋月度账单详情
     * @return
     */
    @JoyLifeCheckParam
    @GetMapping("/getPayItemDetail")
    public ChargeResponse<HomePayItemDetailVO> getHomePayItemDetail(HomePayItemDetailConditionVO conditionVO) {
        ChargeResponse<HomePayItemDetailVO> homePayItemDetail = receivableBillService.getHomePayItemDetail(conditionVO);
        return homePayItemDetail;
    }


}
