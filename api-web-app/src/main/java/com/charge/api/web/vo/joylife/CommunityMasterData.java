package com.charge.api.web.vo.joylife;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class CommunityMasterData implements Serializable {

	@NotBlank
	private String communityMsId;

	@NotEmpty
	private List<String> houseMsIdList;

	private Integer number;

	/**
	 * 资产类型：房间 1， 车位 2
	 */
	private Integer assetType;
}
