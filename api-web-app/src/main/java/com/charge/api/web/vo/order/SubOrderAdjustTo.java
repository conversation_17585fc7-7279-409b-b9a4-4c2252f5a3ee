package com.charge.api.web.vo.order;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 调整到的子订单
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
public class SubOrderAdjustTo {

    /**
     * 订单号
     */
    @NotBlank(message = "主订单号不能为空")
    private String orderNo;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    /**
     * 子订单列表
     */
    @NotEmpty(message = "子订单号不能为空")
    @Valid
    private List<SubOrderAdjustToItem> subOrderAmounts;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    @NotBlank(message = "用户名称不能为空")
    private String customerName;

    /**
     * 定金/意向金 600   佣金 601
     */
    @NotBlank(message = "二级分类不能为空")
    private String secondClassificationId;

    /**
     * 资产id(无指定资产则默认是项目虚拟房间)
     */
    private Long assetId;

    /**
     * 货物名称
     */
    @NotBlank(message = "货物名称不能为空")
    @Length(max = 255, message = "货物名称最大长度255")
    private String goodsName;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 扩展字段，支持业务自定义的字段
     */
    private Map<String, Object> extend;
}