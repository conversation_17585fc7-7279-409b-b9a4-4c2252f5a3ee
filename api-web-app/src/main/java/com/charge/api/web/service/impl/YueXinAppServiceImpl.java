package com.charge.api.web.service.impl;

import com.charge.api.web.config.SwitchConfig;
import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.convert.PreDepositConverter;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.enums.VirtualAssetTypeEnum;
import com.charge.api.web.service.joylife.YueXinAppService;
import com.charge.api.web.support.*;
import com.charge.api.web.vo.joylife.request.BillListReq;
import com.charge.api.web.vo.joylife.request.ZhaoXiAssertRequest;
import com.charge.api.web.vo.joylife.response.*;
import com.charge.bill.client.*;
import com.charge.bill.dto.*;
import com.charge.bill.dto.assetoverview.AssetOverViewDTO;
import com.charge.bill.dto.income.*;
import com.charge.bill.dto.order.OrderAdjustDetailDTO;
import com.charge.bill.dto.order.OrderAdjustQueryDTO;
import com.charge.bill.dto.predeposit.*;
import com.charge.bill.enums.*;
import com.charge.common.dto.BaseDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.AssertUtils;
import com.charge.common.util.DateUtils;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.config.client.item.ChargeItemClient;
import com.charge.config.dto.item.ChargeItemSimpleDTO;
import com.charge.config.dto.item.condition.ChargeSimpleItemRequestDTO;
import com.charge.config.dto.meter.MeterBookAssetDetailDTO;
import com.charge.config.dto.meter.condition.MeterBookDetailConditionDTO;
import com.charge.config.dto.standard.StandardConfigDTO;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.TraceContextUtil;
import com.charge.joylife.dto.*;
import com.charge.joylife.vo.AssetPredepositAccountListVO;
import com.charge.joylife.vo.AssetPredepositQueryVO;
import com.charge.joylife.vo.PrestoreDetailVO;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.BuildingInfoClient;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.enums.HouseTypeEnum;
import com.charge.maindata.enums.StatusEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import com.charge.order.client.OrderClient;
import com.charge.order.dto.OrderDTO;
import com.charge.order.dto.rule.OrderClassifyRequestDTO;
import com.charge.pay.client.PointClient;
import com.charge.pay.dto.point.PointTradeDetailDTO;
import com.charge.pay.dto.point.PointsQueryConditionDTO;
import com.charge.report.client.ReceivableProcessClient;
import com.charge.report.dto.AssetProcessConditionDTO;
import com.charge.report.dto.DwsAssetProcessResp;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class YueXinAppServiceImpl implements YueXinAppService {

    private final AssetClient assetClient;

    private final CommunityClient communityClient;

    private final ReceivableBillClient receivableBillClient;

    private final BuildingInfoClient buildingInfoClient;

    private final ReceivableProcessClient dwsReceivableProcessClient;
    private final AssetSupport assetSupport;
    private final MeterBookSupport meterBookSupport;
    private final PredepositAccountClient predepositAccountClient;
    private final FeeCalculateSupport feeCalculateSupport;

    private final AssetTransactionClient assetTransactionClient;
    private final CustomerSupport customerSupport;

    private final CommunityParamSupport communityParamSupport;

    private final CommunitySupport communitySupport;

    private final PointClient pointClient;

    private final ChargeItemClient chargeItemClient;

    private final PredepositAdjustClient predepositAdjustClient;

    private final PredepositRefundClient predepositRefundClient;

    private final PredepositBillClient predepositBillClient;

    private final OrderSupport orderSupport;

    private final RefundBillClient refundBillClient;

    private final IncomeBillClient incomeBillClient;

    private final DiscountDetailClient discountDetailClient;

    private final OrderAdjustClient orderAdjustClient;

    private final OrderClient orderClient;

    private final AssetOverviewSupport assetOverviewSupport;

    private final BuildingSupport buildingSupport;

    private final SwitchConfig switchConfig;

    @Override
    public ChargeResponse<List<HouseOrderItemArrears>> listAssetsArrearsInfo(ListAssetsArrearsReq condition) throws ChargeBusinessException {

        ChargeResponse<CommunityDTO> communityDTOChargeResponse = communityClient.oneByCondition(CommunityCondition.builder().msId(condition.getCommunityMsId()).build());
        CommunityDTO communityDto = AppInterfaceUtil.getDataThrowException(communityDTOChargeResponse);
        Long communityId = communityDto.getId();
        List<AssetListReqDTO> assetListReqDTOS = condition.getAssetListReqDTOS();
        List<AssetDTO> assetList = new ArrayList<>();
        for (int i = 0,size = assetListReqDTOS.size(); i < size; i++) {
            AssetListReqDTO reqDTO = assetListReqDTOS.get(i);
            AssetCondition assetCondition = AssetCondition.builder().communityId(communityId).msIds(reqDTO.getHouseMsIdList()).type(reqDTO.getAssetType()).build();
            ChargeResponse<List<AssetDTO>> assetResponse = assetClient.listAsset(assetCondition);
            List<AssetDTO> assetDTOS = AppInterfaceUtil.getResponseData(assetResponse);
            assetList.addAll(assetDTOS);
        }
        if (CollectionUtils.isEmpty(assetList)) {
            return new ChargeResponse<>(Collections.emptyList());
        }
        List<Long> assetIdList = assetList.stream().map(e->e.getId()).collect(Collectors.toList());
        //获取订单状态为部分核销、未核销，单据状态为生效中、已挂起、审核中的应收单
        ChargeResponse<List<ReceivableBillDTO>> billResponse = receivableBillClient.queryList(ReceivableConditionDTO.builder()
                .communityId(communityId)
                .assetIdList(assetIdList)
                .payStatuses(Arrays.asList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(),
                        ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode(),ReceivalbleBillPayStatusEnum.COLLECTION.getCode()))
                .billStatuses(Arrays.asList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),
                        ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                .build());
        List<ReceivableBillDTO> receivableBillDTOS = AppInterfaceUtil.getResponseDataThrowException(billResponse);
        checkAndFilterUnValidData(receivableBillDTOS);

        List<HouseOrderItemArrears> resp = new ArrayList<>(assetList.size()+1);

        //查询仪表数据
        List<MeterBookAssetDetailDTO> meterDetails = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(receivableBillDTOS)) {
            List<Long> meterDetailIds = receivableBillDTOS.stream().filter(rec->Objects.nonNull(rec.getMeterBookDetailId()))
                    .map(ReceivableBillDTO::getMeterBookDetailId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(meterDetailIds)) {
                meterDetails = meterBookSupport.getMeterDetailByCondition(MeterBookDetailConditionDTO.builder()
                        .communityId(communityId).meterBookDetailIdList(meterDetailIds).build());
            }
        }
        Map<Long,MeterBookAssetDetailDTO> meterMap = CollectionUtil.isEmpty(meterDetails)?new HashMap<>():meterDetails.stream().collect(Collectors.toMap(m->m.getId(),m->m));

        //查询预存余额
        AssetArrearsCountQueryDTO assetArrearsCountQueryDTO = new AssetArrearsCountQueryDTO();
        assetArrearsCountQueryDTO.setCommunityId(communityId);
        assetArrearsCountQueryDTO.setAssetIdList(assetIdList);
        ChargeResponse<List<AsssetPredepositCountDTO>> listChargeResponse = predepositAccountClient.assetPredepositCountList(assetArrearsCountQueryDTO);
        List<AsssetPredepositCountDTO> predepositCountDTOS = CollectionUtils.isEmpty(AppInterfaceUtil.getResponseData(listChargeResponse)) ? Collections.emptyList() : listChargeResponse.getContent();
        Map<Long, AsssetPredepositCountDTO> accountMap = predepositCountDTOS.stream().collect(Collectors.toMap(a->a.getAssetId(),a->a, (a,b) -> a));

        //查询计费标准
        List<Long> itemIds = Optional.ofNullable(receivableBillDTOS).orElse(new ArrayList<>()).stream().map(r -> r.getItemId()).collect(Collectors.toList());
        Map<Long, List<StandardConfigDTO>> assetStandardsMap = CollectionUtil.isNotEmpty(itemIds)?feeCalculateSupport.getChargeStandardList(communityId, assetIdList, itemIds):new HashMap<>();

        Map<Long,AssetDTO> assetMap = assetList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
        Map<Long,List<ReceivableBillDTO>> recMap = receivableBillDTOS.stream().collect(Collectors.groupingBy(e->e.getAssetId()));
        //默认为业主类型
        Integer chargeObject = Objects.nonNull(condition.getChargeObjectType()) ? condition.getChargeObjectType() : ChargeObjEnum.CHARGE_OBJ_OWNER.getCode();
        assetMap.forEach((k,v)->{
            HouseOrderItemArrears houseOrderItemArrears = HouseOrderItemArrears.fromV2(v, recMap.get(k),meterMap,accountMap,assetStandardsMap.get(k),chargeObject);
            resp.add(houseOrderItemArrears);
        });
        return new ChargeResponse(resp);
    }

    private void checkAndFilterUnValidData(List<ReceivableBillDTO> receivableBillDTOS) {
        List<ReceivableBillDTO> unValidData=receivableBillDTOS.stream()
                .filter(item->BigDecimal.ZERO.compareTo(item.getPenaltyArrearsAmount())>0
                        ||  BigDecimal.ZERO.compareTo(item.getArrearsAmount())>0
                        || (BigDecimal.ZERO.compareTo(item.getPenaltyArrearsAmount())==0 && BigDecimal.ZERO.compareTo(item.getArrearsAmount())==0))
                .collect(Collectors.toList());
       if(CollectionUtil.isNotEmpty(unValidData)){
           log.warn("欠费查询存在非法数据：{}",unValidData);
           receivableBillDTOS.removeAll(unValidData);
       }
    }

    private ArrearsAssetInfo buildArrearsAssetInfo(AssetOverViewDTO assetOverViewDTO){
        return ArrearsAssetInfo.builder()
                .assetId(assetOverViewDTO.getAssetId().toString())
                .msId(assetOverViewDTO.getMsId())
                .assetType(assetOverViewDTO.getAssetType())
                .chargeObjectType(assetOverViewDTO.getChargeObjType())
                .build();
    }

    /** 查询小区欠费资产信息
     * @param condition
     * @return
     */
    @Override
    public ChargeResponse<List<ArrearsAssetInfo>> getArrearsAssetInfoByCondition(CommunityArrearsAssetsReq condition) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(condition.getCommunityMsId());
        List<Long> buildingIds=null;
        if(!CollectionUtils.isEmpty(condition.getBuildingMsIdList())){
            buildingIds = buildingSupport.listIdsByMsId(condition.getBuildingMsIdList(), communityId);
            if (CollectionUtil.isEmpty(buildingIds)) {
                return new ChargeResponse<>(Collections.emptyList());
            }
        }
        if(switchConfig.isUseNewAssetOverview(communityId)){
            List<AssetOverViewDTO> assetOverViewDTOS = assetOverviewSupport.listAssetOverview(communityId, null, null, null, null, true,null,1);
            List<ArrearsAssetInfo> arrearsAssetInfos = assetOverViewDTOS.stream().map(this::buildArrearsAssetInfo).collect(Collectors.toList());
            return new ChargeResponse<>(arrearsAssetInfos);
        }
        //查询欠费资产
        List<Long>  assetIds;
        String queryDate = DateUtils.getDateStr(DateUtils.FORMAT_13);
        if(CollectionUtils.isEmpty(buildingIds)&&condition.getAssetType()==null){
            ChargeResponse<List<Long>> assetIdsResp = receivableBillClient.listArrearsAssetIds(ReceivableConditionDTO.builder().communityId(communityId).build());
            assetIds = AppInterfaceUtil.getResponseDataThrowException(assetIdsResp);
        }else {
            AssetProcessConditionDTO queryDto = AssetProcessConditionDTO.builder().communityId(communityId).buildingIds(buildingIds).assetType(condition.getAssetType()).queryDate(queryDate).build();
            ChargeResponse<List<DwsAssetProcessResp>> listChargeResponse = dwsReceivableProcessClient.assetProcessList(queryDto);
            List<DwsAssetProcessResp> resp = AppInterfaceUtil.getResponseDataThrowException(listChargeResponse);
            if (CollectionUtils.isEmpty(resp)) {
                return new ChargeResponse<>(new ArrayList<>());
            }
            assetIds = resp.stream().map(DwsAssetProcessResp::getAssetId).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(assetIds)) {
            return new ChargeResponse<>(new ArrayList<>());
        }
        //查询资产信息
        List<List<Long>> lists = CollectionUtil.splitList(assetIds, 500);
        List<ArrearsAssetInfo> infos = new ArrayList<>(assetIds.size());
        for (List<Long> ids : lists) {
            List<AssetAdapter> assetList = assetSupport.listAssetByIds(communityId, ids);
            List<ArrearsAssetInfo> assetInfos = assetList.stream().map(e -> {
                ArrearsAssetInfo i = new ArrearsAssetInfo();
                i.setAssetId(String.valueOf(e.getId()));
                i.setAssetType(e.getType());
                i.setMsId(e.getMsId());
                i.setChargeObjectType(e.getChargeObject());
                return i;
            }).collect(Collectors.toList());
            infos.addAll(assetInfos);
        }
        return new ChargeResponse<>(infos);
    }

    /**批量查询资产预收余额明细
     * @param conditionVO
     * @return
     */
    @Override
    public List<AssetPredepositAccountListVO> getAssetPredepositList(AssetPredepositQueryVO conditionVO) throws ChargeBusinessException {
        List<String> houseMsIdList = conditionVO.getHouseMsIdList();
        List<String> parkingMsIdList = conditionVO.getParkingMsIdList();
        List<AssetAdapter> assetList = new ArrayList<>();
        List<AssetPredepositAccountListVO> respList = new ArrayList<>();
        //查询资产id
        if (CollectionUtil.isEmpty(houseMsIdList)&&CollectionUtil.isEmpty(parkingMsIdList)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1006.getCode(),"请传入资产id");
        }
        if (CollectionUtil.isNotEmpty(houseMsIdList)) {
            List<AssetAdapter> houseList = assetSupport.getAssetListByCondition(AssetCondition.builder().msIds(houseMsIdList)
                    .status(StatusEnum.USING.getCode()).type(AssetTypeEnum.HOUSE.getCode()).build());
            if (CollectionUtil.isNotEmpty(houseList)) {
                assetList.addAll(houseList);
            }
        }
        if (CollectionUtil.isNotEmpty(parkingMsIdList)) {
            List<AssetAdapter> parkingList = assetSupport.getAssetListByCondition(AssetCondition.builder().msIds(parkingMsIdList)
                    .status(StatusEnum.USING.getCode()).type(AssetTypeEnum.PARKING_SPACE.getCode()).build());
            if (CollectionUtil.isNotEmpty(parkingList)) {
                assetList.addAll(parkingList);
            }
        }
        if (CollectionUtil.isEmpty(assetList)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1006.getCode(),"查无资产信息，请重试或联系管家处理");
        }
        List<Long> assetIds = assetList.stream().map(AssetAdapter::getId).collect(Collectors.toList());
        Map<Long, AssetAdapter> assetMap = assetList.stream().collect(Collectors.toMap(a -> a.getId(), a -> a));

        //查询预收账户余额
        ChargeResponse<List<PredepositAccountDTO>> listChargeResponse = predepositAccountClient.listPredepositAccount(PredepositAccountConditionDTO.builder()
                .communityId(conditionVO.getCommunityId()).assetIds(assetIds).billType(PredepositBillTypeEnum.PRE_DEPOSIT.getCode())
                .chargeObj(PayRelatedConstants.PREDEPOSIT_CHARGE_OBJ_OWNER).build());
        log.info("{}|批量查询房间预存余额明细|{}，resp:{}",LogCategoryEnum.BUSSINESS,conditionVO,listChargeResponse);
        if (listChargeResponse.isSuccess() && CollectionUtil.isNotEmpty(listChargeResponse.getContent())) {
            List<PredepositAccountDTO> accountList = listChargeResponse.getContent();
            Map<Long, List<PredepositAccountDTO>> assetAccoutMap = accountList.stream().collect(Collectors.groupingBy(a -> a.getAssetId()));
            assetAccoutMap.forEach((k,v)->{
                BigDecimal totalAvailableMoney = v.stream().map(PredepositAccountDTO::getAvailableBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (BigDecimal.ZERO.compareTo(totalAvailableMoney)< 0) {
                    List<PrestoreDetailVO> detailList = v.stream().filter(a->BigDecimal.ZERO.compareTo(a.getAvailableBalance()) < 0).map(a->{
                        return PrestoreDetailVO.builder().availableMoney(a.getAvailableBalance()).freezeMoney(a.getFreezedMoney()).
                                totalMoney(a.getTotalBalance()).itemId(a.getPredepositItemId()).itemName(a.getPredepositItemName()).build();
                    }).collect(Collectors.toList());
                    respList.add(AssetPredepositAccountListVO.builder().assetId(k).assetMsId(assetMap.get(k).getMsId())
                            .detailList(detailList).totalAvailableMoney(totalAvailableMoney).build());
                }
            });
        }
        return respList;
    }

    @Override
    public List<VirtualAssetDTO> listVirtualAsset(VirtualAssetReq req) throws ChargeBusinessException {
        CommunityDTO communityDTO = communityClient.oneByCondition(CommunityCondition.builder().msId(req.getCommunityMsId()).build()).getContent();
        Long communityId = communityDTO.getId();

        List<VirtualAssetDTO> listVirtualAssetDTO = new ArrayList<>();
        //项目虚拟房屋
        AssetDTO communityVirtualHouse = assetClient.getCommunityVirtualHouse(communityId).getContent();
        if (Objects.nonNull(communityVirtualHouse)) {
            VirtualAssetDTO communityVirtualAsset = VirtualAssetDTO.builder().assetName(communityVirtualHouse.getHouseDTO().getHouseName())
                    .assetId(communityVirtualHouse.getId().toString()).assetMsId(communityVirtualHouse.getHouseDTO().getMsId())
                    .type(VirtualAssetTypeEnum.COMMUNITY_VIRTUAL_ASSET.getCode()).build();
            listVirtualAssetDTO.add(communityVirtualAsset);
        }

        //虚拟房屋
        AssetCondition assetCondition = AssetCondition.builder()
                .communityId(communityId)
                .type(AssetTypeEnum.HOUSE.getCode())
                .status(StatusEnum.USING.getCode())
                .houseType(HouseTypeEnum.VIRTUAL_HOUSE.getCode()).build();
        List<AssetDTO> listVirtualAsset = assetClient.listAsset(assetCondition).getContent();
        if (!listVirtualAsset.isEmpty()) {
            for (AssetDTO assetDTO : listVirtualAsset) {
                if (Objects.nonNull(assetDTO.getHouseDTO().getChargeObjId())) {
                    VirtualAssetDTO virtualAssetDTO = VirtualAssetDTO.builder().assetId(assetDTO.getId().toString()).assetMsId(assetDTO.getHouseDTO().getMsId())
                            .assetName(assetDTO.getHouseDTO().getHouseName()).type(VirtualAssetTypeEnum.VIRTUAL_ASSET.getCode()).build();
                    listVirtualAssetDTO.add(virtualAssetDTO);
                }
            }
        }
        return listVirtualAssetDTO;
    }

    @Override
    public List<VirtualAssetDTO> getVirtualAsset(VirtualAssetReq req) throws ChargeBusinessException {
        List<Long> assetIds = req.getAssetId().stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

        List<AssetDTO> listAssetDTO = assetClient.listAssetLessInfo(
                AssetCondition.builder().ids(assetIds).type(AssetTypeEnum.HOUSE.getCode()).status(StatusEnum.USING.getCode()).build()).getContent();
        List<VirtualAssetDTO> result = new ArrayList<>();

        for (AssetDTO assetDTO : listAssetDTO) {
            HouseDTO houseDTO = assetDTO.getHouseDTO();
            VirtualAssetDTO virtualAssetDTO = VirtualAssetDTO.builder()
                    .assetId(assetDTO.getId().toString())
                    .assetName(houseDTO.getHouseName())
                    .assetCode(houseDTO.getHouseCode())
                    .buildingName(houseDTO.getBuildingName() + houseDTO.getUnitName())
                    .communityName(houseDTO.getCommunityName())
                    .assetMsId(houseDTO.getMsId())
                    .build();

            if (HouseTypeEnum.VIRTUAL_HOUSE_COMMUNITY.getCode().equals(houseDTO.getHouseType())) {
                virtualAssetDTO.setType(VirtualAssetTypeEnum.COMMUNITY_VIRTUAL_ASSET.getCode());
            } else if (HouseTypeEnum.VIRTUAL_HOUSE.getCode().equals(houseDTO.getHouseType())) {
                virtualAssetDTO.setType(VirtualAssetTypeEnum.VIRTUAL_ASSET.getCode());
            }
            result.add(virtualAssetDTO);
        }
        return result;
    }

    @Override
    public PagingDTO<VirtualAssetDTO> searchVirtualAsset(VirtualAssetReq req) throws ChargeBusinessException {
        List<VirtualAssetDTO> result = new ArrayList<>();
        PagingDTO<VirtualAssetDTO> pagingDTO = new PagingDTO<>();
        pagingDTO.setPageSize(req.getPageSize());
        pagingDTO.setPageNum(req.getPageNum());
        //如果项目id为空，返回空集合
        if (StringUtils.isBlank(req.getCommunityMsId())) {
             pagingDTO.setList(result);
             pagingDTO.setTotalCount(0);
             return pagingDTO;
        }
        CommunityDTO communityDTO = communityClient.oneByCondition(CommunityCondition.builder().msId(req.getCommunityMsId()).build()).getContent();
        Long communityId = communityDTO.getId();
        String assetName = req.getAssetName();
        //模糊查询虚拟房间
        List<AssetDTO> listAssetDTO = assetClient.listAssetLessInfo(
                AssetCondition.builder()
                        .communityId(communityId)
                        .type(AssetTypeEnum.HOUSE.getCode())
                        .houseType(HouseTypeEnum.VIRTUAL_HOUSE.getCode())
                        .status(StatusEnum.USING.getCode())
                        .searchParam(assetName).build()).getContent();

        if (StringUtils.isNotBlank(assetName) && communityDTO.getName().contains(assetName)) {
            //项目虚拟房屋
            AssetDTO communityVirtualHouse = assetClient.getCommunityVirtualHouse(communityId).getContent();
            listAssetDTO.add(communityVirtualHouse);
        }

        listAssetDTO = listAssetDTO.stream().filter(a -> Objects.nonNull(a.getHouseDTO().getChargeObjId())).collect(Collectors.toList());

        for (AssetDTO assetDTO : listAssetDTO) {
            HouseDTO houseDTO = assetDTO.getHouseDTO();
            VirtualAssetDTO virtualAssetDTO = VirtualAssetDTO.builder()
                    .assetId(assetDTO.getId().toString())
                    .assetName(houseDTO.getHouseName())
                    .assetCode(houseDTO.getHouseCode())
                    .buildingName(houseDTO.getBuildingName() + houseDTO.getUnitName())
                    .communityName(houseDTO.getCommunityName())
                    .assetMsId(houseDTO.getMsId())
                    .build();

            if (HouseTypeEnum.VIRTUAL_HOUSE_COMMUNITY.getCode().equals(houseDTO.getHouseType())) {
                virtualAssetDTO.setType(VirtualAssetTypeEnum.COMMUNITY_VIRTUAL_ASSET.getCode());
            } else if (HouseTypeEnum.VIRTUAL_HOUSE.getCode().equals(houseDTO.getHouseType())) {
                virtualAssetDTO.setType(VirtualAssetTypeEnum.VIRTUAL_ASSET.getCode());
            }
            result.add(virtualAssetDTO);
        }
        pagingDTO.setTotalCount(result.size());
        //分页
        int startIndex = (req.getPageNum() - 1) * req.getPageSize();
        int endIndex = Math.min(startIndex + req.getPageSize(), result.size());
        if (startIndex >= result.size() || startIndex < 0) {
            pagingDTO.setList(new ArrayList<>());
        } else {
            pagingDTO.setList(new ArrayList<>(result.subList(startIndex, endIndex)));
        }

        return pagingDTO;
    }

    @Override
    public List<BillRecordVO> getBillList(BillListReq reqCondition) throws ChargeBusinessException {
        // 获取项目ID并注入上下文
        communityParamSupport.fillCommunityId(reqCondition);
        TraceContextUtil.setCommunityId(reqCondition.getCommunityId());
        fillAssetId(reqCondition);
        /**转账,支付宝,微信,银行托收,现金**/
        List<Integer> payMethodList = Lists.newArrayList(PaymentMethodEnum.CARD.getPaymentCode(),PaymentMethodEnum.TRANSFER_OFFLINE.getPaymentCode(),
                PaymentMethodEnum.ALIPAY.getPaymentCode(),PaymentMethodEnum.WECHAT.getPaymentCode(),PaymentMethodEnum.BANK_COLLECTION.getPaymentCode(),
                PaymentMethodEnum.CASH.getPaymentCode());
        List<Long> reqAssetIds = getAssetIdsByCustomerId(reqCondition.getCommunityId(), reqCondition.getCustomerMsId());
        if(StringUtils.isNotBlank(reqCondition.getCustomerMsId()) && CollectionUtil.isEmpty(reqAssetIds)){
            return Collections.emptyList();
        }
        AssetTransactionConditionDTO conditionDTO = AssetTransactionConditionDTO.builder().communityId(String.valueOf(reqCondition.getCommunityId()))
                .assetOrderNum(reqCondition.getOrderNum()).payTimeStart(reqCondition.getPayTimeStart()).payTimeEnd(reqCondition.getPayTimeEnd())
                .idOffSet(reqCondition.getIdOffSet()).querySize(reqCondition.getQuerySize()).payStatus(BillPayStatusEnum.SUCCESS.getCode())
                .assetIds(reqAssetIds).assetId(reqCondition.getAssetId()).paymentMethodList(payMethodList).build();
        List<AssetTransactionDTO> assetTransactionDTOS = AppInterfaceUtil.getResponseDataThrowException(assetTransactionClient.selectTransactionWriteOffReceivableBillDetail(conditionDTO));
        if(CollectionUtil.isEmpty(assetTransactionDTOS)){
            return Collections.emptyList();
        }

        List<Long> assetIds = assetTransactionDTOS.stream().map(AssetTransactionDTO::getAssetId).distinct().collect(Collectors.toList());
        List<AssetAdapter> assetAdapters = assetSupport.listAssetByIds(reqCondition.getCommunityId(), assetIds);
        Map<Long, AssetAdapter> assetAdapterMap = assetAdapters.stream().collect(Collectors.toMap(AssetAdapter::getId, e -> e));

        List<BillRecordVO> billRecordVOS = assetTransactionDTOS.stream().map(assetTransactionDTO -> {
            BillRecordVO vo = BillRecordVO.builder().transactionId(assetTransactionDTO.getId())
                    .communityName(assetTransactionDTO.getCommunityName()).orderNum(assetTransactionDTO.getAssetOrderNum()).build();
            vo.setTotalAmount(assetTransactionDTO.getMoney().setScale(2, BigDecimal.ROUND_HALF_UP));
            vo.setPayTime(DateUtils.format(assetTransactionDTO.getPaymentTime(), DateUtils.FORMAT_0));
            AssetAdapter assetAdapter = assetAdapterMap.get(assetTransactionDTO.getAssetId());
            if(Objects.nonNull(assetAdapter)) {
                vo.setAssetType(assetAdapter.getType());
                vo.setAssetId(assetAdapter.getId());
                vo.setAssetMsId(assetAdapter.getMsId());
                vo.setAssetName(assetAdapter.getAssetName());
                if(AssetTypeEnum.PARKING_SPACE.getCode().equals(vo.getAssetType())){
                    vo.setCommunityName(assetAdapter.getBuildingName());
                    vo.setAssetName(assetAdapter.getSubNum());
                } else if(HouseTypeEnum.VIRTUAL_HOUSE_COMMUNITY.getCode().equals(assetAdapter.getHouseType())){
                    vo.setAssetName(assetAdapter.getCommunityName());
                    vo.setCommunityName("");
                } else if(HouseTypeEnum.VIRTUAL_HOUSE.getCode().equals(assetAdapter.getHouseType())){
                    vo.setAssetName(assetAdapter.getSubName());
                }
            }
            vo.setItemName(getTransactionItemName(assetTransactionDTO));
            vo.setTransactionTypeName(getTransactionTypeName(assetTransactionDTO));
            return vo;
        }).collect(Collectors.toList());
        return billRecordVOS;
    }

    @Override
    public BillRecordDetailVO getBillDetail(BillDetailReq reqCondition) throws ChargeBusinessException {
        CommunityDTO communityDTO = communityParamSupport.fillCommunityId(reqCondition);
        TraceContextUtil.setCommunityId(reqCondition.getCommunityId());
        List<AssetTransactionDTO> dataList= AppInterfaceUtil.getResponseDataThrowException(assetTransactionClient.selectTransactionWriteOffReceivableBillDetail(AssetTransactionConditionDTO.builder()
                .idList(Arrays.asList(reqCondition.getTransactionId())).communityId(reqCondition.getCommunityId().toString()).build()));
        if(CollectionUtil.isEmpty(dataList)){
            return null;
        }
        AssetTransactionDTO assetTransactionDTO=dataList.get(0);
        List<Long> predepositBillId=new ArrayList<>();
        PointTradeDetailDTO earnPoints=null;
        if(CollectionUtil.isNotEmpty(assetTransactionDTO.getPredepositBillDTOList())){
            predepositBillId.addAll(assetTransactionDTO.getPredepositBillDTOList().stream().map(item->item.getId()).collect(Collectors.toList()));
            PointsQueryConditionDTO pointsQueryCondition=new PointsQueryConditionDTO();
            pointsQueryCondition.setPredepostIdList(predepositBillId);
            earnPoints=AppInterfaceUtil.getResponseData(pointClient.pointsEarnQueryByCondition(pointsQueryCondition));
        }

        AssetAdapter assetAdapter = assetSupport.getAssetInfoById(assetTransactionDTO.getAssetId().toString());
        BillRecordDetailVO billRecordDetailVO= convertTrDtoToAssetBillVoDetail(assetTransactionDTO,earnPoints,communityDTO,assetAdapter);
        return billRecordDetailVO;
    }

    public BillRecordDetailVO convertTrDtoToAssetBillVoDetail(AssetTransactionDTO transactionDTO, PointTradeDetailDTO earnPoints,
                                                              CommunityDTO communityDTO,AssetAdapter assetAdapter) throws ChargeBusinessException {
        BillRecordDetailVO result = convertTransactionTOBillInfo(transactionDTO);
        fillCommunityAndAssetInfo(result, communityDTO, assetAdapter);

        if (Objects.nonNull(earnPoints)) {
            result.setPoints(earnPoints.getPoints());
        }

        List<RecordDetailBillVO> dataInfoDTOList = new ArrayList<>();
        List<WriteOffBillDTO> writeOffBillDTOList = transactionDTO.getWriteOffBillDTOList();
        if (CollectionUtil.isNotEmpty(writeOffBillDTOList)) {
            for(WriteOffBillDTO writeOffBillDTO : writeOffBillDTOList) {
                RecordDetailBillVO recordDetailBillVO = new RecordDetailBillVO();
                recordDetailBillVO.setAmount(writeOffBillDTO.getActualAmount());
                recordDetailBillVO.setBelongYear(writeOffBillDTO.getBelongYears());
                recordDetailBillVO.setChargeBillType(1);
                recordDetailBillVO.setChargeType(writeOffBillDTO.getChargeType());
                recordDetailBillVO.setItemId(writeOffBillDTO.getItemId());
                recordDetailBillVO.setItemName(writeOffBillDTO.getItemName());
                dataInfoDTOList.add(recordDetailBillVO);
            }
        }

        List<PredepositBillDTO> predepositBillDTOS = transactionDTO.getPredepositBillDTOList();
        if (CollectionUtil.isNotEmpty(predepositBillDTOS)) {
            for (PredepositBillDTO predepositBillDTO : predepositBillDTOS) {
                RecordDetailBillVO recordDetailBillVO = new RecordDetailBillVO();
                recordDetailBillVO.setAmount(predepositBillDTO.getPredepositMoney());
                recordDetailBillVO.setChargeBillType(convertPredepositType(predepositBillDTO.getPredepositType()));
                recordDetailBillVO.setItemId(predepositBillDTO.getPredepositItemId());
                String preItemName = predepositBillDTO.getPredepositItemName();
                if(PredepositTypeEnum.SPECIAL_DEPOSIT.getCode().equals(predepositBillDTO.getPredepositType())) {
                    preItemName = "专项预存" + preItemName;
                }
                recordDetailBillVO.setItemName(preItemName);
                dataInfoDTOList.add(recordDetailBillVO);
            }
        }

        List<OrderBillDetailDTO> orderBillDetailDTOS = transactionDTO.getOrderBillDetailList();
        if (CollectionUtil.isNotEmpty(orderBillDetailDTOS)) {
            for (OrderBillDetailDTO orderBillDetailDTO : orderBillDetailDTOS) {
                RecordDetailBillVO recordDetailBillVO = new RecordDetailBillVO();
                recordDetailBillVO.setAmount(orderBillDetailDTO.getTotalAmount());
                recordDetailBillVO.setChargeBillType(3);
                recordDetailBillVO.setItemId(orderBillDetailDTO.getItemId());
                recordDetailBillVO.setItemName(orderBillDetailDTO.getItemName());
                dataInfoDTOList.add(recordDetailBillVO);
            }
        }
        handleBillItemCodes(dataInfoDTOList);

        result.setDetailList(dataInfoDTOList);
        return result;
    }

    private BillRecordDetailVO convertTransactionTOBillInfo(AssetTransactionDTO transactionDTO){
        BillRecordDetailVO billRecordDetailVO = new BillRecordDetailVO();
        IncomeBillDTO incomeBillDTO = transactionDTO.getIncomeBillDTO();
        if (Objects.nonNull(incomeBillDTO)) {
            billRecordDetailVO.setPayMember(incomeBillDTO.getPayMember());
            billRecordDetailVO.setPaymentChannel(incomeBillDTO.getPaymentChannel());
            billRecordDetailVO.setPaymentTerminal(incomeBillDTO.getPaymentTerminal());
            billRecordDetailVO.setTransactionNo(incomeBillDTO.getOutTransactionNo());
        }
        if(Objects.equals(PaymentMethodEnum.DEDUCT.getPaymentCode(),transactionDTO.getPaymentMethod()) ||Objects.equals(PaymentMethodEnum.CARRY_FORWARD_DEDUCTIONS.getPaymentCode(),transactionDTO.getPaymentMethod()) ){
            billRecordDetailVO.setPaymentChannel(PaymentChannelEnum.TRANSFER_OFFLINE.getCode());
            billRecordDetailVO.setPaymentTerminal(PaymentTerminalEnum.CHARGE_SYSTEM.getCode());
            billRecordDetailVO.setTransactionNo("");
            billRecordDetailVO.setPayMember("");
        }
        billRecordDetailVO.setOrderNum(transactionDTO.getAssetOrderNum());
        billRecordDetailVO.setPaymentMethod(transactionDTO.getPaymentMethod());
        billRecordDetailVO.setPayTime(DateUtils.format(transactionDTO.getPaymentTime(), DateUtils.FORMAT_0));
        if(!Objects.equals(PaymentMethodEnum.EQUITY.getPaymentCode(),transactionDTO.getPaymentMethod())){
            billRecordDetailVO.setIncomeAmount(transactionDTO.getMoney());
        }
        billRecordDetailVO.setMemo(Objects.nonNull(transactionDTO.getIncomeBillDTO())?transactionDTO.getIncomeBillDTO().getMemo() : null);
        billRecordDetailVO.setCollectorName(Objects.nonNull(transactionDTO.getIncomeBillDTO())?transactionDTO.getIncomeBillDTO().getCollectorName() : null);
        billRecordDetailVO.setCreateTime(DateUtils.formatToDateStr(DateUtils.FORMAT_0,transactionDTO.getCreateTime()));
        return billRecordDetailVO;
    }

    private List<Long> getAssetIdsByCustomerId(Long communityId, String customerMsId) throws ChargeBusinessException{
        if(Objects.isNull(customerMsId)){
            return Collections.emptyList();
        }

        List<CustomerDTO> customerDTOS = customerSupport.getCustomerListByMsId(communityId, customerMsId);
        List<Long> allAssetIds = customerDTOS.stream()
                .flatMap(customer -> customer.getListAssetId().stream()).collect(Collectors.toList());
        return allAssetIds;
    }

    private void fillAssetId(ZhaoXiAssertRequest req) throws ChargeBusinessException {
        if(req.getAssetId()==null && Objects.nonNull(req.getAssetMsId())){
            AssetDTO assetDTO = assetSupport.getByAssetMsId(req.getAssetMsId(), req.getAssetType());
            AssertUtils.notNull(assetDTO,"朝昔的资产查不到:"+req.getAssetMsId());
            req.setAssetId(assetDTO.getId());
        }
    }

    private String getTransactionTypeName(AssetTransactionDTO assetTransactionDTO){
        String transactionTypeName = "";
        if(Objects.nonNull(assetTransactionDTO)){
            if(CollectionUtil.isNotEmpty(assetTransactionDTO.getWriteOffBillDTOList())){
                transactionTypeName = "欠费";
            } else if(CollectionUtil.isNotEmpty(assetTransactionDTO.getPredepositBillDTOList())){
                List<PredepositBillDTO> preStoreBillDTOS = assetTransactionDTO.getPredepositBillDTOList().stream()
                        .filter(e -> PredepositTypeEnum.SPECIAL_DEPOSIT.getCode().equals(e.getPredepositType())
                                || PredepositTypeEnum.COMMON_DEPOSIT.getCode().equals(e.getPredepositType())).collect(Collectors.toList());
                List<PredepositBillDTO> depositBillDTOS = assetTransactionDTO.getPredepositBillDTOList().stream()
                        .filter(e -> PredepositTypeEnum.DEPOSIT.getCode().equals(e.getPredepositType())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(preStoreBillDTOS)) {
                    transactionTypeName = "预存";
                } else if(CollectionUtil.isNotEmpty(depositBillDTOS) && CollectionUtil.isEmpty(assetTransactionDTO.getOrderBillDetailList())) {
                    transactionTypeName = "押金";
                }
            } else if(CollectionUtil.isNotEmpty(assetTransactionDTO.getOrderBillDetailList())){
                transactionTypeName = "临时";
            }
        }
        return transactionTypeName;
    }
    
    private void fillCommunityAndAssetInfo(BillRecordDetailVO billRecordDetailVO, CommunityDTO communityDTO, AssetAdapter assetAdapter){
        if(Objects.nonNull(assetAdapter)){
            billRecordDetailVO.setAssetId(assetAdapter.getId());
            billRecordDetailVO.setAssetType(assetAdapter.getType());
            billRecordDetailVO.setAssetMsId(assetAdapter.getMsId());
            billRecordDetailVO.setCommunityName(communityDTO.getName());
            billRecordDetailVO.setAssetName(assetAdapter.getAssetName());
            if(AssetTypeEnum.PARKING_SPACE.getCode().equals(assetAdapter.getType())) {
                billRecordDetailVO.setParkingName(assetAdapter.getBuildingName());
                billRecordDetailVO.setAssetName(assetAdapter.getSubNum());
            } else if(HouseTypeEnum.VIRTUAL_HOUSE_COMMUNITY.getCode().equals(assetAdapter.getHouseType())){
                billRecordDetailVO.setAssetName("");
            } else if(HouseTypeEnum.VIRTUAL_HOUSE.getCode().equals(assetAdapter.getHouseType())){
                billRecordDetailVO.setAssetName(assetAdapter.getSubName());
            }
        }
    }

    /**
     * 转换预存-2 押金-4
     * @param predepositType
     * @return
     */
    private Integer convertPredepositType(Integer predepositType){
        Integer result = null;
        if(PredepositTypeEnum.COMMON_DEPOSIT.getCode().equals(predepositType) || PredepositTypeEnum.SPECIAL_DEPOSIT.getCode().equals(predepositType)){
            result = 2;
        } else if(PredepositTypeEnum.DEPOSIT.getCode().equals(predepositType)){
            result = 4;
        }
        return result;
    }

    /**
     * 获取收费项列表
     * @param assetTransactionDTO
     * @return
     */
    private String getTransactionItemName(AssetTransactionDTO assetTransactionDTO){
        StringBuilder transactionItemName = new StringBuilder();
        List<String> itemNamelist = new ArrayList<>();
        if(Objects.nonNull(assetTransactionDTO)){
            if(CollectionUtil.isNotEmpty(assetTransactionDTO.getWriteOffBillDTOList())){
                List<String> itemNames = assetTransactionDTO.getWriteOffBillDTOList().stream().map(e->e.getItemName()).collect(Collectors.toList());
                itemNamelist.addAll(itemNames);
            }

            if(CollectionUtil.isNotEmpty(assetTransactionDTO.getPredepositBillDTOList())){
                List<String> itemNames = assetTransactionDTO.getPredepositBillDTOList().stream().map(e->{
                    String preItemName = e.getPredepositItemName();
                    if(PredepositTypeEnum.SPECIAL_DEPOSIT.getCode().equals(e.getPredepositType())) {
                        preItemName = "专项预存" + preItemName;
                    }
                    return preItemName;
                }).collect(Collectors.toList());
                itemNamelist.addAll(itemNames);
            }

            if(CollectionUtil.isNotEmpty(assetTransactionDTO.getOrderBillDetailList())){
                List<String> itemNames = assetTransactionDTO.getOrderBillDetailList().stream().map(e->e.getItemName()).collect(Collectors.toList());
                itemNamelist.addAll(itemNames);
            }
        }
        return itemNamelist.stream().distinct().collect(Collectors.joining("、"));
    }

    /**
     * 处理缴费详情itemCode内容
     * @param dataInfoDTOList
     */
    private void handleBillItemCodes(List<RecordDetailBillVO> dataInfoDTOList) throws ChargeBusinessException{
        if(CollectionUtil.isEmpty(dataInfoDTOList)){
            return;
        }
        List<Long> itemIds = dataInfoDTOList.stream().map(RecordDetailBillVO::getItemId).collect(Collectors.toList());
        List<ChargeItemSimpleDTO> chargeItemSimpleDTOS = AppInterfaceUtil.getResponseDataThrowException(chargeItemClient.listSimpleChargeItemByIds(ChargeSimpleItemRequestDTO.builder().itemIds(itemIds).build()));
        if(CollectionUtil.isEmpty(chargeItemSimpleDTOS)){
            return;
        }
        Map<Long, String> id2CodeMap = chargeItemSimpleDTOS.stream().collect(Collectors.toMap(ChargeItemSimpleDTO::getItemId, ChargeItemSimpleDTO::getItemCode, (a,b) -> b));
        dataInfoDTOList.forEach(e -> e.setItemCode(id2CodeMap.get(e.getItemId())));
    }

    @Override
    public List<DepositAdjustRecordVO> listDepositAdjustRecord(Long communityId,Long assetId, Long predepositAccountId, Integer pageNum, Integer pageSize) throws ChargeBusinessException {
        //判断是否为押金账户
        PredepositAccountDTO accountDTO = predepositAccountClient.listPredepositAccount(PredepositAccountConditionDTO.builder().id(predepositAccountId).build()).getContent().get(0);
        AssertUtils.isTrue(PredepositTypeEnum.DEPOSIT.getCode().equals(accountDTO.getPredepositType()), ErrorInfoEnum.E1002.getCode(), "非押金账户！");

        //获取押金账户调整记录
        PredepositAdjustConditionDTO conditionDTO = PredepositAdjustConditionDTO.builder().predepositAccountId(predepositAccountId).assetId(assetId).pageNum(pageNum).pageSize(pageSize).build();
        PagingDTO<PredepositAdjustInfoDTO> pagingDTO = predepositAdjustClient.selectByCondition(conditionDTO).getContent();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(pagingDTO.getList())) {
            return new ArrayList<>();
        }

        //获取调出资产信息
        List<Long> targetAssetId = pagingDTO.getList().stream().map(PredepositAdjustInfoDTO::getTargetAssetId).collect(Collectors.toList());
        List<AssetAdapter> assetAdapterList = assetSupport.getAssetAdapterByIds(targetAssetId);
        Map<Long, AssetAdapter> assetAdapterMap = assetAdapterList.stream().collect(Collectors.toMap(AssetAdapter::getId, Function.identity()));

        List<Long> assetTransactionIds = pagingDTO.getList().stream().map(PredepositAdjustInfoDTO::getAssetTransactionId).collect(Collectors.toList());
        AssetTransactionParamDTO assetTransactionParamDTO = new AssetTransactionParamDTO();
        assetTransactionParamDTO.setIds(assetTransactionIds);
        List<AssetTransactionDTO> assetTransactionDTOS = AppInterfaceUtil.getResponseDataThrowException(assetTransactionClient.queryList(assetTransactionParamDTO));
        Map<Long, AssetTransactionDTO> assetTransactionDTOMap = assetTransactionDTOS.stream().collect(Collectors.toMap(AssetTransactionDTO::getId, Function.identity()));

        //调整原因
        List<PredepositBillDTO> predepositBillDTOs = AppInterfaceUtil.getResponseDataThrowException(predepositBillClient.selectByCondition(PredepositBillListQueryDTO.builder().assetTransactionIds(assetTransactionIds).build()));
        Map<Long, List<PredepositBillDTO>> predepositBillDTOMap = predepositBillDTOs.stream().collect(Collectors.groupingBy(t -> t.getAssetTransactionId()));

        List<OrderBillDetailDTO> orderBillDetails = orderSupport.listByTransactionId(communityId, assetTransactionIds);
        Map<Long, OrderBillDetailDTO> orderBillDetailMap = orderBillDetails.stream().collect(Collectors.toMap(t -> t.getAssetTransactionId(), t -> t));

        //构建返回VO
        List<DepositAdjustRecordVO> adjustRecordVOS = pagingDTO.getList().stream().map(e -> {
            DepositAdjustRecordVO vo = new DepositAdjustRecordVO();
            vo.setAdjustDate(e.getAdjustDate());
            vo.setAdjustAssetType(e.getAdjustType());
            vo.setAdjustAssetId(e.getTargetAssetId());
            AssetAdapter assetAdapter = assetAdapterMap.get(e.getTargetAssetId());
            vo.setAdjustAssetName(assetAdapter.getAssetName());
            vo.setAdjustMoney(e.getAdjustMoney());
            vo.setAdjustStatus(1);
            if (CollectionUtil.isNotEmpty(assetTransactionDTOMap) && assetTransactionDTOMap.get(e.getAssetTransactionId()) != null){
                vo.setAdjustNum(assetTransactionDTOMap.get(e.getAssetTransactionId()).getAssetOrderNum());
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(predepositBillDTOs) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(predepositBillDTOMap.get(e.getAssetTransactionId()))) {
                vo.setRemark(predepositBillDTOMap.get(e.getAssetTransactionId()).get(0).getMemo());
                vo.setAdjustUser(predepositBillDTOMap.get(e.getAssetTransactionId()).get(0).getCreateUser());
            } else if (!CollectionUtils.isEmpty(orderBillDetails)) {
                vo.setRemark(orderBillDetailMap.get(e.getAssetTransactionId()).getMemo());
                vo.setAdjustUser(orderBillDetailMap.get(e.getAssetTransactionId()).getCreateUser());
            }
            return vo;
        }).collect(Collectors.toList());

        return adjustRecordVOS;
    }

    /**
     * 预收退款记录
     *
     * @param predepositAccountId 预收账户id
     * @param pageNum             页码
     * @param pageSize            每页数量
     * @return 退款记录
     */
    @Override
    public List<PredepositRefundRecordVO> listRefundRecord(Long predepositAccountId,  Integer pageNum, Integer pageSize) throws ChargeBusinessException {
        PredepositRefundConditionDTO conditionDTO = PredepositRefundConditionDTO.builder().predepositAccountId(predepositAccountId)
                .pageNum(pageNum).pageSize(pageSize).build();
        PagingDTO<PredepositRefundRecordDTO> pageRefundDTO = predepositRefundClient.listRefundRecord(conditionDTO).getContent();
        List<PredepositRefundRecordVO> result = BeanCopierWrapper.copy(pageRefundDTO.getList(), PredepositRefundRecordVO.class);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(result)){
            return new ArrayList<>();
        }

        List<Long> refundBillIds = result.stream().map(t -> t.getRefundBillId()).collect(Collectors.toList());
        List<RefundBillDTO> refundBillDTOs = AppInterfaceUtil.getResponseDataThrowException(refundBillClient.listByIds(refundBillIds));
        Map<Long, RefundBillDTO> orderBillDetailMap = refundBillDTOs.stream().collect(Collectors.toMap(t->t.getId(),t->t));

        result.forEach(item->{
            item.setRemark(orderBillDetailMap.get(item.getRefundBillId()).getRefundRemark());
            item.setReceiveAccount(orderBillDetailMap.get(item.getRefundBillId()).getPayeeBankAccount());
            item.setRefundUser(orderBillDetailMap.get(item.getRefundBillId()).getCreateUser());
        });
        return result;
    }

    /**
     * 充值记录
     */
    @Override
    public PredepositBillVO queryBillList(Long assetId, Long communityId, Long predepositAccountId) throws ChargeBusinessException {
        PredepositBillQueryDTO queryDTO = new PredepositBillQueryDTO();
        queryDTO.setAssetId(assetId);
        queryDTO.setCommunityId(communityId);
        queryDTO.setPredepositAccountId(predepositAccountId);
        queryDTO.setQueryBillEventMoney(true);
        //1、查询预收充值列表接口
        ChargeResponse<PagingDTO<PredepositBillDTO>> response = predepositBillClient.queryPageable(queryDTO);
        if(!response.isSuccess()){
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(),response.getMessage());
        }
        List<PredepositBillVO> predepositBillVOS = PreDepositConverter.INSTANCE.toBillVOS((AppInterfaceUtil.getResponseDataThrowException(response).getList()));
        if (CollectionUtil.isNotEmpty(predepositBillVOS)) {
            //关联单据处理 收据号
            convertRelatedBillInfo(predepositBillVOS,communityId);
        }
        return predepositBillVOS.get(0);
    }

    /**
     * description: 获取资产信息
     **/
    private  Map<Long, Integer> getAssetTypeMap(List<Long> assetIds) {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(assetIds)){
            return Maps.newHashMap();
        }
        Map<Long, Integer> assetMap = new HashMap<>(assetIds.size());
        ChargeResponse<List<AssetDTO>> assetResponse = assetClient.listAsset(AssetCondition.builder().ids(assetIds).build());
        if (assetResponse.isSuccess() && CollectionUtil.isNotEmpty(assetResponse.getContent())) {
            assetMap = assetResponse.getContent().stream().collect(Collectors.toMap(AssetDTO::getId, AssetDTO::getType));
        }
        return assetMap;
    }

    /**
     * 关联单据处理
     * 产品跳转规则：根据支付类型跳转
     * 支付：跳转应收单
     * 待结转：跳转应收单-折扣明细单
     * 红冲调整：跳转应收单-红冲调整单
     * 预收调整：跳转预收单-预存调整单
     * @param predepositBillVOS
     * @throws ChargeBusinessException
     */
    private void convertRelatedBillInfo(List<PredepositBillVO> predepositBillVOS, Long communityId) throws ChargeBusinessException {
        List<Long> assetIds = predepositBillVOS.stream().map(PredepositBillVO::getAssetId).distinct().collect(Collectors.toList());
        Map<Long, Integer> assetInfoMap = getAssetTypeMap(assetIds);
        Map<Integer, List<PredepositBillVO>> collect = predepositBillVOS.stream().collect(Collectors.groupingBy(PredepositBillVO::getPayType));
        for (Map.Entry<Integer, List<PredepositBillVO>> entry : collect.entrySet()) {
            if (PayTypeEnum.PRE_DEPOSIT.getCode().equals(entry.getKey())) {   //1、支付
                List<PredepositBillVO> predepositBillVOList = entry.getValue();
                //1、支付：查询应收单信息信息 - 封装关联单据信息
                buildReceivableRelatedInfo(communityId,predepositBillVOList, assetInfoMap);
            } else if (PayTypeEnum.CARRY_FORWARD.getCode().equals(entry.getKey())) {   //待结转
                List<PredepositBillVO> billVOS = entry.getValue();
                //2、待结转：跳转应收单-折扣明细单 - 封装关联单据信息
                buildDiscountRelatedInfo(billVOS, assetInfoMap);
            } else if (PayTypeEnum.RED_ADJUST.getCode().equals(entry.getKey())) {  //红冲调整
                entry.getValue().stream().filter(predepositBillVO -> Objects.nonNull(predepositBillVO.getRelationId())).forEach(vo -> {
                    //关联单据应该为预收调整单
                    RefundBillDTO refundBillDTO = AppInterfaceUtil.getResponseData(refundBillClient.oneById(vo.getRelationId()));
                    BillRelateInfoVO relateInfoVO = new BillRelateInfoVO();
                    relateInfoVO.setRelatedNo(Objects.isNull(refundBillDTO) ? "" : refundBillDTO.getRefundBillNo());
                    relateInfoVO.setRefundId(vo.getRelationId());
                    relateInfoVO.setAssetId(vo.getAssetId());
                    relateInfoVO.setAssetType(assetInfoMap.get(vo.getAssetId()));
                    vo.getBillRelateInfoList().add(relateInfoVO);
                });
            } else if (PayTypeEnum.DEPOSIT_ADJUST.getCode().equals(entry.getKey())
                    || PayTypeEnum.PRE_DEPOSIT_ADJUST.getCode().equals(entry.getKey())) {
                //预收调整和押金调整
                buildAdvanceDepositPayAdjust( entry);
            }else if(PayTypeEnum.ORDER_ADJUST.getCode().equals(entry.getKey())){
                //补充订单调整的关联信息
                fillBillRelatesOfOrderAdjust(communityId,entry.getValue());
            }
        }
    }

    private void fillBillRelatesOfOrderAdjust( Long communityId,List<PredepositBillVO> preDepositBills) throws ChargeBusinessException {
        Set<Long> assetTransactionIds = preDepositBills.stream().map(PredepositBillVO::getAssetTransactionId).collect(Collectors.toSet());
        ChargeResponse<List<OrderAdjustDetailDTO>> orderAdjustDetailResp = orderAdjustClient.listOrderAdjustDetail(OrderAdjustQueryDTO.builder()
                .communityId(communityId)
                .assetTransactionIds(assetTransactionIds).build());
        List<OrderAdjustDetailDTO> orderAdjustDetails = AppInterfaceUtil.getResponseDataThrowException(orderAdjustDetailResp);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(orderAdjustDetails)){
            return;
        }
        Map<Long, Long> assetTransactionIdToOrderIdMap = orderAdjustDetails.stream().collect(Collectors.toMap(OrderAdjustDetailDTO::getAssetTransactionId, OrderAdjustDetailDTO::getOrderId, (a, b) -> b));
        List<Long> orderIds = orderAdjustDetails.stream().map(OrderAdjustDetailDTO::getOrderId).collect(Collectors.toList());
        OrderClassifyRequestDTO orderClassifyRequestDTO=new OrderClassifyRequestDTO();
        orderClassifyRequestDTO.setOrderIds(orderIds);
        orderClassifyRequestDTO.setCommunityId(communityId);
        ChargeResponse<List<OrderDTO>> orderResp = orderClient.listOrderAndClassifyByIds(orderClassifyRequestDTO);
        Map<Long, OrderDTO> orderMap = AppInterfaceUtil.getResponseDataThrowException(orderResp).stream().collect(Collectors.toMap(OrderDTO::getId, Function.identity(), (a, b) -> a));
        preDepositBills.forEach(predepositBillVO -> {
            Long orderId = assetTransactionIdToOrderIdMap.get(predepositBillVO.getAssetTransactionId());
            if(orderId!=null&&orderMap.containsKey(orderId)){
                OrderDTO orderDTO = orderMap.get(orderId);
                BillRelateInfoVO relateInfoVO = new BillRelateInfoVO();
                relateInfoVO.setRelatedNo(orderDTO.getOrderNum());
                relateInfoVO.setAssetId(orderDTO.getAssetId());
                relateInfoVO.setTargetBillId(orderDTO.getId());
                relateInfoVO.setSecondClassificationId(orderDTO.getSecondClassificationId());
                List<BillRelateInfoVO> relates = new ArrayList<>();
                relates.add(relateInfoVO);
                predepositBillVO.setBillRelateInfoList(relates);
            }
        });
    }

    /**
     * description: 获取预收押金账户信息
     **/
    private void buildAdvanceDepositPayAdjust( Map.Entry<Integer, List<PredepositBillVO>> entry) throws ChargeBusinessException {
        List<Long> assetTransactionIds = entry.getValue().stream().map(PredepositBillVO::getAssetTransactionId).distinct().collect(Collectors.toList());
        AssetTransactionParamDTO assetTransactionParamDTO = new AssetTransactionParamDTO();
        assetTransactionParamDTO.setIds(assetTransactionIds);
        List<AssetTransactionDTO> assetTransactionDTOS = AppInterfaceUtil.getResponseDataThrowException(assetTransactionClient.queryList(assetTransactionParamDTO));
        Map<Long, AssetTransactionDTO> assetTransactionDTOMap = assetTransactionDTOS.stream().collect(Collectors.toMap(AssetTransactionDTO::getId, Function.identity()));
        for (PredepositBillVO vo : entry.getValue()) {
            AssetTransactionDTO assetTransactionDTO = assetTransactionDTOMap.get(vo.getAssetTransactionId());
            List<BillRelateInfoVO> relateInfoVOS = new ArrayList<>();
            BillRelateInfoVO relateInfoVO = new BillRelateInfoVO();
            relateInfoVO.setRelatedNo(assetTransactionDTO.getAssetOrderNum());
            relateInfoVOS.add(relateInfoVO);
            vo.setBillRelateInfoList(relateInfoVOS);
        }
    }

    /**
     * 根据待结转的预收明细id获取到关联应收单关联信息
     */
    private void buildDiscountRelatedInfo(List<PredepositBillVO> billVOS, Map<Long, Integer> assetInfoMap) throws ChargeBusinessException {
        DiscountDetailConditionDTO conditionDTO = new DiscountDetailConditionDTO();
        conditionDTO.setPredepositBillIds(billVOS.stream().map(PredepositBillVO::getId).collect(Collectors.toList()));
        ChargeResponse<List<DiscountDetailDTO>> discountDetailResponse = discountDetailClient.listDiscountDetail(conditionDTO);
        List<DiscountDetailDTO> discountDetailDTOList = AppInterfaceUtil.getDataThrowException(discountDetailResponse);
        //若查询折扣明细不为空，组装应收单-折扣明细
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(discountDetailDTOList)) {
            //查询应收单信息
            List<Long> receiveBillIds = discountDetailDTOList.stream()
                    .filter(dto -> dto.getReceivableBillId() != null)
                    .map(DiscountDetailDTO::getReceivableBillId)
                    .collect(Collectors.toList());
            ReceivableConditionDTO receivableConditionDTO = ReceivableConditionDTO.builder().ids(receiveBillIds).build();
            ChargeResponse<List<ReceivableBillDTO>> receivableBillResponse = receivableBillClient.queryList(receivableConditionDTO);
            List<ReceivableBillDTO> receivableBillDTOS = AppInterfaceUtil.getDataThrowException(receivableBillResponse);

            //根据预收单id进行进行分组
            Map<Long, List<DiscountDetailDTO>> discountGroupMap = discountDetailDTOList.stream().collect(Collectors.groupingBy(DiscountDetailDTO::getPredepositBillId));

            Map<Long, String> receivableIdMap = receivableBillDTOS.stream().collect(Collectors.toMap(ReceivableBillDTO::getId, ReceivableBillDTO::getBillNum, (v1, v2) -> v1));
            for (PredepositBillVO vo : billVOS) {
                List<DiscountDetailDTO> discountDetailDTOS = discountGroupMap.get(vo.getId());
                for (DiscountDetailDTO discountDetailDTO : discountDetailDTOS) {
                    BillRelateInfoVO relateInfoVO = new BillRelateInfoVO();
                    relateInfoVO.setRelatedNo(receivableIdMap.get(discountDetailDTO.getReceivableBillId()));
                    relateInfoVO.setAssetId(vo.getAssetId());
                    relateInfoVO.setAssetType(assetInfoMap.get(vo.getAssetId()));
                    vo.getBillRelateInfoList().add(relateInfoVO);
                }
            }
        }
    }

    /**
     * description: 查询应收单信息信息 - 封装关联单据信息
     **/
    private void buildReceivableRelatedInfo(Long communityId,List<PredepositBillVO> predepositBillVOList, Map<Long, Integer> assetInfoMap) throws ChargeBusinessException {
        List<Long> assetTranIds = predepositBillVOList.stream()
                .filter(dto -> dto.getAssetTransactionId() != null)
                .map(PredepositBillVO::getAssetTransactionId).collect(Collectors.toList());
        AssetTransactionParamDTO paramDTO = new AssetTransactionParamDTO();
        paramDTO.setIds(assetTranIds);
        paramDTO.setCommunityIds(Collections.singletonList(communityId));
        ChargeResponse<List<AssetTransactionDTO>> assetTranResp = assetTransactionClient.queryList(paramDTO);
        if(!assetTranResp.isSuccess()){
            log.info("查询折扣明细接口远程调用异常，入参{},返回{}", paramDTO, assetTranResp);
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(),assetTranResp.getMessage());
        }
        Map<Long, AssetTransactionDTO> incomeMap = assetTranResp.getContent().stream().collect(Collectors.toMap(BaseDTO::getId, Function.identity(), (v1, v2) -> v1));
        predepositBillVOList.forEach(vo -> {
            //需要跳转实收单，通过transaction_relation关联asset_transaction_id通过这个id去关联income_id
            AssetTransactionDTO transactionDTO = incomeMap.get(vo.getAssetTransactionId());
            BillRelateInfoVO relateInfoVO = new BillRelateInfoVO();
            relateInfoVO.setRelatedNo(transactionDTO.getAssetOrderNum());
            relateInfoVO.setAssetId(vo.getAssetId());
            relateInfoVO.setAssetType(assetInfoMap.get(vo.getAssetId()));
            vo.getBillRelateInfoList().add(relateInfoVO);
        });
    }

}
