package com.charge.api.web.controller.joylife.pay;

import com.charge.api.web.constants.YueConstants;
import com.charge.api.web.convert.ChargeBillConverter;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.support.BankCollectionCheckSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.joylife.dto.*;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.pay.client.CommunityMerchantClient;
import com.charge.pay.dto.CommunityMerchantDTO;
import com.charge.pay.dto.CommunityMerchantTradePayTypeDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询商户配置信息
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantController {

    private final CommunityMerchantClient communityMerchantClient;
    private final CommunityClient communityClient;
    private final AssetSupport assetSupport;
    private final CommunitySupport communitySupport;
    private final BankCollectionCheckSupport bankCollectionCheckSupport;

    /**
     * 查询商户号配置
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @ApiOperation(value = "查询商户号配置")
    @PostMapping(value = "/app/merchant/info")
    @Deprecated
    public ChargeResponse<List<CommunityMerchantInfoDTO>> listAssetsArrearsInfo(@RequestBody @Valid CommunityQueryDTO condition) throws ChargeBusinessException {
         List<CommunityDTO> communityDTOS = AppInterfaceUtil.getResponseDataThrowException(communityClient.listCommunity(CommunityCondition.builder().msIds(condition.getMsCommunityIds()).build()));
         if(CollectionUtil.isEmpty(communityDTOS)){
             return new ChargeResponse(Collections.EMPTY_LIST);
         }
         List<CommunityMerchantInfoDTO> result =new ArrayList<>(communityDTOS.size());
         for(CommunityDTO community:communityDTOS){
             CommunityMerchantInfoDTO info=new CommunityMerchantInfoDTO();
             info.setCommunityId(String.valueOf(community.getId()));
             info.setCommunityMsId(community.getMsId());
             info.setCommunityName(community.getName());
             List<CommunityMerchantDTO> merchantDTOList = communitySupport.getCommunityMerchants(community.getId());

             if(CollectionUtil.isEmpty(merchantDTOList)){
                 info.setCommunityMerchantList(Collections.EMPTY_LIST);
                 result.add(info);
             }
             info.setCommunityMerchantList(ChargeBillConverter.INSTANCE.convertMerchantDTO(merchantDTOList));
             result.add(info);
         }
         return new ChargeResponse<>(result);
    }

    /**
     * 查询小区是否开启缴费
     * @return
     */
    @PostMapping(value = "/app/payEnableStatus")
    public ChargeResponse<CommunityPayEnableStatusResp> getCommunityPayEnableStatus(@Valid @RequestBody CommunityPayEnableQueryDTO condition) throws ChargeBusinessException {
        log.info("{}|进入查询小区缴费开启状态接口：{}", LogCategoryEnum.BUSSINESS,condition);

        Long communityId = communitySupport.getCommunityIdByMsId(condition.getCommunityMsId());
        List<CommunityMerchantDTO> configList = communitySupport.getCommunityMerchants(communityId);
        //配置数据
        Integer status = CollectionUtil.isEmpty(configList)? YueConstants.COMMUNITY_PAY_DISABLE :YueConstants.COMMUNITY_PAY_ENABLE;
        //小区缴费锁状态==银行托收报盘中
        Integer payEnableStatus = YueConstants.COMMUNITY_PAY_ENABLE;//默认开启
        List<AssetListReqDTO> assetList = condition.getAssetList();
        try{
            List<Long> assetIds = CollectionUtil.isEmpty(condition.getAssetIds())?new ArrayList<>():condition.getAssetIds();
            if (CollectionUtil.isNotEmpty(assetList)) {
                List<AssetAdapter> adapters = new ArrayList<>();
                for (int i = 0; i < assetList.size(); i++) {
                    AssetListReqDTO reqDTO = assetList.get(i);
                    List<AssetAdapter> adapterList = assetSupport.getAssetListByCondition(AssetCondition.builder().communityId(communityId)
                            .msIds(reqDTO.getHouseMsIdList()).type(reqDTO.getAssetType()).build());
                    adapters.addAll(adapterList);
                    assetIds.addAll(adapterList.stream().map(AssetAdapter::getId).collect(Collectors.toList()));
                }
                //查询报盘状态
                if (CollectionUtil.isNotEmpty(assetIds) && bankCollectionCheckSupport.bankCollectingByAssetIds(assetIds)) {
                    payEnableStatus = YueConstants.COMMUNITY_PAY_DISABLE;
                }
            }
        } catch (ChargeBusinessException exception){
            log.error("{}|查询小区缴费开启状态接口查询资产信息异常：{}，{}", LogCategoryEnum.BUSSINESS,condition,exception.getMessage());
        }

        return new ChargeResponse<>(CommunityPayEnableStatusResp.builder().communityId(Long.toString(communityId)).
                communityMsId(condition.getCommunityMsId()).status(status).payEnableStatus(payEnableStatus).build());
    }


    @PostMapping(value = "/app/merchant/payType")
    public ChargeResponse<CommunityMerchantTradePayTypeDTO> getMerchantTradePayType(@Valid @RequestBody CommunityPayEnableQueryDTO condition) throws ChargeBusinessException {

        Long communityId = communitySupport.getCommunityIdByMsId(condition.getCommunityMsId());
        CommunityMerchantTradePayTypeDTO merchantTradePayType = communitySupport.getMerchantTradePayType(communityId);
        return new ChargeResponse<>(merchantTradePayType);
    }
}
