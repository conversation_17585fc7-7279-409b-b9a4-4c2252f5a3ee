package com.charge.api.web.vo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 调整新增子订单
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SubOrderAdjustAddCmd extends BaseOrderCmdReq {

    /**
     * 子订单列表
     */
    @NotEmpty(message = "子订单号不能为空")
    private List<SubOrderAdd> subOrderAmounts;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    @NotBlank(message = "用户名称不能为空")
    private String customerName;

    private Integer payStatus;

    /**
     * 支付单号，当调整已支付单据时，需要传入
     */
    private String payOrderNum;

}