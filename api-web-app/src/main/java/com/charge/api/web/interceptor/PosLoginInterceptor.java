package com.charge.api.web.interceptor;

import com.charge.api.web.enums.ApiWebErrorEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.UserSessionDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.user.client.AuthAccountClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

import static com.charge.api.web.constants.LakalaConstans.ACCOUNT;

/**
 * POS登录统一拦截器
 * @Description
 * @Author: yjw
 * @Date: 2023/10/17 17:48
 */
@Slf4j
public class PosLoginInterceptor implements HandlerInterceptor {

    @Autowired
    private AuthAccountClient authAccountClient;

    private static final String POS_INVOKE_PROXY_FLAG = "X-POS-INVOKE-PROXY";

    private static final String TOKEN = "token";

    private static final String TRUE = "true";

    /**
     * 在请求匹配controller之前执行，返回true才行进行下一步
     * @param request
     * @param response
     * @param handler
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        String posFlag = request.getHeader(POS_INVOKE_PROXY_FLAG);
        if(StringUtils.isBlank(posFlag)||!TRUE.equals(posFlag)){
            return HandlerInterceptor.super.preHandle(request, response, handler);
        }
        String token = request.getHeader(TOKEN);
        if(StringUtils.isBlank(token)){
            token=request.getParameter(TOKEN);
        }
        if (StringUtils.isBlank(token)) {
            log.warn("PosLoginInterceptor token null,param {}",request.getParameterMap());
            throw new ChargeBusinessException(ApiWebErrorEnum.TOKEN_NOT_EXIST_OR_EXPIRE);
        }
        //开始进行pos2.0登录态校验
        ChargeResponse<UserSessionDTO> loginCheckResponse = authAccountClient.loginCheck(token);
        UserSessionDTO userSessionDTO = AppInterfaceUtil.getResponseDataThrowException(loginCheckResponse);
        if (Objects.isNull(userSessionDTO)) {
            log.warn("{}|用户请求的token为：{},userSession 为空", LogCategoryEnum.BUSSINESS, token);
            throw new ChargeBusinessException(ApiWebErrorEnum.TOKEN_NOT_EXIST_OR_EXPIRE);
        } else {
            ThreadContext.put(ACCOUNT,userSessionDTO.getAccount());
            return HandlerInterceptor.super.preHandle(request, response, handler);
        }
    }
    /**
     * 视图也渲染完了，此时我可以做一些清理工作了
     * @param request
     * @param response
     * @param handler
     * @param ex
     * @throws Exception
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ThreadContext.remove(ACCOUNT);
    }

}
