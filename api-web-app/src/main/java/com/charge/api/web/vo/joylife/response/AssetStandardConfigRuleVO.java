package com.charge.api.web.vo.joylife.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/05 17:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssetStandardConfigRuleVO {
    /**
     * 资产id
     */
    private Long assetId;

    /**
     * 是否有 物业管理费计费标准
     */
    private Boolean isHaveItemStandard;

    /**
     * 计费规则列表
     */
    private List<StandardConfigRuleVO> standardRuleVOList;
}


