package com.charge.api.web.service.impl;

import com.charge.api.web.dto.joylife.PayResultV1;
import com.charge.api.web.service.joylife.IncomeBillService;
import com.charge.api.web.support.CommunitySupport;
import com.charge.bill.client.IncomeBillClient;
import com.charge.bill.dto.income.IncomeBillDTO;
import com.charge.bill.dto.income.IncomeConditionDTO;
import com.charge.bill.dto.income.IncomeOrderInfosDTO;
import com.charge.bill.dto.income.WorkOrderInfosDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.DateUtils;
import com.charge.core.util.TraceContextUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: 实收单
 * @date 2022/12/1417:11
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class IncomeBillServiceImpl implements IncomeBillService {

    private final IncomeBillClient incomeBillClient;

    private final CommunitySupport communitySupport;

    @Override
    public ChargeResponse<Long> createIncomeBills(IncomeOrderInfosDTO incomeOrderInfosDTO) throws ChargeBusinessException {

        return incomeBillClient.createPayIncomeBills(incomeOrderInfosDTO);
    }

    @Override
    public ChargeResponse<Long> createWorkOrderBills(WorkOrderInfosDTO workOrderInfosDTO) throws ChargeBusinessException {
        // 筛选房间id，查询基础数据信息,TODO
        return incomeBillClient.createWorkOrderBills(workOrderInfosDTO);
    }

    @Override
    public ChargeResponse<PayResultV1> getPayResult(String orderNum) throws ChargeBusinessException{
        if (StringUtils.isEmpty(orderNum)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "交易订单号为空！");
        }
        Long communityId = TraceContextUtil.getCommunityId();
        if (communityId == null) {
            communityId = communitySupport.getCommunityIdByOrderNum(orderNum);
            TraceContextUtil.setCommunityId(communityId);
        }
        IncomeConditionDTO incomeConditionDTO = IncomeConditionDTO.builder().orderNum(orderNum).pageNum(1).communityId(communityId)
                .pageSize(10).build();
        ChargeResponse<PagingDTO<IncomeBillDTO>> incomeBillResponse = incomeBillClient.selectByCondition(incomeConditionDTO);
        if(!incomeBillResponse.isSuccess() || incomeBillResponse.getContent() == null ||
                incomeBillResponse.getContent().getList() == null || incomeBillResponse.getContent().getList().size() == 0){
            return new ChargeResponse(902, "交易不存在");
        }

        IncomeBillDTO incomeBillDTO = incomeBillResponse.getContent().getList().get(0);
        PayResultV1 resultMap = toPayResultV1(incomeBillDTO);
        return new ChargeResponse(resultMap);
    }

    private PayResultV1 toPayResultV1(IncomeBillDTO incomeBillDTO){
        PayResultV1 payResultV1 = new PayResultV1();
        payResultV1.setOrderNum(incomeBillDTO.getOrderNum());
        payResultV1.setPayStatus(incomeBillDTO.getPayStatus());
        payResultV1.setBillType("singleOrder");
        String payCode = "OTHER";
        switch (incomeBillDTO.getPayStatus()) {
            case 0:
                payCode = "NOTPAY";
                break;
            case 1:
                payCode = "SUCCESS";
                break;
            case 2:
                payCode = "FAIL";
                break;
            default:
                break;
        }
        payResultV1.setPayCode(payCode);
        payResultV1.setPayTime(DateUtils.format(incomeBillDTO.getPaymentTime(), DateUtils.FORMAT_0));
        payResultV1.setPayMoney(incomeBillDTO.getIncomeMoney().setScale(2).toString());
        if (Objects.nonNull(incomeBillDTO.getPenaltyMoney())) {
            payResultV1.setPayPenalty(incomeBillDTO.getPenaltyMoney().setScale(2).toString());
        } else {
            payResultV1.setPayPenalty("0.00");
        }
        payResultV1.setPayMemberMobile(incomeBillDTO.getPayMemberMobile());
        payResultV1.setPaymentMethod(incomeBillDTO.getPaymentMethod());
        payResultV1.setOutTransactionNo(incomeBillDTO.getOutTransactionNo());
        payResultV1.setPayMember(incomeBillDTO.getPayMember());
        return payResultV1;
    }
}
