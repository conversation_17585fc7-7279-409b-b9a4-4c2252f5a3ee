package com.charge.api.web.controller.joylife.bill.response;

import com.charge.api.web.controller.joylife.bill.request.PayOrAdjustItemVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 实收单对应的Item详情
 */
@Data
public class ItemsVO implements Serializable {

    private static final long serialVersionUID = 2805272637674517204L;

    /**
     * 实收单ID
     */
    private Long incomeBillId;

    /**
     * 资产流水表ID
     */
    private Long transactionId;

    /**
     * 押金列表
     */
    private List<PayOrAdjustItemVO> depositItems;

    /**
     * 临时订单列表
     */
    private List<PayOrAdjustItemVO> orderItems;
}
