package com.charge.api.web.service.pay.impl;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.adapter.AssetAdapter;
import com.charge.api.web.adapter.HouseClientAdapter;
import com.charge.api.web.config.SwitchConfig;
import com.charge.api.web.constants.AssetTypeEnum;
import com.charge.api.web.constants.BillChargeTypeIdEnum;
import com.charge.api.web.constants.LakalaConstans;
import com.charge.api.web.constants.YueConstants;
import com.charge.api.web.dto.pos.AssetTypeArrearsAmount;
import com.charge.api.web.service.pay.LakalaService;
import com.charge.api.web.support.AssetOverviewSupport;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.support.PrePayLockSupport;
import com.charge.api.web.util.FeignUtil;
import com.charge.api.web.util.ShardingUtil;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.lakala.*;
import com.charge.api.web.vo.pos.*;
import com.charge.bill.client.*;
import com.charge.bill.dto.*;
import com.charge.bill.dto.assetoverview.AssetOverViewDTO;
import com.charge.bill.dto.income.*;
import com.charge.bill.dto.predeposit.AsssetPredepositCountDTO;
import com.charge.bill.enums.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.dto.UserSessionDTO;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ChargeStatusEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.util.EnumUtil;
import com.charge.config.client.item.ChargeItemClient;
import com.charge.config.client.item.CommunityChargeItemClient;
import com.charge.config.dto.item.ChargeItemDTO;
import com.charge.config.dto.item.CommunityChargeItemDTO;
import com.charge.config.dto.item.condition.ChargeItemQueryConditionDTO;
import com.charge.config.dto.item.condition.CommunityChargeItemQueryConditionDTO;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.enums.CustomerTypeEnum;
import com.charge.maindata.enums.HouseTypeEnum;
import com.charge.maindata.pojo.dto.*;
import com.charge.pay.PayBusinessCallback;
import com.charge.pay.channel.lakala.enums.LakalaPayMethodEnum;
import com.charge.pay.client.LakalaClient;
import com.charge.pay.client.PayRecordClient;
import com.charge.pay.domain.PayOrderResultDO;
import com.charge.pay.dto.ChargePayResponse;
import com.charge.pay.dto.LakalaPayRequestDTO;
import com.charge.pay.dto.LakalaReturnDTO;
import com.charge.pay.dto.PayRecord;
import com.charge.pay.enums.PayTradeStateEnum;
import com.charge.pos.dto.AccountInfo;
import com.charge.pos.dto.CommunityDtoV1;
import com.charge.starter.jedis.JedisManager;
import com.charge.user.client.AuthAccountClient;
import com.charge.user.client.AuthPermissionClient;
import com.charge.user.client.AuthUserClient;
import com.charge.user.dto.account.AccountLoginRequestDTO;
import com.charge.user.dto.account.AccountLoginResponseDTO;
import com.charge.user.dto.org.AuthCommunitySimpleResponseDTO;
import com.charge.user.dto.user.AuthUserInfoRequestDTO;
import com.charge.user.dto.user.AuthUserInfoResponseDTO;
import com.charge.user.enums.LoginSourceEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.charge.api.web.constants.LakalaConstans.COMMUNITY_ID;
import static com.charge.api.web.constants.LakalaConstans.LOGIN_POS;
import static com.charge.api.web.enums.ApiWebErrorEnum.ACCOUNT_OR_PASSWORD_MISS;
import static com.charge.api.web.service.pos.impl.AssetServiceImpl.*;
import static com.charge.api.web.service.pos.impl.ChargeBillServiceImpl.nullStr;
import static com.charge.api.web.support.AssetSupport.getAssetBuildingName;
import static com.charge.api.web.support.AssetSupport.getAssetNum;
import static com.charge.api.web.util.DesensitizeUtil.desensitize;
import static com.charge.common.constant.PayRelatedConstants.*;
import static com.charge.common.util.DateUtils.FORMAT_0;
import static com.charge.common.util.DateUtils.FORMAT_1;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
@RefreshScope
public class LakalaServiceImpl implements LakalaService {

    private final com.charge.bill.client.BillPayOperateClient billClient;

    private final PayRecordClient payRecordClient;

    private final LakalaClient lakalaClient;

    private final IncomeBillClient incomeBillClient;

    private final HouseClientAdapter houseClientAdapter;

    private final CommunityClient communityClient;

    private final ReceivableBillClient receivableBillClient;

    private final ChargeItemClient chargeItemClient;

    private final AssetTransactionClient assetTransactionClient;

    private final WriteOffBillClient writeOffBillClient;

    private final AssetOverviewSupport assetOverviewSupport;

    private final AssetClient assetClient;

    private final CommunityChargeItemClient communityChargeItemClient;

    private final JedisManager jedisManager;

    private final AuthUserClient authUserClient;

    private final PredepositAccountClient predepositAccountClient;

    private final AuthAccountClient authAccountClient;

    private final AuthPermissionClient authPermissionClient;

    private final CommunitySupport communitySupport;

    private final AssetBillInfoClient assetBillInfoClient;

    private final AssetSupport assetSupport;

    private final PrePayLockSupport prePayLockSupport;

    private static final String SUCCESS ="0";
    private static final String FAIL ="1";

    @Resource
    private PayBusinessCallback payBusinessCallback;

    @Value("${searchPayResult.doubleCheck:true}")
    private Boolean searchPayResultDoubleCheck;

    private final SwitchConfig switchConfig;

    @Override
    public ChargeResponse<SearchPayResultResponse> searchPayResult(SearchPayResultRequest resultRequest) {
        SearchPayResultResponse searchPayResultResponse = SearchPayResultResponse.builder().build();
        if (resultRequest.getPayResult().equals(SUCCESS)) {
            searchPayResultResponse.setResultMsg("支付成功");
            searchPayResultResponse.setResultCode(0);
        } else {
            searchPayResultResponse.setResultMsg("支付失败");
            searchPayResultResponse.setResultCode(1);
        }
        ChargeResponse<SearchPayResultResponse> resp = new ChargeResponse<>();
        resp.setContent(searchPayResultResponse);
        try {
            //因为旧协议返回中需要实收id，因此不得不去查实收单
            ChargeResponse<PayRecord> payRecordChargeResponse = payRecordClient.selectByOrderNum(resultRequest.getOrderNo());
            PayRecord payRecord = AppInterfaceUtil.getResponseDataThrowException(payRecordChargeResponse);
            if (payRecord == null) {
                resp.setSuccess(false);
                resp.setCode(ChargeStatusEnum.FAIL.getCode());
                resp.setMessage("核销失败:订单不存在:" + resultRequest.getOrderNo());
                searchPayResultResponse.setResultMsg("支付失败");
                searchPayResultResponse.setResultCode(1);
                return resp;
            }
            IncomeBillDTO incomeBillDTO = getIncomeBillByOrderNum(resultRequest.getOrderNo(), payRecord.getCommunityId());
            String incomeBillId = nullStr(incomeBillDTO.getId());
            searchPayResultResponse.setPayId(incomeBillId);
            if (BillPayStatusEnum.SUCCESS.getCode().equals(incomeBillDTO.getPayStatus())) {
                log.warn("订单为已支付，不做处理,订单号:{}", resultRequest.getOrderNo());
                return resp;
            }

            if(!org.springframework.util.StringUtils.hasText(resultRequest.getAmount())){
                resultRequest.setAmount(payRecord.getMoney().multiply(BigDecimal.valueOf(100L)).toBigInteger().toString());
            }
            PayOrderResultDO payOrderResultDO = new PayOrderResultDO();
            payOrderResultDO.setMchId(resultRequest.getMercid());
            //己方orderNum
            payOrderResultDO.setOutTradeNo(resultRequest.getOrderNo());
            payOrderResultDO.setOutTransactionId(resultRequest.getOutTransactionId());
            payOrderResultDO.setTransactionId(resultRequest.getTranSeqNo());
            payOrderResultDO.setTradeState(resultRequest.getPayResult().equals(SUCCESS) ? PayTradeStateEnum.SUCCESS.getCode()
                    : PayTradeStateEnum.FAIL.getCode());
            if (!org.springframework.util.StringUtils.hasText(resultRequest.getPayTime())) {
                resultRequest.setPayTime(DateUtils.format(new Date(), DateUtils.FORMAT_0));
            }
            payOrderResultDO.setPayFinishTime(resultRequest.getPayTime());
            payOrderResultDO.setPaymentCode(transfer(resultRequest.getPayMethodChannel()).getPaymentCode());
            payOrderResultDO.setTotalFee(resultRequest.getAmount());
            payBusinessCallback.deal(payOrderResultDO);
            return resp;
        } catch (Exception e) {
            log.error("payBusinessCallback.deal fail {},err {}", resultRequest, e.getMessage(), e);
            resp.setSuccess(false);
            resp.setCode(ChargeStatusEnum.FAIL.getCode());
            resp.setMessage("核销失败:" + e.getMessage());
            searchPayResultResponse.setResultMsg("支付失败");
            searchPayResultResponse.setResultCode(1);
            return resp;
        }
    }

    private LakalaPayMethodEnum transfer (PayMethodChannel payMethodChannel){

        if(PaymentChannelEnum.ALI_PAY.equals(payMethodChannel.getPaymentChannel())){
            return LakalaPayMethodEnum.ALIPAY;
        }else if(PaymentChannelEnum.WECHAT_PAY.equals(payMethodChannel.getPaymentChannel())){
            return LakalaPayMethodEnum.WECHAT;
        }else if(PaymentChannelEnum.BANK.equals(payMethodChannel.getPaymentChannel())){
            return LakalaPayMethodEnum.UPCARD;
        }else {
            return LakalaPayMethodEnum.SCAN_CODE_OTHER;
        }

    }

    @Override
    public PosResponse payNotify(PosPayNotify notify) {
        SearchPayResultRequest payResultRequest = new SearchPayResultRequest(notify.getMchId(), null
                , PayMethodChannel.fromV1PaymentMethod(transferPayTypeToPayMethodV1(notify.getPayType())), null,
                notify.getOutTradeNo()
                , notify.getRefernumber(), null, null, false, false,notify.getTransactionId(),DateUtils.format(notify.getTimeEnd(), "yyyyMMddHHmmss", "yyyy-MM-dd HH:mm:ss"),notify.getPayAmt());
        if ("SUCCESS".equals(notify.getResultCode())) {
            payResultRequest.setPayResult(SUCCESS);
        } else {
            payResultRequest.setPayResult(FAIL);
        }
        if(!org.springframework.util.StringUtils.hasText(payResultRequest.getOrderNo())){
            return PosResponse.fail("订单号不能为空");
        }
        ChargeResponse<SearchPayResultResponse> response = searchPayResult(payResultRequest);
        if (response.isSuccess()) {
            return PosResponse.success();
        } else {
            return PosResponse.fail(response.getMessage());
        }
    }


    private String transferPayTypeToPayMethodV1(String payType) {
       if (LakalaConstans.ALIPAY.equals(payType)) {
            return PAYMENT_METHOD_ALIPAY_CODE;
        } else if (LakalaConstans.WECHAT.equals(payType)) {
            return PAYMENT_METHOD_WEIXIN_CODE;
        }else {
            return PAYMENT_METHOD_POS_CODE;
        }
    }

    private  IncomeBillDTO getIncomeBillByOrderNum(String orderNum, Long communityId) throws ChargeBusinessException {
        IncomeConditionDTO incomeConditionDTO = new IncomeConditionDTO();
        incomeConditionDTO.setOrderNum(orderNum);
        incomeConditionDTO.setCommunityId(communityId);
        incomeConditionDTO.setPageSize(10);
        incomeConditionDTO.setPageNum(1);
        ChargeResponse<PagingDTO<IncomeBillDTO>> incomeBillResp = incomeBillClient.selectByCondition(incomeConditionDTO);
        PagingDTO<IncomeBillDTO> incomeBillDTOPagingDTO = FeignUtil.getContent(incomeBillResp, "查询实收单服务");
        if (org.springframework.util.CollectionUtils.isEmpty(incomeBillDTOPagingDTO.getList())) {
            throw  new ChargeBusinessException(ChargeStatusEnum.FAIL.getCode().toString(),"查询不到对应实收单");
        }
        return incomeBillDTOPagingDTO.getList().get(0);
    }

    private void checkPayResult(SearchPayResultRequest resultRequest) throws ChargeBusinessException {
        ChargeResponse<LakalaReturnDTO> payResult = lakalaClient.getLakalaPayResult(
                LakalaPayRequestDTO.builder()
                        .mercId(resultRequest.getMercid())
                        .orderNum(resultRequest.getOrderNo())
                        .tranSeqNo(resultRequest.getTranSeqNo())
                        .build());
        if (payResult.isSuccess()&&payResult.getContent()!=null) {
            LakalaReturnDTO payResultContent = payResult.getContent();
            if ((!resultRequest.getTranSeqNo().equals(payResultContent.getRefernumber())) || (!resultRequest.getOrderNo().equals(payResultContent.getOutTradeNo()))) {
                log.warn("回查拉卡拉支付结果与回调结果不一致 ,回调 {},回查{}", resultRequest, payResultContent);
                throw  new ChargeBusinessException("回查拉卡拉支付结果与回调结果不一致");
            }
        }
        //todo 对于回查失败的需要以后等迁移新的lakala接口后接入验证，当前的该接口大部分是返回失败的
    }


    private ChargeResponse checkBillMoney(List<ReceivableBillDTO> receivableBillDTOList, String arrearsPrice) {
        BigDecimal arrearsAmount, penaltyArrearsAmount;
        BigDecimal orderArrears = BigDecimal.ZERO;

        for (ReceivableBillDTO receivableBillDTO : receivableBillDTOList) {
            if (ReceivableBillPayStatusEnum.PAY_SUCCESS.getCode().equals(receivableBillDTO.getPayStatus())) {
                return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), "该订单状态异常：" + receivableBillDTO.getPayStatus());
            }
            ChargeResponse errorMsg = checkOrderItem(receivableBillDTO);
            if (!errorMsg.isSuccess()){
                return errorMsg;
            }
            arrearsAmount = receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount();//本金欠费
            penaltyArrearsAmount = receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount();//违约金欠费

            int amountCompare = arrearsAmount.compareTo(BigDecimal.ZERO);
            int penaltyCompare = penaltyArrearsAmount.compareTo(BigDecimal.ZERO);
            if (amountCompare <= 0 && penaltyCompare <= 0) {
                return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), "请勿重复缴费");
            }

            orderArrears = orderArrears.add(arrearsAmount).add(penaltyArrearsAmount);//实际总欠费=本金欠费+违约金欠费
            log.info(receivableBillDTO.getBelongYears() + receivableBillDTO.getItemName() + "，本金欠费：" + arrearsAmount +
                    "，违约金欠费：" + penaltyArrearsAmount + "，当前实际总欠费：" + orderArrears);
        }

        BigDecimal arrears = new BigDecimal(arrearsPrice);
        if (arrears.compareTo(orderArrears) != 0) {
            log.error("欠费总金额与缴欠费总金额不符：" + orderArrears + "!=" + arrears);
            return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), "欠费总金额与缴欠费总金额不符");
        }
        return new ChargeResponse();
    }

    public ChargeResponse checkOrderItem(ReceivableBillDTO receivableBillDTO){
        String errorMsg = "";
        //欠费应收金额
        BigDecimal receivableAmount =receivableBillDTO.getReceivableAmount();
        //欠费欠收金额
        BigDecimal arrearsmount = receivableBillDTO.getArrearsAmount();
        //欠费实收金额
        BigDecimal actualAmount = receivableBillDTO.getIncomeAmount();

        //欠费违约金总额
        BigDecimal penaltyAmount = receivableBillDTO.getPenaltyAmount();
        //欠费违约金减免
        BigDecimal penaltyDerate = receivableBillDTO.getPenaltyDerateAmount();
        //欠费违约金应收
        BigDecimal penaltyReceivable = receivableBillDTO.getPenaltyReceivable();
        //欠费违约金实收
        BigDecimal penaltyActualAmount = receivableBillDTO.getPenaltyIncomeAmount();
        //欠费违约金欠收
        BigDecimal penaltyArrearsAmount = receivableBillDTO.getPenaltyArrearsAmount();
        //折扣金额
        BigDecimal discountMoney = receivableBillDTO.getDiscountMoney();


        //欠费违约金欠收<0，校验失败
        if (penaltyArrearsAmount.compareTo(BigDecimal.ZERO) < 0){
            log.info("二次校验失败原因：欠费违约金欠收小于0~~~orderItemId: {}, 违约金：{}",receivableBillDTO.getId(), penaltyArrearsAmount);
            errorMsg = "欠费金额异常：违约金欠费小于0";
            return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), errorMsg);
        }
        //欠费应收 ！= 欠费实收+欠费欠收+折扣金额，校验失败
        if(receivableAmount.compareTo(actualAmount.add(arrearsmount).add(discountMoney)) != 0){
            log.info("二次校验失败原因：欠费应收{} ！= 欠费实收{}+欠费欠收{}+折扣金额{}", receivableAmount, actualAmount, arrearsmount, discountMoney);
            errorMsg = "欠费金额异常";
            return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), errorMsg);
        }
        //违约金总金额!=违约金应收+违约金减免，校验失败
        if(penaltyAmount.compareTo(penaltyReceivable.add(penaltyDerate)) != 0){
            log.info("二次校验失败原因：违约金总金额{}!=违约金应收{}+违约金减免{}",penaltyAmount, penaltyReceivable, penaltyDerate);
            errorMsg = "欠费金额异常";
            return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), errorMsg);
        }
        //违约金应收!=违约金实收+违约金欠收，校验失败
        if(penaltyReceivable.compareTo(penaltyActualAmount.add(penaltyArrearsAmount)) != 0){
            log.info("二次校验失败原因：违约金应收{}!=违约金实收{}+违约金欠收{}",penaltyReceivable, penaltyActualAmount, penaltyArrearsAmount);
            errorMsg = "欠费金额异常";
            return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), errorMsg);
        }
        return new ChargeResponse();
    }


    public ChargeResponse<SearchPayResultResponse> updatePayResult(int code, String orderNum, IncomeBillDTO incomeBill, String outTransactionNo,
                                                                   PayMethodChannel payMethodChannel, String message, BillPayStatusEnum status){
        try {
            Date date = new Date();
            PayRecord payRecord = new PayRecord().setOrderNum(orderNum)
                    .setReferNo(outTransactionNo).setPayStatus(status.getCode()).setPaymentTime(date);
            if (payMethodChannel!=null) {
                payRecord.setPaymentChannel(payMethodChannel.getPaymentMethod().getPaymentMethod());
                payRecord.setPaymentMethod(payMethodChannel.getPaymentMethod().getPaymentCode());
            }
            ChargeResponse recordResponse = payRecordClient.updateByOrderNum(payRecord);
            if (!recordResponse.isSuccess()) {
                log.info("更新交易记录异常" + recordResponse.getMessage());
                return new ChargePayResponse(YueConstants.CODE_FAILED, "更新交易记录异常" + recordResponse.getMessage());
            }
            BillPayResultDTO payResultDTO = new BillPayResultDTO();
            payResultDTO.setOrderNum(orderNum);
            payResultDTO.setChannelOrderNum(outTransactionNo);
            payResultDTO.setPayStatusEnum(status);
            payResultDTO.setId(incomeBill.getId());
            if(payMethodChannel!=null&&!incomeBill.getPaymentMethod().equals(payMethodChannel.getPaymentMethod().getPaymentCode())){
                payResultDTO.setPaymentMethodEnum(payMethodChannel.getPaymentMethod());
                payResultDTO.setPaymentChannel(payMethodChannel.getPaymentChannel().getPaymentChannel());
            }
            ChargeResponse<BillPayReturnDTO> billResponse = billClient.payStatusOperate(payResultDTO);
            if (!billResponse.isSuccess()) {
                return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), "更新income bill失败");
            }
            prePayLockSupport.unlock(LockObjectDTO.builder().mark(orderNum).build());
            return new ChargeResponse<>(SearchPayResultResponse.builder().payId(String.valueOf(incomeBill.getId())).resultCode(code).resultMsg(message).build());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), e.getMessage());
        }

    }

    @Override
    public ChargePageResponse<AssetBalancesVO> getArrearsHouseList(Long communityId, String keyword, Integer status, String subjectType, Integer currentPage, Integer pageSize) throws Exception {
        AssetBalancesVO houseSearchInfos=new AssetBalancesVO();
        houseSearchInfos.setHouseList(Lists.newArrayList());
        ChargePageResponse<AssetBalancesVO> response=new ChargePageResponse<>(houseSearchInfos,pageSize,0,currentPage,0);
        AssetTypeEnum assetTypeEnum = EnumUtil.getEnumByValueWithNull(AssetTypeEnum.values(), subjectType);
        if(org.springframework.util.StringUtils.hasText(subjectType)&&assetTypeEnum==null){
            return response;
        }
        if(switchConfig.isUseNewAssetOverview(communityId)){
            if(!StringUtils.isEmpty(keyword)){
                status=null;
            }
            PagingDTO<AssetOverViewDTO> assetOverViewPage = assetOverviewSupport.pageAssetOverview(communityId, keyword, getHouseTypes(subjectType), null, currentPage, pageSize,status);
            List<AssetOverViewDTO> assetOverViewS = assetOverViewPage.getList();
            response.setTotalRecord(assetOverViewPage.getTotalCount());
            fillTotalPage(response);
            response.setContent(buildAssetBalancesVO(assetOverViewS));
            return response;
        }

        AssetArrearsCountQueryDTO arrearsCountQueryDTO = new AssetArrearsCountQueryDTO();
        arrearsCountQueryDTO.setCommunityId(communityId);
        arrearsCountQueryDTO.setPageNum(currentPage);
        arrearsCountQueryDTO.setPageSize(pageSize);
        arrearsCountQueryDTO.setSearchParam(Strings.isNotEmpty(keyword)?URLDecoder.decode(keyword,"UTF-8"):keyword);
        if(assetTypeEnum!=null){
            if(assetTypeEnum==AssetTypeEnum.HOUSE){
                arrearsCountQueryDTO.setHouseTypes(Lists.newArrayList(HouseTypeEnum.HOUSE.getCode(),HouseTypeEnum.OTHER.getCode(),
                        HouseTypeEnum.VIRTUAL_HOUSE_COMMUNITY.getCode(), HouseTypeEnum.VIRTUAL_HOUSE.getCode(),
                        HouseTypeEnum.SUPPORTING.getCode(),HouseTypeEnum.UNKNOWN.getCode()));
                arrearsCountQueryDTO.setAssetType(com.charge.maindata.enums.AssetTypeEnum.HOUSE.getCode());
            }else if(assetTypeEnum==AssetTypeEnum.SHOP){
                arrearsCountQueryDTO.setAssetType(com.charge.maindata.enums.AssetTypeEnum.HOUSE.getCode());
                arrearsCountQueryDTO.setHouseType(HouseTypeEnum.SHOPS.getCode());
            }else if(assetTypeEnum==AssetTypeEnum.CAR){
                arrearsCountQueryDTO.setAssetType(com.charge.maindata.enums.AssetTypeEnum.PARKING_SPACE.getCode());
            }else {
                return response;
            }
        }
        arrearsCountQueryDTO.setStatusIdList(Arrays.asList(ReceivableBillPayStatusEnum.NOT_PAY.getCode(), ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode(), ReceivableBillPayStatusEnum.COLLECTION.getCode()));
        arrearsCountQueryDTO.setListBillStatus(Lists.newArrayList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),ReceivableBillStatusEnum.BILL_HOLD.getCode()
        ,ReceivableBillStatusEnum.BILL_ACCOUNTING.getCode(),ReceivableBillStatusEnum.BILL_CHECKING.getCode()));
        arrearsCountQueryDTO.setChargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode());
        //1、根据资产类型查询资产应收单金额合计
        ChargeResponse<PagingDTO<AssetArrearsCountDTO>> arrearsResponse = receivableBillClient.assetArrearsAmountCountListByAssetType(arrearsCountQueryDTO);
        PagingDTO<AssetArrearsCountDTO> arrearsPage = FeignUtil.getContent(arrearsResponse, "账单服务");
        if(CollectionUtils.isEmpty(arrearsPage.getList())){
            response.setTotalRecord(arrearsPage.getTotalCount());
            response.setTotalPage((int)Math.ceil(new Double(arrearsPage.getTotalCount())/pageSize));
            return response;
        }
        List<AssetArrearsCountDTO> assetArrearsList = arrearsPage.getList();

        //2、查询资产预收账户总余额
        List<Long> assetIds = assetArrearsList.stream().map(AssetArrearsCountDTO::getAssetId).collect(Collectors.toList());
        Map<Long, AsssetPredepositCountDTO> assePreDepositMap = this.queryAssetPredepositCount(assetIds);

        AssetCondition assetCondition=AssetCondition.builder()
                .communityId(Long.valueOf(communityId))
                .ids(assetIds)
                .queryCustomer(true)
                .excludeMoveOut(true)
                .customerTypes(Lists.newArrayList(CustomerTypeEnum.OWNER.getCode()))
                .build();
        if(assetTypeEnum!=null){
            if(assetTypeEnum==AssetTypeEnum.HOUSE||assetTypeEnum==AssetTypeEnum.SHOP){
                assetCondition.setType(com.charge.maindata.enums.AssetTypeEnum.HOUSE.getCode());
            }else if(assetTypeEnum==AssetTypeEnum.CAR){
                assetCondition.setType(com.charge.maindata.enums.AssetTypeEnum.PARKING_SPACE.getCode());
            }
        }
        ChargeResponse<List<AssetDTO>> assetResp = assetClient.listAsset(assetCondition);
        Map<Long, AssetDTO> assetMap = AppInterfaceUtil.getResponseDataThrowException(assetResp).stream().collect(Collectors.toMap(AssetDTO::getId, a -> a, (a, b) -> b));

        List<AssetBalanceVO> searchInfos = assetArrearsList.stream().map(assetArrears -> {
            AsssetPredepositCountDTO preDeposit = assePreDepositMap.get(assetArrears.getAssetId());
            AssetDTO asset = assetMap.get(assetArrears.getAssetId());
            AssetBalanceVO houseSearch = AssetBalanceVO.builder()
                    .houseUuid(nullStr(assetArrears.getAssetId()))
                    .arrearAmount(assetArrears.getArrearsAmount().add(assetArrears.getPenaltyArrearsAmount()))
                    //账户余额 = 余额 + 待结转
                    .balanceAmount(preDeposit == null ? BigDecimal.ZERO : preDeposit.getPreDeposit().add(preDeposit.getCarryForward()))
                    //押金
                    .depositAmount(preDeposit == null ? BigDecimal.ZERO : preDeposit.getDeposit())
                    .subjectType(assetTypeEnum != null ? assetTypeEnum.getValue() : AssetTypeEnum.HOUSE.getValue())
                    .build();
            if (asset != null) {
                List<CustomerDTO> customers = null;
                houseSearch.setSubjectType(EnumUtil.getEnumByCode(AssetTypeEnum.values(), asset.getType()).getValue());
                HouseDTO houseDTO = asset.getHouseDTO();
                ParkingSpaceDTO parkingSpace = asset.getParkingSpaceDTO();
                if (houseDTO != null) {
                    customers = houseDTO.getListCustomer();
                    houseSearch.setHouseName(nullStr(houseDTO.getHouseNum()));
                    houseSearch.setCommunityName(nullStr(houseDTO.getCommunityName()));
                    houseSearch.setBuildingName(nullStr(houseDTO.getBuildingName()));
                    houseSearch.setUnitName((houseDTO.getUnitName()));
                } else if (parkingSpace != null) {
                    customers = parkingSpace.getListCustomer();
                    houseSearch.setHouseName(nullStr(parkingSpace.getParkingSpaceNum()));
                    houseSearch.setCommunityName(nullStr(parkingSpace.getCommunityName()));
                    houseSearch.setBuildingName(nullStr(parkingSpace.getParkingName()));
                    houseSearch.setUnitName("");
                }else {
                    houseSearch.setHouseName("");
                    houseSearch.setCommunityName("");
                }
                if (CollectionUtils.isNotEmpty(customers)) {
                    String names = customers.stream().map(CustomerDTO::getCustomerName).filter(Objects::nonNull).collect(Collectors.joining(","));
                    String phones = customers.stream().map(a->desensitize(a.getPhone())).filter(Objects::nonNull).collect(Collectors.joining(","));
                    houseSearch.setResidentUuid(nullStr(customers.get(0).getId()));
                    houseSearch.setMobile(nullStr(phones));
                    houseSearch.setGender(customers.size() == 1 ?  customers.get(0).getSex():new Integer(0));
                    houseSearch.setUserName(nullStr(names));
                }else {
                    houseSearch.setResidentUuid("");
                    houseSearch.setMobile("");
                    houseSearch.setGender( 0 );
                    houseSearch.setUserName("");
                }
            }else {
                houseSearch.setResidentUuid("");
                houseSearch.setMobile("");
                houseSearch.setGender( 0 );
                houseSearch.setUserName("");
                houseSearch.setHouseName("");
                houseSearch.setBuildingName("");
                houseSearch.setUnitName("");
                houseSearch.setCommunityName("");
            }
            if (houseSearch.getArrearAmount().compareTo(BigDecimal.ZERO) > 0) {
                houseSearch.setStatus(1);
            } else {
                houseSearch.setStatus(0);
            }
            format(houseSearch);
            return houseSearch;
        }).collect(Collectors.toList());
        response.setTotalRecord(arrearsPage.getTotalCount());
        houseSearchInfos.setHouseList(searchInfos);
        return response;
    }

    private void format(AssetBalanceVO houseSearch){
        if(houseSearch.getGender()==null){
            houseSearch.setGender(1);
        }
        if(StringUtils.isBlank(houseSearch.getUnitName())){
            houseSearch.setUnitName("");
        }
    }



    /**
     * 查询资产预收账户总余额
     * 根据资产id查询预存/待结转/押金的汇总
     * @param assetIds
     * @return
     */
    private Map<Long,AsssetPredepositCountDTO> queryAssetPredepositCount(List<Long> assetIds) throws ChargeBusinessException {
        AssetArrearsCountQueryDTO queryDTO = new AssetArrearsCountQueryDTO();
        queryDTO.setAssetIdList(assetIds);
        ChargeResponse<List<AsssetPredepositCountDTO>> listChargeResponse = predepositAccountClient.assetPredepositCountList(queryDTO);
        List<AsssetPredepositCountDTO> responseData = AppInterfaceUtil.getResponseData(listChargeResponse);
        return responseData.stream().collect(Collectors.toMap(AsssetPredepositCountDTO::getAssetId, Function.identity()));
    }

    @Override
    public ChargeResponse getHouseInfo(String houseId) throws ChargeBusinessException {
        //房间资产id
        long assetId = NumberUtils.toLong(houseId);
        ChargeResponse<AssetDTO> response = houseClientAdapter.getAssetById(assetId);
        if (!response.isSuccess()) {
            return response;
        }
        AssetDTO assetDTO = response.getContent();
        Long communityId = Objects.nonNull(assetDTO.getHouseDTO()) ? assetDTO.getHouseDTO().getCommunityId() :
                assetDTO.getParkingSpaceDTO().getCommunityId();
        //获取房屋欠费,未支付，银行托收中及挂起
        BigDecimal arrearsAmount = getArrearsAmount(assetDTO.getId(), communityId);

        Map<Long, AsssetPredepositCountDTO> assetAccountCountMap = this.queryAssetPredepositCount(Collections.singletonList(assetId));
        AsssetPredepositCountDTO assetCountDTO = assetAccountCountMap.get(assetId);

        //房屋预存余额
        BigDecimal balanceAmount = assetCountDTO == null ? new BigDecimal(0) : assetCountDTO.getPreDeposit();
        //押金
        BigDecimal depositAmount = assetCountDTO == null ? new BigDecimal(0) : assetCountDTO.getDeposit();
        //待结转
        BigDecimal waitingCarryForwardMoney = assetCountDTO == null ? new BigDecimal(0) : assetCountDTO.getCarryForward();
        SubjectSearch subjectSearch = AssetAdapter.toSubjectSearch(assetDTO);
        return new ChargeResponse<>(HouseInfo.builder().communityName(subjectSearch.getCommunityName())
                .buildingName(subjectSearch.getBuildingName())
                .unitName(subjectSearch.getUnitName())
                .houseName(subjectSearch.getSubjectName())
                .buildArea(subjectSearch.getBuildArea() == null ? "-" : subjectSearch.getBuildArea())
                .insideArea(subjectSearch.getInsideArea() == null ? "-" : subjectSearch.getInsideArea())
                .chargeArea(subjectSearch.getChargeArea() == null ? "-" : subjectSearch.getChargeArea())
                .residentUuid(subjectSearch.getHouseownerId())
                .residentName(subjectSearch.getHouseownerName())
                .mobile(subjectSearch.getHouseownerMobile())
                .complaintNum(3)
                .arrearAmount(arrearsAmount)
                .balanceAmount(balanceAmount)
                .depositAmount(depositAmount)
                .waitingCarryForwardTotal(waitingCarryForwardMoney)
                .memo(nullable(subjectSearch.getMemo()))
                .build());
    }

    private String nullable(String str){
        return str == null ? "" :str;
    }

    @Override
    public ChargeResponse getHousePayItemList(String houseId, Integer currentPage, Integer pageSize) {
        ChargeResponse<AssetDTO> response = houseClientAdapter.getAssetById(NumberUtils.toLong(houseId));
        if (!response.isSuccess()) {
            return response;
        }
        AssetDTO assetDTO = response.getContent();
        SubjectSearch subjectSearch = AssetAdapter.toSubjectSearch(assetDTO);
        ChargeResponse<PagingDTO<ReceivableBillDTO>> receivaleResponse = receivableBillClient.queryByPage(ReceivableConditionDTO.builder()
                .payStatuses(new ArrayList<>(Arrays.asList(ReceivableBillPayStatusEnum.NOT_PAY.getCode(), ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode(), ReceivableBillPayStatusEnum.COLLECTION.getCode())))
                .billStatus(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode())
                .chargeObjectList(Collections.singletonList(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode()))
                .assetId(assetDTO.getId())
                .communityId(Long.parseLong(subjectSearch.getCommunityId()))
                .pageNum(currentPage)
                .pageSize(pageSize)
                .build());
        if (!receivaleResponse.isSuccess()) {
            return receivaleResponse;
        }
        PagingDTO<ReceivableBillDTO> receivaleResponseContent = receivaleResponse.getContent();
        List<ReceivableBillDTO> receivableBillDTOList = receivaleResponseContent.getList();
        log.info("查看房间内缴费的欠费:{},", JSON.toJSONString(receivableBillDTOList));
        //过滤欠费都为0的数据
        receivableBillDTOList = receivableBillDTOList.stream().filter(o -> !(BigDecimal.ZERO.compareTo(o.getArrearsAmount()) == 0 && BigDecimal.ZERO.compareTo(o.getPenaltyArrearsAmount()) == 0)).collect(Collectors.toList());
        receivableBillDTOList.sort((ReceivableBillDTO o1, ReceivableBillDTO o2) -> o2.getBelongYears().compareTo(o1.getBelongYears()));
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("communityName", subjectSearch.getCommunityName());
        resultMap.put("buildingName", subjectSearch.getBuildingName());
        resultMap.put("unitName", subjectSearch.getUnitName());
        resultMap.put("houseName", subjectSearch.getSubjectName());
        resultMap.put("bankStatus", 0);
        // 组装应收单
        Map<String, Map<String, Object>> itemMap = new HashMap<>();
        Set<Long> itemUuidSet = new HashSet<>();
        for (ReceivableBillDTO receivableBillDTO : receivableBillDTOList) {
            // 是否还有银行托收中的费用
            if (ReceivableBillPayStatusEnum.COLLECTION.getCode().equals(receivableBillDTO.getPayStatus())) {
                resultMap.put("bankStatus", 1);
            }
            String itemName = receivableBillDTO.getItemName();
            Long itemId = receivableBillDTO.getItemId();
            itemUuidSet.add(itemId);
            Map<String, Object> objectMap = itemMap.computeIfAbsent(itemName, x -> {
                HashMap<String, Object> map = new HashMap<>();
                map.put("itemName", itemName);
                map.put("itemUuid", itemId);
                map.put("list", new ArrayList());
                return map;
            });
            List list = (List) objectMap.get("list");
            Map<String, Object> map = new HashMap<String, Object>();
            BigDecimal arrears = receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getArrearsAmount();
            BigDecimal penalty = receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : receivableBillDTO.getPenaltyArrearsAmount();
            map.put("orderId", String.valueOf(receivableBillDTO.getId()));
            map.put("statusId", String.valueOf(receivableBillDTO.getBillStatus()));
            map.put("itemId", String.valueOf(receivableBillDTO.getId()));
            map.put("itemUuid", itemId);
            map.put("itemName", itemName);
            map.put("price", arrears.add(penalty));
            map.put("belongYear", receivableBillDTO.getBelongYears());
            map.put("penalty", penalty);
            map.put("createTs", DateUtils.format(receivableBillDTO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            list.add(map);
        }
        // 获取code
        Map<Long, String> idToCodeMap = new HashMap<>();
        ChargeItemQueryConditionDTO conditionDTO = new ChargeItemQueryConditionDTO();
        conditionDTO.setItemIds(new ArrayList<>(itemUuidSet));
        conditionDTO.setPageNum(1);
        conditionDTO.setPageSize(9999);
        ChargeResponse<PagingDTO<ChargeItemDTO>> pagingDTOChargeResponse = chargeItemClient.queryChargeItemList(conditionDTO);
        if (!pagingDTOChargeResponse.isSuccess()) {
            return pagingDTOChargeResponse;
        }
        PagingDTO<ChargeItemDTO> itemDTOPagingDTO = pagingDTOChargeResponse.getContent();
        List<ChargeItemDTO> chargeItemDTOList = itemDTOPagingDTO.getList();
        chargeItemDTOList.forEach( chargeItemDTO -> idToCodeMap.put(chargeItemDTO.getId(), chargeItemDTO.getItemCode()));
        // 转换map为list
        List<Map<String, Object>> itemList = new ArrayList();
        itemMap.forEach( (itemName, map) -> {
            String itemUuid = String.valueOf(map.get("itemUuid"));
            map.put("itemCode", idToCodeMap.get(Long.valueOf(itemUuid)));
            itemList.add(map);
        });
        resultMap.put("itemList", itemList);
        return new ChargePageResponse<>(resultMap, pageSize, receivaleResponseContent.getTotalCount() / pageSize, currentPage,
                receivaleResponseContent.getTotalCount());
    }

    @Override
    public HouseArrearsSV1 getCustomerHouseList(String communityId, String customerId) throws ChargeBusinessException {
        if (StringUtils.isEmpty(communityId)) {
            throw  new ChargeBusinessException(ErrorInfoEnum.E1002);
        }
        if (StringUtils.isEmpty(customerId)) {
            throw  new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"缺失客户信息，请维护客户信息后再查询");
        }
        List<HouseArrearsV1> houseList=Lists.newArrayList();
        HouseArrearsSV1 houseArrearsSV1=new HouseArrearsSV1();
        houseArrearsSV1.setHouseList(houseList);

        ChargeResponse<List<AssetDTO>> assetResp = assetClient.listAssetByCustomerId(Long.valueOf(customerId),200);
        if(assetResp.getMessage()!=null&&assetResp.getMessage().startsWith("用户资产数超过")){
            throw  new ChargeBusinessException(ErrorInfoEnum.E1011.getCode(),"用户资产数超过200,请到朝昔app进行缴费");
        }
        List<AssetDTO> assetDTOS = AppInterfaceUtil.getResponseDataThrowException(assetResp);
        if(CollectionUtils.isEmpty(assetDTOS)){
            return houseArrearsSV1;
        }
        if(assetDTOS.size()>200){
            throw  new ChargeBusinessException(ErrorInfoEnum.E1011.getCode(),"用户资产数超过200,请到朝昔app进行缴费");
        }
        List<Long> assetIdList = assetDTOS.stream().map(AssetDTO::getId).collect(Collectors.toList());

        ChargeResponse<List<ReceivableBillDTO>> receivableResponse = receivableBillClient.queryList(ReceivableConditionDTO.builder()
                .assetIdList(assetIdList)
                .payStatuses(Lists.newArrayList(ReceivableBillPayStatusEnum.NOT_PAY.getCode(),ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()
                        ,ReceivableBillPayStatusEnum.COLLECTION.getCode(), ReceivableBillPayStatusEnum.PAY_WAIT.getCode()))
                .billStatus(BillStatusEnum.BILL_STATUS_PAY_NORMAL.getCode())
                .communityId(Long.parseLong(communityId))
                .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode())
                .build());

        List<ReceivableBillDTO> receivableBillDTOList = AppInterfaceUtil.getResponseDataThrowException(receivableResponse);
        Map<Long, List<ReceivableBillDTO>> assetReceivableMap = receivableBillDTOList.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId));
        assetDTOS.forEach(assetDTO -> {
            com.charge.api.web.dto.joylife.AssetAdapter assetAdapter = AssetSupport.buildAsserAdapter(assetDTO);
            // 若房间资产内迁出，无需构建返回
            List<CustomerDTO> customerDTOS = AssetSupport.filterCustomer(assetAdapter);
            if (customerDTOS.stream().noneMatch(customerDTO -> {
                return Objects.equals(String.valueOf(customerDTO.getId()), customerId);
            })) {
                return;
            }
            HouseArrearsV1 houseArrearsV1 = HouseArrearsV1.builder()
                    .buildingName(nullStr(assetAdapter.getBuildingName()))
                    .unitName(nullStr(assetAdapter.getUnitName()))
                    .owner(nullStr(customerDTOS.stream().map(a->desensitize(a.getCustomerName())).collect(Collectors.joining(","))))
                    .mobile(nullStr(customerDTOS.stream().map(a->desensitize(a.getPhone())).collect(Collectors.joining(","))))
                    .houseCode(nullStr(assetAdapter.getSubCode()))
                    .houseId(nullStr(assetAdapter.getId()))
                    .arrearsAmount(Optional.ofNullable(assetReceivableMap.get(assetAdapter.getId())).orElse(new ArrayList<>()).stream()
                            .map(ReceivableBillDTO::getArrearsAmount).reduce(BigDecimal.ZERO,BigDecimal::add))
                    .penaltyAmount(Optional.ofNullable(assetReceivableMap.get(assetAdapter.getId())).orElse(new ArrayList<>()).stream()
                            .map(ReceivableBillDTO::getPenaltyArrearsAmount).reduce(BigDecimal.ZERO,BigDecimal::add))
                    .subjectTYpe((com.charge.maindata.enums.AssetTypeEnum.HOUSE.getCode().equals(assetAdapter.getType())?AssetTypeEnum.HOUSE:AssetTypeEnum.CAR).getValue())
                    .build();
            if("/".equals(houseArrearsV1.getUnitName())){
                houseArrearsV1.setUnitName("");
            }
            houseList.add(houseArrearsV1);
        });
        houseList.sort(Comparator.comparing(HouseArrearsV1::getHouseCode));
        return houseArrearsSV1;
    }

    @Override
    public BillVO1 getPayOrder(Long transactionId) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityId(transactionId);
        ShardingUtil.addCommunityId2ThreadContext(communityId);
        ChargeResponse<List<AssetTransactionDTO>> transactionsResp = assetTransactionClient.selectTransactionWriteOffReceivableBillDetail(
                AssetTransactionConditionDTO.builder().idList(Lists.newArrayList(transactionId)).communityId(String.valueOf(communityId)).build());
        List<AssetTransactionDTO> transactions = AppInterfaceUtil.getResponseDataThrowException(transactionsResp);
        if (CollectionUtils.isEmpty(transactions)) {
            return null;
        }
        AssetTransactionDTO transaction = transactions.get(0);
        IncomeBillDTO incomeBillDTO = transaction.getIncomeBillDTO();
        ChargeResponse<List<AssetBillInfoDTO>> assetBillsResp = assetBillInfoClient.list(Lists.newArrayList(transaction.getId()), transaction.getCommunityId());
        List<AssetBillInfoDTO> assetBills = AppInterfaceUtil.getResponseDataThrowException(assetBillsResp);
        Assert.isTrue(assetBills.size() == 1, "查询到assetBills与流水数量不一致");
        AssetBillInfoDTO assetBill = assetBills.get(0);

        String fullAssetName = transaction.getBuildingName() + transaction.getUnitName() + transaction.getAssetName();
        PaymentMethodEnum paymentMethod = getOrDefaultPaymentMethodEnum(transaction.getPaymentMethod());
        List<BillVO1.BillItemVO> totalBillItems = Lists.newArrayList();
        AssetDTO asset = assetSupport.getAsset(transaction.getAssetId());
        BillVO1 bill = BillVO1.builder().cancelInfo(buildCancelVO(fullAssetName))
                .paymentMethod(paymentMethod.getPaymentMethod())
                .bankTransactionNo(nullable(transaction.getBankTransactionNo()))
                .payTime(DateUtils.format(transaction.getPaymentTime(), FORMAT_0))
                .orderId(transaction.getAssetOrderNum())
                .memo("")
                .payMember(nullable(assetBill.getPayMember()))
                .tno(nullable(transaction.getBankTransactionNo()))
                .collectorName(nullable(assetBill.getOperatorName()))
                .collectorId(nullable(assetBill.getOperatorId()))
                .communityName(transaction.getCommunityName())
                .buildingName(nullStr(getAssetBuildingName(asset)))
                .unitName(transaction.getUnitName())
                .houseName(nullStr(getAssetNum(asset)))
                .totalPrice(transaction.getMoney().setScale(2, RoundingMode.HALF_UP))
                .paymentName(paymentMethod.getPaymentMethod())
                .payStatus(String.valueOf(transaction.getPayStatus()))
                .orderName(transaction.getGoodsName())
                .arrivalTime(transaction.getPaymentTime() == null ? "" : DateUtils.format(transaction.getPaymentTime(), FORMAT_1))
                .paymentSource(nullable(transaction.getPaymentTerminal()))
                .itemList(totalBillItems)
                .build();
        if (incomeBillDTO != null) {
            bill.setMemo(incomeBillDTO.getMemo());
        }
        fillItems(transaction,totalBillItems);
        return bill;
    }

    private void fillItems(AssetTransactionDTO transaction,List<BillVO1.BillItemVO> totalBillItems){
        if (CollectionUtils.isNotEmpty(transaction.getWriteOffBillDTOList())) {
            List<BillVO1.BillItemVO> billItems = transaction.getWriteOffBillDTOList().stream().map(billDto -> BillVO1.BillItemVO.builder()
                    .itemUuid(billDto.getItemId().intValue()).itemName(billDto.getItemName()).itemCode("").chargeTypeId(BillChargeTypeIdEnum.ROUTINE.getType())
                    .createTime(billDto.getBelongYears())
                    .businessType(BusinessTypeEnum.NORMAL_PAY.getCode()).itemCode("").price(billDto.getActualAmount()).prestoreName(billDto.getItemName()).incomeDetailUuid("").incomeDetailName("")
                    .build()).collect(Collectors.toList());
            sort(billItems);
            totalBillItems.addAll(billItems);
        }
        if (CollectionUtils.isNotEmpty(transaction.getPredepositBillDTOList())) {
            List<BillVO1.BillItemVO> billItems = transaction.getPredepositBillDTOList().stream().map(billDto -> BillVO1.BillItemVO.builder()
                    .itemUuid(billDto.getPredepositItemId().intValue()).itemName(billDto.getPredepositItemName()).itemCode("").chargeTypeId(BillChargeTypeIdEnum.ROUTINE.getType())
                    .createTime(DateUtils.format(billDto.getCreateTime(), DateUtils.FORMAT_14))
                    .businessType(BusinessTypeEnum.PREDEPOSIT_PAY.getCode()).itemCode("").price(billDto.getPredepositMoney()).prestoreName(billDto.getPredepositItemName()).incomeDetailUuid("").incomeDetailName("")
                    .build()).collect(Collectors.toList());
            sort(billItems);
            totalBillItems.addAll(billItems);
        }
        if (CollectionUtils.isNotEmpty(transaction.getOrderBillDetailList())) {
            List<BillVO1.BillItemVO> billItems = transaction.getOrderBillDetailList().stream().map(billDto -> BillVO1.BillItemVO.builder()
                    .itemUuid(billDto.getItemId().intValue()).itemName(billDto.getItemName()).itemCode("").chargeTypeId(BillChargeTypeIdEnum.TEMPORARY.getType())
                    .createTime(DateUtils.format(billDto.getCreateTime(), DateUtils.FORMAT_14))
                    .businessType(BusinessTypeEnum.TEMP_PAY.getCode()).itemCode("").price(billDto.getActualAmount()).prestoreName(billDto.getItemName()).incomeDetailUuid("").incomeDetailName("")
                    .build()).collect(Collectors.toList());
            sort(billItems);
            totalBillItems.addAll(billItems);
        }
    }

    private static void sort(List<BillVO1.BillItemVO> billItems){
        billItems.sort(Comparator.comparing(BillVO1.BillItemVO::getItemName).thenComparing((a,b)->b.getCreateTime().compareTo(a.getCreateTime())));
    }

    private PaymentMethodEnum getOrDefaultPaymentMethodEnum(Integer paymentMethodCode){
        PaymentMethodEnum paymentMethod = PaymentMethodEnum.fromCode(paymentMethodCode);
        if(paymentMethod==null){
            log.error("getOrDefault PaymentMethodEnum error,paymentMethodCode  {}",paymentMethodCode);
            paymentMethod=PaymentMethodEnum.CARD;
        }
        return paymentMethod;
    }

    private BillVO1.CancelVO buildCancelVO(String room) {
        return BillVO1.CancelVO.builder().cancelMethod("").adjustRoom(room).operatorId("").operatorName("")
                .cancelTime(DateUtils.getDateStr()).cancelMemo("").originalBillId("").negativeBillId("")
                .billStatus("0").billType("0").bankCard("").createBillType("0").creatBillTypeName("普通账单").build();
    }

    @Override
    public ChargeResponse getCommunityDisable(String communityUuid) {
        try {
            String message = "该小区缴费功能正常";
            Map<String, Integer> map = new HashMap<>(16);
            map.put("disableStatus", 0);

            // todo 查询redis

            ChargeResponse response = new ChargeResponse(map);
            response.setMessage(message);
            return response;
        } catch (Exception e) {
            return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(), "调用小区缴费禁用状态接口失败！");
        }
    }

    private void fillArrearsSubjectTypeCount(AssetTypeArrearsAmount arrearsAmount, Map<Integer, Long> houseTypeArresarsMap) {
        Long carArrearsAmount = houseTypeArresarsMap.get(HouseTypeEnum.PARKING_SPACE.getCode());
        arrearsAmount.setCar(carArrearsAmount == null ? 0 : carArrearsAmount.intValue());
        Long shopArrearsAmount = houseTypeArresarsMap.get(HouseTypeEnum.SHOPS.getCode());
        arrearsAmount.setShops(shopArrearsAmount == null ? 0 : shopArrearsAmount.intValue());
        int houseArrearsAmount = houseTypeArresarsMap.entrySet().stream().filter(entry -> Objects.equals(entry.getKey(), HouseTypeEnum.HOUSE.getCode())
                        || Objects.equals(entry.getKey(), HouseTypeEnum.VIRTUAL_HOUSE_COMMUNITY.getCode()) || Objects.equals(entry.getKey(), HouseTypeEnum.VIRTUAL_HOUSE.getCode()))
                .map(Map.Entry::getValue).reduce(0L, Long::sum).intValue();
        arrearsAmount.setHouse(houseArrearsAmount);
    }

    @Override
    public AssetTypeArrearsAmount getArrearsSubjectTypeCount(Long communityId) throws ChargeBusinessException {
        AssetTypeArrearsAmount arrearsAmount = new AssetTypeArrearsAmount();
        if(switchConfig.isUseNewAssetOverview(communityId)){
            Map<Integer, Long> houseTypeArresarsMap = assetOverviewSupport.statisticArrearsByHouseType(communityId);
            fillArrearsSubjectTypeCount(arrearsAmount, houseTypeArresarsMap);
            return arrearsAmount;
        }
        ChargeResponse<List<Long>> assetIdsResp = receivableBillClient.listArrearsAssetIds(ReceivableConditionDTO.builder().communityId(communityId)
                .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode()).build());
        List<Long> assetIds = AppInterfaceUtil.getResponseDataThrowException(assetIdsResp);
        if (org.springframework.util.CollectionUtils.isEmpty(assetIds)) {
            return arrearsAmount;
        }
        ChargeResponse<List<AssetDTO>> assetResp = assetClient.listAssetLessInfo(AssetCondition.builder().communityId(communityId).ids(assetIds).build());
        List<AssetDTO> assets = AppInterfaceUtil.getResponseDataThrowException(assetResp);
        int car = 0;
        int shop = 0;
        int house = 0;
        for (AssetDTO assetDTO : assets) {
            if (com.charge.maindata.enums.AssetTypeEnum.PARKING_SPACE.getCode().equals(assetDTO.getType())) {
                car++;
            } else if (com.charge.maindata.enums.AssetTypeEnum.HOUSE.getCode().equals(assetDTO.getType())) {
                HouseDTO houseDTO = assetDTO.getHouseDTO();
                if (houseDTO != null) {
                    if (HouseTypeEnum.HOUSE.getCode().equals(houseDTO.getHouseType())) {
                        house++;
                    } else if (HouseTypeEnum.SHOPS.getCode().equals(houseDTO.getHouseType())) {
                        shop++;
                    }
                }
            }
        }
        arrearsAmount.setShops(shop);
        arrearsAmount.setHouse(house);
        arrearsAmount.setCar(car);
        return arrearsAmount;
    }

    @Override
    public ChargeResponse getLostPayNum(String mercid, String deviceInfo, String communityId) {
        if (StringUtils.isBlank(mercid) || StringUtils.isBlank(deviceInfo)){
            return new ChargeResponse(0);
        }

        return new ChargeResponse(0);
    }

    @Override
    public ChargeResponse queryHouseAllResident(String communityUuid, String houseUuid) {

        ChargeResponse<List<CustomerDTO>> response = assetClient.listCustomerByAssetId(Long.valueOf(houseUuid));
        if (!response.isSuccess()) {
            return new ChargeResponse(ChargeStatusEnum.FAIL.getCode(),  "查询基础服务失败" + response.getMessage());
        }
        List<CustomerDTO> customerDTOList = response.getContent();
        List<HouseResidentInfo> houseResidentInfoList = new ArrayList<>();
        for (CustomerDTO customerDTO : customerDTOList) {
            HouseResidentInfo houseResidentInfo = HouseResidentInfo.builder()
                    .sex(String.valueOf(customerDTO.getSex()))
                    .residentUuid(String.valueOf(customerDTO.getId()))
                    .residentName(customerDTO.getCustomerName())
                    .telephone(desensitize(customerDTO.getPhone()))
                    .accountName(customerDTO.getCustomerName())
                    .identity(String.valueOf(customerDTO.getCustomerType()))
                    .build();
            houseResidentInfoList.add(houseResidentInfo);
        }
        return new ChargeResponse(houseResidentInfoList);
    }

    @Override
    public ChargeResponse getTempChargeItemByCommunity(String communityId, Integer currentPage, Integer pageSize) {
        CommunityChargeItemQueryConditionDTO conditionDTO = new CommunityChargeItemQueryConditionDTO();
        conditionDTO.setCommunityId(Long.valueOf(communityId));
        conditionDTO.setBusinessTypes(Collections.singletonList(com.charge.config.enums.BusinessTypeEnum.TEMPORARY.getCode()));
        conditionDTO.setPageNum(currentPage);
        conditionDTO.setPageSize(pageSize);
        ChargeResponse<PagingDTO<CommunityChargeItemDTO>> response = communityChargeItemClient.queryCommunityChargeItemPage(conditionDTO);
        PagingDTO<CommunityChargeItemDTO> chargeItemDTOPagingDTO = FeignUtil.getContent(response, "临时收费项");
        List<CommunityChargeItemDTO> chargeItemDTOS = chargeItemDTOPagingDTO.getList();
        List<SystemChargeItemVO> systemChargeItemVOS = new ArrayList<>();
        for (CommunityChargeItemDTO chargeItemDTO : chargeItemDTOS) {
            SystemChargeItemVO systemChargeItemVO = SystemChargeItemVO.builder()
                    .commonConfigUuid(String.valueOf(chargeItemDTO.getItemId()))
                    .communityConfigUuid(String.valueOf(chargeItemDTO.getItemId()))
                    // todo 查找对应的费项税率
                    .communityTaxRate(BigDecimal.valueOf(6))
                    .commonTaxRate(BigDecimal.valueOf(6))
                    .createTs(chargeItemDTO.getCreateTime())
                    .dr("0").effectiveTime(chargeItemDTO.getCreateTime())
                    .id(String.valueOf(chargeItemDTO.getId()))
                    .isDiscount("1").itemCode(chargeItemDTO.getItemCode()).itemName(chargeItemDTO.getItemName())
                    .itemUuid(String.valueOf(chargeItemDTO.getId()))
                    .status(String.valueOf(chargeItemDTO.getStatus())).taxDec(2).taxHeadName("")
                    .taxPoint(BigDecimal.valueOf(17)).typeName(chargeItemDTO.getBusinessType()).typeUuid(chargeItemDTO.getBusinessType())
                    .updateTs(chargeItemDTO.getModifyTime())
                    .build();
            systemChargeItemVOS.add(systemChargeItemVO);
        }
        return new ChargeResponse(systemChargeItemVOS);
    }

    @Override
    public ChargeResponse updateReceiptInfo(String id, String printer) {
        ReceiptInfo receiptInfo = ReceiptInfo.builder().build();
        receiptInfo.setId(id);
        receiptInfo.setPrinter(printer);
        // todo 更新printer信息
        return new ChargeResponse(ChargeStatusEnum.SUCCESS.getCode(), "收据修改成功");
    }

    @Override
    public ChargeResponse getPersonData(String userName) {
        PersonData personData = new PersonData();
        try {
            AuthUserInfoRequestDTO authUserInfoRequestDTO = new AuthUserInfoRequestDTO();
            authUserInfoRequestDTO.setLdapName(userName);
            ChargeResponse<AuthUserInfoResponseDTO> chargeResponse = authUserClient.getUserInfo(authUserInfoRequestDTO);
            AuthUserInfoResponseDTO userInfoRes = AppInterfaceUtil.getDataThrowException(chargeResponse);
            personData.setOaAccount(userInfoRes.getLdapName());
            personData.setMobile(desensitize(userInfoRes.getPhone()));
            personData.setName(userInfoRes.getEmpName());
            personData.setCommunityName(userInfoRes.getOrgName());
            personData.setAvatarUrl("https://chargeiposbackend.crlandpm.com.cn/default.png");
        } catch (Exception e){
            log.error("{}|查询员工详情失败，失败原因：{}", LogCategoryEnum.BUSSINESS, e.getMessage());
            return new ChargeResponse(-1, "获取员工账号信息失败");
        }
        return new ChargeResponse<>(personData);
    }

    @Override
    public ChargeResponse listAuthByCommunityUuid(String communityId, String token) {
        List<AuthInfoResponse> authInfoResponseList = new ArrayList<>();
        try {
            //pos2.0校验功能权限
            ChargeResponse<UserSessionDTO> loginCheckResponse = authAccountClient.loginCheck(token);
            log.info("登陆鉴权:{}", loginCheckResponse);
            UserSessionDTO userSessionDTO = AppInterfaceUtil.getResponseData(loginCheckResponse);
            Long userId = userSessionDTO.getUserId();
            ChargeResponse<List<String>> authPermissionReponse = authPermissionClient.listResourceCodeByUserId(userId);
            List<String> authPermssionList = AppInterfaceUtil.getDataThrowException(authPermissionReponse);
            if(CollectionUtil.isEmpty(authPermssionList)){
                log.error("{}|员工功能权限返回为空，登录失败", LogCategoryEnum.BUSSINESS);
                return new ChargeResponse(-1, "该用户无权限登录");
            } else {
                for(String authPerssion : authPermssionList){
                    if("R112001001".equals(authPerssion)){
                        AuthInfoResponse authInfo1 = new AuthInfoResponse();
                        authInfo1.setUuid("c2a82f8e6a874561b14aa77c73395899");
                        authInfo1.setParentUuid("0");
                        authInfo1.setAuthorityKey("SFAPP400000");
                        authInfo1.setType(4);
                        authInfo1.setName("登录");
                        authInfoResponseList.add(authInfo1);
                    } else if("R112002001".equals(authPerssion)){
                        AuthInfoResponse authInfo2 = new AuthInfoResponse();
                        authInfo2.setUuid("ebaa28e37c0b48ae9f6eac060d781c69");
                        authInfo2.setParentUuid("0");
                        authInfo2.setAuthorityKey("B586E9E944E94883B94B76A3E8556542");
                        authInfo2.setType(2);
                        authInfo2.setName("临停配置");
                        authInfoResponseList.add(authInfo2);
                    }
                }
            }

        } catch (Exception e){
            log.error("{}|查询权限信息失败，失败原因：{}", LogCategoryEnum.BUSSINESS, e.getMessage());
            return new ChargeResponse(-1, "获取权限信息失败");
        }
        if(authInfoResponseList.size()==0){
            log.warn("{}|查询该用户功能权限为空", LogCategoryEnum.BUSSINESS);
            return new ChargeResponse(-1, "该用户无权限登录");
        }
        return new ChargeResponse<>(authInfoResponseList);
    }

    @Override
    public ChargeResponse<AccountInfo> login(String username, String password, String url, String systemType, String timeStamp) throws Exception {
        if(StringUtils.isBlank(username) || StringUtils.isBlank(password)){
            throw new ChargeBusinessException(ACCOUNT_OR_PASSWORD_MISS);
        }
        AccountInfo accountInfo = new AccountInfo();
        //开始请求登录接口
        AccountLoginRequestDTO accountLoginRequestDTO = AccountLoginRequestDTO.builder()
                .username(username).password(password).timestamp(timeStamp)
                .source(LoginSourceEnum.POS.getCode()).build();
        ChargeResponse<AccountLoginResponseDTO> loginResponse = authAccountClient.login(accountLoginRequestDTO);
        AccountLoginResponseDTO accountLoginResponseDTO = AppInterfaceUtil.getResponseDataThrowException(loginResponse);
        //根据项目ID获取项目名称
        Long communityId = accountLoginResponseDTO.getCommunityId();
        String communityName = "";
        if(communityId != null) {
            ChargeResponse<CommunityDTO> communityDTOChargeResponse = communityClient.oneByCondition(CommunityCondition.builder().id(communityId).build());
            CommunityDTO communityDTO = AppInterfaceUtil.getDataThrowException(communityDTOChargeResponse);
            communityName = communityDTO.getName();
        }

        //新增权限校验
        String authInfoListStr = "";//pos1.0所有权限节点标识
        Long userId = accountLoginResponseDTO.getUserId();
        ChargeResponse<List<String>> authPermissionReponse = authPermissionClient.listResourceCodeByUserId(userId);
        List<String> authPermssionList = AppInterfaceUtil.getDataThrowException(authPermissionReponse);
        if(authPermssionList == null || authPermssionList.isEmpty()){
            log.error("{}|员工功能权限返回为空，登录失败", LogCategoryEnum.BUSSINESS);
            return new ChargeResponse(-1, "该用户无权限登录");
        } else {
            for(String authPerssion : authPermssionList){
                if("R112001001".equals(authPerssion)){
                    authInfoListStr += "SFAPP400000" + ",";
                } else if("R112002001".equals(authPerssion)){
                    authInfoListStr += "B586E9E944E94883B94B76A3E8556542";
                }
            }
            if(StringUtils.isNotBlank(authInfoListStr)){
                authInfoListStr = authInfoListStr.substring(0, authInfoListStr.length()-1);
            }
        }

        //获取pos2.0用户管辖项目列表
        ChargeResponse<List<AuthCommunitySimpleResponseDTO>> communityListResponse = authUserClient.listCommunity(accountLoginResponseDTO.getUserId());
        List<AuthCommunitySimpleResponseDTO> authCommunityList = AppInterfaceUtil.getDataThrowException(communityListResponse);

        //登录时获取项目虚拟资产
        List<Long> communityIdList = authCommunityList.stream().map(AuthCommunitySimpleResponseDTO::getCommunityId).collect(Collectors.toList());
        List<AssetDTO> assetDTOList = AppInterfaceUtil.getResponseDataThrowException(assetClient.getVirtualHouseByCommunityIds(AssetCondition.builder().communityIds(communityIdList).build()));
        if(CollectionUtils.isEmpty(assetDTOList)){
            log.error("{}|项目虚拟资产返回为空，登录失败", LogCategoryEnum.BUSSINESS);
            return new ChargeResponse(-1, "查询项目虚拟资产异常");
        }
        Map<Long, String> assetCommunityMap = assetDTOList.stream().collect(Collectors.toMap(e -> e.getHouseDTO().getCommunityId(), e -> e.getId().toString(),(a,b)->b));

        Long lastOperateCommunityId=getLastOperateCommunityId(accountLoginResponseDTO);
        List<CommunityDtoV1> communities = authCommunityList.stream().sorted((a, b) -> {
                    if (a.getCommunityId().equals(lastOperateCommunityId)) {
                        return -1;
                    } else if (b.getCommunityId().equals(lastOperateCommunityId)) {
                        return 1;
                    } else {
                        return a.getCommunityId().compareTo(b.getCommunityId());
                    }
                }).map(dto -> new CommunityDtoV1(String.valueOf(dto.getCommunityId()), dto.getCommunityName(), assetCommunityMap.get(dto.getCommunityId())))
                .collect(Collectors.toList());

        accountInfo.setAccount(accountLoginResponseDTO.getUsername());
        accountInfo.setAccountUuid(String.valueOf(accountLoginResponseDTO.getUserId()));
        accountInfo.setRealName(accountLoginResponseDTO.getEmpName());
        accountInfo.setCommunityUuid(String.valueOf(accountLoginResponseDTO.getCommunityId()));
        accountInfo.setCommunityName(communityName);
        accountInfo.setUser_token(accountLoginResponseDTO.getSessionId());
        accountInfo.setCropId("3c384ad60a4911e79f0570106fb01330");
        accountInfo.setResultList(communities);
        accountInfo.setSystemType(systemType);
        accountInfo.setAuthInfoList(authInfoListStr);
        return new ChargeResponse<>(accountInfo);
    }

    private Long getLastOperateCommunityId(AccountLoginResponseDTO dto){
        String community = jedisManager.hget(LOGIN_POS+dto.getUsername(), COMMUNITY_ID);
        if(org.springframework.util.StringUtils.hasText(community)){
            return Long.parseLong(community);
        }else {
            return dto.getCommunityId();
        }
    }

    private BigDecimal getArrearsAmount(long assetId, Long communityId) {
        BigDecimal totalPrice = new BigDecimal(0);
        ChargeResponse<List<ReceivableBillDTO>> receivaleResponse =
                receivableBillClient.queryList(ReceivableConditionDTO.builder()
                .payStatuses(new ArrayList<>(Arrays.asList(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()
                        , ReceivableBillPayStatusEnum.NOT_PAY.getCode()
                        , ReceivableBillPayStatusEnum.COLLECTION.getCode())))
                .billStatuses(Lists.newArrayList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),
                        ReceivableBillStatusEnum.BILL_HOLD.getCode(),
                        ReceivableBillStatusEnum.BILL_ACCOUNTING.getCode(),
                        ReceivableBillStatusEnum.BILL_CHECKING.getCode()))
                .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode())
                .assetId(assetId)
                .communityId(communityId)
                .build());
        if (!receivaleResponse.isSuccess()) {
            return totalPrice;
        }
        List<ReceivableBillDTO> receivableBillDTOList = receivaleResponse.getContent();
        for (ReceivableBillDTO receivableBillDTO : receivableBillDTOList) {
            if (ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(receivableBillDTO.getChargeObject())) {
                BigDecimal arrearsAmount = receivableBillDTO.getArrearsAmount();
                BigDecimal penaltyArrearsAmount = receivableBillDTO.getPenaltyArrearsAmount();
                totalPrice = totalPrice.add(arrearsAmount).add(penaltyArrearsAmount);
            }
        }
        return totalPrice;
    }

}
