package com.charge.api.web.dto.invoice;

import lombok.Data;

import java.io.Serializable;

/**
 * @<PERSON> <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/09/11/ 20:50
 * @description
 */
@Data
public class ThirdPartyInvoiceFailProcessDTO implements Serializable {
    private static final long serialVersionUID = -6943711118011840899L;

    /**
     * 预制发票id
     */
    private String pid;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;
}
