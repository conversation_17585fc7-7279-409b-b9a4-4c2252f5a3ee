package com.charge.api.web.vo.joylife.response;

import com.charge.common.util.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 押金信息
 */
@Data
@ApiModel(value = "depositAccount对象", description = "押金信息")
public class PredepositDepositDetailDTO {

    /**
     * 缴费时间
     */
    @JsonFormat(pattern = DateUtils.FORMAT_0, timezone = "GMT+8")
    private Date paymentTime;

    /**
     * 收费项名称（不拼接编码）
     */
    private String predepositItemName;

    /**
     * 收费项编码
     */
    private String predepositItemCode;

    /**
     * 收费项id
     */
    private Long predepositItemId;


    /**
     * 余额
     */
    private BigDecimal availableBalance;

    /**
     * 缴费人
     */
    private String employeeName;

    /**
     * 支付金额
     */
    private BigDecimal payPrice;

    /**
     * 支付类型
     */
    private Integer paymentType;

    /**
     * 收据号
     */
    private String receiptNumber;

    /**
     * 付款银行账号
     */
    private String payBankAccount;

    /**
    * 押金调整
    */
    private List<DepositAdjustRecordVO> depositAdjustRecordVO;

    /**
     * 押金退款
     */
    private List<PredepositRefundRecordVO> predepositRefundRecordVO;


}
