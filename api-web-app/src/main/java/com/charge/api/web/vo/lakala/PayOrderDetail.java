package com.charge.api.web.vo.lakala;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class PayOrderDetail {
    private String payMember; //支付人
    private String communityName; //小区名称
    private String buildingName; //楼栋名称
    private String unitName; //单元号
    private String houseName; //房屋号
    private BigDecimal totalPrice; //支付金额
    private String paymentMethod; //付款方式
    private String paymentName;
    private String payStatus; //支付状态
    private String payTime; //支付时间
    private String orderId; //订单号
    private String orderName; //订单名称
    private String tno; //交易流水
    private List itemList; //订单项列表
    private String memo;//备注
    private String collectorId;//收费员id
    private String collectorName;//收费员
    private String operatorId;//操作人id
    private String adjustBillId;//调整账单Id
    private Map<String, Object> cancelInfo;//红冲作废信息
    private String transactionFlowId;
    private Map<String, Object> basicInfo;//基本信息
    private String bankTransactionNo;  //银行流水号
    private String arrivalTime;  //到账时间
    private String paymentSource; //支付来源
}
