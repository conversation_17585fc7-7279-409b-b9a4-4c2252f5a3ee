package com.charge.api.web.vo.parking;

import lombok.Data;

import java.io.Serializable;


@Data
public class InvoiceTaxDeviceVO implements Serializable {
    private static final long serialVersionUID = 2384146398691326184L;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 纳税主体
     */
    private String taxpayer;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 可开票类型，逗号隔开
     */
    private String allowInvoiceType;

    /**
     * 销方名称
     */
    private String sellerName;
    
    /**
     * 项目名称
     */
    private String communityName;

    /**
     * 复核人名称
     */
    private String checkUserName;

    /**
     * 收款人名称
     */
    private String payeeUserName;

    /**
     * 专票限额
     */
    private String specialTicketLimit;

    /**
     * 电票限额
     */
    private String electricTicketLimit;

    /**
     * 普票限额
     */
    private String generalTicketLimit;

    /**
     * 余票预警阈值
     */
    private Integer remainTicketAlter;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 修改时间
     */
    private String modifyTime;
}
