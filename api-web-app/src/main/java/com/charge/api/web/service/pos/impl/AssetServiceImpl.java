package com.charge.api.web.service.pos.impl;

import com.charge.api.web.config.SwitchConfig;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.service.pos.AssetService;
import com.charge.api.web.support.AssetOverviewSupport;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.util.FeignUtil;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.AssetBalanceVO;
import com.charge.api.web.vo.pos.AssetBalancesVO;
import com.charge.api.web.vo.pos.UserVO;
import com.charge.bill.client.PredepositAccountClient;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.AssetArrearsCountDTO;
import com.charge.bill.dto.AssetArrearsCountQueryDTO;
import com.charge.bill.dto.assetoverview.AssetOverViewDTO;
import com.charge.bill.dto.predeposit.AsssetPredepositCountDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.SpringContextUtil;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CustomerClient;
import com.charge.maindata.condition.CustomerCondition;
import com.charge.maindata.condition.SearchAssetCondition;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.enums.CustomerTypeEnum;
import com.charge.maindata.enums.HouseTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import com.charge.maindata.pojo.dto.ParkingSpaceDTO;
import com.charge.maindata.pojo.vo.CustomerPageVO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.charge.api.web.service.pos.impl.ChargeBillServiceImpl.nullStr;
import static com.charge.api.web.support.AssetSupport.*;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/10
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AssetServiceImpl implements AssetService {

    private final AssetClient assetClient;

    private final AssetSupport assetSupport;

    private final CustomerClient customerClient;

    private final ReceivableBillClient receivableBillClient;

    private final PredepositAccountClient predepositAccountClient;

    private final AssetOverviewSupport assetOverviewSupport;

    private static final Integer HOUSE_TYPE_HOUSE = 2;

    private static final Integer HOUSE_TYPE_SHOP = 1;

    private final SwitchConfig switchConfig;

    public static List<HouseTypeEnum>  getHouseTypes(String subjectType){
        if(StringUtils.hasText(subjectType)){
            if ("house".equals(subjectType)) {
                return Lists.newArrayList(HouseTypeEnum.HOUSE,HouseTypeEnum.VIRTUAL_HOUSE_COMMUNITY
                                ,HouseTypeEnum.SUPPORTING,HouseTypeEnum.UNKNOWN,HouseTypeEnum.APARTMENT
                                ,HouseTypeEnum.VIRTUAL_HOUSE,HouseTypeEnum.OTHER);
            } else if ("shops".equals(subjectType)) {
               return Lists.newArrayList(HouseTypeEnum.SHOPS);
            } else if ("car".equals(subjectType)) {
                return Lists.newArrayList(HouseTypeEnum.PARKING_SPACE);
            }else {
                return Lists.newArrayList(HouseTypeEnum.OTHER);
            }
        }
        return Lists.newArrayList();
    }

    public static  void fillTotalPage(ChargePageResponse chargePageResponse){
        double totalRecords = Math.ceil((double) chargePageResponse.getTotalRecord() /chargePageResponse.getPageSize());
        chargePageResponse.setTotalPage((int) totalRecords);
    }

    public static  AssetBalancesVO buildAssetBalancesVO(List<AssetOverViewDTO> overViewDTOs) throws ChargeBusinessException {
        if(CollectionUtils.isEmpty(overViewDTOs)){
            return new AssetBalancesVO(Lists.newArrayList());
        }
        List<Long> assetIds = overViewDTOs.stream().map(AssetOverViewDTO::getAssetId).distinct().collect(Collectors.toList());
        AssetSupport assetSupport = SpringContextUtil.getBean(AssetSupport.class);
        Map<Long,AssetAdapter> assetAdapterMap = assetSupport.listAssetByIds(overViewDTOs.get(0).getCommunityId(), assetIds).stream()
                .collect(Collectors.toMap(AssetAdapter::getId, Function.identity(), (a, b) -> a));
        List<AssetBalanceVO> assetBalanceVOS = overViewDTOs.stream().map(overView -> {
            AssetBalanceVO assetBalanceVO = AssetBalanceVO.builder()
                    .arrearAmount(overView.getTotalOwnerArrearsAmount())
                    .balanceAmount(overView.getTotalOwnerPrestoreBalance())
                    .depositAmount(overView.getTotalOwnerDepositeBalance())
                    .houseName(overView.getAssetNum())
                    .houseUuid(overView.getAssetId().toString())
                    .userName(overView.getAssetOwnerName())
                    .mobile(overView.getAssetOwnerPhone())
                    .status(overView.getOwnerArrearsStatus())
                    .build();
            AssetAdapter assetAdapter = assetAdapterMap.get(overView.getAssetId());
            if(assetAdapter!=null){
                assetBalanceVO.setCommunityName(assetAdapter.getCommunityName());
                assetBalanceVO.setBuildingName(assetAdapter.getBuildingName());
                assetBalanceVO.setUnitName(assetAdapter.getUnitName());
                List<CustomerDTO> listCustomer = assetAdapter.getListCustomer();
                boolean hasCustomer = CollectionUtils.isNotEmpty(listCustomer);
                assetBalanceVO.setResidentUuid(hasCustomer ? nullStr(listCustomer.get(0).getId()) : "");
                assetBalanceVO .setGender(hasCustomer ? transferSexFromV2ToV1(listCustomer.get(0).getSex()) : 0);
                if (Objects.equals(assetAdapter.getType(), AssetTypeEnum.PARKING_SPACE.getCode())) {
                    assetBalanceVO.setSubjectType("car");
                } else if (Objects.equals(assetAdapter.getType(), AssetTypeEnum.HOUSE.getCode())) {
                    if (Objects.equals(assetAdapter.getHouseType(), HouseTypeEnum.SHOPS.getCode())) {
                        assetBalanceVO.setSubjectType("shops");
                    } else  {
                        assetBalanceVO.setSubjectType("house");
                    }
                }
            }else{
                assetBalanceVO.setCommunityName("");
                assetBalanceVO.setBuildingName("");
                assetBalanceVO.setUnitName("");
                assetBalanceVO.setResidentUuid("");
                assetBalanceVO .setGender(0);
                assetBalanceVO.setSubjectType("house");
            }
            return assetBalanceVO;
        }).collect(Collectors.toList());
        return new AssetBalancesVO(assetBalanceVOS);
    }

    @Override
    public ChargePageResponse<AssetBalancesVO> assetSearchPage(Long communityId, String keyword, String subjectType, Integer currentPage, Integer pageSize) throws ChargeBusinessException {
        ChargePageResponse<AssetBalancesVO> chargePageResponse = new ChargePageResponse<>(new AssetBalancesVO(Lists.newArrayList()), pageSize,
                0, currentPage,0);
        if(switchConfig.isUseNewAssetOverview(communityId)){
            PagingDTO<AssetOverViewDTO> assetOverViewPage = assetOverviewSupport.pageAssetOverview(communityId, keyword, getHouseTypes(subjectType), null, currentPage, pageSize,1);
            List<AssetOverViewDTO> assetOverViewS = assetOverViewPage.getList();
            chargePageResponse.setTotalRecord(assetOverViewPage.getTotalCount());
            fillTotalPage(chargePageResponse);
            chargePageResponse.setContent(buildAssetBalancesVO(assetOverViewS));
            return chargePageResponse;
        }
        SearchAssetCondition.SearchAssetConditionBuilder builder = SearchAssetCondition.builder().communityId(communityId).pageNum(currentPage)
                .customerTypes(Lists.newArrayList(CustomerTypeEnum.OWNER.getCode())).excludeMoveOut(true).pageSize(pageSize)
                .searchParam(keyword).queryAsset(true).queryCustomer(true);
        if(StringUtils.hasText(subjectType)){
            if ("house".equals(subjectType)) {
                builder.assetType(AssetTypeEnum.HOUSE.getCode())
                        .houseTypes(Lists.newArrayList(HouseTypeEnum.HOUSE.getCode(),HouseTypeEnum.VIRTUAL_HOUSE_COMMUNITY.getCode()
                                ,HouseTypeEnum.SUPPORTING.getCode(),HouseTypeEnum.UNKNOWN.getCode(),HouseTypeEnum.APARTMENT.getCode()
                                ,HouseTypeEnum.VIRTUAL_HOUSE.getCode(),HouseTypeEnum.OTHER.getCode()));
            } else if ("shops".equals(subjectType)) {
                builder.assetType(AssetTypeEnum.HOUSE.getCode())
                        .houseType(HouseTypeEnum.SHOPS.getCode());
            } else if ("car".equals(subjectType)) {
                builder.assetType(AssetTypeEnum.PARKING_SPACE.getCode());
            }else {
                return chargePageResponse;
            }
        }
        ChargeResponse<PagingDTO<AssetDTO>> searchAssetResp = assetClient.pageSearchAsset(builder.build());
        PagingDTO<AssetDTO> assetPage = AppInterfaceUtil.getResponseData(searchAssetResp);
        chargePageResponse.setTotalRecord(assetPage.getTotalCount());
        chargePageResponse.setTotalPage((int) Math.ceil(assetPage.getTotalCount() / pageSize.doubleValue()));
        List<AssetDTO> assets = assetPage.getList();
        if (CollectionUtils.isEmpty(assets)) {
            return chargePageResponse;
        }

        List<Long> assetIds = assets.stream().map(AssetDTO::getId).collect(Collectors.toList());

        //1、根据资产类型查询资产应收单金额合计
        AssetArrearsCountQueryDTO arrearsCountQueryDTO = new AssetArrearsCountQueryDTO();
        arrearsCountQueryDTO.setCommunityId(communityId);
        arrearsCountQueryDTO.setPageNum(1);
        arrearsCountQueryDTO.setPageSize(10000);
        arrearsCountQueryDTO.setAssetIdList(assetIds);
        arrearsCountQueryDTO.setStatusIdList(Arrays.asList(ReceivableBillPayStatusEnum.NOT_PAY.getCode(), ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()));
        arrearsCountQueryDTO.setListBillStatus(Lists.newArrayList(Lists.newArrayList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),ReceivableBillStatusEnum.BILL_HOLD.getCode(),
                ReceivableBillStatusEnum.BILL_ACCOUNTING.getCode(),ReceivableBillStatusEnum.BILL_CHECKING.getCode())));
        arrearsCountQueryDTO.setChargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode());
        if(StringUtils.hasText(subjectType)){
            if ("house".equals(subjectType)) {
                arrearsCountQueryDTO.setAssetType(AssetTypeEnum.HOUSE.getCode());
                arrearsCountQueryDTO.setHouseTypes(
                        Lists.newArrayList(HouseTypeEnum.HOUSE.getCode(),HouseTypeEnum.VIRTUAL_HOUSE_COMMUNITY.getCode()
                                ,HouseTypeEnum.SUPPORTING.getCode(),HouseTypeEnum.UNKNOWN.getCode(),HouseTypeEnum.APARTMENT.getCode()
                                ,HouseTypeEnum.VIRTUAL_HOUSE.getCode(),HouseTypeEnum.OTHER.getCode()));
            } else if ("shops".equals(subjectType)) {
                arrearsCountQueryDTO.setAssetType(AssetTypeEnum.HOUSE.getCode());
                arrearsCountQueryDTO.setHouseType(HouseTypeEnum.SHOPS.getCode());
            }else if ("car".equals(subjectType)) {
                arrearsCountQueryDTO.setAssetType(AssetTypeEnum.PARKING_SPACE.getCode());
            }
        }
        ChargeResponse<PagingDTO<AssetArrearsCountDTO>> arrearsResp = receivableBillClient.assetArrearsAmountCountListByAssetType(arrearsCountQueryDTO);
        PagingDTO<AssetArrearsCountDTO> pagingDTO = FeignUtil.getContent(arrearsResp, "账单服务");
        Map<Long, List<AssetArrearsCountDTO>> assetArrearsMap = pagingDTO.getList().stream().collect(Collectors.groupingBy(AssetArrearsCountDTO::getAssetId));

        AssetArrearsCountQueryDTO queryDTO = new AssetArrearsCountQueryDTO();
        queryDTO.setAssetIdList(assetIds);
        ChargeResponse<List<AsssetPredepositCountDTO>> preDepositResp = predepositAccountClient.assetPredepositCountList(queryDTO);
        Map<Long, AsssetPredepositCountDTO> preDepositMap = AppInterfaceUtil.getResponseData(preDepositResp).stream().collect(Collectors.toMap(AsssetPredepositCountDTO::getAssetId, a -> a, (a, b) -> b));
        chargePageResponse.setContent(transferTtoAssetBalanceVO(assets,assetArrearsMap,preDepositMap));
        return chargePageResponse;
    }

    private AssetBalancesVO transferTtoAssetBalanceVO(List<AssetDTO> assets, Map<Long, List<AssetArrearsCountDTO>> assetArrearsMap, Map<Long, AsssetPredepositCountDTO> preDepositMap) {
        List<AssetBalanceVO> assetBalanceVOS = assets.stream().map(asset -> {
            List<AssetArrearsCountDTO> assetArrears = assetArrearsMap.get(asset.getId());
            AsssetPredepositCountDTO assetPreDeposit = preDepositMap.get(asset.getId());
            List<CustomerDTO> listCustomer;
            HouseDTO house = asset.getHouseDTO();
            ParkingSpaceDTO parkingSpace = asset.getParkingSpaceDTO();
            if (house != null) {
                listCustomer = house.getListCustomer();
            } else if (parkingSpace != null) {
                listCustomer = parkingSpace.getListCustomer();
            } else {
                listCustomer = Lists.newArrayList();
            }
            boolean hasCustomer = !CollectionUtils.isEmpty(listCustomer);
            AssetBalanceVO assetBalanceVO = AssetBalanceVO.builder()
                    .arrearAmount(CollectionUtils.isNotEmpty(assetArrears)? assetArrears.stream().map(a->a.getArrearsAmount().add(a.getPenaltyArrearsAmount()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO)
                    .balanceAmount(assetPreDeposit != null ? assetPreDeposit.getPreDeposit() : BigDecimal.ZERO)
                    .depositAmount(assetPreDeposit != null ? assetPreDeposit.getDeposit() : BigDecimal.ZERO)
                    .communityName(house != null ? nullStr(house.getCommunityName()) : parkingSpace != null ? nullStr(parkingSpace.getCommunityName()) : "")
                    .buildingName(nullStr(getAssetBuildingName(asset)))
                    .houseName(nullStr(getAssetNum(asset)))
                    .houseUuid(nullStr(asset.getId()))
                    .unitName(nullStr(getAssetUnitName(asset)))
                    .userName(hasCustomer?nullStr(listCustomer.stream().map(CustomerDTO::getCustomerName).filter(StringUtils::hasText)
                            .collect(Collectors.joining(","))):"")
                    .residentUuid(hasCustomer ? nullStr(listCustomer.get(0).getId()) : "")
                    .gender(hasCustomer ? transferSexFromV2ToV1(listCustomer.get(0).getSex()) : 0)
                    .mobile(hasCustomer ? nullStr(listCustomer.get(0).getPhone()):"")
                    .build();
            if (assetBalanceVO.getArrearAmount().compareTo(BigDecimal.ZERO) > 0) {
                assetBalanceVO.setStatus(1);
            } else {
                assetBalanceVO.setStatus(0);
            }
            if (parkingSpace != null) {
                assetBalanceVO.setSubjectType("car");
            } else if (house != null) {
                if (Objects.equals(house.getHouseType(), HouseTypeEnum.SHOPS.getCode())) {
                    assetBalanceVO.setSubjectType("shops");
                } else  {
                    assetBalanceVO.setSubjectType("house");
                }
            }
            return assetBalanceVO;
        }).collect(Collectors.toList());
        return new AssetBalancesVO(assetBalanceVOS);
    }

    public static Integer transferSexFromV2ToV1(Integer sex) {
        if (sex == null) {
            return 0;
        } else if (sex == 1) {
            return 1;
        } else if (sex == 0) {
            return 2;
        } else {
            return 0;
        }
    }

    @Override
    public void updateAssetMemo(Long assetId, String memo) throws ChargeBusinessException {
        ChargeResponse<String> response = assetClient.updateAssetMemo(assetId, memo);
        AppInterfaceUtil.getResponseDataThrowException(response);
    }

    @Override
    public ChargePageResponse<List<UserVO>> customerSearchPage(Long communityId, String keyword, Integer currentPage, Integer pageSize) throws ChargeBusinessException {
        CustomerCondition condition=CustomerCondition.builder()
                .communityId(communityId)
                .pageNum(currentPage)
                .pageSize(pageSize)
                .customerNature(0)
                .queryHouse(true)
                .build();
        if(StringUtils.hasText(keyword)){
            condition.setSearchParam(keyword);
        }
        ChargeResponse<PagingDTO<CustomerPageVO>> resp = customerClient.page(condition);
        PagingDTO<CustomerPageVO> customerPage = AppInterfaceUtil.getResponseData(resp);
        ChargePageResponse<List<UserVO>> userPageResponse = new ChargePageResponse<>(null, pageSize,
                (int) Math.ceil(customerPage.getTotalCount() / pageSize.doubleValue()), currentPage, customerPage.getTotalCount());
        List<CustomerPageVO> customers = customerPage.getList();
        if (CollectionUtils.isEmpty(customers)) {
            userPageResponse.setContent(Lists.newArrayList());
            return userPageResponse;
        }
        List<UserVO> users = customers.stream().map(customer ->
                new UserVO(customer.getCustomerName(), "", CollectionUtils.isEmpty(customer.getHouses()) ? 0 : customer.getHouses().size(), customer.getPhone(), String.valueOf(customer.getId()))
        ).collect(Collectors.toList());
        userPageResponse.setContent(users);
        return userPageResponse;
    }
}


