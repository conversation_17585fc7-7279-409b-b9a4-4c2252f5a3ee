package com.charge.api.web.controller.joylife.pay;

import com.charge.api.web.service.pay.PrestoreService;
import com.charge.api.web.vo.joylife.AssetChargeStandVO;
import com.charge.api.web.vo.joylife.request.BatchZhaoXiAssetReq;
import com.charge.api.web.vo.joylife.request.PrestoreOrderRequest;
import com.charge.api.web.vo.joylife.response.BatchCalculatePreStoreVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.starter.web.annotation.Idempotent;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 预存支付接口
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PrestoreController {

    private final PrestoreService prestoreService;

    @PostMapping("/new/chargeAPI/getPayStatus")
    public ChargeResponse getPayStatus(String paymentSource, String id) throws Exception {
        return prestoreService.postPayStatus(paymentSource, id);
    }

    @ApiOperation(value = "app、小程序预存下单")
    @RequestMapping(value = "/new/weixin/addPrestoreInfo", method = {RequestMethod.POST})
    @Idempotent
    public ChargeResponse<Object> addPrestoreInfo(@RequestBody PrestoreOrderRequest request) throws Exception {
        return prestoreService.addPrestoreInfo(request);
    }

    /**
     * 批量资产计算月度费用
     *
     * @param calculatePreStoreAmountReq 计算请求
     * @return 计算结果
     * @throws ChargeBusinessException
     */
    @PostMapping("/new/batchAsset/getPrestoreItemCostPerMonth")
    public ChargeResponse<BatchCalculatePreStoreVO> calculatePreStoreAmount(@RequestBody @Valid BatchZhaoXiAssetReq calculatePreStoreAmountReq) throws ChargeBusinessException {
        return new ChargeResponse<>(prestoreService.batchCalculatePreStoreAmount(calculatePreStoreAmountReq));
    }

    /**
     * 列出资产的计费标准
     *
     * @param zhaoXiAssetReq 朝夕批量资产请求
     * @return 计费标准列表
     * @throws ChargeBusinessException
     */
    @PostMapping("/new/batchAsset/listAssetChargeStand")
    public ChargeResponse<List<AssetChargeStandVO>> listAssetChargeStand(@RequestBody @Valid BatchZhaoXiAssetReq zhaoXiAssetReq) throws ChargeBusinessException {
        return new ChargeResponse<>(prestoreService.listAssetChargeStand(zhaoXiAssetReq));
    }

}
