package com.charge.api.web.vo.joylife.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/02
 */
@Data
public class HousePayItemListConditionVO {

    /**
     * 朝夕房间id
     */
    @NotBlank(message = "房屋参数不能为空")
    @ApiModelProperty(value = "房屋uuid", required = true)
    private String houseUuid;

    /**
     * 费用所属年月-起始
     */
    @NotBlank(message = "开始时间不能为空")
    @ApiModelProperty(value = "费用所属年月-起始", required = true)
    private String belongStartYear;

    /**
     * 费用所属年月-终止
     */
    @ApiModelProperty("费用所属年月-终止")
    private String belongEndYear;
}
