package com.charge.api.web.service.pos.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.charge.api.web.config.AssetPayGrayConfig;
import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.dto.pos.AddPredepositRequestDTO;
import com.charge.api.web.service.bill.pos.AddNewDepositInfoService;
import com.charge.api.web.service.order.*;
import com.charge.api.web.service.pos.PreDepositService;
import com.charge.api.web.support.*;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.joylife.request.PreStoreCalReq;
import com.charge.api.web.vo.pos.AddPredepositResultVo;
import com.charge.api.web.vo.pos.ItemAccountDetailDTOV1;
import com.charge.api.web.vo.pos.PreDepositAccountPosDTOV1;
import com.charge.api.web.vo.pos.PredepositItemCostPerMonthVO;
import com.charge.bill.client.PredepositManagerPosClient;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.dto.predeposit.WaitingCarryForwardDTO;
import com.charge.bill.dto.predeposit.pos.*;
import com.charge.bill.enums.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.config.client.assets.AssetsChargeConfigClient;
import com.charge.config.client.item.CommunityPreStoreItemClient;
import com.charge.config.dto.assets.AssetChargeConfigDetailDTO;
import com.charge.config.dto.assets.condition.AssetChargeConfigQueryConditionDTO;
import com.charge.config.dto.item.ItemInfo;
import com.charge.config.dto.item.PreStoreItemConfigDTO;
import com.charge.config.dto.item.condition.PreStoreItemConfigQueryConditionDTO;
import com.charge.config.dto.item.condition.ZXSpecialPreStoreQueryConditionDTO;
import com.charge.config.enums.AssetChargeConfigStatusEnum;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.StringUtil;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import com.charge.maindata.pojo.dto.ParkingSpaceDTO;
import com.charge.order.enums.OrderRuleCategoryEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/2/28 15:09
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PreDepositServiceImpl implements PreDepositService {

    private final PredepositManagerPosClient predepositManagerPosClient;

    private final PosCreateOrderService posCreateOrderService;

    private final AssetSupport assetSupport;

    private final CommunityPreStoreItemClient communityPreStoreItemClient;

    private final ReceivableBillClient receivableBillClient;

    private final CommunityClient communityClient;

    private final FeeCalculateSupport feeCalculateSupport;

    private final AssetsChargeConfigClient assetsChargeConfigClient;

    private final CommunityParamSupport communityParamSupport;

    private final CommunitySupport communitySupport;


    private final BankCollectionCheckSupport bankCollectionCheckSupport;
    private final AssetPayGrayConfig assetPayGrayConfig;
    private final AddNewDepositInfoService addNewDepositInfoService;


    @Override
    public ChargeResponse<WaitingCarryForwardDTO> getWaitingCarryForward(String communityUuid, String houseUuid) {

        PredepositAccountPosQueryDTO posQueryDTO = PredepositAccountPosQueryDTO.builder()
                .communityId(Long.valueOf(communityUuid))
                .assetId(Long.valueOf(houseUuid))
                .build();
        return predepositManagerPosClient.getForwardAccountForPos(posQueryDTO);
    }

    @Override
    public ChargeResponse<PreDepositAccountPosDTOV1> getPreStoreDetail(String communityUuid, String houseUuid) {
        PredepositAccountPosQueryDTO posQueryDTO = PredepositAccountPosQueryDTO.builder()
                .communityId(Long.valueOf(communityUuid))
                .assetId(Long.valueOf(houseUuid))
                .build();
        ChargeResponse<PredepositAccountPosDTO> predepositResp = predepositManagerPosClient.getPredepositAccountForPos(posQueryDTO);
        PredepositAccountPosDTO preDepositAccount = AppInterfaceUtil.getResponseData(predepositResp);
        return new ChargeResponse<>(toPreDepositAccountPosDTOV1(preDepositAccount));
    }

    private PreDepositAccountPosDTOV1 toPreDepositAccountPosDTOV1(PredepositAccountPosDTO preDepositAccount){
        if(preDepositAccount==null){
            return null;
        }
        List<ItemAccountDetailDTOV1> itemAccountDetailDTOV1s = preDepositAccount.getPrestore().stream().map(a -> new ItemAccountDetailDTOV1(a.getPredepositAccountId(), a.getTotalMoney(), a.getFreezeMoney(), a.getAvailableMoney(), a.getItemId(), a.getItemName()))
                .collect(Collectors.toList());
        return new PreDepositAccountPosDTOV1(preDepositAccount.getMonty(),preDepositAccount.getCommon(),itemAccountDetailDTOV1s);

    }

    @Override
    public ChargeResponse<PredepositRefundDetailPosDTO> getCheckDetail(String id, String communityUuid, String houseId) throws ChargeBusinessException {
        PredepositRefundPosQueryDTO refundQueryDTO = PredepositRefundPosQueryDTO.builder()
                .communityId(Long.valueOf(communityUuid))
                .assetId(StringUtil.isEmpty(houseId) ? null : Long.valueOf(houseId))
                .transactionId(StringUtil.isEmpty(id) ? null : Long.valueOf(id))
                .build();
        return predepositManagerPosClient.getCheckDetail(refundQueryDTO);
    }

    @Override
    public ChargePageResponse<List<PredepositRefundSearchDetailDTO>> refundSearch(String communityUuid, String receiptNo, String paymentName, String houseNo, String payStart,
                                                                                   String payEnd, String payMonth, Integer pageNum, Integer pageSize) {
        PredepositRefundSearchQueryDTO searchQueryDTO = PredepositRefundSearchQueryDTO.builder()
                .communityId(Long.valueOf(communityUuid))
                .assetName(houseNo)
                .receiptNo(receiptNo)
                .paymentName(paymentName)
                .payStart(payStart)
                .payEnd(payEnd)
                .payMonth(payMonth)
                .pageNum(pageNum)
                .pageSize(pageSize).build();
        ChargeResponse<PagingDTO<PredepositRefundSearchDetailDTO>> response = predepositManagerPosClient.refundSearchForPos(searchQueryDTO);
        if(!response.isSuccess()){
            ChargePageResponse<List<PredepositRefundSearchDetailDTO>> refundPageResponse = new ChargePageResponse<>(response.getCode(), response.getMessage());
            return refundPageResponse;
        }
        response.getContent().getList().stream().forEach(item->{
            item.setCropId("3c384ad60a4911e79f0570106fb01330");
            item.setPaymentType("0");
            noNullStringAttr(item);
        });
        return new ChargePageResponse<>(response.getContent().getList(), pageSize,
                (int) Math.ceil(response.getContent().getTotalCount() / pageSize.doubleValue()), pageNum, response.getContent().getTotalCount());
    }



    @Override
    public AddPredepositResultVo addNewDepositInfo4House(AddPredepositRequestDTO request) throws ChargeBusinessException {
        // -- POS预存充值
        Long assetId = request.getAssetId();
        // 校验资产是否在托收中
        if (bankCollectionCheckSupport.bankCollectingByAssetId(assetId)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E3042);
        }

        //专项预存金额
        List<JSONObject> specialPreStoreJSON = JSONArray.parseArray(request.getSpecialPreStoreJson(), JSONObject.class);
        //通用预存金额
        String payMoney = request.getPayMoney();
        //查询资产信息、查询Community信息
        AssetDTO assetDTO = assetSupport.getAssetById(assetId);
        Long communityId = assetSupport.getCommunityId(assetDTO);

        if (assetPayGrayConfig.isOpenGray(communityId, "/addNewDepositInfo")) {
            CreateBillResponse response = addNewDepositInfoService.createBill(request, assetDTO);
            //返回实收单id
            AddPredepositResultVo resultVo = new AddPredepositResultVo();
            Long transactionId = response.getAssetTransactionId();
            resultVo.setId(String.valueOf(transactionId));
            resultVo.setOrderNum(response.getOrderNum());
            // NOTE 缓存transactionId与communityId的关系
            communitySupport.setAssetTransactionIdCommunityIdRelation(transactionId, communityId);
            return resultVo;
        }
        //1、构建预存缴费
        CreateOrderContext context = buildContext4AddPredeposit(request, assetDTO);

        //【核心】存放在上下文的billBusinessList
        List<BillBusiness> billBusinessList = new ArrayList<>();

        //2、临时缴费
        buildTempBillBusiness(request, billBusinessList);

        //3、查询应收，查看是否缴费，生成核销List和充值List
        //获取项目预存配置
        PreStoreItemConfigDTO preStoreItemConfig = getCommunityPreStoreItem(communityId);
        //通用预存对应可抵扣的的专项
        List<Long> genaralList = preStoreItemConfig.getCommonPreStoreItems();
        //【欠费查询】查询房间应收数据 key为itemId费用项
        Map<Long, List<ReceivableBillDTO>> recBillMap = this.queryReceivableBillMap(communityId, request.getAssetId(), specialPreStoreJSON, genaralList);

        //4、准备构建billBusiness
        List<PredepositBillBusiness> predepositBills = preparePredepositBillBusinesses(specialPreStoreJSON, payMoney);
        //校验专项预存项是否房间已经配置
        checkChargeItemIdEffective(predepositBills,assetDTO,context.getCommunity());

        //是否存在预收
        if(recBillMap.isEmpty()) {
            //不存在欠费
            billBusinessList.addAll(predepositBills);
        } else{
            //存在欠费 先缴费 后充值
            //预存实体
            List<BillBusiness> predepositResultList = new ArrayList<>();
            //核销应收实体
            List<BillBusiness> writeOffResultList = new ArrayList<>();
            //【核心方法】先交费后充值，返回predepositResult为待充值数据，writeOffResult为应收待核销数据
            this.deduction(predepositBills, predepositResultList, writeOffResultList, communityId, assetDTO.getId(), genaralList, recBillMap);
            billBusinessList.addAll(predepositResultList);
            billBusinessList.addAll(writeOffResultList);
        }

        List<AssetBillBusiness> assetBillBusinessList = new ArrayList<>();
        AssetBillBusiness assetBillBusiness = new AssetBillBusiness();
        assetBillBusiness.setAsset(assetDTO);
        assetBillBusiness.setBillBusinesses(billBusinessList);
        assetBillBusinessList.add(assetBillBusiness);
        context.setAssetBillBusinesses(assetBillBusinessList);
        //【核心】调用通用下单接口
        posCreateOrderService.createOrder(context);

        //返回实收单id
        AddPredepositResultVo resultVo = new AddPredepositResultVo();
        Long transactionId = context.getAssetBillBusinesses().get(0).getAssetTransactionId();
        resultVo.setId(String.valueOf(transactionId));
        resultVo.setOrderNum(context.getOrderNum());
        // NOTE 缓存transactionId与communityId的关系
        communitySupport.setAssetTransactionIdCommunityIdRelation(transactionId, communityId);
        return resultVo;
    }

    private void checkChargeItemIdEffective(List<PredepositBillBusiness> predepositBills,AssetDTO assetDTO,CommunityDTO communityDTO) throws ChargeBusinessException {
        List<Long> specialPreDepositChargeItemIds = predepositBills.stream().filter(predepositBillBusiness -> predepositBillBusiness.getPredepositType().equals(PredepositTypeEnum.SPECIAL_DEPOSIT.getCode()))
                .map(PredepositBillBusiness::getChargeItemId).distinct().collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(specialPreDepositChargeItemIds)){
            List<Long> notBindChargeItemIds = listChargeItemIdsNotBind(specialPreDepositChargeItemIds, communityDTO.getId(), assetDTO.getId());
            if(!CollectionUtils.isEmpty(notBindChargeItemIds)){
                String notBindItemName = predepositBills.stream().filter(predepositBillBusiness -> notBindChargeItemIds.contains(predepositBillBusiness.getChargeItemId())).map(PredepositBillBusiness::getName).distinct()
                        .collect(Collectors.joining(","));
                if(org.springframework.util.StringUtils.hasText(notBindItemName)){
                    throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),String.format("收费项%s未绑定，无法预存",notBindItemName));
                }
            }
        }
    }

    public List<Long> listChargeItemIdsNotBind(List<Long> chargeItemIds, Long communityId, Long assetId) throws ChargeBusinessException {
        AssetChargeConfigQueryConditionDTO assetChargeConfigQueryConditionDTO = AssetChargeConfigQueryConditionDTO.builder()
                .communityId(Lists.newArrayList(communityId))
                .itemIds(chargeItemIds)
                .assetId(assetId)
                .status(AssetChargeConfigStatusEnum.EFFECTIVE.getCode())
                .queryStandardDetailFlag(false)
                .build();
        ChargeResponse<List<AssetChargeConfigDetailDTO>> chargeResponse = assetsChargeConfigClient.queryAssetChargeConfigList(assetChargeConfigQueryConditionDTO);
        List<Long> bindChargeItemIds = AppInterfaceUtil.getResponseDataThrowException(chargeResponse).stream().map(AssetChargeConfigDetailDTO::getItemId).distinct().collect(Collectors.toList());
        return chargeItemIds.stream().filter(chargeItemId -> !bindChargeItemIds.contains(chargeItemId)).collect(Collectors.toList());
    }

    @Override
    public PredepositItemCostPerMonthVO calPreStore(PreStoreCalReq calRequest) throws ChargeBusinessException {
        communityParamSupport.fillCommunityId(calRequest);
        assetSupport.fillAssetId(calRequest);
        return getPredepositItemCostPerMonth(calRequest.getCommunityId(),calRequest.getAssetId(), calRequest.getItemId());
    }

    @Override
    public PredepositItemCostPerMonthVO getPredepositItemCostPerMonth(Long communityId, Long assetId, Long itemId) throws ChargeBusinessException {
        String message="";
        //小区缴费开关，如果有每月金额则1展示，2不展示，如果没有每月金额则2展示，1不展示;
        Integer disableStatus = PayRelatedConstants.V1_PREDEPOSIT_PAY_TYPE;
        //校验 v2逻辑，验证房屋和小区是否存在
        ChargeResponse<List<CommunityDTO>> listChargeResponse = communityClient.listCommunity(CommunityCondition.builder().ids(Lists.newArrayList(communityId)).build());
        if (listChargeResponse == null || listChargeResponse.getContent() == null ||listChargeResponse.getContent().isEmpty()) {
            message="查询不到小区信息，请重试或联系管家";
            disableStatus = PayRelatedConstants.V1_PREDEPOSIT_DISABLE_PAY_TYPE;
            return PredepositItemCostPerMonthVO.builder().type(disableStatus).message(message).build();
        }

        //查房间专项预存及计算金额
        ZXSpecialPreStoreQueryConditionDTO condition =  new ZXSpecialPreStoreQueryConditionDTO();
        condition.setCommunityId(communityId);
        condition.setAssetId(assetId);
        ChargeResponse<List<ItemInfo>> specialList = communityPreStoreItemClient.listSpecialItemForZX(condition);
        List<ItemInfo> itemInfoList = AppInterfaceUtil.getResponseData(specialList).stream().filter(item -> item.getItemId().equals(itemId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemInfoList)) {
            message="查询不到对应收费项信息";
            disableStatus = PayRelatedConstants.V1_PREDEPOSIT_DISABLE_PAY_TYPE;
            return PredepositItemCostPerMonthVO.builder().type(disableStatus).message(message).build();
        }
        ItemInfo itemInfo = itemInfoList.get(0);
        com.charge.feecalculte.dto.ReceivableBillDTO reDTO = feeCalculateSupport.calculatePreStorePerMonth(communityId,assetId,itemId);
        if (reDTO != null) {
            String info ="";
            if (reDTO.getChargeArea()!=null && BigDecimal.ZERO.compareTo(reDTO.getChargeArea()) <0) {
                info = "每月"+itemInfo.getItemName()+" "+reDTO.getStandardStr()+"*" + reDTO.getChargeArea() + "平米 = " + reDTO.getReceivableAmount() + "元";
            } else if ((reDTO.getChargeArea()==null || BigDecimal.ZERO.compareTo(reDTO.getChargeArea())==0)) {
                info= "每月" + itemInfo.getItemName() + " " + reDTO.getReceivableAmount() + "元";
            }
            return PredepositItemCostPerMonthVO.builder().type(disableStatus).info(info).value(reDTO.getReceivableAmount()).message(message).build();
        }else {
            message="计算异常";
            disableStatus = PayRelatedConstants.V1_PREDEPOSIT_DISABLE_PAY_TYPE;
            return PredepositItemCostPerMonthVO.builder().type(disableStatus).message(message).build();
        }
    }

    private List<PredepositBillBusiness> preparePredepositBillBusinesses(List<JSONObject> specialPreStoreJSON, String payMoney) {
        List<PredepositBillBusiness> predepositBills = new ArrayList<>();
        if(new BigDecimal(payMoney).compareTo(BigDecimal.ZERO)>0){
            PredepositBillBusiness commonPredeposit = new PredepositBillBusiness();
            commonPredeposit.setType(BusinessTypeEnum.PREDEPOSIT_PAY);
            commonPredeposit.setName(PayRelatedConstants.PREDEPOSIT_ITEM_GENERAL_NAME);
            commonPredeposit.setChargeItemId(PayRelatedConstants.PREDEPOSIT_ITEM_GENERAL_ID);
            commonPredeposit.setMoney(new BigDecimal(payMoney));
            commonPredeposit.setPredepositType(PredepositTypeEnum.COMMON_DEPOSIT.getCode());
            predepositBills.add(commonPredeposit);
        }
        for (JSONObject json : specialPreStoreJSON) {
            PredepositBillBusiness specialPredeposit = new PredepositBillBusiness();
            specialPredeposit.setType(BusinessTypeEnum.PREDEPOSIT_PAY);
            specialPredeposit.setName(json.getString("itemName"));
            specialPredeposit.setChargeItemId(json.getLong("itemID"));
            specialPredeposit.setMoney(json.getBigDecimal("price"));
            specialPredeposit.setPredepositType(PredepositTypeEnum.SPECIAL_DEPOSIT.getCode());
            predepositBills.add(specialPredeposit);
        }
        return predepositBills;
    }

    /**
     * 获取项目预存配置
     * @param communityId
     * @return
     * @throws ChargeBusinessException
     */
    @Override
    public PreStoreItemConfigDTO getCommunityPreStoreItem(Long communityId) throws ChargeBusinessException {
        PreStoreItemConfigQueryConditionDTO query = new PreStoreItemConfigQueryConditionDTO();
        query.setCommunityIds(Lists.newArrayList(communityId));
        ChargeResponse<List<PreStoreItemConfigDTO>> preStoreItemConfigList = communityPreStoreItemClient.getPreStoreItemConfig(query);
        if (preStoreItemConfigList == null || AppInterfaceUtil.APP_SUCCESS != preStoreItemConfigList.getCode()
                || CollectionUtils.isEmpty(preStoreItemConfigList.getContent())) {
            log.error("{}|查询小区预存配置信息异常|{}", LogCategoryEnum.BUSSINESS,preStoreItemConfigList);
            throw new ChargeBusinessException("当前小区不能预存，请联系管家");
        }
        PreStoreItemConfigDTO preStoreItemConfig = preStoreItemConfigList.getContent().get(0);
        return preStoreItemConfig;
    }

    /**
     * 构建临时缴费billBusinessList
     * @param request
     * @param billBusinessList
     */
    private static void buildTempBillBusiness(AddPredepositRequestDTO request, List<BillBusiness> billBusinessList) {
        if(StringUtils.isNotBlank(request.getTempBillJson())) {
            List<JSONObject> tempBillList = JSONArray.parseArray(request.getTempBillJson(), JSONObject.class);
            tempBillList.stream().forEach(json -> {
                OrderBillBusiness billBiz = new OrderBillBusiness();
                billBiz.setType(BusinessTypeEnum.TEMP_PAY);
                billBiz.setSubsetId(Long.valueOf(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_300.getCode())); //临时类填固定的值
                billBiz.setName(json.getString("itemName"));
                billBiz.setChargeItemId(json.getLong("itemID"));
                billBiz.setMoney(json.getBigDecimal("price"));
                billBusinessList.add(billBiz);
            });
        }
    }

    /**
     * 查询资产欠费应收信息，返回Map，key为itemId
     * @param communityId
     * @param assetId
     * @param specialPreStoreJSON
     * @param genaralList
     * @return
     */
    private Map<Long, List<ReceivableBillDTO>> queryReceivableBillMap(Long communityId, Long assetId, List<JSONObject> specialPreStoreJSON, List<Long> genaralList) {
        //请求的专项预存itemid
        List<Long> deductList = new ArrayList<>();
        if(specialPreStoreJSON != null) {
            deductList = specialPreStoreJSON.stream().map(json -> json.getLong("itemID")).collect(Collectors.toList());
        }
        deductList.addAll(genaralList);
        //查询房间欠费列表
        List<ReceivableBillDTO> recBillList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deductList)) {
            ReceivableConditionDTO recCondition = ReceivableConditionDTO.builder().communityId(communityId).assetId(assetId).billStatus(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode())
                    .payStatuses(Lists.newArrayList(ReceivableBillPayStatusEnum.NOT_PAY.getCode(), ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
                    .itemIdList(deductList).build();

            ChargeResponse<List<ReceivableBillDTO>> listChargeResponse = receivableBillClient.queryList(recCondition);
            recBillList = listChargeResponse.getContent();
        }
        log.info("{}|【朝昔】预存充值2.0，房间欠费|{}",LogCategoryEnum.BUSSINESS,recBillList);

        Map<Long, List<ReceivableBillDTO>> recBillMap = recBillList.stream().filter(e->ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(e.getChargeObject())).collect(Collectors.groupingBy(ReceivableBillDTO::getItemId));
        log.info("{}|【朝昔】预存充值2.0，欠费信息map|{}",LogCategoryEnum.BUSSINESS,recBillMap);
        return recBillMap;
    }

    /**
     * 构建预存缴费context
     * @param request
     * @param asset
     * @return
     * @throws ChargeBusinessException
     */
    private static CreateOrderContext buildContext4AddPredeposit(AddPredepositRequestDTO request, AssetDTO asset) throws ChargeBusinessException {
        CommunityDTO communityDTO = new CommunityDTO();
        if(asset.getHouseDTO()!=null){
            HouseDTO houseDTO = asset.getHouseDTO();
            communityDTO.setId(houseDTO.getCommunityId());
            communityDTO.setName(houseDTO.getCommunityName());
        }else if(asset.getParkingSpaceDTO()!=null){
            ParkingSpaceDTO parkingSpaceDTO = asset.getParkingSpaceDTO();
            communityDTO.setId(parkingSpaceDTO.getCommunityId());
            communityDTO.setName(parkingSpaceDTO.getCommunityName());
        }else {
            throw new ChargeBusinessException("该资产无有效的房间或者车位信息");
        }
        String paymentMethod = request.getPaymentMethod();
        PaymentMethodEnum paymentMethodEnum;
        PaymentChannelEnum paymentChannel;
        if (PayRelatedConstants.PAYMENT_METHOD_POS_CODE.equals(paymentMethod)) {
            paymentMethodEnum = PaymentMethodEnum.CARD;
            paymentChannel = PaymentChannelEnum.BANK;
        } else if (PayRelatedConstants.PAYMENT_METHOD_ALIPAY_CODE.equals(paymentMethod)) {
            paymentMethodEnum = PaymentMethodEnum.ALIPAY;
            paymentChannel = PaymentChannelEnum.ALI_PAY;
        } else if (PayRelatedConstants.PAYMENT_METHOD_WEIXIN_CODE.equals(paymentMethod)) {
            paymentMethodEnum = PaymentMethodEnum.WECHAT;
            paymentChannel = PaymentChannelEnum.WECHAT_PAY;
        } else if (PayRelatedConstants.PAYMENT_METHOD_CHEQUE_CODE.equals(paymentMethod)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "已升级项目不支持POS-支票支付，请选择扫码或刷卡");
        } else {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002, "已升级项目不支持POS-转账支付，请选择扫码或刷卡");
        }
        //构建CreateOrderContext
        CreateOrderContext context = CreateOrderContext.builder()
                .community(communityDTO)
                .paymentMethod(paymentMethodEnum)
                .paymentChannel(paymentChannel)
                .payHouseCount(1)
                .assetType(AssetType.HOUSE)
                .paymentTerminal(PaymentTerminalEnum.POS)
                .payMember(request.getPayMember())
                .totalPrice(new BigDecimal(request.getMoney()))
                .memo(request.getMemo())
                .collectorId(request.getCollectorId())
                .collectorName(request.getCollectorName())
                .mercid(request.getMercid())
                .deviceInfo(request.getDeviceInfo())
                .createTime(request.getArrivalDate() != null ? DateUtils.parse(request.getArrivalDate(), DateUtils.FORMAT_0) : new Date())
                .build();
        return context;
    }


    private void deduction(List<PredepositBillBusiness> predepositBills, List<BillBusiness> predepositResultList, List<BillBusiness> writeOffResultList,
                           Long comId, Long assetId, List<Long> genaralList, Map<Long, List<ReceivableBillDTO>> recBillMap) {
        List<PredepositBillBusiness> common = predepositBills.stream().filter(b -> PredepositTypeEnum.COMMON_DEPOSIT.getCode().equals(b.getPredepositType())).collect(Collectors.toList());
        List<PredepositBillBusiness> special = predepositBills.stream().filter(b -> PredepositTypeEnum.SPECIAL_DEPOSIT.getCode().equals(b.getPredepositType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(special)) {
            //组装缴费明细数据
            for (PredepositBillBusiness specPre : special) {
                Long itemId = specPre.getChargeItemId();
                String itemName = specPre.getName();
                BigDecimal price = specPre.getMoney();
                List<ReceivableBillDTO> orderList = recBillMap.get(Long.valueOf(itemId));
                //待缴费用
                if (CollectionUtils.isNotEmpty(orderList)) {
                    orderList.sort(Comparator.comparing(ReceivableBillDTO::getBelongYears));
                    log.info("【预存缴费】生成预支付订单--专项预存抵扣：comId:{}, assetId:{}, item_name:{}查询待预存冲抵的欠费{}", comId, assetId, itemName, orderList);

                    Iterator<ReceivableBillDTO> iterator = orderList.iterator();
                    while (iterator.hasNext()){
                        ReceivableBillDTO recBill = iterator.next();
                        if (price.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        }
                        if (recBill.getPenaltyArrearsAmount().compareTo(BigDecimal.ZERO)<0||recBill.getArrearsAmount().compareTo(BigDecimal.ZERO)<0) {
                           log.error("recBill illegal.{}",recBill);
                            continue;
                        }
                        if (BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount())<0 && (price.compareTo(BigDecimal.ZERO) > 0 && price.compareTo(recBill.getArrearsAmount().add(recBill.getPenaltyArrearsAmount())) >= 0) ){
                            log.info("【预存缴费】生成预支付订单---专项预存抵扣：comId:{}, assetId:{}, item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}", comId, assetId, itemName, price, recBill.getItemId(),recBill);
                            price = price.subtract(recBill.getArrearsAmount()).subtract(recBill.getPenaltyArrearsAmount());
                            //新增核销单
                            addWriteOffBills(recBill, writeOffResultList, itemName, itemId);
                            iterator.remove();
                        } else if(price.compareTo(BigDecimal.ZERO) > 0 && BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount())==0){
                            log.info("【预存缴费】无违约金情况下，生成预支付订单---专项预存抵扣：comId:{}, assetId:{}, item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}", comId, assetId, itemName, price, recBill.getItemId(),recBill);
                            BigDecimal arrears = recBill.getArrearsAmount();
                            BigDecimal deductMoney = price.compareTo(arrears)<0? price:arrears;
                            price = price.subtract(deductMoney);
                            recBill.setArrearsAmount(deductMoney);
                            //新增核销单
                            addWriteOffBills(recBill, writeOffResultList, itemName, itemId);
                            if (arrears.compareTo(deductMoney)>0) {
                                recBill.setArrearsAmount(arrears.subtract(deductMoney));
                            } else {
                                iterator.remove();
                            }
                        }else {
                            break;
                        }
                    }
                }
                if (price.compareTo(BigDecimal.ZERO) > 0) {
                    PredepositBillBusiness newPre = new PredepositBillBusiness();
                    newPre.setType(BusinessTypeEnum.PREDEPOSIT_PAY);
                    newPre.setName(specPre.getName());
                    newPre.setChargeItemId(specPre.getChargeItemId());
                    newPre.setMoney(price);
                    newPre.setPredepositType(PredepositTypeEnum.SPECIAL_DEPOSIT.getCode());
                    predepositResultList.add(newPre);
                    log.info("【预存缴费】生成预支付订单-专项预存抵扣：comId:{}, assetId:{}, item_name:{}price:{}剩余预存进行存储：{}", comId, assetId, itemName, price, newPre);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(common)) {
            PredepositBillBusiness orderInfo = common.get(0);
            BigDecimal commonAmount = orderInfo.getMoney();
            for (Long itemId : genaralList) {
                //获取指定收费项并按时间排序的欠费数据
                List<ReceivableBillDTO> orderList = recBillMap.get(itemId);
                if (CollectionUtils.isNotEmpty(orderList)) {
                    orderList.sort(Comparator.comparing(ReceivableBillDTO::getBelongYears));
                    for (ReceivableBillDTO recBill : orderList) {
                        if (recBill.getPenaltyArrearsAmount().compareTo(BigDecimal.ZERO)<0||recBill.getArrearsAmount().compareTo(BigDecimal.ZERO)<0) {
                            log.error("recBill illegal.{}",recBill);
                            continue;
                        }
                        if (BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount())<0 && (commonAmount.compareTo(BigDecimal.ZERO) > 0 && commonAmount.compareTo(recBill.getArrearsAmount().add(recBill.getPenaltyArrearsAmount())) >= 0)){
                            log.info("【预存缴费】生成预支付订单---通用预存抵扣：comId:{}, assetId:{}, item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}", comId, assetId, recBill.getItemName(), commonAmount, recBill.getItemId(),recBill);
                            commonAmount = commonAmount.subtract(recBill.getArrearsAmount()).subtract(recBill.getPenaltyArrearsAmount());
                            //新增核销单
                            addWriteOffBills(recBill, writeOffResultList, recBill.getItemName(), itemId);
                        } else if (commonAmount.compareTo(BigDecimal.ZERO) > 0 && BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount())==0){
                            log.info("【预存缴费】生成预支付订单---通用预存抵扣：comId:{}, assetId:{}, item_name:{}price:{},开始进行抵扣 item_id:{},recBill:{}", comId, assetId, recBill.getItemName(), commonAmount, recBill.getItemId(),recBill);
                            BigDecimal arrears = recBill.getArrearsAmount();
                            BigDecimal deductMoney = commonAmount.compareTo(arrears)<0? commonAmount:arrears;
                            commonAmount = commonAmount.subtract(deductMoney);
                            recBill.setArrearsAmount(deductMoney);
                            //新增核销单
                            addWriteOffBills(recBill, writeOffResultList, recBill.getItemName(), itemId);
                        } else {
                            break;
                        }
                    }
                }
            }
            if (commonAmount.compareTo(BigDecimal.ZERO) > 0) {
                PredepositBillBusiness commonPredeposit = new PredepositBillBusiness();
                commonPredeposit.setType(BusinessTypeEnum.PREDEPOSIT_PAY);
                commonPredeposit.setName(PayRelatedConstants.PREDEPOSIT_ITEM_GENERAL_NAME);
                commonPredeposit.setChargeItemId(PayRelatedConstants.PREDEPOSIT_ITEM_GENERAL_ID);
                commonPredeposit.setMoney(commonAmount);
                commonPredeposit.setPredepositType(PredepositTypeEnum.COMMON_DEPOSIT.getCode());
                predepositResultList.add(commonPredeposit);
                log.info("【预存缴费】生成预支付订单-通用预存抵扣：comId:{}，price:{}剩余预存进行存储", comId, commonAmount);
            }
        }
    }

    /**
     * 生成应收核销数据
     * @param recBill
     * @param writeOffResultList
     * @param itemName
     * @param itemId
     */
    private void addWriteOffBills(ReceivableBillDTO recBill, List<BillBusiness> writeOffResultList, String itemName, Long itemId) {
        ChargeObjEnum chargeObjEnum;
        if (ChargeObjEnum.CHARGE_OBJ_OWNER.getCode().equals(recBill.getChargeObject())) {
            chargeObjEnum = ChargeObjEnum.CHARGE_OBJ_OWNER;
        }else {
            chargeObjEnum = ChargeObjEnum.CHARGE_OBJ_DEVELOPER;
        }
        BusinessTypeEnum writeOffType = BusinessTypeEnum.NORMAL_PAY;
        if (BigDecimal.ZERO.compareTo(recBill.getArrearsAmount()) < 0) {
            ReceivablePayBillBusiness arrearsBill = new ReceivablePayBillBusiness();
            arrearsBill.setType(writeOffType);
            arrearsBill.setName(itemName);
            arrearsBill.setChargeItemId(itemId);
            arrearsBill.setChargeType(ChargeTypeEnum.PRINCIPAL);
            arrearsBill.setMoney(recBill.getArrearsAmount());
            arrearsBill.setReceivableBillId(recBill.getId());
            arrearsBill.setBelongYears(recBill.getBelongYears());
            arrearsBill.setChargeObject(chargeObjEnum);
            arrearsBill.setAssetUseStatus(recBill.getAssetUseStatus());
            writeOffResultList.add(arrearsBill);
        }
        //存在违约金的情况
        if (BigDecimal.ZERO.compareTo(recBill.getPenaltyArrearsAmount()) < 0) {
            ReceivablePayBillBusiness arrearsBill = new ReceivablePayBillBusiness();
            arrearsBill.setType(writeOffType);
            arrearsBill.setName(itemName);
            arrearsBill.setChargeItemId(itemId);
            arrearsBill.setChargeType(ChargeTypeEnum.PENALTY);
            arrearsBill.setMoney(recBill.getPenaltyArrearsAmount());
            arrearsBill.setReceivableBillId(recBill.getId());
            arrearsBill.setBelongYears(recBill.getBelongYears());
            arrearsBill.setChargeObject(chargeObjEnum);
            arrearsBill.setAssetUseStatus(recBill.getAssetUseStatus());
            writeOffResultList.add(arrearsBill);
        }
    }

    /**
     * 把对象中的 String 类型的null字段，转换为空字符串
     *
     * @param <T> 待转化对象类型
     * @param cls 待转化对象
     * @return 转化后的对象
     */
    private static <T> T noNullStringAttr(T cls) {
        Field[] fields = cls.getClass().getDeclaredFields();
        if (fields == null || fields.length == 0) {
            return cls;
        }
        for (Field field : fields) {
            if ("String".equals(field.getType().getSimpleName())) {
                field.setAccessible(true);
                try {
                    Object value = field.get(cls);
                    if (value == null) {
                        field.set(cls, "");
                    }
                } catch (IllegalArgumentException | IllegalAccessException e) {
                    log.error("{}|【小区应用-退款处理】字符串转换异常,原因：{}", LogCategoryEnum.BUSSINESS, e.getMessage());
                }
            }
        }
        return cls;
    }
}
