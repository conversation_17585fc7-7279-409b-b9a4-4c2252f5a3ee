package com.charge.api.web.vo.joylife.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/8/26 16:13
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordDetailBillVO {

    /**
     * 费用所属年月
     */
    private String belongYear;

    /**
     * 收费项Id
     */
    private Long itemId;

    /**
     * 收费项名称
     */
    private String itemName;

    /**
     * 实收合计金额
     */
    private BigDecimal amount;

    /**
     * 0-本金，1-违约金
     */
    private Integer chargeType;

    /**
     * 仪表类欠费计数
     */
    private String memo;

    /**
     * 1-欠费，2-预存，3-订单，4-押金
     */
    private Integer chargeBillType;

    /**
     * 收费项编码
     */
    private String itemCode;

}
