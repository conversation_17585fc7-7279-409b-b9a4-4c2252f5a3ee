package com.charge.api.web.constants;

import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentMethodEnum;
import com.charge.common.exception.ChargeBusinessException;

/**
 * pos的支付方式
 *
 * <AUTHOR>
 * @date 2023/10/26
 */
public enum PosPayMethodEnum {
    /**
     * 扫码默认微信支付
     */
    SCAN_CODE("SCAN_CODE",PaymentMethodEnum.WECHAT,PaymentChannelEnum.WECHAT_PAY),
    BANK_CARD("BANK_CARD",PaymentMethodEnum.CARD,PaymentChannelEnum.BANK);

    public String getCode() {
        return code;
    }

    public PaymentMethodEnum getPaymentMethod() {
        return paymentMethod;
    }

    public PaymentChannelEnum getPaymentChannel() {
        return paymentChannel;
    }

    public static PosPayMethodEnum byCode(String code) throws ChargeBusinessException {
        if(SCAN_CODE.getCode().equals(code)){
            return SCAN_CODE;
        }else if(BANK_CARD.getCode().equals(code)){
            return BANK_CARD;
        }
        throw new ChargeBusinessException("pos不支持的支付方式code:"+code);
    }

    private final String code;
    private final PaymentMethodEnum paymentMethod;

    private final PaymentChannelEnum paymentChannel;

    PosPayMethodEnum(String value, PaymentMethodEnum paymentMethod, PaymentChannelEnum paymentChannel){
        code=value;
        this.paymentMethod=paymentMethod;
        this.paymentChannel=paymentChannel;
    }
}


