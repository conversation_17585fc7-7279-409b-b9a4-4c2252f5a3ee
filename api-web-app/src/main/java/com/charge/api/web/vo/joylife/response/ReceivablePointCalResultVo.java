package com.charge.api.web.vo.joylife.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-05 18:25
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceivablePointCalResultVo implements Serializable {

    private static final long serialVersionUID = 8668205371000916678L;

    /**
     * 资产维度算分结果
     */
    List<ReceivablePointCalResultItemVo> items;





}
