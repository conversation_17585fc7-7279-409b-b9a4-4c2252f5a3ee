package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/5/16
 */
@Data
@Builder
public class DepositItemVO {
    
    private String id;
    
    private String itemUuid;
    
    private String itemName;
    
    private String typeUuid;
    
    private String typeName;
    
    private String billingId;
    
    private String billingName;
    
    private String billingTypeId;
    
    private String billingTypeName;
    
    private String memo;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Timestamp effectiveTime;
    
    private String taxPoint;
    
    private BigDecimal taxDec;
    
    private String meterType;
    
    private String meterTypeId;
    
    private String createUserId;
    
    private String createUser;
    
    private String dr;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Timestamp updateTs;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Timestamp createTs;
    
    private String unit;
    
    private String itemCode;
    
    private String status;
    
    private String communityUuid;
    
    private String commonConfigUuid;
    
    private String commonTaxRate;
    
    private String communityConfigUuid;
    
    private BigDecimal communityTaxRate;
    
    private String taxHeadName;
    
    private String isDiscount;
}


