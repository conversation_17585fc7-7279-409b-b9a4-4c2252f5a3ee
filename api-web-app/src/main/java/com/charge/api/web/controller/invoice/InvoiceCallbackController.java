package com.charge.api.web.controller.invoice;

import com.charge.api.web.convert.InvoiceConverter;
import com.charge.api.web.dto.invoice.ThirdPartyInvoiceApplyCallbackRequestDTO;
import com.charge.api.web.dto.invoice.ThirdPartyInvoiceCallbackRequestDTO;
import com.charge.api.web.dto.invoice.ThirdPartyInvoiceFailCallbackRequestDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.invoice.client.InvoiceClient;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @<PERSON> <PERSON><PERSON>
 * @Date: 2023/08/31/ 19:23
 * @description
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping(value = "/invoice")
public class InvoiceCallbackController {

    private final InvoiceClient invoiceClient;

    /**
     * 发票回写
     *
     * @param requestDTO
     * @return
     */
    @PostMapping(value = "/callback")
    public Map invoiceCallback(@RequestBody ThirdPartyInvoiceCallbackRequestDTO requestDTO) {
        ChargeResponse<Boolean> response;
        try {
            response = invoiceClient.invoiceCallback(InvoiceConverter.INSTANCE.convert2InvoiceCallbackRequestDTO(requestDTO));
        } catch (Exception e) {
            log.error("发票回写回调接口请求失败，失败详情:", e);
            response = new ChargeResponse<>();
            response.setCode(Integer.valueOf(ErrorInfoEnum.E1003.getCode()));
            response.setMessage(e.getMessage());
            response.setContent(Boolean.TRUE);
            return ImmutableMap.of("RESPONSE", response);
        }

        return ImmutableMap.of("RESPONSE", response);

    }

    /**
     * 业务单据上传回调接口
     * @param requestDTO
     * @return
     */
    @PostMapping(value = "/apply/callback")
    public Map invoiceApplyCallback(@RequestBody ThirdPartyInvoiceApplyCallbackRequestDTO requestDTO) {
        ChargeResponse<Boolean> response;
        try {
            response = invoiceClient.invoiceApplyCallback(InvoiceConverter.INSTANCE.convert2InvoiceApplyCallbackRequestDTO(requestDTO));
        } catch (Exception e) {
            log.error("业务单据上传回调接口请求失败，失败详情:", e);
            response = new ChargeResponse<>();
            response.setCode(Integer.valueOf(ErrorInfoEnum.E1003.getCode()));
            response.setMessage(e.getMessage());
            response.setContent(Boolean.TRUE);
            return ImmutableMap.of("RESPONSE", response);
        }

        return ImmutableMap.of("RESPONSE", response);
    }

    /**
     * 发票开具失败回调接口
     * @param requestDTO
     * @return
     */
    @PostMapping(value = "/fail/callback")
    public Map invoiceFailCallback(@RequestBody ThirdPartyInvoiceFailCallbackRequestDTO requestDTO) {
        ChargeResponse<Boolean> response;
        try {
            response = invoiceClient.invoiceFailCallback(InvoiceConverter.INSTANCE.convert2InvoiceFailCallbackRequestDTO(requestDTO));
        } catch (Exception e) {
            log.error("发票开具失败回调接口请求失败，失败详情:", e);
            response = new ChargeResponse<>();
            response.setCode(Integer.valueOf(ErrorInfoEnum.E1003.getCode()));
            response.setMessage(e.getMessage());
            response.setContent(Boolean.TRUE);
            return ImmutableMap.of("RESPONSE", response);
        }

        return ImmutableMap.of("RESPONSE", response);
    }
}
