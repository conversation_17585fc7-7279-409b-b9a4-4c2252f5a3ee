package com.charge.api.web.service.ebusiness;

import com.charge.api.web.vo.ebusiness.*;
import com.charge.common.exception.ChargeBusinessException;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/18 14:22
 */
public interface EBusinessOrderService {

    EBusinessPayBankInfo getEBusinessPayBankInfo(Long communityId) throws ChargeBusinessException;

    void paymentOrders(EBusinessPaymentOrderCondition condition) throws ChargeBusinessException;

    void refundOrders(EBusinessRefundOrderCondition condition) throws ChargeBusinessException;

    void writeOffOrders(EBusinessWriteOffOrderConditon condition) throws ChargeBusinessException;

    void finishOrders(EBusinessFinishOrderCondition condition) throws ChargeBusinessException;

    EBusinessPayBankInfo paymentFurnishOrders(EBusinessFurnishPaymentOrderCondition condition) throws ChargeBusinessException;

    void acceptanceOrders(EBusinessAcceptanceOrderConditon condition) throws ChargeBusinessException;

    String emsRefundOrders(EBusinessEmsRefundOrderCondition condition) throws ChargeBusinessException;

}
