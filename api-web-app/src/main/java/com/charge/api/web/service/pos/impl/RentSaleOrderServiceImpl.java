package com.charge.api.web.service.pos.impl;

import com.charge.api.web.service.pos.RentSaleOrderService;
import com.charge.api.web.vo.pos.rent.RentSaleOrderDetailQuery;
import com.charge.api.web.vo.pos.rent.RentSaleOrderQuery;
import com.charge.api.web.vo.pos.rent.RentSaleOrderVO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.order.client.EBusinessBaseOrderClient;
import com.charge.order.dto.rent.ExtendBaseOrderDTO;
import com.charge.order.dto.rent.ExtendOrderDetailQueryDTO;
import com.charge.order.dto.rent.ExtendOrderQueryDTO;
import com.charge.order.enums.EBusinessOrderPayStatusEnum;
import com.charge.order.enums.OrderSourceEnum;
import lombok.RequiredArgsConstructor;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/12/11 14:18
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RentSaleOrderServiceImpl implements RentSaleOrderService {

    private final EBusinessBaseOrderClient eBusinessBaseOrderClient;

    @Override
    public List<RentSaleOrderVO> listOrder(RentSaleOrderQuery rentSaleOrderQuery) throws ChargeBusinessException {
        ExtendOrderQueryDTO extendOrderQueryDTO = BeanCopierWrapper.copy(rentSaleOrderQuery, ExtendOrderQueryDTO.class);
        extendOrderQueryDTO.setPayStatusList(convertPayStatus(rentSaleOrderQuery.getPayStatus()));
        extendOrderQueryDTO.setSource(OrderSourceEnum.LEASE.name());
        List<ExtendBaseOrderDTO> rentSaleOrders = AppInterfaceUtil.getResponseDataThrowException(eBusinessBaseOrderClient.rollList(extendOrderQueryDTO));
        return BeanCopierWrapper.copy(rentSaleOrders, RentSaleOrderVO.class);
    }

    @Override
    public RentSaleOrderVO orderDetail(RentSaleOrderDetailQuery detailQuery) throws ChargeBusinessException {
        ExtendOrderDetailQueryDTO detailQueryDTO = BeanCopierWrapper.copy(detailQuery, ExtendOrderDetailQueryDTO.class);
        ExtendBaseOrderDTO rentSaleOrder = AppInterfaceUtil.getResponseDataThrowException(eBusinessBaseOrderClient.detail(detailQueryDTO));
        return BeanCopierWrapper.copy(rentSaleOrder, RentSaleOrderVO.class);
    }

    private List<Integer> convertPayStatus(Integer payStatus) {
        if(payStatus == 0){
            return Lists.newArrayList(EBusinessOrderPayStatusEnum.TO_PAY.getCode(), EBusinessOrderPayStatusEnum.PARTIAL_PAID.getCode());
        }
        return Lists.newArrayList(EBusinessOrderPayStatusEnum.PAID.getCode());
    }
}
