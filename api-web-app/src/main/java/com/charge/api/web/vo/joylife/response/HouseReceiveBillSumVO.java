package com.charge.api.web.vo.joylife.response;

import com.alibaba.fastjson.JSONObject;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.maindata.pojo.dto.HouseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 房间房间欠费信息
 */
@Data
public class HouseReceiveBillSumVO implements Serializable {
    /**
     * 房间朝昔ｉＤ
     */
    @ApiModelProperty(value = "房屋运营平台ID")
    private String houseMsId;
    /**
     * 房屋ID
     */
    @ApiModelProperty(value = "房屋ID")
    private Long houseId;
    /**
     * 房屋名称
     */
    @ApiModelProperty(value = "房屋名称")
    private String houseName;
    /**
     * 房屋编码
     */
    @ApiModelProperty(value = "房屋编码")
    private String houseCode;
    /**
     * houseType
     */
    @ApiModelProperty(value = "房屋类型")
    private String houseType;
    /**
     * 房屋欠费金额
     */
    @ApiModelProperty(value = "房屋欠费金额")
    private String houseTotalArrearsAmount;
    /**
     * 房屋托收中金额
     */
    @ApiModelProperty(value = "房屋托收中金额")
    private String houseTotalCollectAmount;
    /**
     * 房屋预存余额
     */
    @ApiModelProperty(value = "房屋预存余额")
    private String housePrestoreBalance;
    /**
     * 房屋冻结余额
     */
    @ApiModelProperty(value = "房屋冻结余额")
    private String houseHangUpAmount;
    /**
     * 账龄
     */
    @ApiModelProperty(value = "账龄")
    private int arrearsAge;
    /**
     *  房屋欠费详情
     */
    private List<ReceivableBillMonthVO> arrearsBillList;
    /**
     * 房屋挂起详情
     */
    private List<ReceivableBillMonthVO> hangUpBillList;

    public static HouseReceiveBillSumVO from(HouseDTO item, List<ReceivableBillDTO> orderItemBSList) {
        HouseReceiveBillSumVO houseOrderItemArrears = new HouseReceiveBillSumVO();
        StringBuilder houseName = new StringBuilder();
        houseName.append(item.getHouseName());
        List<ReceivableBillMonthVO> billList = new ArrayList<>();
        List<ReceivableBillMonthVO> hangupBillList = new ArrayList<>();
        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal collectAmount = BigDecimal.ZERO;
        BigDecimal handUpAmount = BigDecimal.ZERO;
        int arrearsAge = 0;
        if (!CollectionUtils.isEmpty(orderItemBSList)) {
            Optional<BigDecimal> collectAmountOptional = orderItemBSList.stream()
                    .filter(orderItemBS -> orderItemBS.getPayStatus().equals(ReceivableBillPayStatusEnum.COLLECTION.getCode()))
                    .map(orderItemBS -> (orderItemBS.getArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getArrearsAmount()).add(orderItemBS.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getPenaltyArrearsAmount()))
                    .reduce(BigDecimal::add);
            collectAmount = collectAmountOptional.isPresent() ? collectAmountOptional.get() : BigDecimal.ZERO;
            Optional<BigDecimal> amountOptional = orderItemBSList.stream()
                    .filter(orderItemBS -> orderItemBS.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode()))
                    .map(orderItemBS -> (orderItemBS.getArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getArrearsAmount()).add(orderItemBS.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getPenaltyArrearsAmount()))
                    .reduce(BigDecimal::add);
            amount = amountOptional.isPresent() ? amountOptional.get() : BigDecimal.ZERO;
            Optional<BigDecimal> hangupAmountOptional = orderItemBSList.stream()
                    .filter(orderItemBS -> orderItemBS.getBillStatus().equals(ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                    .map(orderItemBS -> (orderItemBS.getArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getArrearsAmount()).add(orderItemBS.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getPenaltyArrearsAmount()))
                    .reduce(BigDecimal::add);
            handUpAmount = hangupAmountOptional.isPresent() ? hangupAmountOptional.get() : BigDecimal.ZERO;
            List<ReceivableBillDTO> arrearsOrderList = orderItemBSList.stream()
                    .filter(orderItemBS -> orderItemBS.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                            || orderItemBS.getPayStatus().equals(ReceivableBillPayStatusEnum.COLLECTION.getCode()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(orderItemBSList)) {
                ReceivableBillDTO minOrder = orderItemBSList.stream()
                        .filter(orderItemBS -> StringUtils.isNotBlank(orderItemBS.getBelongYears())).min(Comparator.comparing(ReceivableBillDTO::getBelongYears)).get();
                String belongYear = minOrder.getBelongYears();
                try {
                    //arrearsAge= DateUtils.getMonth(belongYear, YearMonth.now().toString());
                } catch (Exception e) {
                    arrearsAge = 0;
                }
            }
            List<ReceivableBillDTO> hangupOrderList = orderItemBSList.stream()
                    .filter(orderItemBS -> orderItemBS.getBillStatus().equals(ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                    .collect(Collectors.toList());
            billList = ReceivableBillMonthVO.from(arrearsOrderList);
            hangupBillList = ReceivableBillMonthVO.fromHangup(hangupOrderList);
        }
        billList.sort(Comparator.comparing(ReceivableBillMonthVO::getMonth).reversed());
        hangupBillList.sort(Comparator.comparing(ReceivableBillMonthVO::getMonth).reversed());
        houseOrderItemArrears.setHouseId(item.getId());
        houseOrderItemArrears.setHouseCode(item.getHouseCode());
        houseOrderItemArrears.setHouseMsId(item.getMsId());
        houseOrderItemArrears.setHouseName(houseName.toString());
        houseOrderItemArrears.setArrearsBillList(billList);
        houseOrderItemArrears.setHangUpBillList(hangupBillList);
        houseOrderItemArrears.setHouseTotalArrearsAmount(amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        houseOrderItemArrears.setHouseTotalCollectAmount(collectAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        houseOrderItemArrears.setHouseHangUpAmount(handUpAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        houseOrderItemArrears.setArrearsAge(arrearsAge);
        return houseOrderItemArrears;
    }


    public static HouseReceiveBillSumVO fromPrestore(HouseDTO item, List<ReceivableBillDTO> houseUuid, JSONObject prestore) {
        HouseReceiveBillSumVO houseOrderItemArrears = from(item, houseUuid);
        houseOrderItemArrears.setHousePrestoreBalance(prestore == null ? BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP).toString() : ((BigDecimal) prestore.get("totalMoneyAvailable")).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        return houseOrderItemArrears;
    }
}
