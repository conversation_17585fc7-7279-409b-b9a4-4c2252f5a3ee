package com.charge.api.web.controller;

import com.charge.api.web.vo.FileUploadVO;
import com.charge.bill.client.UploadFileRecordClient;
import com.charge.bill.dto.UploadFileRecordDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.AliBucketEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.util.OssUtil;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.core.enums.LogCategoryEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 文件上传
 * <AUTHOR>
 * @description
 * @date 2023/01/06
 */
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FileUploadController {

    private final UploadFileRecordClient uploadFileRecordClient;

    @PostMapping("file/upload")
    public ChargeResponse<List<FileUploadVO>> batchUpload(@RequestParam("files") MultipartFile[] uploadFiles) throws IOException, ChargeBusinessException {

        List<UploadFileRecordDTO> uploadFileRecords = new ArrayList<>();

        for (MultipartFile multipartFile : uploadFiles) {
            if (multipartFile.isEmpty()) {
                log.warn("{}|上传文件为空", LogCategoryEnum.BUSSINESS);
                continue;
            }
            String originalFilename = multipartFile.getOriginalFilename();
            String fileKey = DateUtils.get14StrCurrentTime().concat("-").concat(originalFilename);
            OssUtil.putOssFile(null, AliBucketEnum.COMMON, multipartFile.getInputStream(), fileKey, false);

            UploadFileRecordDTO uploadFileRecordDO = new UploadFileRecordDTO();
            uploadFileRecordDO.setOriginalName(originalFilename);
            uploadFileRecordDO.setOssKey(fileKey);
            uploadFileRecords.add(uploadFileRecordDO);
        }
        if (CollectionUtils.isNotEmpty(uploadFileRecords)) {
            ChargeResponse<List<UploadFileRecordDTO>> chargeResponse = uploadFileRecordClient.batchSaveFileRecord(uploadFileRecords);
            List<FileUploadVO> fileUploadVOS = BeanCopierWrapper.copy(AppInterfaceUtil.getDataThrowException(chargeResponse), FileUploadVO.class);
            return new ChargeResponse(fileUploadVOS);
        } else {
            return new ChargeResponse(Collections.emptyList());
        }

    }


    @GetMapping("file/url")
    public ChargeResponse<String> getFileUrl(@Valid @NotBlank String fileKey) throws ChargeBusinessException {
        String ossFileUrl = OssUtil.getOssFileUrl(AliBucketEnum.COMMON, fileKey);
        return new ChargeResponse(ossFileUrl);
    }
}
