package com.charge.api.web.service.pos.impl;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.adapter.AssetAdapter;
import com.charge.api.web.adapter.HouseClientAdapter;
import com.charge.api.web.config.AssetPayGrayConfig;
import com.charge.api.web.constants.BillChargeTypeIdEnum;
import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.convert.ChargeBillConverter;
import com.charge.api.web.service.bill.pos.*;
import com.charge.api.web.service.order.*;
import com.charge.api.web.service.pos.ChargeBillService;
import com.charge.api.web.support.*;
import com.charge.api.web.util.FeignUtil;
import com.charge.api.web.util.ShardingUtil;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.lakala.*;
import com.charge.api.web.vo.pos.*;
import com.charge.bill.client.*;
import com.charge.bill.dto.LockObjectDTO;
import com.charge.bill.dto.ReceiptPrintRecordDTO;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.dto.domain.response.CreateBillsResponse;
import com.charge.bill.dto.income.*;
import com.charge.bill.dto.income.pos.AssetBillPosQueryDTO;
import com.charge.bill.dto.predeposit.*;
import com.charge.bill.enums.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ChargeStatusEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.enums.redis.RedisKeyCommonEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.AssertUtils;
import com.charge.common.util.DateUtils;
import com.charge.config.constant.CommonChargeItem;
import com.charge.invoice.client.InvoiceSellerConfigClient;
import com.charge.invoice.dto.SellerDTO;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.*;
import com.charge.order.enums.OrderRuleCategoryEnum;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.charge.api.web.support.AssetSupport.getAssetFullName;
import static com.charge.common.util.DateUtils.FORMAT_0;
import static com.charge.common.util.DateUtils.FORMAT_14;

/**
 * [收费项2.0 pos账单服务接口]
 *
 * <AUTHOR>
 * @date 2023/02/28
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RefreshScope
public class ChargeBillServiceImpl implements ChargeBillService {

    @Value("${pos.bill.query.time:true}")
    private Boolean posBillQueryTime;
    @Value("${pos.bill.query.year:1}")
    private Integer posBillQueryYear;

    private final AssetTransactionClient assetTransactionClient;

    private final HouseClientAdapter houseClientAdapter;

    private final ReceiptInfoClient receiptInfoClient;

    private final ReceivableBillClient receivableBillClient;

    private final PosCreateOrderService posCreateOrderService;

    private final CommunityClient communityClient;

    private final IncomeBillClient incomeBillClient;

    private final AssetClient assetClient;

    private static final String ORDER_SOURCE = "收费系统";

    private final PredepositRefundClient predepositRefundClient;

    private final PredepositAccountClient predepositAccountClient;

    private final PredepositAdjustClient predepositAdjustClient;

    private final PredepositBillClient predepositBillClient;
    private final CommunitySupport communitySupport;

    private final AssetSupport assetSupport;
    private final BankCollectionCheckSupport bankCollectionCheckSupport;

    private final AssetBillInfoClient assetBillInfoClient;
    private final AssetPayGrayConfig assetPayGrayConfig;
    private final AddNewPayOrderService addNewPayOrderService;
    private final AddNewDepositService addNewDepositService;
    private final AddNewTempChargeService addNewTempChargeService;
    private final AddCommunityTempService addCommunityTempService;
    private final AddNewCommunityTempChargeService addNewCommunityTempChargeService;
    private final AddBatchPayOrderService addBatchPayOrderService;

    private final PrePayLockSupport prePayLockSupport;
    private final InvoiceSellerConfigClient invoiceSellerConfigClient;


    /**
     * 1.0的支付方式（0表示POS,1表示转账,2表示支票,3表示支付宝,4表示微信,6员工代付）
     */
    private static final List<String> SUPPORT_PAY_METHODS_OLD = Lists.newArrayList("0", "3", "4");


    public static final List<Integer> PREDEPOSIT_BILL_BUSINESS_TYPES =Lists.newArrayList(BusinessTypeEnum.PREDEPOSIT_PAY.getCode(),BusinessTypeEnum.PREDEPOSIT_ADJUST.getCode(),
            BusinessTypeEnum.PREDEPOSIT_DEDUCT.getCode(),BusinessTypeEnum.PREDEPOSIT_REFUND.getCode(),BusinessTypeEnum.PAY_DEPOSIT.getCode());

    public static final List<Integer> ORDER_BUSINESS_TYPES =Lists.newArrayList(BusinessTypeEnum.TEMP_PAY.getCode());

    @Override
    public ChargePageResponse<List<PosBillMonthVO>> listPosBill(PosBillQueryCondition conditionVO) throws ChargeBusinessException {
        List<PosBillMonthVO> posBillMonthVos = Lists.newArrayList();
        //查询账单服务
        PosIncomeConditionDTO posIncomeCondition = ChargeBillConverter.INSTANCE.buildPosIncomeConditionDTO(conditionVO);
        if (posBillQueryTime && Objects.isNull(conditionVO.getAssetId())) {
            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();
            posIncomeCondition.setBeginTime(Timestamp.valueOf(currentTime.minusYears(posBillQueryYear)));
            posIncomeCondition.setEndTime(Timestamp.valueOf(currentTime));
        }
        ChargeResponse<PagingDTO<PosIncomeBillDTO>> posPageResp = assetTransactionClient.getPosBillList(posIncomeCondition);
        PagingDTO<PosIncomeBillDTO> posPage = AppInterfaceUtil.getResponseDataThrowException(posPageResp);
        int totalPage = (posPage.getTotalCount() + posPage.getPageSize() - 1) / posPage.getPageSize();
        List<PosIncomeBillDTO> bills = posPage.getList();
        if (CollectionUtils.isEmpty(bills)) {
            return new ChargePageResponse<>(posBillMonthVos, conditionVO.getPageSize(), totalPage,conditionVO.getCurrentPage(), posPage.getTotalCount());
        }
        //资产信息查询
        List<Long> assetIds = bills.stream().map(PosIncomeBillDTO::getAssetId).distinct().collect(Collectors.toList());
        List<AssetDTO> assetDTOList = houseClientAdapter.getAssetByIds(assetIds);
        List<SubjectSearch> subjectSearchList = assetDTOList.stream().map(AssetAdapter::toSubjectSearch).collect(Collectors.toList());
        Map<String, SubjectSearch> assetSubjectSearchMap = subjectSearchList.stream().collect(Collectors.toMap(SubjectSearch::getSubjectId, e -> e));

        Map<String, List<PosBillVO>> monthBillMap = new HashMap<>();
        Map<String, Object> cacheCommunityMap = Maps.newHashMapWithExpectedSize(bills.size() * 2);

        List<Long> preDePositBillIds = bills.stream().flatMap(bill -> bill.getTransactionRelations().stream().filter
                (tr -> PREDEPOSIT_BILL_BUSINESS_TYPES.contains(tr.getBusinessType())).map(TransactionRelationDTO::getBusinessId)).collect(Collectors.toList());
        Map<Long,Integer> predepositBillIdToTypeMap=new HashMap<>(preDePositBillIds.size());
        if(!CollectionUtils.isEmpty(preDePositBillIds)){
            ChargeResponse<List<PredepositBillDTO>> preDePositBillResp = predepositBillClient.selectByCondition(PredepositBillListQueryDTO.builder().idList(preDePositBillIds).communityId(conditionVO.getCommunityId()).build());
            List<PredepositBillDTO> predepositBills = AppInterfaceUtil.getResponseDataThrowException(preDePositBillResp);
            if(!CollectionUtils.isEmpty(predepositBills)){
                predepositBills.forEach(predepositBillDTO -> predepositBillIdToTypeMap.put(predepositBillDTO.getId(),predepositBillDTO.getPredepositType()));
            }
        }
        for (PosIncomeBillDTO posIncomeBillDTO : bills) {
            String createMonth = DateUtils.format(posIncomeBillDTO.getCreateTime(), "yyyy-MM");
            PosBillVO posBillVO = ChargeBillConverter.INSTANCE.buildBillDetailVo(posIncomeBillDTO, assetSubjectSearchMap.get(String.valueOf(posIncomeBillDTO.getAssetId())));
            posBillVO.setCreateMonth(createMonth);
            List<PosBillVO> billVos = monthBillMap.computeIfAbsent(createMonth, e -> Lists.newArrayList());
            Set<String> billChargeTypeIdSet=new HashSet<>(16);
            for (TransactionRelationDTO transactionRelationDTO:posIncomeBillDTO.getTransactionRelations()){
                if (ORDER_BUSINESS_TYPES.contains(transactionRelationDTO.getBusinessType())) {
                    billChargeTypeIdSet.add(BillChargeTypeIdEnum.TEMPORARY.getType());
                } else if (PREDEPOSIT_BILL_BUSINESS_TYPES.contains(transactionRelationDTO.getBusinessType())) {
                    Integer predepositType = predepositBillIdToTypeMap.get(transactionRelationDTO.getBusinessId());
                    if (predepositType != null && predepositType.equals(PredepositTypeEnum.DEPOSIT.getCode())) {
                        billChargeTypeIdSet.add(BillChargeTypeIdEnum.DEPOSIT.getType());
                    } else {
                        billChargeTypeIdSet.add(BillChargeTypeIdEnum.PRESTORE.getType());
                    }
                } else {
                    billChargeTypeIdSet.add(BillChargeTypeIdEnum.ROUTINE.getType());
                }
            }
            if(StringUtils.hasText(conditionVO.getChargeTypeId())){
                posBillVO.setChargeTypeId(conditionVO.getChargeTypeId());
            } else if (billChargeTypeIdSet.size()==1) {
                posBillVO.setChargeTypeId(billChargeTypeIdSet.iterator().next());
            }else {
                posBillVO.setChargeTypeId(BillChargeTypeIdEnum.ROUTINE.getType());
            }
            posBillVO.setPrintTip(billChargeTypeIdSet.contains(BillChargeTypeIdEnum.PRESTORE.getType())
                    ||billChargeTypeIdSet.contains(BillChargeTypeIdEnum.DEPOSIT.getType()) ? 0 : 1);
            billVos.add(posBillVO);
            cacheCommunityMap.put(RedisKeyCommonEnum.ASSET_TRANSACTION_ID_MATCH_COMMUNITY.key(posIncomeBillDTO.getId()),posIncomeCondition.getCommunityId());
            cacheCommunityMap.put(RedisKeyCommonEnum.INCOME_BILL_ID_MATCH_COMMMUNITY.key(posIncomeBillDTO.getIncomeId()),posIncomeCondition.getCommunityId());
        }
        communitySupport.cacheCommunity(cacheCommunityMap);
        monthBillMap.forEach((month, list) -> {
            list.sort((a,b)->b.getCreateTime().compareTo(a.getCreateTime()));
            posBillMonthVos.add(new PosBillMonthVO(month, list));
        });
        posBillMonthVos.sort((a,b)->b.getMonth().compareTo(a.getMonth()));
        return new ChargePageResponse<>(posBillMonthVos, posPage.getPageSize(), totalPage, posPage.getPageNum(), posPage.getTotalCount());
    }

    @Override
    public Integer getLostPayNum(String mercid, String deviceInfo, String communityId) throws ChargeBusinessException {
        if(!StringUtils.hasText(communityId)){
           return 0;
        }
        ChargeResponse<Integer> response = assetTransactionClient.getPosLostPayNum(AssetBillPosQueryDTO.builder()
                .mercid(mercid)
                .deviceInfo(deviceInfo)
                .communityId(communityId)
                .startTime(DateUtils.format(DateUtils.addMinutes(DateUtils.getCurrentTimestamp(), -5), "yyyy-MM-dd HH:mm:ss"))
                .endTime(DateUtils.getDateStr())
                .build());
        return AppInterfaceUtil.getResponseDataThrowException(response);
    }

    @Override
    public ChargeResponse<List<LostPay>> queryLostPay(String mercid, String deviceInfo, String communityId) throws ChargeBusinessException {
        ChargeResponse<List<PosIncomeBillDTO>> response = assetTransactionClient.queryPosLostPay(AssetBillPosQueryDTO.builder()
                .mercid(mercid)
                .deviceInfo(deviceInfo)
                .communityId(communityId)
                .startTime(DateUtils.format(DateUtils.addMinutes(DateUtils.getCurrentTimestamp(), -5), "yyyy-MM-dd HH:mm:ss"))
                .endTime(DateUtils.getDateStr())
                .build());
        if (!response.isSuccess()) {
            return new ChargePageResponse(ChargeStatusEnum.FAIL.getCode(), "查询账单服务异常：" + response.getMessage());
        }
        ChargeResponse<List<LostPay>> chargeResponse = new ChargeResponse<>();
        if (CollectionUtils.isNotEmpty(response.getContent())) {
            Map<Long, List<PosIncomeBillDTO>> incomeIdMap = response.getContent().stream().collect(Collectors.groupingBy(PosIncomeBillDTO::getIncomeId));
            List<LostPay> lostPays = response.getContent().stream().map(bill ->
                    LostPay.builder()
                            .payId(nullStr(bill.getId()))
                            .mercid(mercid)
                            .buildingName(bill.getBuildingName())
                            .itemName(bill.getGoodsName())
                            .orderNo(bill.getOrderNum())
                            .unitName(bill.getUnitName())
                            .createTime(nullFormatDate(bill.getCreateTime()))
                            .price(bill.getMoney())
                            .payId(nullStr(bill.getIncomeId()))
                            .subjectName(bill.getAssetName())
                            .paymentMethod(transferToPayMethodV1(bill.getPaymentChannel()))
                            .type(incomeIdMap.get(bill.getIncomeId()).size() > 1 ? "1" : "0")
                            .tranSeqNo(nullStr(bill.getOutTransactionNo()))
                            .build()
            ).collect(Collectors.toList());
            chargeResponse.setContent(lostPays);
        }
        return chargeResponse;
    }

    /**
     * PayMethodV1  支付方式(0表示POS,1表示转账,2表示支票,3表示支付宝扫码,4表示微信扫码,5表示预存扣款,6表示员工代付,7表示银行托收,8表示减免,9表示润钱包,10权益性调整,11收入结转)
     * PaymentChannelEnum V2
     * BANK("BANK","银行"),
     * TRANSFER_OFFLINE("TRANSFER_OFFLINE","线下转帐"),
     * ALI_PAY("ALI","支付宝"),
     * WECHAT_PAY("WECHAT","微信"),
     * BANK_COLLECTION("BANK_COLLECTION","银行托收");
     */
    private Integer transferToPayMethodV1(String paymentChannelEnum) {
        if (PaymentChannelEnum.BANK.getPaymentChannel().equals(paymentChannelEnum)) {
            return 0;
        } else if (PaymentChannelEnum.ALI_PAY.getPaymentChannel().equals(paymentChannelEnum)) {
            return 3;
        } else if (PaymentChannelEnum.WECHAT_PAY.getPaymentChannel().equals(paymentChannelEnum)) {
            return 4;
        } else {
            return 3;
        }
    }


    @Override
    public ChargeResponse updateReceiptInfo(String id, String printer) throws ChargeBusinessException {
        ChargeResponse chargeResponse = receiptInfoClient.updateReceiptInfo(new ReceiptPrintRecordDTO(id, printer, 1, new Timestamp(System.currentTimeMillis())));
        return chargeResponse;
    }

    @Override
    public PaymentResponse createBill(ReceivableAndOrderBillCreateRequest request) throws ChargeBusinessException {
        //校验入参
        Assert.isTrue((new BigDecimal(request.getTotalPrice()).compareTo(new BigDecimal("********.99"))) < 0
                , "缴费总金额不能超过8位，请重新输入");
        String paymentMethod = request.getPaymentMethod();
        Assert.isTrue(SUPPORT_PAY_METHODS_OLD.contains(paymentMethod), "pos上只支持扫码和刷卡，其他暂不支持");
        // 查询房间是否禁用
        String houseUuid = request.getHouseUuid();
        ChargeResponse<AssetDTO> assetResponse = houseClientAdapter.getAssetById(Long.parseLong(houseUuid));
        AssetDTO asset = FeignUtil.getContent(assetResponse, "基础数据服务");
        Long communityId = assetSupport.getCommunityId(asset);
        //校验欠费
        List<Long> receivableBillIds = Arrays.stream(request.getPayItemIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(receivableBillIds) && bankCollectionCheckSupport.bankCollectingByAssetId(asset.getId())) {
            // 校验是否托收中
            throw new ChargeBusinessException(ErrorInfoEnum.E3042);
        }
        ChargeResponse<List<ReceivableBillDTO>> response = receivableBillClient.queryList(ReceivableConditionDTO.builder().ids(receivableBillIds).communityId(communityId).build());
        List<ReceivableBillDTO> receivableBills = FeignUtil.getContent(response, "账单服务");
        Assert.notEmpty(receivableBills, "不存在有效的欠费");

        for (ReceivableBillDTO receivableBill : receivableBills) {
            if (Objects.equals(receivableBill.getPayStatus(), ReceivableBillPayStatusEnum.COLLECTION.getCode())) {
                throw new ChargeBusinessException(ErrorInfoEnum.E3044);
            }
        }

        BigDecimal totalArrears = receivableBills.stream().map(receivableBill -> receivableBill.getArrearsAmount().add(receivableBill.getPenaltyArrearsAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if(totalArrears.compareTo(new BigDecimal(request.getArrearsPrice())) != 0){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002,String.format("欠费总金额与缴欠费总金额不符,欠费:%s,缴费:%s", totalArrears, request.getArrearsPrice()));
        }
        String orderNum = OrderNumGeneratorSupport.gen();
        LockObjectDTO lockObjectDTO = LockObjectDTO.builder().mark(orderNum)
                .payMember(request.getPayMember()).businessScene(BillPrePaySceneEnum.POS.getCode())
                .receiveBillIdList(receivableBills.stream().map(ReceivableBillDTO::getId).collect(Collectors.toSet()))
                .build();
        prePayLockSupport.lockAndClose(lockObjectDTO);
        // ------- POS缴费下单----
        if (assetPayGrayConfig.isOpenGray(communityId, "/addNewPayOrder")) {
            CreateBillResponse createBillResponse = addNewPayOrderService.createBill(request, asset, receivableBills);
            communitySupport.setAssetTransactionIdCommunityIdRelation(createBillResponse.getAssetTransactionId(), communityId);
            return new PaymentResponse(String.valueOf(createBillResponse.getAssetTransactionId()), createBillResponse.getOrderNum());
        }
        CreateOrderContext context = buildFromReceivableAndOrderBillCreateRequest(request);

        AssetBillBusiness assetBillBusiness = new AssetBillBusiness();
        context.setAssetBillBusinesses(Lists.newArrayList(assetBillBusiness));

        fillCommunityAndAssetBillBusiness(context, assetBillBusiness, asset);
        // 缴欠费
        fillReceivablePayBillBusiness(assetBillBusiness, receivableBills);
        // 订单类
        if (StringUtils.hasText(request.getTempChargeData())) {
            List<TempChargeVO> tempChargeVOS = JSON.parseArray(request.getTempChargeData(), TempChargeVO.class);
            fillOrderBillBusiness(assetBillBusiness, tempChargeVOS, OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_300);
        }
        context.setOrderNum(orderNum);
        try {
            //下单
            posCreateOrderService.createOrder(context);
        }catch (Exception ex){
            OrderNumGeneratorSupport.clear();
            prePayLockSupport.unlock(lockObjectDTO);
            throw ex;
        }
        Long transactionId = context.getAssetBillBusinesses().get(0).getAssetTransactionId();
        communitySupport.setAssetTransactionIdCommunityIdRelation(transactionId, communityId);
        return new PaymentResponse(String.valueOf(transactionId), context.getOrderNum());
    }

    private CreateOrderContext buildFromReceivableAndOrderBillCreateRequest(ReceivableAndOrderBillCreateRequest request) {
        CreateOrderContext context = CreateOrderContext.builder()
                .deviceInfo(request.getDeviceInfo())
                .mercid(request.getMercid())
                .assetType(AssetType.HOUSE)
                .memo(request.getMemo())
                .collectorId(request.getCollectorId())
                .collectorName(request.getCollectorName())
                .totalPrice(new BigDecimal(request.getTotalPrice()))
                .paymentMethod(toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())))
                .outTransactionNo(request.getBankTransactionNo())
                .payMember(request.getPayMember())
                .bankAccountNum(request.getBankAccountNum())
                .bankAccountUuid(request.getBankAccountUuid())
                .payHouseCount(1)
                .createTime(new Date())
                .build();
        fillPosChannel(context, request.getPaymentMethod());
        return context;
    }

    private void fillCommunityAndAssetBillBusiness(CreateOrderContext context, AssetBillBusiness assetBillBusiness, AssetDTO asset) {

        CommunityDTO communityDTO = new CommunityDTO();
        if (asset.getHouseDTO() != null) {
            HouseDTO houseDTO = asset.getHouseDTO();
            communityDTO.setId(houseDTO.getCommunityId());
            communityDTO.setName(houseDTO.getCommunityName());
        } else if (asset.getParkingSpaceDTO() != null) {
            ParkingSpaceDTO parkingSpaceDTO = asset.getParkingSpaceDTO();
            communityDTO.setId(parkingSpaceDTO.getCommunityId());
            communityDTO.setName(parkingSpaceDTO.getCommunityName());
        }
        context.setCommunity(communityDTO);
        assetBillBusiness.setAsset(asset);
        assetBillBusiness.setBillBusinesses(Lists.newArrayList());

    }

    private void fillOrderBillBusiness(AssetBillBusiness assetBillBusiness, List<TempChargeVO> tempChargeVOS, OrderRuleCategoryEnum orderRuleCategoryEnum) {
        tempChargeVOS.forEach(tempCharge -> {
            OrderBillBusiness orderBillBusiness = new OrderBillBusiness()
                    .setSubsetId(orderRuleCategoryEnum.getCode().longValue());
            orderBillBusiness.setMoney(new BigDecimal(tempCharge.getPrice()))
                    .setChargeItemId(Long.parseLong(tempCharge.getTempItemId()))
                    .setName(tempCharge.getItemName())
                    .setType(BusinessTypeEnum.TEMP_PAY);
            assetBillBusiness.getBillBusinesses().add(orderBillBusiness);
        });
    }

    public static void fillReceivablePayBillBusiness(AssetBillBusiness assetBillBusiness, List<ReceivableBillDTO> receivableBills) {
        receivableBills.forEach(receivableBill -> {
            if (receivableBill.getPenaltyArrearsAmount()!=null&&receivableBill.getPenaltyArrearsAmount().compareTo(BigDecimal.ZERO) > 0) {
                ReceivablePayBillBusiness penalty = new ReceivablePayBillBusiness()
                        .setBelongYears(receivableBill.getBelongYears())
                        .setReceivableBillId(receivableBill.getId())
                        .setChargeType(ChargeTypeEnum.PENALTY)
                        .setAssetUseStatus(receivableBill.getAssetUseStatus())
                        .setChargeObject(chargeObjEnumOfCode(receivableBill.getChargeObject()));
                penalty.setName(receivableBill.getItemName())
                        .setChargeItemId(receivableBill.getItemId())
                        .setMoney(receivableBill.getPenaltyArrearsAmount())
                        .setType(BusinessTypeEnum.NORMAL_PAY);
                assetBillBusiness.getBillBusinesses().add(penalty);
            }
            if (receivableBill.getArrearsAmount()!=null&&receivableBill.getArrearsAmount().compareTo(BigDecimal.ZERO) > 0) {
                ReceivablePayBillBusiness arrears = new ReceivablePayBillBusiness()
                        .setBelongYears(receivableBill.getBelongYears())
                        .setReceivableBillId(receivableBill.getId())
                        .setChargeType(ChargeTypeEnum.PRINCIPAL)
                        .setAssetUseStatus(receivableBill.getAssetUseStatus())
                        .setChargeObject(chargeObjEnumOfCode(receivableBill.getChargeObject()));
                arrears.setName(receivableBill.getItemName())
                        .setChargeItemId(receivableBill.getItemId())
                        .setMoney(receivableBill.getArrearsAmount())
                        .setType(BusinessTypeEnum.NORMAL_PAY);
                assetBillBusiness.getBillBusinesses().add(arrears);
            }
        });
    }


    public static ChargeObjEnum chargeObjEnumOfCode(Integer code) {
        return Arrays.stream(ChargeObjEnum.values()).filter(chargeObjEnum -> chargeObjEnum.getCode().equals(code))
                .findFirst().orElseThrow(() -> new ChargeRuntimeException("不支持的ChargeObject:" + code));
    }

    /**
     * 支付方式（0表示POS,1表示转账,2表示支票,3表示支付宝,4表示微信）
     */
    private PaymentMethodEnum toPaymentMethodEnum(Integer v1PaymentMethod) {
        switch (v1PaymentMethod) {
            case 0:
                return PaymentMethodEnum.CARD;
            case 3:
                return PaymentMethodEnum.ALIPAY;
            case 4:
                return PaymentMethodEnum.WECHAT;
            default:
                throw new ChargeRuntimeException("只支持刷卡和扫码");
        }
    }

    private PaymentChannelEnum toPaymentChannelEnum(Integer paymentMethod) throws ChargeBusinessException {
        switch (paymentMethod) {
            case 0:
                return PaymentChannelEnum.BANK;
            case 3:
                return PaymentChannelEnum.ALI_PAY;
            case 4:
                return PaymentChannelEnum.WECHAT_PAY;
            default:
                throw new ChargeBusinessException("只支持刷卡和扫码");
        }
    }


    @Override
    public PaymentResponse createBill(CommunityOrderBillCreateRequest request) throws ChargeBusinessException {
        //校验入参
        Assert.isTrue((new BigDecimal(request.getAmount()).compareTo(new BigDecimal("********.99"))) < 0
                , "缴费总金额不能超过8位，请重新输入");
        String paymentMethod = request.getPaymentMethod();
        Assert.isTrue(SUPPORT_PAY_METHODS_OLD.contains(paymentMethod), "pos上只支持扫码和刷卡，其他暂不支持");

        ChargeResponse<CommunityDTO> response = communityClient.oneByCondition(CommunityCondition.builder()
                .id(Long.valueOf(request.getOwnerId())).build());
        CommunityDTO communityDTO = FeignUtil.getContent(response, "基础数据服务");
        Assert.notNull(communityDTO, "项目不存在");
        ChargeResponse<AssetDTO> communityVirtualHouseResp = assetClient.getCommunityVirtualHouse(communityDTO.getId());
        AssetDTO assetDTO = AppInterfaceUtil.getDataThrowException(communityVirtualHouseResp);
        //  灰度
        if (assetPayGrayConfig.isOpenGray(communityDTO.getId(), "/addCommunityTempChargeInfo")) {
            CreateBillResponse createBillResponse = addCommunityTempService.createBill(request, communityDTO, assetDTO);
            return new PaymentResponse(String.valueOf(createBillResponse.getAssetTransactionId()), createBillResponse.getOrderNum());
        }
        CreateOrderContext context = buildFromCommunityOrderBillCreateRequest(request);
        context.setCommunity(communityDTO);

        AssetBillBusiness assetBillBusiness = new AssetBillBusiness();
        assetBillBusiness.setAsset(assetDTO);
        assetBillBusiness.setBillBusinesses(Lists.newArrayList());
        context.setAssetBillBusinesses(Lists.newArrayList(assetBillBusiness));

        List<TempChargeVO> tempChargeVOS = Lists.newArrayList(new TempChargeVO().setTempItemId(request.getChargeItemUuid()).setItemName(request.getItemName()).setPrice(request.getAmount()));

        fillOrderBillBusiness(assetBillBusiness, tempChargeVOS, OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_301);
        //下单
        posCreateOrderService.createOrder(context);
        return new PaymentResponse(String.valueOf(context.getAssetBillBusinesses().get(0).getAssetTransactionId()), context.getOrderNum());
    }

    private void validateHouseReceivableItems( List<HouseReceivableItems> houseReceivableItems){
        for(HouseReceivableItems houseReceivableItem: houseReceivableItems){
            AssertUtils.isTrue(new BigDecimal(houseReceivableItem.getArrearsPrice()).compareTo(BigDecimal.ZERO)>0,houseReceivableItem.getHouseId()+"房屋欠费必须大于0");
            for (ChargeItemPrice chargeItemPrice: houseReceivableItem.getItemList()){
                AssertUtils.isTrue(new BigDecimal(chargeItemPrice.getPrice()).compareTo(BigDecimal.ZERO)>0,chargeItemPrice.getItemName()+"欠费必须大于0");
            }
        }
    }

    @Override
    public PaymentResponse createBill(BatchBillCreateRequest request) throws ChargeBusinessException {
        //校验入参
        Assert.isTrue((new BigDecimal(request.getArrearsPrice()).compareTo(new BigDecimal("********.99"))) < 0
                , "缴费总金额不能超过8位，请重新输入");
        String paymentMethod = request.getPaymentMethod();
        Assert.isTrue(SUPPORT_PAY_METHODS_OLD.contains(paymentMethod), "pos上只支持扫码和刷卡，其他暂不支持");

        ChargeResponse<CommunityDTO> response = communityClient.oneByCondition(CommunityCondition.builder()
                .id(Long.valueOf(request.getCommunityId())).build());
        CommunityDTO communityDTO = FeignUtil.getContent(response, "基础数据服务");
        Assert.notNull(communityDTO, "项目不存在");
        String chargeData = request.getChargeData();
        List<HouseReceivableItems> houseReceivableItems = JSON.parseArray(chargeData, HouseReceivableItems.class);
        validateHouseReceivableItems(houseReceivableItems);
        //校验欠费
        List<Long> receivableBillIds = houseReceivableItems.stream().flatMap(houseReceivableItem -> houseReceivableItem.getItemList().stream()
                .filter(chargeItemPrice -> new BigDecimal(chargeItemPrice.getPrice()).compareTo(BigDecimal.ZERO) > 0).map(ChargeItemPrice::getItemId))
                .distinct().map(Long::parseLong).collect(Collectors.toList());

        List<Long> assetIds = houseReceivableItems.stream().map(HouseReceivableItems::getHouseId)
                .distinct().map(Long::parseLong).collect(Collectors.toList());

        // 校验资产列表是否在托收中
        if (bankCollectionCheckSupport.bankCollectingByAssetIds(assetIds)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E3042);
        }
        ChargeResponse<List<ReceivableBillDTO>> receivableResponse = receivableBillClient.queryList(ReceivableConditionDTO.builder().communityId(communityDTO.getId()).ids(receivableBillIds).build());
        List<ReceivableBillDTO> receivableBills = FeignUtil.getContent(receivableResponse, "账单服务");
        Assert.notEmpty(receivableBills, "不存在有效的欠费");
        for (ReceivableBillDTO receivableBill : receivableBills) {
            if (Objects.equals(receivableBill.getPayStatus(), ReceivableBillPayStatusEnum.COLLECTION.getCode())) {
                throw new ChargeBusinessException(ErrorInfoEnum.E3044);
            }
        }
        Map<Long, ReceivableBillDTO> receivableMap = receivableBills.stream().collect(Collectors.toMap(ReceivableBillDTO::getId, a -> a, (a, b) -> b));
        List<AssetDTO> assets = null;
        try {
            assets = houseClientAdapter.getAssetByIds(assetIds);
        } catch (ChargeBusinessException e) {
            throw new ChargeRuntimeException(e);
        }
        Map<Long, AssetDTO> assetMap = assets.stream().collect(Collectors.toMap(AssetDTO::getId, a -> a, (a, b) -> b));
        Assert.isTrue(assets.size() == assetIds.size(), "部分资产不存在");
        String orderNum = OrderNumGeneratorSupport.gen();
        LockObjectDTO lockObjectDTO = LockObjectDTO.builder().mark(orderNum)
                .payMember(request.getPayMember()).businessScene(BillPrePaySceneEnum.POS.getCode())
                .receiveBillIdList(receivableBills.stream().map(ReceivableBillDTO::getId).collect(Collectors.toSet()))
                .build();
        prePayLockSupport.lockAndClose(lockObjectDTO);

        if (assetPayGrayConfig.isOpenGray(communityDTO.getId(),"/batchPay/addBatchPayOrder")) {
            CreateBillsResponse createBillsResponse = addBatchPayOrderService.createBills(request, communityDTO, assetMap, houseReceivableItems, receivableMap);
            return new PaymentResponse(String.valueOf(createBillsResponse.getAssetTransactionIds().get(0)), createBillsResponse.getOrderNum());
        }
        CreateOrderContext context = buildFromBatchBillCreateRequest(request, houseReceivableItems);
        context.setCommunity(communityDTO);
        List<AssetBillBusiness> assetBillBusinesses = houseReceivableItems.stream().map(houseReceivableItem -> {
            AssetBillBusiness assetBillBusiness = new AssetBillBusiness();
            assetBillBusiness.setAsset(assetMap.get(Long.parseLong(houseReceivableItem.getHouseId())));
            assetBillBusiness.setBillBusinesses(Lists.newArrayList());
            List<ReceivableBillDTO> billList = houseReceivableItem.getItemList().stream().map(item -> receivableMap.get(Long.parseLong(item.getItemId()))).collect(Collectors.toList());
            fillReceivablePayBillBusiness(assetBillBusiness, billList);
            return assetBillBusiness;
        }).filter(houseReceivableItem -> CollectionUtils.isNotEmpty(houseReceivableItem.getBillBusinesses())).collect(Collectors.toList());

        context.setAssetBillBusinesses(assetBillBusinesses);
        context.setOrderNum(orderNum);
        try {
            posCreateOrderService.createOrder(context);
        }catch (Exception ex){
            OrderNumGeneratorSupport.clear();
            prePayLockSupport.unlock(lockObjectDTO);
            throw ex;
        }
        return new PaymentResponse(String.valueOf(context.getAssetBillBusinesses().get(0).getAssetTransactionId()), context.getOrderNum());
    }

//    private void checkArrears(List<HouseReceivableItems> houseReceivableItems, Map<Long, ReceivableBillDTO> receivableMap) {
//        Assert.isTrue(houseReceivableItems.size() == receivableMap.size(), "部分欠费不存在");
//        houseReceivableItems.forEach(houseReceivableItem -> {
//            houseReceivableItem.getItemList().forEach(chargeItemPrice -> {
//                ReceivableBillDTO receivableBillDTO = receivableMap.get(Long.parseLong(chargeItemPrice.getItemId()));
//                Assert.isTrue(receivableBillDTO.getArrearsAmount().add(receivableBillDTO.getPenaltyArrearsAmount()).compareTo(new BigDecimal(chargeItemPrice.getPrice())) == 0
//                        , String.format("收费项%s欠费金额发生了变化", chargeItemPrice.getItemName()));
//            });
//        });
//    }

    @Override
    public PaymentResponse createBill(CommunityOrdersBillCreateRequest request) throws ChargeBusinessException {
        //校验入参
        Assert.isTrue((new BigDecimal(request.getAmount()).compareTo(new BigDecimal("********.99"))) < 0
                , "缴费总金额不能超过8位，请重新输入");
        String paymentMethod = request.getPaymentMethod();
        Assert.isTrue(SUPPORT_PAY_METHODS_OLD.contains(paymentMethod), "pos上只支持扫码和刷卡，其他暂不支持");

        ChargeResponse<CommunityDTO> response = communityClient.oneByCondition(CommunityCondition.builder()
                .id(Long.valueOf(request.getOwnerId())).build());
        CommunityDTO communityDTO = FeignUtil.getContent(response, "基础数据服务");
        Assert.notNull(communityDTO, "项目不存在");
        ChargeResponse<AssetDTO> communityVirtualHouse = assetClient.getCommunityVirtualHouse(communityDTO.getId());
        AssetDTO assetDTO = AppInterfaceUtil.getDataThrowException(communityVirtualHouse);
        // pos小区批量临时收费下单
        if (assetPayGrayConfig.isOpenGray(communityDTO.getId(), "/addNewCommunityTempChargeInfo")) {
            CreateBillResponse createBillResponse = addNewCommunityTempChargeService.createBill(request, assetDTO);
            return new PaymentResponse(String.valueOf(createBillResponse.getAssetTransactionId()), createBillResponse.getOrderNum());
        }
        CreateOrderContext context = buildFromCommunityOrderBillCreateRequest(request);
        context.setCommunity(communityDTO);
        AssetBillBusiness assetBillBusiness = new AssetBillBusiness();
        assetBillBusiness.setAsset(assetDTO);
        assetBillBusiness.setBillBusinesses(Lists.newArrayList());
        context.setAssetBillBusinesses(Lists.newArrayList(assetBillBusiness));

        List<TempChargeVO> tempChargeVOS = JSON.parseArray(request.getTempChargeData(), TempChargeVO.class);

        fillOrderBillBusiness(assetBillBusiness, tempChargeVOS, OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_300);
        //下单
        posCreateOrderService.createOrder(context);
        return new PaymentResponse(String.valueOf(context.getAssetBillBusinesses().get(0).getAssetTransactionId()), context.getOrderNum());
    }

    private void fillPosChannel(CreateOrderContext context, String paymentMethod) {
        if (PayRelatedConstants.PAYMENT_METHOD_POS_CODE.equals(paymentMethod)) {
            context.setPaymentChannel(PaymentChannelEnum.BANK);
            context.setPaymentMethod(PaymentMethodEnum.CARD);
        } else if (PayRelatedConstants.PAYMENT_METHOD_ALIPAY_CODE.equals(paymentMethod)) {
            context.setPaymentChannel(PaymentChannelEnum.ALI_PAY);
            context.setPaymentMethod(PaymentMethodEnum.ALIPAY);
        } else if (PayRelatedConstants.PAYMENT_METHOD_WEIXIN_CODE.equals(paymentMethod)) {
            context.setPaymentChannel(PaymentChannelEnum.WECHAT_PAY);
            context.setPaymentMethod(PaymentMethodEnum.WECHAT);
        }
        context.setPaymentTerminal(PaymentTerminalEnum.POS);
    }


    private CreateOrderContext buildFromCommunityOrderBillCreateRequest(CommunityOrderBillCreateRequest request) {
        CreateOrderContext context = CreateOrderContext.builder()
                .deviceInfo(request.getDeviceInfo())
                .mercid(request.getMercid())
                .assetType(AssetType.HOUSE)
                .memo(request.getMemo())
                .collectorId(request.getCollectorId())
                .collectorName(request.getCollectorName())
                .totalPrice(new BigDecimal(request.getAmount()))
                .paymentMethod(toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())))
                .outTransactionNo(request.getBankTransactionNo())
                .payMember(request.getPayMember())
                .payHouseCount(1)
                .createTime(new Date())
                .build();
        fillPosChannel(context, request.getPaymentMethod());
        return context;
    }

    private CreateOrderContext buildFromAddDepositRequest(AddDepositRequest request,AssetDTO assetDTO) throws ChargeBusinessException {
        CreateOrderContext context = CreateOrderContext.builder()
                .deviceInfo(request.getDeviceInfo())
                .mercid(request.getMercid())
                .assetType(AssetType.HOUSE)
                .memo(request.getMemo())
                .collectorId(request.getCollectorId())
                .collectorName(request.getCollectorName())
                .totalPrice(request.getMoney())
                .goodsName(request.getItemName())
                .paymentMethod(toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())))
                .payMember(request.getPayMember())
                .payHouseCount(1)
                .createTime(new Date())
                .build();
        fillPosChannel(context, request.getPaymentMethod());
        if(assetDTO.getHouseDTO()!=null){
            context.setAssetType(AssetType.HOUSE);
        }else if(assetDTO.getParkingSpaceDTO()!=null){
            context.setAssetType(AssetType.PARKING_SPACE);
        }else {
            throw new ChargeBusinessException("资产无有效的房间或者车位信息");
        }
        return context;
    }

    private CreateOrderContext buildFromBatchBillCreateRequest(BatchBillCreateRequest request, List<HouseReceivableItems> houseReceivableItems) {
        CreateOrderContext context = CreateOrderContext.builder()
                .deviceInfo(request.getDeviceInfo())
                .mercid(request.getMercid())
                .assetType(AssetType.HOUSE)
                .memo(request.getMemo())
                .collectorId(request.getCollectorId())
                .collectorName(request.getCollectorName())
                .totalPrice(new BigDecimal(request.getTotalPrice()))
                .paymentMethod(toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())))
                .outTransactionNo(request.getBankTransactionNo())
                .payMember(request.getPayMember())
                .payMemberId(Long.parseLong(request.getCustomerId()))
                .payHouseCount(houseReceivableItems.size())
                .createTime(new Date())
                .build();
        fillPosChannel(context, request.getPaymentMethod());
        return context;
    }

    private CreateOrderContext buildFromCommunityOrderBillCreateRequest(CommunityOrdersBillCreateRequest request) {
        CreateOrderContext context = CreateOrderContext.builder()
                .deviceInfo(request.getDeviceInfo())
                .mercid(request.getMercid())
                .assetType(AssetType.HOUSE)
                .memo(request.getMemo())
                .collectorId(request.getCollectorId())
                .collectorName(request.getCollectorName())
                .totalPrice(new BigDecimal(request.getAmount()))
                .paymentMethod(toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())))
                .outTransactionNo(request.getBankTransactionNo())
                .bankAccountNum(request.getBankAccountNum())
                .bankAccountUuid(request.getBankAccountUuid())
                .payMember(request.getPayMember())
                .payHouseCount(1)
                .createTime(new Date())
                .build();
        fillPosChannel(context, request.getPaymentMethod());
        return context;
    }

    @Override
    public PaymentResponse createBill(OrdersBillCreateRequest request) throws ChargeBusinessException {
        //校验入参
        Assert.isTrue((new BigDecimal(request.getAmount()).compareTo(new BigDecimal("********.99"))) < 0
                , "缴费总金额不能超过8位，请重新输入");
        String paymentMethod = request.getPaymentMethod();
        Assert.isTrue(SUPPORT_PAY_METHODS_OLD.contains(paymentMethod), "pos上只支持扫码和刷卡，其他暂不支持");

        // 查询房间是否禁用
        ChargeResponse<AssetDTO> assetResponse = houseClientAdapter.getAssetById(Long.parseLong(request.getOwnerId()));
        AssetDTO asset = FeignUtil.getContent(assetResponse, "基础数据服务");
        CommunityDTO communityDTO = getCommunityDTOFromAsset(asset);
        if (assetPayGrayConfig.isOpenGray(communityDTO.getId(), "/addNewTempChargeInfo")) {
            CreateBillResponse response = addNewTempChargeService.createBill(request, asset);
            return new PaymentResponse(String.valueOf(response.getAssetTransactionId()), response.getOrderNum());
        }
        CreateOrderContext context = buildFromOrdersBillCreateRequest(request);
        context.setCommunity(communityDTO);

        AssetBillBusiness assetBillBusiness = new AssetBillBusiness();
        assetBillBusiness.setAsset(asset);
        assetBillBusiness.setBillBusinesses(Lists.newArrayList());
        context.setAssetBillBusinesses(Lists.newArrayList(assetBillBusiness));

        List<TempChargeVO> tempChargeVOS = JSON.parseArray(request.getTempChargeData(), TempChargeVO.class);
        fillOrderBillBusiness(assetBillBusiness, tempChargeVOS, OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_300);
        //下单
        posCreateOrderService.createOrder(context);
        return new PaymentResponse(String.valueOf(context.getAssetBillBusinesses().get(0).getAssetTransactionId()), context.getOrderNum());
    }

    private static CommunityDTO getCommunityDTOFromAsset(AssetDTO asset) {
        CommunityDTO communityDTO = new CommunityDTO();
        if (asset.getHouseDTO() != null) {
            HouseDTO houseDTO = asset.getHouseDTO();
            communityDTO.setId(houseDTO.getCommunityId());
            communityDTO.setName(houseDTO.getCommunityName());
        } else if (asset.getParkingSpaceDTO() != null) {
            ParkingSpaceDTO parkingSpaceDTO = asset.getParkingSpaceDTO();
            communityDTO.setId(parkingSpaceDTO.getCommunityId());
            communityDTO.setName(parkingSpaceDTO.getCommunityName());
        }
        return communityDTO;
    }

    private CreateOrderContext buildFromOrdersBillCreateRequest(OrdersBillCreateRequest request) {
        CreateOrderContext context = CreateOrderContext.builder()
                .deviceInfo(request.getDeviceInfo())
                .mercid(request.getMercid())
                .assetType(AssetType.HOUSE)
                .memo(request.getMemo())
                .collectorId(request.getCollectorId())
                .collectorName(request.getCollectorName())
                .totalPrice(new BigDecimal(request.getAmount()))
                .paymentMethod(toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())))
                .outTransactionNo(request.getBankTransactionNo())
                .payMember(request.getPayMember())
                .payHouseCount(1)
                .bankAccountUuid(request.getBankAccountUuid())
                .bankAccountNum(request.getBankAccountNum())
                .createTime(new Date())
                .build();
        fillPosChannel(context, request.getPaymentMethod());
        return context;
    }

    @Override
    public List<ReceivableV2> listArrearsByAssets(Long communityId, List<Long> assetIds) {
        List<ReceivableV2> bills = listReceivableByAssets(communityId, assetIds, Lists.newArrayList(),null,true).stream().map(this::buildReceivableV2)
                .filter(a -> a.getTotalArrearsPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        if (org.springframework.util.CollectionUtils.isEmpty(bills)) {
            log.warn("listReceivableBill result empty list");
        }
        return bills;
    }

    @Override
    public HouseReceivablesV3 listArrearsDetailByAssets(Long communityId, List<Long> assetIds, List<Long> chargeItemIds, String belongYearsEnd) throws ChargeBusinessException {
        List<ReceivableBillDTO> receivables = listReceivableByAssets(communityId, assetIds, chargeItemIds, belongYearsEnd,false);
        List<AssetDTO> assets = houseClientAdapter.getAssetByIds(assetIds);
        return buildHouseReceivables(receivables, assets);
    }

    private HouseReceivablesV3 buildHouseReceivables(List<ReceivableBillDTO> receivables, List<AssetDTO> assets) {
        Map<Long, AssetDTO> assetMap = assets.stream().collect(Collectors.toMap(AssetDTO::getId, a -> a, (a, b) -> b));
        List<HouseReceivableV3> houseReceivableV3s = receivables.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId)).entrySet().stream().filter(entry -> assetMap.containsKey(entry.getKey()))
                .map(entry -> {
                    Long houseId = entry.getKey();
                    AssetDTO assetDTO = assetMap.get(houseId);
                    List<ReceivableBillDTO> receivablesOfHouse = entry.getValue();
                    List<ChargeItemReceivablesV3> chargeItemReceivablesV3s = receivablesOfHouse.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getItemId)).entrySet().stream()
                            .map(entry1 -> {
                                List<ReceivableBillDTO> receivablesOfChargeItem = entry1.getValue();
                                BigDecimal penaltyArrearsAmount = receivablesOfChargeItem.stream().map(ReceivableBillDTO::getPenaltyArrearsAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                List<ReceivableV3> receivableV3s = receivablesOfChargeItem.stream().map(a -> new ReceivableV3(String.valueOf(a.getId()), String.valueOf(a.getId()), String.valueOf(a.getItemId()),a.getItemName(), a.getArrearsAmount().add(a.getPenaltyArrearsAmount())
                                        , a.getBelongYears(), a.getPenaltyArrearsAmount(), formatTimeStamp(a.getCreateTime()))).collect(Collectors.toList());
                                BigDecimal arrearsAmount = receivableV3s.stream().map(ReceivableV3::getPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                return new ChargeItemReceivablesV3(arrearsAmount, receivableV3s, penaltyArrearsAmount, receivablesOfChargeItem.get(0).getItemName());
                            }).collect(Collectors.toList());
                    HouseReceivableV3 houseReceivableV3 = new HouseReceivableV3();
                    houseReceivableV3.setOrderAmountList(chargeItemReceivablesV3s);
                    houseReceivableV3.setHouseId(String.valueOf(houseId));
                    if (assetDTO.getHouseDTO() != null) {
                        HouseDTO houseDTO = assetDTO.getHouseDTO();
                        houseReceivableV3.setHouseName(houseDTO.getHouseName());
                        houseReceivableV3.setHouseCode(houseDTO.getHouseCode());
                        houseReceivableV3.setBuildingName(houseDTO.getBuildingName());
                        houseReceivableV3.setCommunityName(houseDTO.getCommunityName());
                        houseReceivableV3.setUnitName(houseDTO.getUnitName());
                    } else if (assetDTO.getParkingSpaceDTO() != null) {
                        ParkingSpaceDTO parkingSpaceDTO = assetDTO.getParkingSpaceDTO();
                        houseReceivableV3.setHouseName(parkingSpaceDTO.getParkingSpaceName());
                        houseReceivableV3.setHouseCode(parkingSpaceDTO.getParkingSpaceCode());
                        houseReceivableV3.setCommunityName(parkingSpaceDTO.getCommunityName());
                        houseReceivableV3.setBuildingName("");
                        houseReceivableV3.setUnitName("");
                    }
                    return houseReceivableV3;
                }).collect(Collectors.toList());

        List<CustomerDTO> customerList = assetMap.values().stream().flatMap(assetDTO -> {
            HouseDTO houseDTO = assetDTO.getHouseDTO();
            if (houseDTO != null && houseDTO.getListCustomer() != null) {
                return houseDTO.getListCustomer().stream();
            } else if (assetDTO.getParkingSpaceDTO() != null && assetDTO.getParkingSpaceDTO().getListCustomer() != null) {
                return assetDTO.getParkingSpaceDTO().getListCustomer().stream();
            } else return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, List<CustomerDTO>> customerMap = customerList.stream().collect(Collectors.groupingBy(CustomerDTO::getId));
        List<CustomerDTO> customerListEqual = new ArrayList<>();
        customerMap.forEach((key,value)->{
            if(value.size() == assets.size()){
                customerListEqual.add(value.get(0));
            }
        });
        String userNames = customerListEqual.stream().map(CustomerDTO::getCustomerName).collect(Collectors.joining(","));
        houseReceivableV3s.sort(Comparator.comparing(HouseReceivableV3::getHouseCode));
        return new HouseReceivablesV3("", userNames,customerListEqual, houseReceivableV3s, assetMap.size(), "");
    }


    private String formatTimeStamp(Timestamp timestamp) {
        if (timestamp == null) {
            return "";
        }
        Date date = new Date(timestamp.getTime());
        return DateUtils.format(date, FORMAT_0);
    }

    private List<com.charge.bill.dto.ReceivableBillDTO> listReceivableByAssets(Long communityId, List<Long> assetIds, List<Long> chargeItemIds, String belongYearsEnd,Boolean queryCollection) {
        int pageNum = 1;
        ReceivableConditionDTO receivableCondition = new ReceivableConditionDTO();
        receivableCondition.setChargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode());
        receivableCondition.setCommunityId(communityId);
        receivableCondition.setAssetIdList(assetIds);
        receivableCondition.setBillType(0);
        receivableCondition.setPayStatuses(Lists.newArrayList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(), ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()));
        receivableCondition.setBillStatuses(Lists.newArrayList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode()));
        receivableCondition.setPageSize(500);
        if (!org.springframework.util.CollectionUtils.isEmpty(chargeItemIds)) {
            receivableCondition.setItemIdList(chargeItemIds);
        }
        if (StringUtils.hasText(belongYearsEnd)) {
            receivableCondition.setBelongYearsEnd(belongYearsEnd);
        }
        if(Boolean.TRUE.equals(queryCollection)){
            receivableCondition.getPayStatuses().add(ReceivalbleBillPayStatusEnum.COLLECTION.getCode());
        }
        Integer total = 0;
        List<com.charge.bill.dto.ReceivableBillDTO> totalBills = new ArrayList<>();
        do {
            receivableCondition.setPageNum(pageNum);
            ChargeResponse<PagingDTO<com.charge.bill.dto.ReceivableBillDTO>> response = receivableBillClient.queryByPage(receivableCondition);
            if (response.isSuccess()) {
                PagingDTO<com.charge.bill.dto.ReceivableBillDTO> pagingDTO = response.getContent();
                if (pagingDTO != null) {
                    List<com.charge.bill.dto.ReceivableBillDTO> dtoS = pagingDTO.getList();
                    if (!org.springframework.util.CollectionUtils.isEmpty(dtoS)) {
                        totalBills.addAll(dtoS);
                    }
                    total = pagingDTO.getTotalCount();
                }
            } else {
                throw new ChargeRuntimeException("receivableBillClient.queryByPage error:" + response.getMessage());
            }
        } while (pageNum++ < (total / 500 + 1));
        return totalBills;
    }

    private ReceivableV2 buildReceivableV2(com.charge.bill.dto.ReceivableBillDTO receivableBillDTO) {
        return new ReceivableV2(receivableBillDTO.getId(), receivableBillDTO.getItemName(), receivableBillDTO.getItemId(), receivableBillDTO.getArrearsAmount().add(receivableBillDTO.getPenaltyArrearsAmount()));

    }

    @Override
    public OrderDetailVO getOrderDetailByIncomeBill(Long incomeBillId, Long communityId) throws ChargeBusinessException {
        ShardingUtil.addCommunityId2ThreadContext(communityId);
        ChargeResponse<AssetTransactionDTO> assetTransactionResp = assetTransactionClient.getById(incomeBillId);
        AssetTransactionDTO assetTransaction = AppInterfaceUtil.getResponseDataThrowException(assetTransactionResp);
        if (assetTransaction == null || assetTransaction.getIncomeId() == null) {
            return null;
        }
        IncomeConditionDTO condition = IncomeConditionDTO.builder().idList(Lists.newArrayList(assetTransaction.getIncomeId())).communityId(assetTransaction.getCommunityId()).build();
        ChargeResponse<List<AssetIncomeBillDTO>> chargeResponse = incomeBillClient.selectIncomeWriteOffReceivableBill(condition);
        List<AssetIncomeBillDTO> assetIncomes = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        if (org.springframework.util.CollectionUtils.isEmpty(assetIncomes)) {
            return null;
        } else {
            return transferToOrderDetailVO(assetIncomes.get(0), incomeBillId);
        }
    }

    @Override
    public List<ReceiptVO> getReceiptsByIncomeBill(Long assetTransactionId, Long communityId) throws ChargeBusinessException {
        ShardingUtil.addCommunityId2ThreadContext(communityId);
        ChargeResponse<AssetTransactionDTO> assetTransactionResp = assetTransactionClient.getById(assetTransactionId);
        AssetTransactionDTO assetTransaction = AppInterfaceUtil.getResponseDataThrowException(assetTransactionResp);
        if (assetTransaction == null) {
            return Lists.newArrayList();
        }
        AssetTransactionConditionDTO condition = AssetTransactionConditionDTO.builder().incomeId(assetTransaction.getIncomeId()).communityId(String.valueOf(communityId)).build();
        ChargeResponse<List<AssetTransactionDTO>> chargeResponse = assetTransactionClient.selectTransactionWriteOffReceivableBillDetail(condition);
        List<AssetTransactionDTO> assetTransactions = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        if (org.springframework.util.CollectionUtils.isEmpty(assetTransactions)) {
            return Lists.newArrayList();
        } else {
            return transferToReceipts(assetTransactions);
        }
    }

    @Override
    public ReceiptVO getReceiptsByAssetTransactionId(Long assetTransactionId) throws ChargeBusinessException {
        // NOTE community_id 从缓存读取
        Long communityId = communitySupport.getCommunityId(assetTransactionId);
        ShardingUtil.addCommunityId2ThreadContext(communityId);
        ChargeResponse<List<AssetTransactionDTO>> chargeResponse = assetTransactionClient.selectTransactionWriteOffReceivableBillDetail(
                AssetTransactionConditionDTO.builder().idList(Lists.newArrayList(assetTransactionId)).communityId(String.valueOf(communityId)).build());
        List<AssetTransactionDTO> assetTransactions = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        if (org.springframework.util.CollectionUtils.isEmpty(assetTransactions)) {
            log.warn("getReceiptsByAssetTransactionId transactionDTO 不存在");
            return null;
        } else {
            return transferToReceipts(assetTransactions).get(0);
        }
    }

    public static String nullStr(String str) {
        return str == null ? "" : str;
    }

    public static String nullStr(BigDecimal big) {
        return big == null ? "" : big.toString();
    }

    public static String nullStr(Integer big) {
        return big == null ? "" : big.toString();
    }

    public static String nullStr(Long str) {
        return str == null ? "" : String.valueOf(str);
    }

    public static ReceiptVO buildReceiptVO(AssetTransactionDTO assetTransactionDTO, AssetBillInfoDTO assetBillInfo,
                                           IncomeBillDTO incomeBillDTO, CommunityDTO communityDTO,
                                           ReceiptPrintRecordDTO recordDTO, SellerDTO sellerDTO) {
        PaymentMethodEnum paymentMethod = PaymentMethodEnum.codeOf(assetTransactionDTO.getPaymentMethod());
        List<ReceivableItemV1> receivableItemV1s = Lists.newArrayList();
        ReceivableItemsV1 receivableItemsV1 = new ReceivableItemsV1(receivableItemV1s);
        if (!CollectionUtils.isEmpty(assetTransactionDTO.getWriteOffBillDTOList())) {
            List<ReceivableItemV1> receivableItemList = assetTransactionDTO.getWriteOffBillDTOList().stream().map(writeOffBillDTO ->
                            new ReceivableItemV1(writeOffBillDTO.getItemName(), writeOffBillDTO.getActualAmount(), writeOffBillDTO.getBelongYears()))
                    .collect(Collectors.toList());
            sort(receivableItemList);
            receivableItemV1s.addAll(receivableItemList);
        }

        if (!CollectionUtils.isEmpty(assetTransactionDTO.getPredepositBillDTOList())) {
            List<ReceivableItemV1> receivableItemList = assetTransactionDTO.getPredepositBillDTOList().stream().map(predepositBillDTO -> {
                String predepositItemName;
                if (1 == predepositBillDTO.getPredepositType()) {
                    predepositItemName = "预存—" + predepositBillDTO.getPredepositItemName();
                } else if (3 == predepositBillDTO.getPredepositType()) {
                    predepositItemName = "押金—" + predepositBillDTO.getPredepositItemName();
                } else {
                    predepositItemName = predepositBillDTO.getPredepositItemName();
                }
                return new ReceivableItemV1(predepositItemName, predepositBillDTO.getPredepositMoney(), "");
            }).collect(Collectors.toList());
            sort(receivableItemList);
            receivableItemV1s.addAll(receivableItemList);
        }

        if (!CollectionUtils.isEmpty(assetTransactionDTO.getOrderBillDetailList())) {
            List<ReceivableItemV1> receivableItemList = assetTransactionDTO.getOrderBillDetailList().stream().map(writeOffBillDTO ->
                            new ReceivableItemV1(writeOffBillDTO.getItemName(), writeOffBillDTO.getActualAmount(), nullFormatDate(writeOffBillDTO.getCreateTime())))
                    .collect(Collectors.toList());
            sort(receivableItemList);
            receivableItemV1s.addAll(receivableItemList);
        }

        return ReceiptVO.builder()
                .id(nullStr(assetTransactionDTO.getId()))
                .referNo(nullStr(incomeBillDTO.getBillNum()))
                .bankReferNo(nullStr(assetTransactionDTO.getBankTransactionNo()))
                .chargerId(nullStr(assetBillInfo.getOperatorId()))
                .chargerName(nullStr(assetBillInfo.getOperatorName()))
                .communityId(nullStr(assetTransactionDTO.getCommunityId()))
                .communityName(nullStr(assetTransactionDTO.getCommunityName()))
                .companyId(nullStr(communityDTO.getCityCom()))
                .companyName(Objects.nonNull(sellerDTO) ? nullStr(sellerDTO.getSellerName()) : nullStr(communityDTO.getLegalCompanyCname()))
                .payTime(nullFormatDate(assetTransactionDTO.getPaymentTime()))
                .payId(nullStr(incomeBillDTO.getId()))
                .payerId(nullStr(assetTransactionDTO.getOwnerId()))
                .payerName(nullStr(assetBillInfo.getPayMember()))
                .houseId(nullStr(assetTransactionDTO.getAssetId()))
                .payHouse(nullStr(assetTransactionDTO.getAssetName()))
                .payType(paymentMethod == null ? "" : paymentMethod.getValue())
                .payfee(assetTransactionDTO.getMoney())
                .chargeItemInfo(JSON.toJSONString(receivableItemsV1))
                .url("敬请期待!")
                .orderNo(assetTransactionDTO.getAssetOrderNum())
                .receiptNo(assetTransactionDTO.getAssetOrderNum())
                .printer(recordDTO != null ? recordDTO.getPrinter() : "")
                .printTimes(recordDTO != null ? recordDTO.getTimes() : 0)
                .createTime(DateUtils.formatToDateStr(FORMAT_0, assetTransactionDTO.getCreateTime()))
                .updateTime(DateUtils.formatToDateStr(FORMAT_0, assetTransactionDTO.getModifyTime()))
                .massBillId(nullStr(incomeBillDTO.getId()))
                .cropId("")
                .build();
    }

    private static void sort(List<ReceivableItemV1> receivableItemList){
        receivableItemList.sort(Comparator.comparing(ReceivableItemV1::getItemName).thenComparing((a,b)->b.getCreateTime().compareTo(a.getCreateTime())));
    }

    private List<ReceiptVO> transferToReceipts(List<AssetTransactionDTO> assetTransactions) throws ChargeBusinessException {
        Assert.notEmpty(assetTransactions,"转换收据失败，账单流水为空");
        AssetTransactionDTO assetTransaction1 = assetTransactions.get(0);
        CommunityCondition condition = CommunityCondition.builder().id(assetTransaction1.getCommunityId()).build();
        ChargeResponse<CommunityDTO> communityResponse = communityClient.oneByCondition(condition);
        CommunityDTO communityDTO = AppInterfaceUtil.getResponseDataThrowException(communityResponse);

        IncomeBillDTO incomeBillDTO = assetTransaction1.getIncomeBillDTO();
        ChargeResponse<ReceiptPrintRecordDTO> response = receiptInfoClient.getReceiptInfo(String.valueOf(assetTransaction1.getId()));
        ReceiptPrintRecordDTO recordDTO = AppInterfaceUtil.getResponseData(response);
        ChargeResponse<List<AssetBillInfoDTO>> assetBillsResp = assetBillInfoClient.list(assetTransactions.stream().map(AssetTransactionDTO::getId).collect(Collectors.toList()), assetTransaction1.getCommunityId());
        Map<Long, AssetBillInfoDTO> assetBillMap = AppInterfaceUtil.getResponseDataThrowException(assetBillsResp).stream().collect(Collectors.toMap(AssetBillInfoDTO::getAssetTransactionId, Function.identity(), (a, b) -> b));
        Assert.isTrue(assetTransactions.size()==assetBillMap.size(),"资产流水与资产账单数量不匹配");
        fillAssetFullName(assetTransactions);
        SellerDTO sellerDTO = AppInterfaceUtil.getResponseDataThrowException(invoiceSellerConfigClient.getSellerByCommunity(assetTransaction1.getCommunityId()));
        return assetTransactions.stream().map(assetTransactionDTO -> buildReceiptVO(assetTransactionDTO,
                assetBillMap.get(assetTransactionDTO.getId()), incomeBillDTO, communityDTO, recordDTO, sellerDTO)).collect(Collectors.toList());
    }

    private void fillAssetFullName(List<AssetTransactionDTO> assetTransactions) throws ChargeBusinessException {
        if(CollectionUtils.isEmpty(assetTransactions)){
            return;
        }
        List<Long> assetIds = assetTransactions.stream().map(AssetTransactionDTO::getAssetId).distinct().collect(Collectors.toList());
        ChargeResponse<List<AssetDTO>> listChargeResponse = assetClient.listAsset(AssetCondition.builder().ids(assetIds).build());
        Map<Long,AssetDTO> assetMap = AppInterfaceUtil.getResponseDataThrowException(listChargeResponse).stream().collect(Collectors.toMap(AssetDTO::getId,Function.identity(),(a,b)->b));
        assetTransactions.forEach(assetTransactionDTO ->
        {
            AssetDTO assetDTO = assetMap.get(assetTransactionDTO.getAssetId());
            if(assetDTO!=null){
                assetTransactionDTO.setAssetName(nullStr(getAssetFullName(assetDTO)));
            }
        });
    }

    public static String nullFormatDate(Timestamp timestamp) {
        if (timestamp == null) {
            return "";
        }
        return DateUtils.formatToDateStr(FORMAT_14, timestamp);
    }

    public static String nullFormatDate(Date date) {
        if (date == null) {
            return "";
        }
        return DateUtils.format(date, FORMAT_0);
    }

    private OrderDetailVO transferToOrderDetailVO(AssetIncomeBillDTO assetIncome, Long incomeBillId) throws ChargeBusinessException {
        List<AssetTransactionDTO> assetTransactionDTOList = assetIncome.getAssetTransactionDTOList();
        AssetTransactionDTO assetTransaction1 = assetTransactionDTOList.get(0);
        IncomeBillDTO incomeBillDTO = assetTransaction1.getIncomeBillDTO();
        ChargeResponse<ReceiptPrintRecordDTO> response = receiptInfoClient.getReceiptInfo(String.valueOf(incomeBillDTO.getId()));
        ReceiptPrintRecordDTO recordDTO = AppInterfaceUtil.getResponseData(response);

        List<PaymentItemVO> paymentItemVOS = assetIncome.getAssetTransactionDTOList().stream().flatMap(assetTransactionDTO -> assetTransactionDTO.getWriteOffBillDTOList().stream())
                .map(writeOffBillDTO -> new PaymentItemVO(writeOffBillDTO.getItemName(), writeOffBillDTO.getActualAmount(),
                        DateUtils.format(new Date(assetTransaction1.getCreateTime().getTime()), FORMAT_0))).collect(Collectors.toList());

        return OrderDetailVO.builder()
                .orderId(String.valueOf(incomeBillId))
                .communityName(assetIncome.getCommunityName())
                .paymentMethod(String.valueOf(transferPayMethodFromV2ToV1(assetTransaction1.getPaymentMethod())))
                .totalPrice(assetIncome.getIncomeMoney())
                .paymentName(transferPaymentNameFromV2ToV1(assetTransaction1.getPaymentMethod()))
                .customerName(assetIncome.getPayMember())
                .collectorId(incomeBillDTO.getCollectorId())
                .collectorName(incomeBillDTO.getCollectorName())
                .houseAmount(String.valueOf(assetTransactionDTOList.size()))
                .orderSource(ORDER_SOURCE)
                .memo(incomeBillDTO.getMemo())
                .createTime(DateUtils.format(assetTransaction1.getPaymentTime(), FORMAT_0))
                .bankTransactionNo(assetIncome.getOutTransactionNo())
                .orderNum(assetIncome.getOrderNum())
                .billName(assetIncome.getGoodsName())
                .billStatus(String.valueOf(transferBillStatusFromV2ToV1(assetTransaction1.getPayStatus())))
                .printTimes(recordDTO != null ? recordDTO.getTimes() : 0)
                .operationStatus(transferV2PayStatusToV1OperationStatus(incomeBillDTO.getPayStatus()))
                .itemList(paymentItemVOS)
                .build();

    }

    /**
     * 1.0   进行中   、正常  、异常
     */

    public String transferV2PayStatusToV1OperationStatus(Integer payStatus) {
        if (2 == payStatus) {
            return "异常";
        } else if (0 == payStatus || 3 == payStatus) {
            return "进行中";
        } else {
            return "正常";
        }

    }

    /**
     * 将2.0的支付状态转成1.0的账单状态
     * 1.0 `bill_status` char(1) NOT NULL DEFAULT '0' COMMENT '账单状态(0表示已完成,1待支付，2已关闭，3表示已作废，4已红冲)',
     * <p>
     * 2.0 (0:未支付，1:已支付，2:支付失败,3：待支付)
     */
    private static Integer transferBillStatusFromV2ToV1(Integer payStatus) {
        if (1 == payStatus) {
            return 0;
        } else {
            return 1;
        }
    }

    /**
     * 1.0payMethod 方式(0表示POS,1表示转账,2表示支票,3表示支付宝扫码,4表示微信扫码,5表示预存扣款.
     * 2.0payMethod    0银行，1转账，2支付宝，3微信，4银行托收，5预存扣款等
     */
    private static Integer transferPayMethodFromV2ToV1(Integer payMethodV2) {
        if (PaymentMethodEnum.CARD.getPaymentCode() == payMethodV2) {
            return 0;
        } else if (PaymentMethodEnum.TRANSFER_OFFLINE.getPaymentCode() == payMethodV2) {
            return 1;
        } else if (PaymentMethodEnum.ALIPAY.getPaymentCode() == payMethodV2) {
            return 3;
        } else if (PaymentMethodEnum.WECHAT.getPaymentCode() == payMethodV2) {
            return 4;
        } else if (PaymentMethodEnum.DEDUCT.getPaymentCode() == payMethodV2) {
            return 5;
        } else {
            return 0;
        }
    }

    private static String transferPaymentNameFromV2ToV1(Integer payMethodV2) {
        return Arrays.stream(PaymentMethodEnum.values()).filter(paymentMethodEnum -> paymentMethodEnum.getPaymentCode() == payMethodV2)
                .findFirst().orElse(PaymentMethodEnum.CARD).getPaymentMethod();
    }

    private CommunityDTO buildCommunityDTO(AssetDTO asset ) throws ChargeBusinessException {
        CommunityDTO communityDTO=new CommunityDTO();
        if(asset.getHouseDTO()!=null){
            HouseDTO houseDTO = asset.getHouseDTO();
            communityDTO.setId(houseDTO.getCommunityId());
            communityDTO.setName(houseDTO.getCommunityName());
        }else if(asset.getParkingSpaceDTO()!=null){
            ParkingSpaceDTO spaceDTO = asset.getParkingSpaceDTO();
            communityDTO.setId(spaceDTO.getCommunityId());
            communityDTO.setName(spaceDTO.getCommunityName());
        }else {
            throw new ChargeBusinessException("该资产无有效的房间或者车位信息");
        }
        return communityDTO;
    }

    @Override
    public PaymentResponse addDeposit(AddDepositRequest request, String URI) throws ChargeBusinessException {
        //校验入参
        Assert.isTrue((request.getMoney().compareTo(new BigDecimal("********.99"))) < 0&& (request.getMoney().compareTo(BigDecimal.ZERO)) > 0
                , "缴费总金额不能超过8位且大于0，请重新输入");
        String paymentMethod = request.getPaymentMethod();
        Assert.isTrue(SUPPORT_PAY_METHODS_OLD.contains(paymentMethod), "pos上只支持扫码和刷卡，其他暂不支持");
        AssetDTO asset;
        if (request.getHouseId() != null) {
            asset = houseClientAdapter.getAssetById(request.getHouseId()).getContent();
        } else {
            asset = houseClientAdapter.getCommunityVirtualDto(request.getCommunityUuid());
        }

        CommunityDTO community=buildCommunityDTO(asset);
        if (assetPayGrayConfig.isOpenGray(community.getId(), URI)) {
            CreateBillResponse createBillResponse = addNewDepositService.createBill(request, asset, URI);
            return new PaymentResponse(String.valueOf(createBillResponse.getAssetTransactionId()), createBillResponse.getOrderNum());
        }
        CreateOrderContext context = buildFromAddDepositRequest(request, asset);
        context.setCommunity(community);

        AssetBillBusiness assetBillBusiness = new AssetBillBusiness();
        assetBillBusiness.setAsset(asset);
        PredepositBillBusiness billBusiness=new PredepositBillBusiness();

        billBusiness.setPredepositType(PredepositTypeEnum.DEPOSIT.getCode())
                .setChargeItemId(request.getItemId())
                .setMoney(request.getMoney())
                .setName(request.getItemName())
                .setType(BusinessTypeEnum.PREDEPOSIT_PAY);
        assetBillBusiness.setBillBusinesses(Lists.newArrayList(billBusiness));
        context.setAssetBillBusinesses(Lists.newArrayList(assetBillBusiness));

        posCreateOrderService.createOrder(context);
        return new PaymentResponse(String.valueOf(context.getAssetBillBusinesses().get(0).getAssetTransactionId()), context.getOrderNum());

    }

    @Override
    public void preStoreRefund(@Valid PreStoreRefundRequest request) throws ChargeBusinessException {

        List<PredepositAccountDTO> preDepositAccounts = predepositAccountClient.listPredepositAccount(PredepositAccountConditionDTO.builder().communityId(request.getCommunityId())
                .assetIds(Collections.singletonList(request.getHouseId())).predepositTypes(com.google.common.collect.Lists.newArrayList(PredepositTypeEnum.COMMON_DEPOSIT.getCode()
                        ,PredepositTypeEnum.SPECIAL_DEPOSIT.getCode())).billType(PredepositBillTypeEnum.PRE_DEPOSIT.getCode()).chargeObj(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode()).build()).getContent();
        Map<Long, PredepositAccountDTO> itemIdToPreDepositAccountMap = preDepositAccounts.stream().collect(Collectors.toMap(PredepositAccountDTO::getPredepositItemId, a -> a,(a,b)->b));
        Optional<PredepositAccountDTO> commonPreDepositAccountOpt = preDepositAccounts.stream().filter(a -> PredepositTypeEnum.COMMON_DEPOSIT.getCode().equals(a.getPredepositType())).findFirst();
        List<PredepositRefundItemDTO> refundItems=Lists.newArrayList();

        if(request.getCommonRefundMoney().compareTo(BigDecimal.ZERO)>0){
            if((!commonPreDepositAccountOpt.isPresent())||commonPreDepositAccountOpt.get().getAvailableBalance().compareTo(request.getCommonRefundMoney())<0){
                throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"超出通用预存可用余额，退款失败");
            }
            PredepositAccountDTO predepositAccountDTO = commonPreDepositAccountOpt.get();
            PredepositRefundItemDTO refundItemDTO=toPreDepositRefundItemDTO(predepositAccountDTO,request.getCommonRefundMoney());
            refundItems.add(refundItemDTO);
        }
        if(!CollectionUtils.isEmpty(request.getSpecialRefundChargeItems())){
            for (ChargeItemPriceV2 specialRefundChargeItem:request.getSpecialRefundChargeItems()){
                long itemId = Long.parseLong(specialRefundChargeItem.getItemID());
                PredepositAccountDTO predepositAccountDTO = itemIdToPreDepositAccountMap.get(itemId);
                BigDecimal money = new BigDecimal(specialRefundChargeItem.getPrice());
                if(predepositAccountDTO==null||predepositAccountDTO.getAvailableBalance().compareTo(money)<0){
                    throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),String.format("超出%s可用余额，退款失败",specialRefundChargeItem.getItemName()));
                }
                PredepositRefundItemDTO refundItemDTO=toPreDepositRefundItemDTO(predepositAccountDTO,money);
                refundItems.add( refundItemDTO);
            }
        }

        PredepositRefundDTO refundDTO=toPreDepositRefundDTO(request,refundItems);
        ChargeResponse<Void> refundResp = predepositRefundClient.refund(refundDTO);
        AppInterfaceUtil.getResponseDataThrowException(refundResp);
    }

    private PredepositRefundDTO toPreDepositRefundDTO(PreStoreRefundRequest request,List<PredepositRefundItemDTO> refundItems){
        PredepositRefundDTO refundDTO=new PredepositRefundDTO();
        refundDTO.setAssetId(request.getHouseId());
        refundDTO.setCommunityId(request.getCommunityId());
        refundDTO.setRefundRemark(request.getRemark());
        refundDTO.setType(0);
        refundDTO.setListItem(refundItems);
        refundDTO.setAdvancePay(request.getAdvancePay());
        refundDTO.setEmployeeName(request.getEmployeeName());
        refundDTO.setCreateUser(request.getOperator());
        refundDTO.setPhone(request.getPhone());
        refundDTO.setPid(request.getPid());
        refundDTO.setRevertPoints(request.getRevertPoints());
        refundDTO.setPayBankAccount(request.getPayBankAccount());
        refundDTO.setAcceptAccountCity(request.getAcceptAccountCity());
        refundDTO.setAcceptAccountName(request.getAcceptAccountName());
        refundDTO.setAcceptAccountOpeningBank(request.getAcceptAccountOpeningBank());
        refundDTO.setAcceptBankName(request.getAcceptBankName());
        refundDTO.setBankAccountNo(request.getAcceptBankAccount());
        return refundDTO;
    }

    private PredepositRefundItemDTO toPreDepositRefundItemDTO(PredepositAccountDTO predepositAccountDTO,BigDecimal refundAmount){
        PredepositRefundItemDTO refundItemDTO=new PredepositRefundItemDTO();
        refundItemDTO.setItemId(predepositAccountDTO.getPredepositItemId());
        refundItemDTO.setItemName(predepositAccountDTO.getPredepositItemName());
        refundItemDTO.setPredepositAccountId(predepositAccountDTO.getId());
        refundItemDTO.setRefundAmount(refundAmount);
        return refundItemDTO;
    }

    @Override
    public void preDepositRefund(PreDepositRefundRequest request) throws ChargeBusinessException {
        List<PredepositAccountDTO> preDepositAccounts = predepositAccountClient.listPredepositAccount(PredepositAccountConditionDTO.builder()
                .id(request.getPredepositAccountId())
                .communityId(request.getCommunityId())
                .build()).getContent();
        if(CollectionUtils.isEmpty(preDepositAccounts)){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"押金不存在，退款失败");
        }
        PredepositAccountDTO outPreDepositAccount = preDepositAccounts.get(0);
        if(ChargeObjEnum.CHARGE_OBJ_DEVELOPER.getCode().equals(outPreDepositAccount.getChargeObj())){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"资产收费对象为开发商，押金退款请到收费系统网页版操作");
        }
        if(outPreDepositAccount.getAssetId()!=null&&request.getHouseId()==null){
            request.setHouseId(outPreDepositAccount.getAssetId());
        }
        if(outPreDepositAccount.getAvailableBalance().compareTo(request.getMoney())<0){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),String.format("超出%s可用余额，退款失败",request.getItemName()));
        }
        if(Boolean.TRUE.equals(request.getToPreStore())){
            //退款转预存
            ChargeResponse<AssetDTO> communityVirtualHouseResp = assetClient.getCommunityVirtualHouse(request.getCommunityId());
            AssetDTO virtualAsset = AppInterfaceUtil.getDataThrowException(communityVirtualHouseResp);
            if(virtualAsset.getId().equals(request.getHouseId())){
                throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),"小区的押金不支持转预存");
            }
            PreDepositAdjustBatchConditionDTO preDepositAdjustBatch=toPreDepositAdjustBatchConditionDTO(request,outPreDepositAccount);
            ChargeResponse<Void> chargeResponse = predepositAdjustClient.adjustBatch(preDepositAdjustBatch);
            AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        }else {
            //押金退款
            PredepositRefundDTO refundDTO=toPreDepositRefundItemDTO(request,outPreDepositAccount);
            ChargeResponse<Void> chargeResponse = predepositRefundClient.refund(refundDTO);
            AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        }

    }

    private PredepositRefundDTO toPreDepositRefundItemDTO(PreDepositRefundRequest request,PredepositAccountDTO outPreDepositAccount){
        PredepositRefundItemDTO refundItemDTO=toPreDepositRefundItemDTO(outPreDepositAccount,request.getMoney());
        PredepositRefundDTO refundDTO=new PredepositRefundDTO();
        refundDTO.setAssetId(request.getHouseId());
        refundDTO.setCommunityId(request.getCommunityId());
        refundDTO.setRefundRemark(request.getRemark());
        refundDTO.setType(1);
        refundDTO.setListItem(Lists.newArrayList(refundItemDTO));
        refundDTO.setFileIds(request.getFileIds());
        refundDTO.setAdvancePay(request.getAdvancePay());
        refundDTO.setEmployeeName(request.getEmployeeName());
        refundDTO.setCreateUser(request.getOperator());
        refundDTO.setPayBankAccount(request.getPayBankAccount());
        refundDTO.setAcceptAccountName(request.getAcceptAccountName());
        refundDTO.setAcceptAccountOpeningBank(request.getAcceptAccountOpeningBank());
        refundDTO.setAcceptAccountCity(request.getAcceptAccountCity());
        refundDTO.setBankAccountNo(request.getAcceptBankAccount());
        refundDTO.setAcceptBankName(request.getAcceptBankName());
        return refundDTO;
    }

    private PreDepositAdjustBatchConditionDTO toPreDepositAdjustBatchConditionDTO(PreDepositRefundRequest request,PredepositAccountDTO outPreDepositAccount){
        PreDepositAdjustBatchConditionDTO preDepositAdjustBatch=new PreDepositAdjustBatchConditionDTO();
        preDepositAdjustBatch.setCommunityId(request.getCommunityId());
        preDepositAdjustBatch.setAdjustAssetType(1);
        preDepositAdjustBatch.setTotalMoney(request.getMoney());
        preDepositAdjustBatch.setOriginAssetId(request.getHouseId());
        preDepositAdjustBatch.setTargetAssetId(request.getHouseId());
        preDepositAdjustBatch.setReason(request.getRemark());
        preDepositAdjustBatch.setFileIds(request.getFileIds());
        preDepositAdjustBatch.setCreateUser(request.getOperator());

        DepositBillCreateAdjustConditionDTO depositBillCreateAdjust=new DepositBillCreateAdjustConditionDTO();
        depositBillCreateAdjust.setAdjustMoney(request.getMoney());
        depositBillCreateAdjust.setPredepositAccountId(outPreDepositAccount.getId());
        depositBillCreateAdjust.setPredepositItemId(outPreDepositAccount.getPredepositItemId());
        depositBillCreateAdjust.setPredepositItemName(outPreDepositAccount.getPredepositItemName());
        preDepositAdjustBatch.setPreDepositAdjustOutList(Lists.newArrayList(depositBillCreateAdjust));

        DepositBillAdjustConditionDTO  depositBillAdjust=new DepositBillAdjustConditionDTO();
        depositBillAdjust.setAdjustMoney(request.getMoney());
        //需要改为通用预存
        depositBillAdjust.setPredepositItemId(CommonChargeItem.ITEM_ID);
        depositBillAdjust.setPredepositItemName(CommonChargeItem.ITEM_NAME);
        preDepositAdjustBatch.setPreDepositAdjustInList(Lists.newArrayList(depositBillAdjust));
        return preDepositAdjustBatch;
    }
}
