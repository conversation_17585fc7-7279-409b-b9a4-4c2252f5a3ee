package com.charge.api.web.vo.joylife;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/6/7 17:42
 */
@Data
public class AssetBillItem implements Serializable {

    private static final long serialVersionUID = 1014302715838221319L;

    /**
     * 收费项id
     */
    @NotNull(message = "收费项不能为空")
    private Long itemId;

    /**
     * 收费项名称
     */
    @NotBlank(message = "收费项名称不能为空")
    private String itemName;

    /**
     * 应收单ID（非应收单置空）
     */
    private Long id;

    /**
     * 订单项金额
     */
    @NotNull(message = "订单项金额不能为空")
    @DecimalMin(value = "0.01",message = "订单项金额必须大于0")
    private BigDecimal amount;

    /**
     * 预存该费项赠送的积分
     */
    private Integer points;
}
