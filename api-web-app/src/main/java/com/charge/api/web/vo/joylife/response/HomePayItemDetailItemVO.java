package com.charge.api.web.vo.joylife.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HomePayItemDetailItemVO implements Serializable {

    /**
     * 收费项Id（2.0的自增Id）
     */
    private String ItemId;
    /**
     * 收费项名称
     */
    private String itemName;
    /**
     * 合计金额（含违约金）
     */
    private BigDecimal sumPrice;
    /**
     * 违约金欠收
     */
    private BigDecimal arrearsPenaltyPrice;
    /**
     * 欠费合计（含违约金）
     */
    private BigDecimal arrearsPrice;
    /**
     * 折扣金额
     */
    private BigDecimal discountPrice;
    /**
     * 托收中欠费合计
     */
    private BigDecimal collectionPrice;
    /**
     * 违约金合计金额
     */
    private BigDecimal sumPenaltyPrice;
    /**
     * 其他状态下违约金欠费合计金额
     */
    private BigDecimal otherPenaltyPrice;
    /**
     * 违约金已收合计金额
     */
    private BigDecimal payPenaltyPrice;
    /**
     * 实收合计金额（含违约金）
     */
    private BigDecimal payPrice;
    /**
     * 待支付金额（含违约金）
     */
    private BigDecimal toPayPrice;
    /**
     * 挂起合计金额
     */
    private BigDecimal holdPrice;
    /**
     * 仪表类欠费计数
     */
    private String memo;

}
