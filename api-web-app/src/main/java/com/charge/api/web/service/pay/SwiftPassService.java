package com.charge.api.web.service.pay;

import com.charge.api.web.vo.joylife.BatchArrearsOrderList;
import com.charge.api.web.vo.joylife.request.BatchPayDataRequest;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.income.AssetArrearsListDTO;
import com.charge.bill.dto.income.AssetReceivalbeBillListDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import net.sf.json.JSONObject;

import java.math.BigDecimal;
import java.util.List;

public interface SwiftPassService {

    ChargeResponse<Long> createIncomeBill(BatchPayDataRequest pay, Long communityId, BigDecimal penaltyMoney, BigDecimal principalMoney,
        List<AssetArrearsListDTO> assetArrearsLists, String deadLine, int houseCount, String orderNum,boolean disAllFlag,Long pointsTransRecordId,JSONObject jsonObject) throws ChargeBusinessException;


    AssetArrearsListDTO buildAssetArrears(BatchArrearsOrderList batchArrearsOrders, List<AssetReceivalbeBillListDTO> assetReceivalbeBillLists, String paymentMethod);

    Long fetchCommunityId(String communityId) throws ChargeBusinessException;

    JSONObject createOrder(BatchPayDataRequest pay, Long communityId, String orderNum) throws ChargeBusinessException;

    ChargeResponse createBatchPay(BatchPayDataRequest pay) throws ChargeBusinessException;

    void checkReceivableBillStatus(List<ReceivableBillDTO> effectiveReceivableBillDTOS,List<ReceivableBillDTO> checkingReceivableBillDTOS,List<ReceivableBillDTO> effectiveAllReceivableBills) throws ChargeBusinessException;

}
