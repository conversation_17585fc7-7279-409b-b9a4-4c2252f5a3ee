package com.charge.api.web.service.parking;

import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.bill.dto.tempark.ParkingSyncRecordDTO;
import feign.Param;

import java.util.List;

/**
 * @ClassName: PlatformBankBusinessFlowService
 * @Author: wangle
 * @Description: 获取云平台银行收款业务流水服务接口
 * @Date: 2021/11/12 16:15:12
 * @Version: 1.0
 */
public interface PlatformBankBusinessFlowService {

    /**
     * 新增 云平台银行收款业务流水入库
     *
     * @return list
     */
    ChargeResponse insertPlatformBankBusinessFlow(@Param("recordList")  List<ParkingSyncRecordDTO> recordList) throws ChargeBusinessException;

}
