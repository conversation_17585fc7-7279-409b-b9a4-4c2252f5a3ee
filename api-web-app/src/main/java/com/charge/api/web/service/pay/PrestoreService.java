package com.charge.api.web.service.pay;

import com.charge.api.web.vo.joylife.AssetChargeStandVO;
import com.charge.api.web.vo.joylife.request.BatchZhaoXiAssetReq;
import com.charge.api.web.vo.joylife.request.PrestoreOrderRequest;
import com.charge.api.web.vo.joylife.response.BatchCalculatePreStoreVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;

import java.util.List;

/**
 * 预存充值销单
 *
 * <AUTHOR>
 */
public interface PrestoreService {

    /**
     * 预存充值销单
     *
     * @param paymentSource 支付渠道
     * @param transactionId 实收单id
     * @return success
     */
    ChargeResponse postPayStatus(String paymentSource, String transactionId);


    /**
     * 朝昔缴预存
     * @param request
     * @return
     */
    ChargeResponse<Object> addPrestoreInfo(PrestoreOrderRequest request) throws ChargeBusinessException;

    /**
     * 批量资产计算月度费用
     * @param costVO
     * @return
     * @throws ChargeBusinessException
     */
    BatchCalculatePreStoreVO batchCalculatePreStoreAmount(BatchZhaoXiAssetReq costVO) throws ChargeBusinessException;


    List<AssetChargeStandVO> listAssetChargeStand(BatchZhaoXiAssetReq costVO) throws ChargeBusinessException;

}
