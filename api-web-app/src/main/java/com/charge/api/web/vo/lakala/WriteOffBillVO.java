package com.charge.api.web.vo.lakala;

import com.charge.bill.dto.ReceivableBillDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WriteOffBillVO {
    private static final long serialVersionUID = 1L;
    ReceivableBillDTO receivableBill;
    @ApiModelProperty("实收单id")
    private Long incomeId;
    @ApiModelProperty("实收单据号")
    private Long incomeBillNum;
    @ApiModelProperty("资产流水id")
    private Long assetTransactionId;
    @ApiModelProperty("关联应收单id")
    private Long receivableBillId;
    @ApiModelProperty("核销单据号")
    private String billNum;
    @ApiModelProperty("收费项id")
    private Long itemId;
    @ApiModelProperty("收费项名称")
    private String itemName;
    private String itemCode;
    @ApiModelProperty("收费细项ID")
    private Long incomeDetailUuid;
    @ApiModelProperty("收费细项名称")
    private String incomeDetailName;
    @ApiModelProperty("订单项描述")
    private String memo;
    @ApiModelProperty("核销类型:1-支付，2-抵扣，3-退款红冲，4-抵扣红冲")
    private Integer writeOffType;
    @ApiModelProperty("账单状态(0表示正常,1表示已作废,2表示已红冲,3表示红冲退款中,4部分退款,5退款完成,6部分退款中,7退款中,8退款失败,9待支付,10已关闭)")
    private Integer billStatus;
    @ApiModelProperty("实收金额")
    private BigDecimal actualAmount;
    @ApiModelProperty("收款类型(0表示本金，1表示违约金)")
    private Integer chargeType;
    @ApiModelProperty("所属年月")
    private String belongYears;
    @ApiModelProperty("抵扣账户id")
    private Long predepositAccountId;
    @ApiModelProperty("抵扣预收费项名称")
    private String predepositItemName;
    @ApiModelProperty("预收类型(0表示通用预存，1表示专项预存，2表示待结转)")
    private Integer predepositType;
    @ApiModelProperty("是否对账：0否，1是")
    private Integer isBalance;
    @ApiModelProperty("是否已对账:默认0未对账，1已对账")
    private Integer balanceStatus;
    @ApiModelProperty("入账标志(0表示未入账,1表示入账中，2表示已入账)")
    private Integer enterStatus;
    @ApiModelProperty("单据类型：0单个房间缴费，1批量缴费")
    private Integer billType;
}
