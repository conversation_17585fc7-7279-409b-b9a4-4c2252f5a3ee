package com.charge.api.web.util;

import com.charge.common.enums.common.AliBucketEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码工具类，将url生成二维码并上传到阿里云指定的ossKey，返回阿里云的url，如果存在则直接返回
 * <p>
 * Description:
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/22
 */
public class QRCodeGenerator {

    public static String generateQRCodeToOss(String url, String ossFileKey) throws ChargeBusinessException {
        // 设置二维码的参数
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        // 创建二维码对象
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        try {
            BitMatrix bitMatrix = qrCodeWriter.encode(url, BarcodeFormat.QR_CODE, 200, 200, hints);
            // 将二维码输出到文件
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream);
        } catch (WriterException | IOException e) {
            throw new RuntimeException(e);
        }
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(outputStream.toByteArray());
        return OssFileUtil.uploadOssByStream(AliBucketEnum.POS, byteArrayInputStream, ossFileKey, false);
    }


}
