package com.charge.api.web.controller.pos;

import com.charge.api.web.vo.pos.BuildingReceivablePayStatisticVO;
import com.charge.api.web.vo.pos.ReceivablePayStatisticsDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 统计相关接口
 *
 * <AUTHOR>
 * @date 2023/3/4
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StatisticController {

    /**
     * 小区收缴率接口
     */
    @ApiOperation(value = "小区收缴率接口", notes = "小区收缴率接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "belongYears", value = "当前所属年月(2018-01)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/general/getCollectionRate", method = {RequestMethod.GET})
    public ChargeResponse<ReceivablePayStatisticsDTO> getCollectionRate(String communityUuid, String belongYears, String token) {
        return new ChargeResponse<>(ErrorInfoEnum.E1017);
    }

    @RequestMapping(value = "/general/getCollectionRateOrderByBuilding", method = {RequestMethod.GET})
    public ChargeResponse<List<BuildingReceivablePayStatisticVO>> getCollectionRateOrderByBuilding(String communityUuid, String belongYears,
                                                                                                   @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                                                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, String token) {
        return new ChargeResponse<>(ErrorInfoEnum.E1017);
    }


}


