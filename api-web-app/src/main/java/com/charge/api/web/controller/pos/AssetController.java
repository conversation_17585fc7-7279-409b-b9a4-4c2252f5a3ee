package com.charge.api.web.controller.pos;

import com.charge.api.web.service.pos.AssetService;
import com.charge.api.web.service.pos.CustomerService;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.AssetBalancesVO;
import com.charge.api.web.vo.pos.points.AssetCustomerQueryVO;
import com.charge.api.web.vo.pos.points.CustomerPointsVO;
import com.charge.api.web.vo.pos.UserVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * pos资产相关接口
 *
 * <AUTHOR>
 * @date 2023/3/9
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(value = "收费2.0 pos资产相关接口")
public class AssetController {

    private final AssetService assetService;
    
    private final CustomerService customerService;

    @ApiOperation(value = "[NEW]模糊搜索房屋列表)", notes = "模糊搜索房屋列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "keyword", value = "关键字", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "subjectType", value = "标的物类型：house,position,shops,lock,car,card", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "currentPage", value = "当前页数（默认为1）", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量（默认为10）", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/index/getNewArrearsHouseList", method = {RequestMethod.GET})
    public ChargePageResponse<AssetBalancesVO> getNewArrearsHouseList(@RequestParam String communityUuid, @RequestParam(required = false) String keyword, @RequestParam String subjectType,
                                                                      @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam String token) throws ChargeBusinessException {
        return assetService.assetSearchPage(Long.parseLong(communityUuid), keyword, subjectType, currentPage, pageSize);
    }


    @ApiOperation(value = "根据业主手机号，姓名，客户编码查询业主", notes = "根据业主手机号，姓名，客户编码查询业主")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "keyword", value = "关键字（订单号，或订单名称）", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "页码，默认1", required = false, dataType = "int", defaultValue = "1"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页大小，默认10", required = false, dataType = "int", defaultValue = "10"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @GetMapping(value = "/batchPay/listOwnerInfoByKeyword")
    public ChargePageResponse<List<UserVO>> listOwnerInfoByKeyword(@RequestParam String communityId, @RequestParam(required = false) String keyword,
                                                                   @RequestParam(required = false, defaultValue = "1") String pageNum, @RequestParam(required = false, defaultValue = "10") String pageSize,
                                                                   @RequestParam String token) throws ChargeBusinessException {

        return assetService.customerSearchPage(Long.parseLong(communityId), keyword, Integer.parseInt(pageNum), Integer.parseInt(pageSize));
    }

    @ApiOperation(value = "修改房屋备注", notes = "修改房屋备注")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "houseUuid", value = "房屋uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "memo", value = "备注", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/updateHouseMemo", method = {RequestMethod.POST})
    public ChargeResponse<String> updateHouseMemo(String houseUuid, String memo, String token) throws ChargeBusinessException {
        assetService.updateAssetMemo(Long.parseLong(houseUuid), memo);
        return new ChargeResponse<>();
    }

    @PostMapping(value = "/asset/customer/query")
    public ChargeResponse<List<CustomerPointsVO>> getAssetCustomerList(@Valid @RequestBody AssetCustomerQueryVO queryVO) throws ChargeBusinessException {
        return new ChargeResponse<>(customerService.getAssetCustomerList(queryVO));
    }

}


