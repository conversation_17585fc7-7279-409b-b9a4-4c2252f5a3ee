package com.charge.api.web.controller.pos;

import com.charge.api.web.service.pos.RentSaleOrderService;
import com.charge.api.web.vo.pos.rent.RentSaleOrderDetailQuery;
import com.charge.api.web.vo.pos.rent.RentSaleOrderQuery;
import com.charge.api.web.vo.pos.rent.RentSaleOrderVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description 租售订单
 * @Author: yjw
 * @Date: 2024/12/10 16:49
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping(value = "/order/rent-sale")
public class RentSaleOrderController {

    private final RentSaleOrderService rentSaleOrderService;

    /**
     * 租售-滚动查询待支付和已支付的订单列表
     *
     * @param rentSaleOrderQuery 租售订单查询
     * @return 租售订单列表
     */
    @PostMapping(value = "/roll-list")
    public ChargeResponse<List<RentSaleOrderVO>> rollListOrder(@RequestBody @Valid RentSaleOrderQuery rentSaleOrderQuery) throws ChargeBusinessException {
        return new ChargeResponse<>(rentSaleOrderService.listOrder(rentSaleOrderQuery));
    }

    /**
     * 租售-查询订单详情
     *
     * @param detailQuery 查询条件
     * @return 租售订单详情
     */
    @PostMapping("/detail")
    public ChargeResponse<RentSaleOrderVO> orderDetail(@Valid @RequestBody RentSaleOrderDetailQuery detailQuery) throws ChargeBusinessException {
        return new ChargeResponse<>(rentSaleOrderService.orderDetail(detailQuery));
    }

}
