package com.charge.api.web;

import com.charge.api.web.config.ThreadPoolConfig;
import com.charge.api.web.interceptor.PosLoginInterceptor;
import com.charge.cloud.api.core.OpenApiTool;
import com.charge.common.config.OssConfig;
import com.charge.common.support.ThreadContextTaskDecorator;
import com.charge.common.util.SpringContextUtil;
import com.charge.core.filter.ChargeFeignRequestInterceptor;
import com.charge.pay.config.PayConfig;
import feign.RequestInterceptor;
import feign.Retryer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
@SpringBootApplication
@EnableDiscoveryClient
@ServletComponentScan(basePackages = {"com.charge.core.filter"})
@Import(value = {OssConfig.class, PayConfig.class})
@EnableFeignClients(basePackages={"com.charge.maindata.client","com.charge.bill.client", "com.charge.pay.client", "com.charge.config.client"
        , "com.charge.user.client","com.charge.order.client","com.charge.finance.client","com.charge.feecalculte.client" ,"com.charge.report.client",
        "com.charge.invoice.client", "com.charge.apicloud.client", "com.charge.general.client","com.charge.leaf.client"})
public class Application {

    @Bean
    public PosLoginInterceptor posLoginInterceptor(){
        return new PosLoginInterceptor();
    }

    /**
     * Feign拦截器
     *
     * @return
     */
    @Bean
    public RequestInterceptor initInterceptor() {
        return new ChargeFeignRequestInterceptor();
    }

    /**
     * 初始化Retryer 不重试(默认 5次包裹第一次的请求)
     *
     * @return
     */
    @Bean
    public Retryer initRetryer() {
        return Retryer.NEVER_RETRY;
    }

    @Bean
    public OpenApiTool openApiTool() {
        return new OpenApiTool();
    }

    @Bean
    public SpringContextUtil springContextUtil() {
        return new SpringContextUtil();
    }

    @Bean
    public ThreadPoolTaskExecutor chargeCommonExecutor(ThreadPoolConfig threadPoolConfig) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //线程池活跃线程数
        executor.setCorePoolSize(threadPoolConfig.getCorePoolSize());
        // 线程池最大活跃线程数
        executor.setMaxPoolSize(threadPoolConfig.getMaxPoolSize());
        // 队列的最大容量
        executor.setQueueCapacity(threadPoolConfig.getQueueCapacity());
        executor.setTaskDecorator(new ThreadContextTaskDecorator());

        return executor;
    }

    /**
     * 启动服务
     *
     * @param args
     */
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        PayConfig.setPayConfig(SpringContextUtil.getBean(PayConfig.class));
    }


}
