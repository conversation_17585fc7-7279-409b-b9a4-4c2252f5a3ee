package com.charge.api.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * SwitchConfig
 *
 * <AUTHOR>
 * @date 2025/1/9
 */
@ConfigurationProperties(prefix = "switch")
@Configuration
@RefreshScope
@Data
public class SwitchConfig {

    private Boolean useNewAssetOverview=false;

    private List<Long> useNewAssetOverviewCommunityIds;

    public Boolean isUseNewAssetOverview(Long communityId) {
        return useNewAssetOverview
                && (CollectionUtils.isEmpty(useNewAssetOverviewCommunityIds)
                || useNewAssetOverviewCommunityIds.contains(communityId));
    }
}