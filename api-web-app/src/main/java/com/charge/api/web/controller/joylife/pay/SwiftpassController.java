package com.charge.api.web.controller.joylife.pay;

import com.charge.api.web.adapter.HouseClientAdapter;
import com.charge.api.web.constants.IncomeBillConstants;
import com.charge.api.web.constants.YueConstants;
import com.charge.api.web.service.pay.SwiftPassService;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.vo.joylife.request.BatchPayDataRequest;
import com.charge.bill.client.BillPayOperateClient;
import com.charge.bill.client.IncomeBillClient;
import com.charge.bill.dto.income.IncomeBillDTO;
import com.charge.bill.dto.income.IncomeConditionDTO;
import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.RedisKeyLockUtil;
import com.charge.common.util.XmlConverter;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.TraceContextUtil;
import com.charge.general.client.bff.PayCallbackClient;
import com.charge.general.dto.PayCallbackResultDTO;
import com.charge.general.dto.PayOrderResultDTO;
import com.charge.pay.client.PayClient;
import com.charge.pay.client.PayRecordClient;
import com.charge.pay.client.PointClient;
import com.charge.pay.client.SwiftpassClient;
import com.charge.pay.dto.ChargePayResponse;
import com.charge.pay.dto.OrderStatusCheckDTO;
import com.charge.pay.dto.SwiftpassCallbackDTO;
import com.charge.pay.dto.pay.QueryOrderRequestDTO;
import com.charge.pay.dto.pay.QueryOrderResponseDTO;
import com.charge.pay.dto.pay.TradeCloseRequestDTO;
import com.charge.pay.dto.pay.TradeCloseResponseDTO;
import com.charge.pay.enums.PayExceptionEnum;
import com.charge.pay.enums.PayTradeStateEnum;
import com.charge.starter.jedis.JedisManager;
import com.charge.starter.web.annotation.Idempotent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

/**
 * 威富通
 *
 * <AUTHOR>
 * @date 2022/12/7
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(value = "威富通支付相关接口")
public class SwiftpassController {

    private final SwiftpassClient swiftpassClient;

    private final IncomeBillClient incomeBillClient;

    private final PayRecordClient payRecordClient;

    private final BillPayOperateClient billClient;

    private final HouseClientAdapter houseClientAdapter;

    private final SwiftPassService swiftPassService;

    private final PayCallbackClient payCallbackClient;
    private final PointClient pointClient;
    private final JedisManager jedisManager;
    private final PayClient payClient;
    private final CommunitySupport communitySupport;

    private final static Integer TRADE_CLOSE_FAIL =1;


    @ApiOperation(value = "下单接口")
    @RequestMapping(value = "/new/chargeAPI/createBatchPay", method = {RequestMethod.POST})
    @Idempotent(expire = "5")
    public ChargeResponse createBatchPay(@RequestBody @Validated BatchPayDataRequest pay) throws ChargeBusinessException {

        return swiftPassService.createBatchPay(pay);
    }


    /**
     * description: app缴费查询订单状态
     * author: wuChao
     * date: 2023/7/7
     * param [batchOrderId, paymentSource, paymentMethod, payResult =支付结果（0-支付成功，1-支付失败）]
     * return com.charge.common.dto.ChargeResponse
     **/
    @ApiOperation(value = "app缴费查询订单状态")
    @RequestMapping(value = "/batchPay/handlePayReturn", method = {RequestMethod.POST})
    public ChargeResponse handlePayReturn(String batchOrderId, String paymentSource, String paymentMethod,
                                          Integer payResult) throws ChargeBusinessException {

        // 根据batchOrderId查询小区id、paymentSource
        IncomeConditionDTO incomeConditionDTO = new IncomeConditionDTO();
        incomeConditionDTO.setId(Long.valueOf(batchOrderId));
        incomeConditionDTO.setPageNum(1);
        incomeConditionDTO.setPageSize(10);

        Long communityId = communitySupport.getCommunityIdByIncomeId(incomeConditionDTO.getId());
        if (communityId != null) {
            TraceContextUtil.setCommunityId(communityId);
            incomeConditionDTO.setCommunityId(communityId);
        }
        ChargeResponse<PagingDTO<IncomeBillDTO>> incomeBillDTOResponse =
                incomeBillClient.selectByCondition(incomeConditionDTO);
        if (!incomeBillDTOResponse.isSuccess()) {
            log.info("账单服务异常");
            return new ChargeResponse(YueConstants.CODE_FAILED, "账单服务异常" + incomeBillDTOResponse.getMessage());
        }

        PagingDTO<IncomeBillDTO> incomeBillPaging = incomeBillDTOResponse.getContent();
        if (incomeBillPaging == null || CollectionUtil.isEmpty(incomeBillPaging.getList()) || incomeBillPaging.getList().size() != 1) {
            log.info("{}[app缴费查询订单状态]实收单id:{},查询不到对应记录，或者查到多条记录", LogCategoryEnum.BUSSINESS, batchOrderId);
            return new ChargeResponse<>(YueConstants.CODE_FAILED, "查询不到对应记录，或者查到多条记录");
        }

        IncomeBillDTO incomeBillDTO = incomeBillDTOResponse.getContent().getList().get(0);
        if (incomeBillDTO == null || IncomeBillConstants.OPERATION_STATUS_BILL_PAID.equals(incomeBillDTO.getOperationStatus())) {
            log.info("{}[app缴费查询订单状态]实收单id:{},该订单已缴费完成，请勿重复操作", LogCategoryEnum.BUSSINESS, batchOrderId);
            return new ChargeResponse<>(YueConstants.CODE_SUCCESS, "该订单已缴费完成，请勿重复操作");
        }

        try {
            String orderNum = incomeBillDTO.getOrderNum();
            QueryOrderRequestDTO queryOrderRequestDTO=new QueryOrderRequestDTO();
            queryOrderRequestDTO.setPaymentTerminal(paymentSource);
            queryOrderRequestDTO.setOutTradeNo(orderNum);
            queryOrderRequestDTO.setSyncQuery(true);
            ChargeResponse<QueryOrderResponseDTO> chargeResponse = payClient.tradeQuery(queryOrderRequestDTO);
            log.info("{}|[app缴费查询订单状态]查询下单支付结果:{}", LogCategoryEnum.BUSSINESS, chargeResponse);
            if (!chargeResponse.isSuccess()) {
                return new ChargeResponse<>(YueConstants.CODE_FAILED, chargeResponse.getMessage());
            }

            // 调用实收单接口（校验、更新状态）
            QueryOrderResponseDTO callback = chargeResponse.getContent();

            //支付宝支付 直接返回，无需实时退分
            if (Objects.equals(callback.getTradeState(), PayTradeStateEnum.NOTPAY.getCode()) && Objects.equals(incomeBillDTO.getPaymentChannel(), PaymentChannelEnum.ALI_PAY.getPaymentChannel())) {
                return new ChargePayResponse<>(YueConstants.CODE_NO_PAY, "支付渠道查询支付结果,交易状态为未支付，请稍后再试");
            }

            //判断交易状态，同时确认是否撤销积分通兑
            if (!Objects.equals(callback.getTradeState(), PayTradeStateEnum.SUCCESS.getCode())) {
                //非交易成功；传入payResult为支付失败且关单成功时，退分
                boolean pointsFlag =
                        incomeBillDTO.getEquityMoney() != null && (BigDecimal.ZERO.compareTo(incomeBillDTO.getEquityMoney()) < 0);
                if (pointsFlag && (PayTradeStateEnum.getFinalFailStateList().contains(callback.getTradeState()) || Objects.equals(PayTradeStateEnum.NOTPAY.getCode(),callback.getTradeState()))
                        && Objects.equals(incomeBillDTO.getPaymentChannel(), PaymentChannelEnum.WECHAT_PAY.getPaymentChannel())) {
                    //先关单，关单成功再退分
                    ChargeResponse<TradeCloseResponseDTO> closeRespResult = payClient.closePayTrade(TradeCloseRequestDTO.builder().orderNum(orderNum).paymentTerminal(paymentSource).build());
                    if (Objects.equals(closeRespResult.getCode(), PayExceptionEnum.PAY_OVER.getCode())) {
                        callback.setTradeState(PayTradeStateEnum.SUCCESS.getCode());
                        callback.setTotalFee(incomeBillDTO.getIncomeMoney().multiply(new BigDecimal("100")).toPlainString());
                        return getSuccessPayResponse(batchOrderId, communityId, callback);
                    }
                    TradeCloseResponseDTO closeResp = AppInterfaceUtil.getDataThrowException(closeRespResult);
                    if (Objects.equals(TRADE_CLOSE_FAIL,closeResp.getResultCode())) {
                        log.info("{}|[app缴费查询订单状态]关单失败:{}", LogCategoryEnum.BUSSINESS, closeResp);
                        return new ChargeResponse(YueConstants.CODE_FAILED, "支付渠道关单失败，2小时后退回万象星！" + incomeBillDTOResponse.getMessage());
                    }
                }
                return new ChargeResponse<>(YueConstants.CODE_SUCCESS, callback.getTradeState());
            }

            return getSuccessPayResponse(batchOrderId, communityId, callback);
        } catch (Exception e) {
            log.error("{}|[app缴费查询订单状态]批量订单类型异常", LogCategoryEnum.BUSSINESS, e);
            return new ChargeResponse<>(YueConstants.CODE_FAILED, "批量订单类型异常");
        }
    }

    private ChargePayResponse getSuccessPayResponse(String batchOrderId, Long communityId, QueryOrderResponseDTO callback) throws ChargeBusinessException {
        PayOrderResultDTO payOrderResultDTO = BeanCopierWrapper.copy(callback, PayOrderResultDTO.class);
        payOrderResultDTO.setCommunityId(communityId);
        ChargeResponse<PayCallbackResultDTO> payResponse = payCallbackClient.operate(payOrderResultDTO);
        if (!payResponse.isSuccess()) {
            log.info("{}|[app缴费查询订单状态]更新账单异常,{}", LogCategoryEnum.BUSSINESS, payResponse);
            return new ChargePayResponse<>(YueConstants.CODE_FAILED, "更新账单异常" + payResponse.getMessage());
        }
        JSONObject jsonReturn = new JSONObject();
        jsonReturn.put("payId", batchOrderId);
        jsonReturn.put("resultCode", 0);
        jsonReturn.put("resultMsg", "支付成功");
        return new ChargePayResponse<>(YueConstants.CODE_SUCCESS, "success", jsonReturn);
    }

    @Deprecated
    @ApiOperation(value = "app、小程序查询预存订单状态")
    // @RequestMapping(value = "/new/chargeAPI/getPayStatus", method = {RequestMethod.POST})
    public ChargeResponse getPayStatus(String paymentSource, String id) throws IOException {

        // todo 校验交易流水

        // todo 判断积分状态

        // todo 根据batchOrderId查询小区id、paymentSource
        OrderStatusCheckDTO swiftpassCallbackDTO = OrderStatusCheckDTO.builder().outTradeNo("").communityId(1L).paymentSource("").build();
        ChargeResponse response = swiftpassClient.checkOrder(swiftpassCallbackDTO);
        if (response.isSuccess()) {
            JSONObject jsonObject = JSONObject.fromObject(response.getContent());
            if (jsonObject.containsKey("trade_state") && YueConstants.YUE_PAY_SUCCESS.equals(jsonObject.get("trade_state"))) {
                // todo 修改订单状态
            }
        }
        return new ChargeResponse();
    }

    /**
     * 该方法已作废，合并为统一回调地址
     * @param req
     * @return
     * @throws IOException
     */
    @Deprecated
    @ApiOperation(value = "小程序缴费异步回调接口")
    @RequestMapping(value = "/weiXin/doPostCheck", method = {RequestMethod.POST})
    public String doPostCheck(HttpServletRequest req) throws IOException {
        // 成功，返回success；失败，返回fail
        String response = "success";
        // 输入流仅能读取一次
        String data = IOUtils.toString(req.getInputStream(), StandardCharsets.UTF_8);

        Map<String, String> map = XmlConverter.read(data, Map.class);
        String orderNum = map.get("out_trade_no") == null ? "" : map.get("out_trade_no");
        // 根据orderNum 加锁
        RedisKeyLockUtil.kickOffConcurrent(jedisManager, IncomeBillConstants.LOCK_PRE_ORDER_NUM + orderNum, 300);

        // todo 判断实收单是否已经更新
        SwiftpassCallbackDTO callbackDTO = SwiftpassCallbackDTO.builder().build();
        callbackDTO.setRawString(data);
        // todo 根据orderNum查询到community_id，paymentSource
        callbackDTO.setCommunityId(1L);
        callbackDTO.setPaymentSource("YUEHOME_PAY");
        ChargeResponse payResponse = swiftpassClient.callback(callbackDTO);

        if (payResponse.isSuccess()) {
            // todo 调用实收单接口（校验、更新状态）
            log.info("调用实收单接口（校验、更新状态）");
            return response;
        }

        return "fail";
    }
}
