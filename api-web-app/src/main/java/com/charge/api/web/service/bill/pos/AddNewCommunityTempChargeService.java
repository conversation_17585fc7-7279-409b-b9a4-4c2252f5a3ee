package com.charge.api.web.service.bill.pos;

import com.charge.api.web.vo.lakala.CommunityOrdersBillCreateRequest;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.domain.AssetPayDTO;
import com.charge.bill.dto.domain.AssetPaymentDetailDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.order.enums.OrderRuleCategoryEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * pos小区批量临时收费下单
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddNewCommunityTempChargeService {

    private final CreateBillFlowService createOrderService;

    public CreateBillResponse createBill(CommunityOrdersBillCreateRequest request, AssetDTO asset) throws ChargeBusinessException {
        return createOrderService.createOrder(buildAssetPayDTO(request, asset));
    }

    private AssetPayDTO buildAssetPayDTO(CommunityOrdersBillCreateRequest request, AssetDTO asset) throws ChargeBusinessException {
        AssetPayDTO assetPayDTO = new AssetPayDTO();
        assetPayDTO.setAssetPayBaseDTO(buildAssetPayBaseDTO(request, asset));
        assetPayDTO.setAssetPaymentDetailDTO(buildAssetPaymentDetailDTO(request, asset));
        return assetPayDTO;
    }

    private AssetPayBaseDTO buildAssetPayBaseDTO(CommunityOrdersBillCreateRequest request, AssetDTO asset) throws ChargeBusinessException {
        AssetPayBaseDTO assetPayBaseDTO = new AssetPayBaseDTO();
        assetPayBaseDTO.setCommunityId(asset.getHouseDTO().getCommunityId());
        assetPayBaseDTO.setCommunityName(asset.getHouseDTO().getCommunityName());
        assetPayBaseDTO.setOrderNum(IdGeneratorSupport.getIstance().nextId());
        assetPayBaseDTO.setOutTransactionNo(request.getBankTransactionNo());
        assetPayBaseDTO.setActualPrice(new BigDecimal(request.getAmount()));
        assetPayBaseDTO.setPaymentMethod(FillCommonUtil.toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())).getPaymentCode());
        assetPayBaseDTO.setPayMember(request.getPayMember());
        assetPayBaseDTO.setCollectorId(request.getCollectorId());
        assetPayBaseDTO.setCollectorName(request.getCollectorName());
        assetPayBaseDTO.setMemo(request.getMemo());
        assetPayBaseDTO.setBankAccountUuid(request.getBankAccountUuid());
        assetPayBaseDTO.setBankAccountNo(request.getBankTransactionNo());
        assetPayBaseDTO.setDeviceInfo(request.getDeviceInfo());
        assetPayBaseDTO.setMercid(request.getMercid());
        assetPayBaseDTO.setPaymentTerminal(PaymentTerminalEnum.POS.getCode());
        assetPayBaseDTO.setPayHouseCount(1);
        assetPayBaseDTO.setClientSourceEnum(ClientSourceEnum.POS_BATCH_COMMUNITY_TEMP_PAY);
        FillCommonUtil.fillAssetPayBaseCommon(request.getPaymentMethod(), assetPayBaseDTO);
        return assetPayBaseDTO;
    }

    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(CommunityOrdersBillCreateRequest request, AssetDTO assetDTO) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 临时订单: OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_300
        assetPaymentDetailDTO.setOrderItems(FillCommonUtil.buildOrderItem(request.getTempChargeData()));
        // 资产信息
        assetPaymentDetailDTO.setBillAssetInfoDTO(FillCommonUtil.buildBillAssetInfo(assetDTO));
        return assetPaymentDetailDTO;
    }
}
