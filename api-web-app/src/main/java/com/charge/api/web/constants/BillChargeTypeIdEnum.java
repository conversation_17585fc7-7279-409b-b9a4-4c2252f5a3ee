package com.charge.api.web.constants;

import com.charge.bill.enums.BusinessTypeEnum;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * pos 账单类型枚举类
 */
@Getter
public enum BillChargeTypeIdEnum {
    ROUTINE("routine", Arrays.asList(BusinessTypeEnum.NORMAL_PAY.getCode(),BusinessTypeEnum.DISCOUNT.getCode(),BusinessTypeEnum.PENALTY_DERATE.getCode()), "常规"),
    DEPOSIT("deposit", Arrays.asList(BusinessTypeEnum.PREDEPOSIT_PAY.getCode(), BusinessTypeEnum.PREDEPOSIT_ADJUST.getCode()), "押金"),
    PRESTORE("prestore", Arrays.asList(BusinessTypeEnum.PREDEPOSIT_DEDUCT.getCode(), BusinessTypeEnum.TEMP_PAY.getCode(), BusinessTypeEnum.DISCOUNT.getCode()), "预存"),
    TEMPORARY("temporary", Arrays.asList(BusinessTypeEnum.TEMP_PAY.getCode()), "临时"),
    ;

    BillChargeTypeIdEnum(String type, List<Integer> businessTypes, String desc) {
        this.type = type;
        this.businessTypes = businessTypes;
        this.desc = desc;
    }


    public final String type;
    public final List<Integer> businessTypes;
    public final String desc;

    public static String getChargeType(List<Integer> listAllType) {
        String type = ROUTINE.type;
        if (CollectionUtils.isEmpty(listAllType)) {
            return type;
        }
        for (BillChargeTypeIdEnum chargeTypeEnum : values()) {
            if (chargeTypeEnum.businessTypes.contains(listAllType.get(0))) {
                return chargeTypeEnum.type;
            }
        }
        return type;
    }
}
