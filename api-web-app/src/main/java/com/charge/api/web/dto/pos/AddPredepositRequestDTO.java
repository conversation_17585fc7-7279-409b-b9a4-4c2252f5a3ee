package com.charge.api.web.dto.pos;

import lombok.Builder;
import lombok.Data;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON><PERSON>
 */
@Data
@Builder
public class AddPredepositRequestDTO {
    /**
     * 资产id / houseUuid
     */
    private Long assetId;
    /**
     * 总金额
     */
    private String money;
    /**
     * 通用预存充值金额
     */
    private String payMoney;
    /**
     * 支付方式(0表示POS,1表示转账,2表示支票,3表示支付宝,4表示微信,6员工代付)
     */
    private String paymentMethod;
    /**
     * 临时收费 - (json数据格式)[{"chargeType":"0","itemID":"100","itemName":"收费项名称","incomeDetailUuid":"200","incomeDetailName":"收费细项名称","price":"100"},{"chargeType":"0","itemID":"200","itemName":"收费项名称","incomeDetailUuid":"200","incomeDetailName":"收费细项名称","price":"100"}]
     */
    private String tempBillJson;
    /**
     * 专项预存 - 添加专项预存充值(json数据格式)[{"itemID":"100","itemName":"收费项名称","price":"100"},{"itemID":"200","itemName":"收费项名称","price":"100"}]
     */
    private String specialPreStoreJson;
    /**
     * 收费员id
     */
    private String collectorId;
    /**
     * 收费员姓名
     */
    private String collectorName;
    /**
     * 缴费人
     */
    private String payMember;
    /**
     * 银行流水号
     */
    private String bankTransactionNo;
    /**
     * 到账日期(yyyy-MM-dd)
     */
    private String arrivalDate;
    /**
     * 项目银行账号配置id
     */
    private String bankAccountUuid;
    /**
     * 银行账号
     */
    private String bankAccountNum;
    /**
     * 备注信息
     */
    private String memo;
    /**
     * 设备号
     */
    private String deviceInfo;
    /**
     * 商户号
     */
    private String mercid;




}
