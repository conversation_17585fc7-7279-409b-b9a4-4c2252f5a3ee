package com.charge.api.web.convert;

import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.common.util.DateMapper;
import com.charge.maindata.pojo.dto.AssetDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 资产转换器
 * <AUTHOR>
 * @description
 * @date 2023/02/28
 */
@Mapper(uses = DateMapper.class)
public interface AssertConverter {

    AssertConverter INSTANCE = Mappers.getMapper(AssertConverter.class);
    AssetAdapter map(AssetDTO conditionVO);


}
