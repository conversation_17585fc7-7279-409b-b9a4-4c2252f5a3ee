package com.charge.api.web.support;

import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.bill.enums.ReceivableBillTypeEnum;
import com.charge.bill.enums.ReceivalbleBillPayStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import lombok.RequiredArgsConstructor;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 应收工具类
 *
 * <AUTHOR>
 * @date 2023/11/27
 */
@Component
@RequiredArgsConstructor
public class ReceivableSupport {

    private final ReceivableBillClient receivableBillClient;

    public List<ReceivableBillDTO> listReceivableBills(Long communityId, List<Long> assetIds, List<Long> chargeItemIds ,List<Integer> chargeObjectIds) throws ChargeBusinessException {
        List<ReceivableBillDTO> receivableBills=Lists.newArrayList();
        ReceivableConditionDTO receivableConditionDTO=ReceivableConditionDTO.builder()
                .communityId(communityId)
                .assetIdList(assetIds)
                .itemIdList(chargeItemIds)
                .pageSize(500)
                .pageNum(1)
                .chargeObjectList(chargeObjectIds)
                .billStatuses(Lists.newArrayList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode()))
                .payStatuses(Lists.newArrayList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(),ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()))
                .billType(ReceivableBillTypeEnum.RECEIVABLE.getCode())
                .build();
        PagingDTO<ReceivableBillDTO> receivablePage;
        do{
            ChargeResponse<PagingDTO<ReceivableBillDTO>>  pageResp = receivableBillClient.queryByPage(receivableConditionDTO);
            receivablePage = AppInterfaceUtil.getResponseDataThrowException(pageResp);
            if(!CollectionUtils.isEmpty(receivablePage.getList())){
                receivableBills.addAll(receivablePage.getList());
            }
            receivableConditionDTO.setPageNum(receivableConditionDTO.getPageNum()+1);
            //加后面一个条件避免当total不准时，遍历不结束
        } while (receivablePage.getTotalCount()>receivableBills.size()&&!CollectionUtils.isEmpty(receivablePage.getList()));
        return receivableBills;
    }

    public List<ReceivableBillDTO> listReceivableBills(Long communityId, List<Long> assetIds,
                                                       List<Long> chargeItemIds ,List<Integer> chargeObjectIds, String endBelongYears) throws ChargeBusinessException {
        List<ReceivableBillDTO> receivableBills=Lists.newArrayList();
        ReceivableConditionDTO receivableConditionDTO=ReceivableConditionDTO.builder()
                .communityId(communityId)
                .assetIdList(assetIds)
                .itemIdList(chargeItemIds)
                .pageSize(500)
                .pageNum(1)
                .chargeObjectList(chargeObjectIds)
                .billStatuses(Lists.newArrayList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode()))
                .payStatuses(Lists.newArrayList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(),ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()))
                .billType(ReceivableBillTypeEnum.RECEIVABLE.getCode())
                .belongYearsEnd(endBelongYears)
                .build();
        PagingDTO<ReceivableBillDTO> receivablePage;
        do{
            ChargeResponse<PagingDTO<ReceivableBillDTO>>  pageResp = receivableBillClient.queryByPage(receivableConditionDTO);
            receivablePage = AppInterfaceUtil.getResponseDataThrowException(pageResp);
            if(!CollectionUtils.isEmpty(receivablePage.getList())){
                receivableBills.addAll(receivablePage.getList());
            }
            receivableConditionDTO.setPageNum(receivableConditionDTO.getPageNum()+1);
            //加后面一个条件避免当total不准时，遍历不结束
        } while (receivablePage.getTotalCount()>receivableBills.size()&&!CollectionUtils.isEmpty(receivablePage.getList()));
        return receivableBills;
    }

}


