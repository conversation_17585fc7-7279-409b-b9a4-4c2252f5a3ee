package com.charge.api.web.vo.joylife.request;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


import javax.validation.constraints.NotNull;


/**
 * 预存计算请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class PreStoreCalReq extends ZhaoXiAssertRequest {

    private static final long serialVersionUID = 2975728929297694877L;
    /**
     * 费项
     */
    @NotNull(message = "费项不能为空")
    private Long itemId;

}
