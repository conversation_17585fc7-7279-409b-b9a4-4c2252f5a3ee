package com.charge.api.web.support;

import com.charge.bill.client.AssetOverviewClient;
import com.charge.bill.dto.assetoverview.AssetOverViewConditionDTO;
import com.charge.bill.dto.assetoverview.AssetOverViewDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.enums.HouseTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/07
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AssetOverviewSupport {

    private final AssetOverviewClient overviewClient;

    public PagingDTO<AssetOverViewDTO> pageAssetOverview( Long communityId, String searchParam, List<HouseTypeEnum> houseTypes,
                                                    AssetTypeEnum assetType, Integer pageNum, Integer pageSize, Integer ownerArrearsStatus) throws ChargeBusinessException {
        Assert.notNull(communityId, "communityId不能为空");
        AssetOverViewConditionDTO assetCondition = AssetOverViewConditionDTO.builder().communityId(communityId).searchParam(searchParam)
                .pageNum(pageNum).pageSize(pageSize).ownerArrearsStatus(ownerArrearsStatus).build();
        if(CollectionUtils.isNotEmpty(houseTypes)){
            assetCondition.setHouseTypes(houseTypes);
        }
        if(assetType!=null){
            assetCondition.setAssetTypes(Lists.newArrayList(assetType));
        }
        ChargeResponse<PagingDTO<AssetOverViewDTO>> response = overviewClient.pageAssetOverview(assetCondition);
        return AppInterfaceUtil.getResponseDataThrowException(response);
    }

    public List<AssetOverViewDTO> listAssetOverview(Long communityId, String searchParam, List<HouseTypeEnum> houseTypes,
                                                    AssetTypeEnum assetType, Integer ownerArrearsStatus, Boolean querySimple,List<Long> assetIds, Integer effectiveArrearsStatus) throws ChargeBusinessException {
        Assert.notNull(communityId, "communityId不能为空");
        AssetOverViewConditionDTO assetCondition = AssetOverViewConditionDTO.builder().communityId(communityId).searchParam(searchParam).querySimple(querySimple)
                .ownerArrearsStatus(ownerArrearsStatus).assetIds(assetIds).effectiveArrearsStatus(effectiveArrearsStatus).build();
        if(CollectionUtils.isNotEmpty(houseTypes)){
            assetCondition.setHouseTypes(houseTypes);
        }
        if(assetType!=null){
            assetCondition.setAssetTypes(Lists.newArrayList(assetType));
        }
        ChargeResponse<List<AssetOverViewDTO>> response = overviewClient.listAssetOverview(assetCondition);
        return AppInterfaceUtil.getResponseDataThrowException(response);
    }

    public Map<Integer, Long> statisticArrearsByHouseType( Long communityId) throws ChargeBusinessException {
        Assert.notNull(communityId, "communityId不能为空");
        ChargeResponse<Map<Integer, Long>> response = overviewClient.statisticArrearsByHouseType(communityId);
        return AppInterfaceUtil.getResponseDataThrowException(response);
    }

}
