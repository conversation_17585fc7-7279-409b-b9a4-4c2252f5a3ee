package com.charge.api.web.aop;

import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.JoyLifeErrorInfoEnum;
import com.charge.common.util.ValidParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/09
 */
@Slf4j
@Aspect
@Component
public class JoyLifeCheckParamAspect {
    @Pointcut("@annotation(com.charge.api.web.annotation.JoyLifeCheckParam)")
    public void pointCut() {

    }

    /**
     * @param point
     * @return
     * @Description: 环绕通知, 环绕增强，相当于MethodInterceptor
     */
    @Around("pointCut()")
    public Object around(ProceedingJoinPoint point) {
        log.info("【注解：Around . 环绕前】方法环绕start.....");
        Object[] args = point.getArgs();
        try {
            if (Objects.nonNull(args) || args.length > 0) {
                Object arg = args[0];
                String inValidMessage = ValidParamUtil.getInValidMessage(arg);
                if (StringUtils.isNotBlank(inValidMessage)) {
                    ChargeResponse chargeResponse = new ChargeResponse();
                    chargeResponse.setCode(JoyLifeErrorInfoEnum.FAIL.getCode());
                    chargeResponse.setMessage(inValidMessage);
                    return chargeResponse;
                }

            }
            return point.proceed();
        } catch (Throwable e) {
            log.error("业务处理异常", e);
            ChargeResponse chargeResponse = new ChargeResponse();
            chargeResponse.setCode(JoyLifeErrorInfoEnum.FAIL.getCode());
            chargeResponse.setMessage(e.getMessage());
            return chargeResponse;
        }
    }
}
