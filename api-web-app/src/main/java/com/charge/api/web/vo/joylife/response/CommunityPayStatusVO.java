package com.charge.api.web.vo.joylife.response;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class CommunityPayStatusVO implements Serializable {
	private Integer disableStatus;
	private Integer prestoreChargeStatus;
	private String message;

	public static CommunityPayStatusVO from(String message, Integer disableStatus, Integer prestoreChargeStatus) {
		return CommunityPayStatusVO.builder().message(message).disableStatus(disableStatus).prestoreChargeStatus(prestoreChargeStatus).build();
	}
}
