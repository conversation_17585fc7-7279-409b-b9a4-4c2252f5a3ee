package com.charge.api.web.vo.pos.rent;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/12/11 11:03
 */
@Data
public class RentSaleOrderDetailQuery {

    /**
     * 租售订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long  orderId;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long communityId;
}
