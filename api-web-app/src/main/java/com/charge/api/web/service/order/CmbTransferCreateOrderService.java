package com.charge.api.web.service.order;

import com.charge.bill.enums.BalanceStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeRuntimeException;
import com.charge.pay.client.PayRecordClient;
import com.charge.pay.dto.PayRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * pos下单服务
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
@Service
@Slf4j
public  class CmbTransferCreateOrderService extends BaseCreateOrderService {
    @Resource
    protected PayRecordClient payRecordClient;
    @Override
    public void orderPay(CreateOrderContext context) {
        PayRecord payRecord = new PayRecord()
                .setOrderNum(context.getOrderNum())
                .setPayMember(context.getPayMember())
                .setPayMemberId(context.getPayMember())
                .setPayStatus(0)
                .setPaymentMethod(context.getPaymentMethod().getPaymentCode())
                .setPaymentChannel(context.getPaymentChannel().getPaymentChannel())
                .setPaymentTerminal(context.getPaymentTerminal().getCode())
                .setMoney(context.getTotalPrice())
                .setMemo(context.getMemo()).setCommunityId(context.getCommunity().getId())
                .setCommunityName(context.getCommunity().getName())
                .setGoodsName(context.getGoodsName())
                .setBalanceStatus(BalanceStatusEnum.UNRECONCILED.getCode())
                .setBankAccountNo(context.getBankAccountNum())
                .setReferNo(context.getOutTransactionNo())
                .setBankAccountUuid(context.getBankAccountUuid());
        try {
            ChargeResponse response = payRecordClient.insert(payRecord);
            Assert.isTrue(response.isSuccess(), "写入交易记录异常" + response.getMessage());
        } catch (Exception e) {
            throw new ChargeRuntimeException("调用创建支付记录异常:" + e.getMessage(), e);
        }
    }
}


