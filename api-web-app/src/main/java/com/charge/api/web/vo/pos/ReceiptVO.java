package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/14
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
@Builder
public class ReceiptVO {
    @JsonProperty("id")
    private String id;
    @JsonProperty("referNo")
    private String referNo;
    @JsonProperty("companyId")
    private String companyId;
    @JsonProperty("companyName")
    private String companyName;
    @JsonProperty("communityId")
    private String communityId;
    @JsonProperty("communityName")
    private String communityName;
    @JsonProperty("chargerId")
    private String chargerId;
    @JsonProperty("chargerName")
    private String chargerName;
    @JsonProperty("payTime")
    private String payTime;
    @JsonProperty("payerId")
    private String payerId;
    @JsonProperty("payerName")
    private String payerName;
    @JsonProperty("houseId")
    private String houseId;
    @JsonProperty("payHouse")
    private String payHouse;
    @JsonProperty("payType")
    private String payType;
    @JsonProperty("payfee")
    private BigDecimal payfee;
    @JsonProperty("url")
    private String url;
    @JsonProperty("orderNo")
    private String orderNo;
    @JsonProperty("receiptNo")
    private String receiptNo;
    @JsonProperty("chargeItemInfo")
    private String chargeItemInfo;
    @JsonProperty("printer")
    private String printer;
    @JsonProperty("printTimes")
    private Integer printTimes;
    @JsonProperty("payId")
    private String payId;
    @JsonProperty("createTime")
    private String createTime;
    @JsonProperty("updateTime")
    private String updateTime;
    @JsonProperty("cropId")
    private String cropId;
    @JsonProperty("bankReferNo")
    private String bankReferNo;
    @JsonProperty("massBillId")
    private String massBillId;
}


