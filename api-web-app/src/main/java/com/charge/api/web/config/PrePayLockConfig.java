package com.charge.api.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/01/02
 */
@ConfigurationProperties(prefix = "pre-pay-lock")
@Configuration
@RefreshScope
@Data
public class PrePayLockConfig {

    /**
     * 是否开启锁
     */
    private boolean enableLock = true;


    /**
     * 禁止锁的业务场景
     */
    private Set<String> forbidLockScenes;

    /**
     * 仅仅是检测的支付终端
     */

    private Set<String> onlyCheckPaymentTerminals;

    /**
     * 允许关闭前一个 的业务场景
     */
    private Set<String> allowClosePreviousScenes;

    /**
     * 多少秒后允许关闭前一个
     */
    private int allowClosePreviousAfterSec = 0;

    /**
     * 是否检测重复，重复将拒绝
     */
    private boolean checkRepeat = false;
    /**
     * 多少秒内不允许重复
     */
    private int denyRepeatSecs = 3;

    /**
     * 业务锁时间
     * (秒)
     */
    private long businessLockSecs = 5 * 60L;

    /**
     * 支付锁时间
     * (秒)
     */
    private long payLockSecs = 4 * 60L;
}
