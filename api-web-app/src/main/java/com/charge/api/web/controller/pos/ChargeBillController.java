package com.charge.api.web.controller.pos;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.service.pos.ChargeBillService;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.lakala.*;
import com.charge.api.web.vo.pos.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.starter.web.annotation.Idempotent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.charge.api.web.service.pos.impl.ChargeBillServiceImpl.nullStr;

@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(value = "收费2.0 pos账单相关接口")
public class ChargeBillController {
    private final ChargeBillService chargeBillService;

    private final AssetSupport assetSupport;

    @ApiOperation(value = "收费记录(已调通)", notes = "收费记录")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "receiptNumber", value = "收据号", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "chargeTypeId", value = "类型id", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "chargeTypeName", value = "类型名称", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "paymentSource", value = "支付来源", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemUuid", value = "收费项id", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "currentPage", value = "当前页数（默认为1）", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量（默认为10）", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getChargeRecord", method = {RequestMethod.GET})
    public ChargePageResponse<List<PosBillMonthVO>> getChargeRecord(@RequestParam Long communityUuid, @RequestParam(required = false) String receiptNumber,
                                                                    @RequestParam(required = false) String chargeTypeId, @RequestParam(required = false) String chargeTypeName,
                                                                    @RequestParam(required = false) String paymentSource,
                                                                    @RequestParam(defaultValue = "1") Integer currentPage, @RequestParam(defaultValue = "10") Integer pageSize,
                                                                    @RequestParam String token) throws ChargeBusinessException {
        return chargeBillService.listPosBill(PosBillQueryCondition.builder().communityId(communityUuid).receiptNumber(receiptNumber).chargeTypeId(chargeTypeId).chargeTypeName(chargeTypeName)
                .currentPage(currentPage).paymentSource(paymentSource).pageSize(pageSize).build());
    }

    @ApiOperation(value = "查询房屋账单列表[NEW]", notes = "查询房屋账单列表[NEW]")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "houseUuid", value = "房屋uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "paymentSource", value = "支付来源", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "receiptNumber", value = "收据号", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "chargeTypeId", value = "账单收费类型（routine：常规类，deposit：押金类，prestore：预存类，temporary：临时收费）", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemUuid", value = "收费项id", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "currentPage", value = "当前页数（默认为1）", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量（默认为10）", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getNewBillList", method = {RequestMethod.GET})
    public ChargePageResponse<List<PosBillMonthVO>> getHouseInfo(@RequestParam Long houseUuid, @RequestParam(required = false) String paymentSource,
                                                                 @RequestParam(required = false) String receiptNumber, @RequestParam(required = false) String chargeTypeId,
                                                                 @RequestParam(defaultValue = "1") Integer currentPage, @RequestParam(defaultValue = "10") Integer pageSize,
                                                                 @RequestParam String token) throws ChargeBusinessException {

        AssetDTO assetDTO = assetSupport.getAssetById(houseUuid);
        Assert.notNull(assetDTO, "资产不存在");
        return chargeBillService.listPosBill(PosBillQueryCondition.builder().communityId(getCommunityId(assetDTO)).assetId(houseUuid).receiptNumber(receiptNumber).chargeTypeId(chargeTypeId)
                .currentPage(currentPage).pageSize(pageSize).paymentSource(paymentSource).build());
    }

    public Long getCommunityId(AssetDTO assetDTO) throws ChargeBusinessException {
        if(assetDTO.getHouseDTO()!=null){
            return assetDTO.getHouseDTO().getCommunityId();
        }else if(assetDTO.getParkingSpaceDTO()!=null){
            return assetDTO.getParkingSpaceDTO().getCommunityId();
        }else {
            throw new ChargeBusinessException("无效资产"+assetDTO);
        }
    }

    @ApiOperation(value = "查询pos机掉单数量（5分钟内掉单）", notes = "查询pos机掉单数量")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "mercid", value = "商户号", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "deviceInfo", value = "设备号", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区id", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String")
    })
    @RequestMapping(value = "/getLostPayNum", method = {RequestMethod.GET})
    public ChargeResponse<Integer> getLostPayNum(String mercid, String deviceInfo, String communityId, String token) throws ChargeBusinessException {
        return new ChargeResponse<>(chargeBillService.getLostPayNum(mercid, deviceInfo, communityId));
    }


    @ApiOperation(value = "查询pos机掉单列表集合（5分钟内掉单）", notes = "查询pos机掉单列表集合")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "mercid", value = "商户号", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "deviceInfo", value = "设备号", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区id", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "type", value = "类型（0-pos掉单进行二次校验并刷新  ）", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String")
    })
    @RequestMapping(value = "/getLostPay", method = {RequestMethod.GET})
    public ChargeResponse<List<LostPay>> getLostPay(String mercid, String deviceInfo, String communityId, String type, String token) throws ChargeBusinessException {
        return chargeBillService.queryLostPay(mercid, deviceInfo, communityId);
    }


    @ApiOperation(value = "查询业主缴费项列表", notes = "查询业主缴费项列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseIdList", value = "房屋列表(逗号分隔)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @GetMapping(value = "/batchPay/getBatchCustomerItemList")
    public ChargeResponse<List<ReceivableV1>> getBatchCustomerItemList(@RequestParam String communityId, @RequestParam String houseIdList, @RequestParam String token) {
        List<Long> houseIds = Arrays.stream(houseIdList.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<ReceivableV1> receivableV1s = chargeBillService.listArrearsByAssets(Long.parseLong(communityId), houseIds).stream().map(this::toReceivableV1).collect(Collectors.toList());
        List<ReceivableV1> receivableV1List= receivableV1s.stream().collect(Collectors.groupingBy(ReceivableV1::getItemUuid)).values().stream().map(receivables -> {
            BigDecimal merge = receivables.stream().map(ReceivableV1::getTotalArrearsPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            receivables.get(0).setTotalArrearsPrice(merge);
            return receivables.get(0);
        }).collect(Collectors.toList());
        return new ChargeResponse<>(receivableV1List);
    }

    private ReceivableV1 toReceivableV1(ReceivableV2 receivableV2) {
        return new ReceivableV1(nullStr(receivableV2.getChargeItemId()), receivableV2.getChargeItemName(), receivableV2.getTotalArrearsPrice());
    }



    /**
     * 生成缴费（缴费+临时订单）订单并支付,接口定义见ipos工程的相同mapping
     */
    @PostMapping(value = "/addNewPayOrder")
    @Idempotent
    public ChargeResponse<PaymentResponse> addNewPayOrder(@RequestParam(required = false) String deviceInfo, @RequestParam(required = false) String mercid, @RequestParam String houseUuid,
                                                          @RequestParam String paymentMethod, @RequestParam String collectorId, @RequestParam String collectorName,
                                                          @RequestParam String payItemIds, @RequestParam String arrearsPrice, @RequestParam(required = false) String tempChargeData,
                                                          @RequestParam String totalPrice, @RequestParam(required = false) String payMember, @RequestParam(required = false) String bankTransactionNo,
                                                          @RequestParam(required = false) String arrivalDate, @RequestParam(required = false) String bankAccountUuid, @RequestParam(required = false) String bankAccountNum,
                                                          @RequestParam(required = false) String memo, @RequestParam String token) throws ChargeBusinessException {
        ReceivableAndOrderBillCreateRequest request = new ReceivableAndOrderBillCreateRequest(deviceInfo, mercid, houseUuid, paymentMethod, collectorId, collectorName, payItemIds, arrearsPrice,
                tempChargeData, totalPrice, payMember, bankTransactionNo, arrivalDate, bankAccountUuid, bankAccountNum, memo, token);
        return new ChargeResponse<>(chargeBillService.createBill(request));
    }

    /**
     * 小区临时订单--临停用
     */
    @PostMapping(value = "/addCommunityTempChargeInfo")
    @Idempotent(keyAppendSecond = true)
    public ChargeResponse<PaymentResponse> addCommunityTempChargeInfo(@RequestParam(required = false) String deviceInfo, @RequestParam(required = false) String mercid,
                                                                      @RequestParam String ownerId, @RequestParam String chargeItemUuid, @RequestParam String itemName,
                                                                      @RequestParam String amount, @RequestParam String paymentMethod, @RequestParam String collectorId,
                                                                      @RequestParam String collectorName, @RequestParam(required = false) String payMember, @RequestParam(required = false) String bankTransactionNo,
                                                                      @RequestParam(required = false) String arrivalDate, @RequestParam(required = false) String memo, @RequestParam String token) throws Exception {

        CommunityOrderBillCreateRequest request = new CommunityOrderBillCreateRequest(deviceInfo, mercid, ownerId, chargeItemUuid, itemName, amount, paymentMethod, collectorId, collectorName,
                payMember, bankTransactionNo, arrivalDate, memo, token);
        return new ChargeResponse<>(chargeBillService.createBill(request));
    }

    /**
     * 房间多临时订单下单
     */
    @PostMapping(value = "/addNewTempChargeInfo")
    @Idempotent
    public ChargeResponse<PaymentResponse> addNewTempChargeInfo(@RequestParam(required = false) String deviceInfo, @RequestParam(required = false) String mercid, @RequestParam String ownerId,
                                                                @RequestParam String tempChargeData, @RequestParam String amount, @RequestParam String paymentMethod,
                                                                @RequestParam String collectorId, @RequestParam String collectorName, @RequestParam String payMember,
                                                                @RequestParam(required = false) String bankTransactionNo, @RequestParam(required = false) String arrivalDate, @RequestParam(required = false) String bankAccountUuid,
                                                                @RequestParam(required = false) String bankAccountNum, @RequestParam(required = false) String memo, @RequestParam String token) throws Exception {
        OrdersBillCreateRequest request = new OrdersBillCreateRequest(deviceInfo, mercid, ownerId, tempChargeData, amount, paymentMethod, collectorId, collectorName, payMember, bankTransactionNo,
                arrivalDate, bankAccountUuid, bankAccountNum, memo, token);
        return new ChargeResponse<>(chargeBillService.createBill(request));
    }

    /**
     * 小区批量临时收费
     */
    @PostMapping(value = "/addNewCommunityTempChargeInfo")
    @Idempotent(keyAppendSecond = true)
    public ChargeResponse<PaymentResponse> addNewCommunityTempChargeInfo(@RequestParam(required = false) String deviceInfo, @RequestParam(required = false) String mercid,
                                                                         @RequestParam String ownerId, @RequestParam String tempChargeData, @RequestParam String amount,
                                                                         @RequestParam String paymentMethod, @RequestParam String collectorId, @RequestParam String collectorName,
                                                                         @RequestParam String payMember, @RequestParam(required = false) String bankTransactionNo, @RequestParam(required = false) String arrivalDate,
                                                                         @RequestParam(required = false) String bankAccountUuid, @RequestParam(required = false) String bankAccountNum, @RequestParam(required = false) String memo,
                                                                         @RequestParam String token) throws Exception {
        CommunityOrdersBillCreateRequest request = new CommunityOrdersBillCreateRequest(deviceInfo, mercid, ownerId, tempChargeData, amount, paymentMethod, collectorId, collectorName, payMember
                , bankTransactionNo, arrivalDate, bankAccountUuid, bankAccountNum, memo, token);
        return new ChargeResponse<>(chargeBillService.createBill(request));
    }

    @PostMapping(value = "/batchPay/addBatchPayOrder")
    @Idempotent
    public ChargeResponse<PaymentResponse> addBatchPayOrder(@RequestParam String paymentMethod, @RequestParam String collectorId, @RequestParam String collectorName,
                                                            @RequestParam String chargeData, @RequestParam String arrearsPrice, @RequestParam String totalPrice,
                                                            @RequestParam String penaltyMoney, @RequestParam String principalMoney, @RequestParam String customerId,
                                                            @RequestParam String customerName, @RequestParam(required = false) String payMember, @RequestParam(required = false) String bankTransactionNo,
                                                            @RequestParam(required = false) String arrivalDate, @RequestParam(required = false) String memo,
                                                            @RequestParam String communityId, @RequestParam String deadline, @RequestParam String token, @RequestParam String chargeType,
                                                            @RequestParam String deviceInfo, @RequestParam String mercid) throws Exception {
        BatchBillCreateRequest request = new BatchBillCreateRequest(paymentMethod, collectorId, collectorName, chargeData, arrearsPrice, totalPrice, penaltyMoney, principalMoney
                , customerId, customerName, payMember, bankTransactionNo, arrivalDate, memo, communityId, deadline, token, chargeType, deviceInfo, mercid);
        return new ChargeResponse<>(chargeBillService.createBill(request));

    }

    @GetMapping(value = "/batchPay/searchBatchPayingBillList")
    public ChargePageResponse<BillsVO> getPayItemList(String communityId, String keyword,
                                                      @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                      String token) {
        // todo sprint4暂时不做实现
        return new ChargePageResponse<>(new BillsVO(Lists.newArrayList()), 1, 1, 1, 0);
    }



    @PostMapping(value = "/batchPay/getBatchBillListByHouseId")
    public ChargeResponse<HouseReceivablesV3> getBatchBillListByHouseId( @RequestParam String ownerId,  @RequestParam String communityId,  @RequestParam String houseIdListStr,
                                                                         @RequestParam String itemList,  @RequestParam String deadline, @RequestParam  String token) throws ChargeBusinessException {
        List<Long> houseIds = JSON.parseArray(houseIdListStr, String.class).stream().map(Long::parseLong).collect(Collectors.toList());
        List<Long> chargeItemIds = JSON.parseArray(itemList, ReceivableV1.class).stream().map(receivableV1 -> Long.parseLong(receivableV1.getItemUuid())).collect(Collectors.toList());
        return new ChargeResponse<>(chargeBillService.listArrearsDetailByAssets(Long.parseLong(communityId),houseIds ,chargeItemIds,deadline));
    }

    /**
     * 获取批量缴费的详情
     * @param orderId 实收单id
     * @param communityId 项目id
     */
    @GetMapping(value = "/batchPay/getBatchOrderDetail")
    public ChargeResponse<OrderDetailVO> getBatchOrderDetail(@RequestParam String orderId,@RequestParam String communityId,@RequestParam String token) throws Exception {
        OrderDetailVO orderDetailByIncomeBill = chargeBillService.getOrderDetailByIncomeBill(Long.parseLong(orderId), Long.parseLong(communityId));
        return new ChargeResponse<>(orderDetailByIncomeBill);
    }

    /**
     * 废弃接口、等待前端更新作废
     */
    @GetMapping(value = "/setOrderPayNum")
    @Deprecated
    public ChargeResponse<String> setOrderPayNum() {
        return new ChargeResponse<>();
    }


}
