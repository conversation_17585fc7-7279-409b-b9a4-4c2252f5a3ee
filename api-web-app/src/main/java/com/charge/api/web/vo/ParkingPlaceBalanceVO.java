package com.charge.api.web.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 产权车位可用余额返回体
 * @Author: yjw
 * @Date: 2024/3/12 10:39
 */
@Data
public class ParkingPlaceBalanceVO implements Serializable {

    private static final long serialVersionUID = -3682740722787852642L;

    private Long itemId;

    private String itemName;

    private String totalAmount;

    private String predepositAmount;

    private String carryForwardAmount;

    private String month;

    private String parkingSpaceMsId;

    private String monthAmount;
}
