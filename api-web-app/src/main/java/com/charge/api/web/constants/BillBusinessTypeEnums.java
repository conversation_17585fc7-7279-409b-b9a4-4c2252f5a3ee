package com.charge.api.web.constants;

import com.charge.bill.enums.BusinessTypeEnum;

import java.util.Objects;

public enum BillBusinessTypeEnums {

    NORMAL("0", "routine"), //常规型
    DEPOSIT("9", "deposit"), //押金型
    REFUNDDEPOSIT("9", "deposit"), //退押金
    PRESTORE("1", "prestore"), //缴预存
    DEDUCTPRESTORE("3", "prestore"), //扣预存
    REFUNDRESTORE("6", "prestore"), //退预存
    TEMPORARY("4", "temporary"); //临时收费

    private String billTypeId;
    private String billTypeName;

    BillBusinessTypeEnums(String billTypeId, String billTypeName) {
        this.billTypeId = billTypeId;
        this.billTypeName = billTypeName;
    }

    public static BillBusinessTypeEnums fromCode(String id) {
        BillBusinessTypeEnums[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            BillBusinessTypeEnums value = var1[var3];
            if (Objects.equals(value.getBillTypeId(), id)) {
                return value;
            }
        }

        return null;
    }

    public String getBillTypeId() {
        return billTypeId;
    }

    public void setBillTypeId(String billTypeId) {
        this.billTypeId = billTypeId;
    }

    public String getBillTypeName() {
        return billTypeName;
    }

    public void setBillTypeName(String billTypeName) {
        this.billTypeName = billTypeName;
    }

    public static String getBillTypeName(String billTypeId) {
        for (BillBusinessTypeEnums billBusinessTypeEnums : BillBusinessTypeEnums.values()) {
            if (billTypeId.equals(billBusinessTypeEnums.getBillTypeId())) {
                return billBusinessTypeEnums.billTypeName;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
