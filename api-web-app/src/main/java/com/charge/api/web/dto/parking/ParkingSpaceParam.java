package com.charge.api.web.dto.parking;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/3/7 15:37
 */
@Data
public class ParkingSpaceParam  implements Serializable {

    private static final long serialVersionUID = -2578073508288062779L;

    /**
     * 小区id
     */
    @NotBlank(message = "项目ID不能为空")
    private String communityMsId;

    /**
     * 车场id
     */
    @NotBlank(message = "停车场ID不能为空")
    private String parkingMsId;

    /**
     * 车位id
     */
    @NotEmpty(message = "车位ID列表不能为空")
    private List<String> parkingSpaceMsId;

}
