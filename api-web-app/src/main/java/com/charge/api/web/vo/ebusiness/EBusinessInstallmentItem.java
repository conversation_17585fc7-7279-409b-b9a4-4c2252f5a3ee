package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/11/8 11:41
 */
@Data
public class EBusinessInstallmentItem {

    /**
     * 分期进度ID
     */
    @NotBlank(message = "分期进度ID不能为空")
    private String installmentId;

    /**
     * 分期进度顺序
     */
    @NotNull(message = "分期进度顺序不能为空")
    private Integer installmentSort;

    /**
     * 分期应付金额
     */
    @NotNull(message = "分期应付金额不能为空")
    private BigDecimal installmentTotalPrice;

    /**
     * 分期实际支付金额
     */
    @NotNull(message = "分期实际支付金额不能为空")
    private BigDecimal installmentPayPrice;

    /**
     * 商品明细
     */
    @NotEmpty(message = "商品明细不能为空")
    @Valid
    private List<EBusinesssFurnishOrderItem> orderItems;

    /**
     * 分期支付状态(未支付-UNPAID,已支付-PAID,部分支付-PART_PAID)
     */
    @NotBlank(message = "分期实际支付状态不能为空")
    private String payState;
}
