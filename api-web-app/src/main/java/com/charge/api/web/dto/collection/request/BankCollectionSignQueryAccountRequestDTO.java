package com.charge.api.web.dto.collection.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BankCollectionSignQueryAccountRequestDTO implements Serializable {
    private static final long serialVersionUID = -8360096699188199161L;

    @NotNull(message = "请输入项目ms id")
    private String communityMsId;

    @NotNull(message = "请输入卡号")
    private String bankAccount;

    @NotNull(message = "请输入配置id")
    private Long configId;
}
