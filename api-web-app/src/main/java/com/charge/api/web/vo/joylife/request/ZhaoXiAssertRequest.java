package com.charge.api.web.vo.joylife.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * description
 *
 * <AUTHOR>
 * @date 2023/12/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class ZhaoXiAssertRequest extends ZhaoXiCommunityReq {

    private static final long serialVersionUID = 2975728929297694877L;

    /**
     * 收费的资产id
     */
    private Long assetId;

    /**
     * 朝昔的资产id（当不传收费的资产id时需要）
     */
    private String assetMsId;

    /**
     * 资产类型：1-房间,2-车位 （当不传收费的资产id时需要）
     */
    private Integer assetType;

    /**
     * 收费对象类型（0-业主，1-开发商）
     */
    private Integer chargeObjectType;

}


