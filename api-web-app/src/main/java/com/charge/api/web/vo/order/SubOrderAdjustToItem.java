package com.charge.api.web.vo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 订单子单新增
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SubOrderAdjustToItem extends BaseChargeItem {

    /**
     * 子订单号
     */
    @NotBlank(message = "子订单号不能为空")
    private String subOrderNo;

    /**
     * 子订单金额
     */
    @NotNull(message = "子订单金额")
    private BigDecimal amount;

    /**
     * 货物名称
     */
    @NotBlank(message = "货物名称不能为空")
    private String goodsName;

    /**
     * 规格型号
     */
    private String specsType;


    /**
     * 货物单价
     */
    private BigDecimal goodsUnitPrice;

    /**
     * 物品数量
     */
    private Integer goodsQuantity;

    /**
     * 物品单位
     */
    private String goodsUnit;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税收分类编码
     */
    private String taxCateCode;

    /**
     * 类目
     */
    private String cateName;

}