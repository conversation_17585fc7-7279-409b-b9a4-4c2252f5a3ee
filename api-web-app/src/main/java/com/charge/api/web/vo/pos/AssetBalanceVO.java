package com.charge.api.web.vo.pos;

import com.charge.common.serializer.BigDecimalSerializer;
import com.charge.common.serializer.DesensitizeSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/9
 */
@NoArgsConstructor
@Data
@Builder
@AllArgsConstructor
public class AssetBalanceVO {

    @JsonProperty("houseUuid")
    private String houseUuid;
    @JsonProperty("residentUuid")
    private String residentUuid;
    @JsonProperty("userName")
    private String userName;
    @JsonProperty("mobile")
    @JsonSerialize(using = DesensitizeSerializer.class)
    private String mobile;
    @JsonProperty("gender")
    private Integer gender;
    @JsonProperty("arrearAmount")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal arrearAmount;
    @JsonProperty("balanceAmount")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal balanceAmount;
    @JsonProperty("depositAmount")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal depositAmount;
    @JsonProperty("communityName")
    private String communityName;
    @JsonProperty("buildingName")
    private String buildingName;
    @JsonProperty("unitName")
    private String unitName;
    @JsonProperty("houseName")
    private String houseName;
    @JsonProperty("status")
    private Integer status;
    /**
     *   `subject_type` varchar(64) DEFAULT NULL COMMENT '房屋类型(house,shops,card,lock)',
     */
    @JsonProperty("subjectType")
    private String subjectType;
}


