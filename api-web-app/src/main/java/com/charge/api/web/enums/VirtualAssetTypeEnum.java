package com.charge.api.web.enums;


import com.charge.common.enums.common.BaseError;
import com.charge.core.enums.BaseEnum;

/**
 * 异常
 *
 * <AUTHOR>
 * @date 2023/9/7
 */
public enum VirtualAssetTypeEnum implements BaseEnum<Integer> {

    COMMUNITY_VIRTUAL_ASSET(1,"项目虚拟房屋"),
    VIRTUAL_ASSET(2,"虚拟房屋"),
    ;

    final int code;
    final String value;

    VirtualAssetTypeEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return value;
    }

}
