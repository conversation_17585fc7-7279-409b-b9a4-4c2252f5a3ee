package com.charge.api.web.support;

import com.charge.api.web.vo.order.BaseChargeItem;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.config.client.item.ChargeItemClient;
import com.charge.config.dto.item.ChargeItemDTO;
import com.charge.config.dto.item.condition.ChargeItemQueryConditionDTO1;
import com.charge.config.dto.tax.TaxDetailDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 收费项税收相关
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChargeItemSupport {

    private final ChargeItemClient chargeItemClient;

    public TaxDetailDTO getTaxDetailDTO(Long taxId, List<TaxDetailDTO> taxDetailDTOList){
        for(TaxDetailDTO taxDetailDTO: taxDetailDTOList){
            if (taxDetailDTO.getId().equals(taxId)){
                return taxDetailDTO;
            }
        }
        return null;
    }

    public void fillChargeItem(List<? extends BaseChargeItem> chargeItems) throws ChargeBusinessException {
        chargeItems.forEach(chargeItem -> {
            Assert.isTrue(chargeItem.getItemId()!=null|| StringUtils.hasText(chargeItem.getItemCode()),"费项id和编码不能同时为空");
        });
        List<String> chargeItemCodes = chargeItems.stream().map(BaseChargeItem::getItemCode)
                .filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(chargeItemCodes)){
            return ;
        }
        ChargeItemQueryConditionDTO1 condition =new  ChargeItemQueryConditionDTO1();
        condition.setItemCode(chargeItemCodes);
        ChargeResponse<List<ChargeItemDTO>> listChargeResponse = chargeItemClient.listChargeItem(condition);
        List<ChargeItemDTO> chargeItemDTOS = AppInterfaceUtil.getResponseDataThrowException(listChargeResponse);

        if(CollectionUtils.isEmpty(chargeItemDTOS)||chargeItemDTOS.size()!=chargeItemCodes.size()){
            throw new IllegalArgumentException("部分收费项不存在");
        }
        Map<String, ChargeItemDTO> itemCodeToChargeItemMap = chargeItemDTOS.stream().collect(Collectors.toMap(ChargeItemDTO::getItemCode, Function.identity(), (v1, v2) -> v1));
        chargeItems.forEach(chargeItem -> {
            if(chargeItem.getItemId()==null){
                ChargeItemDTO chargeItemDTO = itemCodeToChargeItemMap.get(chargeItem.getItemCode());
                if(chargeItemDTO==null){
                    throw new IllegalArgumentException("收费项不存在");
                }
                chargeItem.setItemId(chargeItemDTO.getId());
            }
        });



    }

}
