package com.charge.api.web.service.pos;

import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.AssetBalancesVO;
import com.charge.api.web.vo.pos.UserVO;
import com.charge.common.exception.ChargeBusinessException;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/10
 */
public interface AssetService {

    ChargePageResponse<AssetBalancesVO> assetSearchPage(Long communityId, String keyword, String subjectType, Integer currentPage, Integer pageSize) throws ChargeBusinessException;

    ChargePageResponse<List<UserVO>> customerSearchPage(Long communityId, String keyword, Integer currentPage, Integer pageSize) throws ChargeBusinessException;

    void updateAssetMemo(Long assetId,String memo) throws ChargeBusinessException;


}
