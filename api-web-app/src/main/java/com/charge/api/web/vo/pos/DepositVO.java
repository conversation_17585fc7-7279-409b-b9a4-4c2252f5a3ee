package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@NoArgsConstructor
@Data
@AllArgsConstructor
public class DepositVO {
    /**
     * 主键id
     */
    private String id;

    /**
     * 住户id
     */
    private String householdId;

    /**
     * 住户姓名
     */
    private String householdName;

    /**
     * 房屋id
     */
    private String houseUuid;

    /**
     * 房屋名称
     */
    private String houseName;

    /**
     * 小区id
     */
    private String communityUuid;

    /**
     * 小区名称
     */
    private String communityName;

    /**
     * 押金项id
     */
    private String itemId;

    /**
     * 押金项名称
     */
    private String itemName;

    /**
     * 金额
     */
    private BigDecimal money;
    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 支付方式(0标识pos，1标识转账，2标识支票，3标识支付宝，4标识微信)
     */
    private String paymentMethod;

    /**
     * 支票类型
     */
    private String checkType;

    /**
     * 收据编号
     */
    private String receiptNumber;

    /**
     * 银行信息
     */
    private String bankInfo;

    /**
     * 支付账号
     */
    private String paymentAccount;

    /**
     * 收款账号
     */
    private String receiveAccount;

    /**
     * 凭证照片地址
     */
    private String receiptUrl;

    /**
     * 交易流水号
     */
    private String payNum;

    /**
     * 押金支付状态(0:支付失败，1:支付成功)
     */
    private String payStatus;

    /**
     * 创建时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date createTime;

    /**
     * 租户id
     */
    private String cropId;
    /**
     * 订单号
     */
    private String orderNum;

    /**
     * 二维码
     */
    private String url;

    /**
     * 交款人
     */
    private String collectorName;

    /**
     * 交款人
     */
    private String draweeName;

    /**
     * 标的物类型
     */
    private String houseType;

    /**
     * 支付类型
     */
    private String paymentType;

    /**
     * 代缴人ID
     */
    private String employeeId;

    /**
     * 代缴人
     */
    private String employeeName;

    /**
     * 银行流水号
     */
    private String bankTransactionNo;

    /**
     * 到账时间
     */
    private Date arrivalTime;

    /**
     * 缴费人
     */
    private String payMember;

    private String billStatus;

    private String enterStatus;

    private String memo;//备注

    private String customerCode;

    private String incomeDetailUuid;

    private String incomeDetailName;
}
