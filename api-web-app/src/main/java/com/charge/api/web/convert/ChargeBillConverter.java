package com.charge.api.web.convert;

import com.charge.api.web.vo.lakala.SubjectSearch;
import com.charge.api.web.vo.pos.PosBillQueryCondition;
import com.charge.api.web.vo.pos.PosBillVO;
import com.charge.bill.dto.income.PosIncomeBillDTO;
import com.charge.bill.dto.income.PosIncomeConditionDTO;
import com.charge.common.util.DateMapper;
import com.charge.joylife.dto.JoylifeCommunityMerchantDTO;
import com.charge.pay.dto.CommunityMerchantDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/02/28
 */
@Mapper(uses = DateMapper.class)
public interface ChargeBillConverter {

    ChargeBillConverter INSTANCE = Mappers.getMapper(ChargeBillConverter.class);

    @Mappings({
            @Mapping(target = "pageNum", source = "currentPage")
    })
    PosIncomeConditionDTO buildPosIncomeConditionDTO(PosBillQueryCondition conditionVO);

    @Mappings({
            @Mapping(target = "unitName", expression = "java(posIncomeBillDTO.getUnitName() == null ? \"\" : posIncomeBillDTO.getUnitName())"),
            @Mapping(target = "buildingName", expression = "java(posIncomeBillDTO.getBuildingName() == null ? \"\" : posIncomeBillDTO.getBuildingName())"),
            @Mapping(target = "itemName", source = "posIncomeBillDTO.goodsName"),
            @Mapping(target = "payId", source = "posIncomeBillDTO.id"),
            @Mapping(target = "receiptNo", source = "posIncomeBillDTO.orderNum"),
            @Mapping(target = "price", expression = "java((com.charge.common.enums.common.CreateBillTypeEnum.REFUND.getCode().equals(String.valueOf(posIncomeBillDTO.getCreateBillType()))) ? com.charge.api.web.util.CancelZeroUtil.precisionDecimal(posIncomeBillDTO.getMoney().multiply(new java.math.BigDecimal(-1))):com.charge.api.web.util.CancelZeroUtil.precisionDecimal(posIncomeBillDTO.getMoney()))"),
            @Mapping(target = "paymentMethod", source = "posIncomeBillDTO.paymentChannel"),
            @Mapping(target = "createTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "month", expression = "java(com.charge.common.util.DateUtils.getDateMonth(posIncomeBillDTO.getCreateTime()))"),
            @Mapping(target = "consumeType", expression = "java(com.charge.api.web.constants.IncomeBillConstants.convertToType(posIncomeBillDTO.getBusinessTypeList()))"),
            @Mapping(target = "billStatus", expression = "java(posIncomeBillDTO.getPayStatus() == 1 ? \"0\" : \"1\")"),
            @Mapping(target = "paymentResource", source = "posIncomeBillDTO.paymentTerminal"),
            @Mapping(target = "subjectName", source = "subjectSearch.subjectName"),
            @Mapping(target = "subjectId", source = "subjectSearch.subjectId"),
            @Mapping(target = "subjectType", source = "subjectSearch.subjectType")
    })
    PosBillVO buildBillDetailVo(PosIncomeBillDTO posIncomeBillDTO, SubjectSearch subjectSearch);


    List<JoylifeCommunityMerchantDTO> convertMerchantDTO(List<CommunityMerchantDTO> merchantDTOList);
}
