package com.charge.api.web.vo.arrearsnotice;

import com.charge.common.serializer.BigDecimalSerializer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * MonthReceivables
 * <p>
 * Description: 月份应收明细
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/22
 */
@Data
public class MonthReceivablesVO {

 @JsonIgnore
 private Date monthDate;
 private String month;
 private List<ArrearsNoticeReceivableVO> assetReceivables;
 @JsonSerialize(using = BigDecimalSerializer.class)
 private BigDecimal monthTotalArrearsAmount;

}