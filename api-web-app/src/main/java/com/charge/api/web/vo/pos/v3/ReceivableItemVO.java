package com.charge.api.web.vo.pos.v3;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/10/19 16:04
 */
@Data
public class ReceivableItemVO implements Serializable {

    private static final long serialVersionUID = 5820152752448594018L;

    /**
     * 收费项id
     */
    private Long itemId;

    /**
     * 收费项名称
     */
    private String itemName;

    /**
     * 应收单ID
     */
    private Long receivableBillId;

    /**
     * 金额
     */
    private BigDecimal amount;
}
