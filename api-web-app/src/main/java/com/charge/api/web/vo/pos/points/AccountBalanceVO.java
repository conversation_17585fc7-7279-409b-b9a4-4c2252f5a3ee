package com.charge.api.web.vo.pos.points;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/11/3 15:30
 */
@Data
public class AccountBalanceVO implements Serializable {

    /**
     * 积分余额
     */
    private Integer points;

    /**
     * 可用余额
     */
    private Integer availablePoints;

    /**
     * 冻结积分
     */
    private Integer holdPoints;

    /**
     * 大会员pid
     */
    private String pid;
}
