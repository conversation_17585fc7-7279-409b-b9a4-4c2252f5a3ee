package com.charge.api.web.vo.joylife.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/02
 */
@Data
public class HousePayItemListVO implements Serializable {
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String communityName;

    /**
     * 楼栋名称
     */
    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    /**
     * 单元名称
     */
    @ApiModelProperty(value = "单元名称")
    private String unitName;

    /**
     * 房间id
     */
    @ApiModelProperty(value = "房间id")
    private String houseId;

    /**
     * 房间名称
     */
    @ApiModelProperty(value = "房间名称")
    private String houseName;

    /**
     * 是否存在银行托收中数据：0-无 1-有
     */
    @ApiModelProperty(value = "是否存在银行托收中数据：0-无 1-有")
    private String orderStatus;

    /**
     * 总欠费（本金 + 违约金）
     */
    @ApiModelProperty(value = "总欠费")
    private BigDecimal totalArrears;

    @ApiModelProperty(value = "欠费列表")
    private List<HousePayItemVO> itemList;

}
