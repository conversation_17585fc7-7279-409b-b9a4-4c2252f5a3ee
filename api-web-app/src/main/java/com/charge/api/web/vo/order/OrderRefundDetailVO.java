package com.charge.api.web.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 子订单退款单vo
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderRefundDetailVO extends BaseOrderVO {

    private static final long serialVersionUID = 3984622926506892690L;

    /**
     * 收费系统的 退款单号
     */
    private String refundNo;


    private List<SubOrderAmount> subOrderAmounts;

    /**
     * 调整金额
     */
    private BigDecimal amount;


    /**
     * 收款银行账号
     */
    private String payeeBankAccount;

    /**
     * 收款户名
     */
    private String acceptAccountName;

    /**
     *
     * 收款开户行
     */
    private String acceptAccountOpeningBank;


    /**
     * 退款完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date refundTime;

    /**
     * 退款状态： 3退款已驳回, 4退款成功, 5退款失败  9 退款中
     */
    private Integer refundStatus;

    /**
     * 退款申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date refundApplyTime;

    /**
     * 支付方式(微信：WECHAT 支付宝：ALIPAY 线下支付：OFFLINE)
     */
    private String payType;

}
