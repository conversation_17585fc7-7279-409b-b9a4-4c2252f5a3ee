package com.charge.api.web.vo.arrearsnotice;

import com.charge.common.serializer.IdSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 通知单支付结果
 * <p>
 * Description:
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ArrearsNoticePayResultVO {
    /**
     * 缴费状态 1已支付 0 未支付
     */
    private Integer payStatus;
    private String communityName;
    private BigDecimal totalAmount;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;
    private String orderNum;
    private String collectorName;
    private String payMember;
    /**
     * 支付方式 0 刷卡  1 转帐  2 支付宝 3 微信
     */
    private Integer payMethod;
    private Integer assetNumber;
    private String externalOrderNum;
    @JsonSerialize(using = IdSerializer.class)
    private Long assetTransactionId;
}