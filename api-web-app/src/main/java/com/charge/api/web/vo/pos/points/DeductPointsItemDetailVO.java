package com.charge.api.web.vo.pos.points;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 积分扣除明细
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeductPointsItemDetailVO implements Serializable {
    private static final long serialVersionUID = -2768053921818760350L;

    /**
     * 预收账户id
     */
    private Long predepositAccountId;

    /**
     * 预存收费项id
     */
    private Long itemId;

    /**
     * 扣除积分数量
     */
    private Integer deductPoints;

    /**
     * 扣除-赠分款金额
     */
    private BigDecimal pointsMoney;

}
