package com.charge.api.web.vo.pos;

import com.charge.maindata.pojo.dto.CustomerDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class HouseReceivablesV3 {

    @JsonProperty("residentCode")
    private String residentCode;
    @JsonProperty("residentName")
    private String residentName;
    @JsonProperty("residentList")
    private List<CustomerDTO> residentList;
    @JsonProperty("houseList")
    private List<HouseReceivableV3> houseList;
    @JsonProperty("houseCount")
    private Integer houseCount;
    @JsonProperty("memo")
    private String memo;

}


