package com.charge.api.web.vo.pos.points;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预收退款 收费项
 * <AUTHOR>
 */
@Data
public class PredepositRefundItemVO implements Serializable {

    private static final long serialVersionUID = 4514792836245199573L;
    /**
     * 预收账户id
     */
    @NotNull(message = "预收账户id不能为空")
    private Long predepositAccountId;

    /**
     * 预存收费项id
     */
    @NotNull(message = "收费项id不能为空")
    private Long itemId;

    /**
     * 预存收费项名称
     */
    @NotBlank(message = "收费项名不能为空")
    private String itemName;

    /**
     * 退款金额
     */
    @DecimalMin(value = "0.01", message = "退款金额大于等于0.01")
    @Digits(integer = 8, fraction=2, message = "小数位不能超过两位")
    private BigDecimal refundAmount;

}
