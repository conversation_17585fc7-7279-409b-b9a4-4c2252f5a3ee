package com.charge.api.web.vo.joylife.request;

import com.charge.api.web.vo.joylife.BatchArrearsOrderList;
import com.charge.bill.dto.income.AssetArrearsListDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@ApiModel(description = "生活缴费请求实体", value = "生活缴费实体")
@Data
public class BatchPayDataRequest implements Serializable {

    private static final long serialVersionUID = 4212334121131L;

    @ApiModelProperty(value = "小区ID（运营平台小区ID）", required = true)
    @NotNull(message = "小区Id参数缺失,请重试")
    private String communityId;

    @ApiModelProperty(value = "小区名称", required = true)
    @NotNull(message = "小区名称参数缺失,请重试")
    private String communityName;

    @ApiModelProperty(value = "支付来源(微信小程序：WECHAT_APPLET，悦家APP：YUEHOME_PAY，润钱包：CRT_PAY)", required = true)
    @NotNull(message = "支付来源参数缺失,请重试")
    private String paymentSource;

    @ApiModelProperty(value = "系统参考号", required = false)
    private String referNo;

    @ApiModelProperty(value = "支付方式（微信支付：3）", required = true)
    @NotNull(message = "支付方式参数缺失,请重试")
    private String paymentMethod;

    @ApiModelProperty(value = "操作系统类型（0：Android；1：iOS）", required = true)
    @NotNull(message = "操作系统参数缺失,请重试")
    private String systemType;

    @ApiModelProperty(value = "支付人(当前登录用户姓名)", required = true)
    @NotNull(message = "用户名称参数缺失,请重试")
    private String userName;

    @ApiModelProperty(value = "客户ID（当前登录用户唯一标识，记录用户缴费历史）", required = true)
    @NotNull(message = "用户Id参数缺失,请重试")
    private String userId;

    @ApiModelProperty(value = "缴费数据(使用查询欠费列表数据)，json数组字符串", required = true)
    @NotEmpty(message = "缴费信息参数缺失,请重试")
    private List<BatchArrearsOrderList> arrearsOrderList;

    @ApiModelProperty(value = "总价格（两位小数）", required = true)
    @NotNull(message = "总金额参数缺失,请重试")
    private String totalPrice;

    @ApiModelProperty(value = "实缴金额（两位小数）:微信真正支付的金额,没有积分抵扣时等于totalPrice", required = true)
    @NotNull(message = "实际支付金额参数缺失,请重试")
    private String actualPrice;

    @ApiModelProperty(value = "积分抵扣金额（两位小数），默认0", required = false)
    private String pointsMoney;

    @ApiModelProperty(value = "抵扣积分数量,默认0", required = false)

    private String points;

    @ApiModelProperty(value = "积分账户", required = false)
    private String equityAccount;

    @ApiModelProperty(value = "商品名称描述", required = false)
    private String orderDes;

    @ApiModelProperty(value = "终端IP", required = true)
    @NotNull(message = "终端IP参数缺失,请重试")
    private String mchCreateIp;

    @ApiModelProperty(value = "备注", required = false)
    private String memo;

    @ApiModelProperty(value = "AppID", required = true)
    @NotNull(message = "APPId参数缺失,请重试")
    private String subAppid;

    @ApiModelProperty(value = "微信用户小程序关注商家的openid(小程序缴费必传)", required = false)
    private String subOpenid;

    @ApiModelProperty(value = "电子发票", required = false)
    private boolean needReceipt;

    @ApiModelProperty(value = "客户类型(0一户多房)", required = false)
    private String chargeType;

    @ApiModelProperty(value = "用户手机号", required = false)
    private String phone;

    @ApiModelProperty(value = "朝昔用户msId", required = true)
    private String customId;

    /**
     * 买家支付宝账号(支付宝时必传)
     */
    private String buyerLogonId;
    /**
     * 实际支付下单费项（去除了积分抵扣费项）
     */
    List<AssetArrearsListDTO> actualList;

    /**
     * 是否是app支付，使用其他渠道拉起支付宝app（非支付宝小程序）的需要传该值为true
     */
    private Boolean payNative;

    /**
     * 支付成功后跳转地址
     */
    private String returnUrl;

    /**
     * 赠送的万象星数量
     */
    private Integer giftStarCount;

}
