package com.charge.api.web.service.maindata;

import com.charge.api.web.vo.joylife.request.ListCommunityReq;
import com.charge.api.web.vo.joylife.response.CommunityVO;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.enums.DataAuthKeyEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.DataAuthThreadContext;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.PageCommunityCondition;
import com.charge.maindata.enums.CommunitySourceEnum;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.user.client.AuthUserClient;
import com.charge.user.dto.user.AuthUserInfoRequestDTO;
import com.charge.user.dto.user.AuthUserInfoResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/8/27 9:15
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class CommunityService {

    private final AuthUserClient authUserClient;

    private final CommunityClient communityClient;

    public PagingDTO<CommunityVO> listCommunityByUser(ListCommunityReq reqCondition) throws ChargeBusinessException {
        if(StringUtils.hasText(reqCondition.getUser())){
            AuthUserInfoResponseDTO authUserInfoResponseDTO = AppInterfaceUtil.getResponseDataThrowException(authUserClient.getUserInfo(AuthUserInfoRequestDTO.builder().ldapName(reqCondition.getUser()).build()));
            if(Objects.isNull(authUserInfoResponseDTO)){
                throw new ChargeBusinessException(ErrorInfoEnum.E2003.getCode(), "用户信息查询为空");
            }
            Long userId = authUserInfoResponseDTO.getUserId();
            DataAuthThreadContext.put(DataAuthKeyEnum.USER_ID.getCode(), userId);
        }
        PageCommunityCondition pageCommunityCondition = PageCommunityCondition.builder().searchName(reqCondition.getCommunityName()).communityMsIds(reqCondition.getCommunityMsIds())
                .communityId(reqCondition.getCommunityId()).pageNum(reqCondition.getPageNum()).pageSize(reqCondition.getPageSize()).build();
        if(Objects.nonNull(reqCondition.getIsVirtual())){
            if(reqCondition.getIsVirtual()) {
                pageCommunityCondition.setCommunitySource(CommunitySourceEnum.CHARGE_SYSTEM.getCode());
            } else {
                pageCommunityCondition.setCommunitySource(CommunitySourceEnum.ZHAO_XI.getCode());
            }
        }
        PagingDTO<CommunityDTO> pagingDTO = AppInterfaceUtil.getResponseDataThrowException(communityClient.pageCommunity(pageCommunityCondition));
        List<CommunityVO> communityVOS = convertToCommunityVOS(pagingDTO.getList());
        return new PagingDTO<>(communityVOS, pagingDTO.getPageNum(), pagingDTO.getPageSize(), pagingDTO.getTotalCount());
    }

    private List<CommunityVO> convertToCommunityVOS(List<CommunityDTO> communityDTOS){
        if(CollectionUtil.isEmpty(communityDTOS)){
            return Collections.emptyList();
        }
        return communityDTOS.stream().map(e -> {
            return CommunityVO.builder().communityId(e.getId()).communityName(e.getName())
                    .msId(e.getMsId()).isVirtual(Objects.equals(e.getCommunitySource(), CommunitySourceEnum.CHARGE_SYSTEM.getCode())).build();
        }).collect(Collectors.toList());
    }

}
