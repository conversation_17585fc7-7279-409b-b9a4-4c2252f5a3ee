package com.charge.api.web.vo.joylife.invoice;

import com.charge.api.web.vo.joylife.request.ZhaoXiCommunityReq;
import com.charge.invoice.dto.CreateInvoiceRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class InvoiceBatchCreateRequestVO extends ZhaoXiCommunityReq {

    @NotEmpty(message = "发票信息不能为空")
    List<CreateInvoiceRequestDTO> invoiceRequestDTOS;
}
