package com.charge.api.web.service.ebusiness;

import com.charge.api.web.vo.ebusiness.EBusinessItemCondition;
import com.charge.api.web.vo.ebusiness.EBusinessItemDetail;
import com.charge.common.exception.ChargeBusinessException;

import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/13 16:18
 */
public interface EBusinessItemService {

    List<EBusinessItemDetail> getItemVOList(EBusinessItemCondition condition) throws ChargeBusinessException;

}
