package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/15
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class ReceivableItemV1 {
    @JsonProperty("itemName")
    private String itemName;
    @JsonProperty("price")
    private BigDecimal price;
    @JsonProperty("createTime")
    private String createTime;
}


