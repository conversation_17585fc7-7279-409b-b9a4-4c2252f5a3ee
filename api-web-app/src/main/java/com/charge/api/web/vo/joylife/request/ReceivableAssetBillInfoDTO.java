package com.charge.api.web.vo.joylife.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-07 18:24
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceivableAssetBillInfoDTO implements Serializable {

    private static final long serialVersionUID = -161827961659965978L;

    /**
     * 资产id
     */
    private String assetId;

    /**
     * 资产账单信息
     */
    private List<ReceivableBillInfoDTO> billInfoList;
}
