package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/8
 */
@NoArgsConstructor
@Data
@Builder
@AllArgsConstructor
public class OrderDetailVO {

    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("communityName")
    private String communityName;
    @JsonProperty("totalPrice")
    private BigDecimal totalPrice;
    @JsonProperty("paymentMethod")
    private String paymentMethod;
    @JsonProperty("paymentName")
    private String paymentName;
    @JsonProperty("customerName")
    private String customerName;
    @JsonProperty("collectorId")
    private String collectorId;
    @JsonProperty("collectorName")
    private String collectorName;
    @JsonProperty("houseAmount")
    private String houseAmount;
    @JsonProperty("orderSource")
    private String orderSource;
    @JsonProperty("itemList")
    private List<PaymentItemVO> itemList;
    @JsonProperty("memo")
    private String memo;
    @JsonProperty("createTime")
    private String createTime;
    @JsonProperty("bankTransactionNo")
    private String bankTransactionNo;
    @JsonProperty("orderNum")
    private String orderNum;
    @JsonProperty("billName")
    private String billName;
    @JsonProperty("billStatus")
    private String billStatus;
    @JsonProperty("printTimes")
    private Integer printTimes;
    @JsonProperty("operationStatus")
    private String operationStatus;

}


