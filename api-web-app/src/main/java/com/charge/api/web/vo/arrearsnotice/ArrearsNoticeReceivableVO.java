package com.charge.api.web.vo.arrearsnotice;

import com.charge.common.serializer.BigDecimalSerializer;
import com.charge.common.serializer.IdSerializer;
import com.charge.config.enums.ChargeTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;

/**
 * ArrearsNoticeAssetReceivableVO
 * <p>
 * Description: 缴费通知单明细
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/22
 */
@Data
public class ArrearsNoticeReceivableVO {
    @JsonSerialize(using = IdSerializer.class)
    private Long id;
    private Long assetId;
    private String itemName;
    @JsonSerialize(using = IdSerializer.class)
    private Long receivableId;
    private String chargeStandard;
    private String chargeAmount;
    /**
     * 费项备注字段
     */
    private String extra;
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal arrearsAmount;
    @JsonSerialize(using = BigDecimalSerializer.class)
    /**
     *  违约金欠费金额
     */
    private BigDecimal penaltyArrearsAmount;
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalArrearsAmount;

    public void fillExtra(String chargeType) {
        //仪表类的才需要额外的字段
        if (StringUtils.hasText(chargeAmount)&& ChargeTypeEnum.METER_NORMAL.getType().equals(chargeType)) {
            extra = "(" + chargeAmount + ")";
        }
    }


}