package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/18 14:46
 */
@Data
public class EBusinessPayItem {

    /**
     *  二级订单号
     */
    @NotBlank(message = "二级订单号不能为空")
    private String oid;
    /**
     *  商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String skuName;
    /**
     *  sku编码
     */
    @NotBlank(message = "sku编码不能为空")
    private String skuCode;
    /**
     * 商品数量
     */
    @NotNull(message = "商品数量不能为空")
    private Integer num;
    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空")
    private BigDecimal payAmount;
}
