package com.charge.api.web.vo.joylife;

import com.charge.api.web.vo.joylife.response.MonthOrderItemArrears;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class BatchArrearsOrderList {

    @ApiModelProperty(value = "房屋ID")
    private String houseId;
    @ApiModelProperty(value = "房屋名称")
    private String houseName;
    @ApiModelProperty(value = "房屋编码")
    private String houseCode;
    @ApiModelProperty(value = "房屋类型")
    private String houseType;
    @ApiModelProperty(value = "房屋欠费总金额（含违约金）")
    private String houseTotalAmount;
    @ApiModelProperty(value = "房屋欠费明细")
    private List<MonthOrderItemArrears> billList;
    /**
     * 赠送的万象星数量(按资产维度)
     */
    @ApiModelProperty(value = "赠送的万象星数量(按资产维度)")
    private Integer giftStarCount;

}
