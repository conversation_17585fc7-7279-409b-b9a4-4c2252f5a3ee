package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/18 14:58
 */
@Data
public class EBusinessApportionItem {

    /**
     * 分摊项id
     */
    @NotBlank(message = "分摊项id不能为空")
    private String apportionId;
    /**
     * 分摊类型(运费、满减、满赠、优惠券、拼团)
     */
    @NotBlank(message = "分摊类型不能为空")
    private String apportionTypeName;
    /**
     * 分摊金额，优惠分摊为负数 运费分摊为正数
     */
    @NotNull(message = "分摊金额不能为空")
    private BigDecimal apportionAmount;
}
