package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class ReceivableV3 {
    private String id;
    @JsonProperty("itemId")
    private String itemId;
    @JsonProperty("chargeItemId")
    private String chargeItemId;
    @JsonProperty("itemName")
    private String itemName;
    @JsonProperty("price")
    private BigDecimal price;
    @JsonProperty("belongYear")
    private String belongYear;
    @JsonProperty("penalty")
    private BigDecimal penalty;
    @JsonProperty("createTs")
    private String createTs;
}


