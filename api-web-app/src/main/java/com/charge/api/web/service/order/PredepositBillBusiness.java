package com.charge.api.web.service.order;

import com.charge.bill.enums.PredepositTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @description:
 * @author: zhen<PERSON><PERSON><PERSON><PERSON>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PredepositBillBusiness extends BillBusiness{

    /**
     * 预存类型(0表示通用预存，1表示专项预存，2待结转)
     * @see PredepositTypeEnum
     */
    private Integer predepositType;
}
