package com.charge.api.web.vo.joylife.invoice;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class InvoiceOrderBillQueryVO {
    private static final long serialVersionUID = 2249224867719401704L;
    @NotBlank(
            message = "communityMsId 不能为空"
    )
    private String communityMsId;
    @NotBlank(
            message = "assetMsId 不能为空"
    )
    private String assetMsId;
    @NotNull(
            message = "assetType 不能为空"
    )
    private Integer assetType;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            locale = "zh_CN",
            timezone = "GMT+8"
    )
    private Date paymentBeginTime;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            locale = "zh_CN",
            timezone = "GMT+8"
    )
    private Date paymentEndTime;
    /**
     * 支付人id
     */
    private String payMemberId;
}
