package com.charge.api.web.dto.ssdp;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 退款付款完成
 * 
 * <AUTHOR>
 * @date 2023/2/27
 */
@Data
public class SsdpRefundPaymentDTO implements Serializable {

    private static final long serialVersionUID = -294905699340803298L;

    /**
     * 单据总数，每次1笔
     */
    private String totalCount;

    /**
     * 是否付款完成 true 完成
     */
    private Boolean refundStatus;

    /**
     * 退款记录ID
     */
    private String refundRecordId;

    /**
     * 银行退票标识 true是银行退票
     */
    private Boolean bankRefundFlag;

    /**
     * 银行退票时间，到秒的时间戳
     */
    private Long bankRefundTime;

    /**
     * 退款列表详情
     */
    private List<SsdpRefundPaymentDetailDTO> recordList;

}
