package com.charge.api.web.enums;

import com.charge.core.util.CollectionUtil;
import com.charge.invoice.enums.InvoiceTypeEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
public enum ParkingInvoiceModeEnum {
    DIGITAL(1, "数电", Arrays.asList(InvoiceTypeEnum.DIGITAL_GENERAL_INVOICE,InvoiceTypeEnum.DIGITAL_SPECIAL_INVOICE)),
    TAX_DEVICE(2, "税控",Arrays.asList(InvoiceTypeEnum.ELECTRONIC_GENERAL_INVOICE)),
    UN_SUPPORT(-1, "暂不支持开票",null),
    ;

    private final Integer code;
    private final String value;
    private final List<InvoiceTypeEnum> invoiceTypeCodeEnums;


    ParkingInvoiceModeEnum(Integer code, String value, List<InvoiceTypeEnum> invoiceTypeEnums) {
        this.code = code;
        this.value = value;
        this.invoiceTypeCodeEnums=invoiceTypeEnums;
    }


    public static ParkingInvoiceModeEnum fromInvoiceTypeCode(List<Integer> allowInvoiceType) {
        if(CollectionUtil.isEmpty(allowInvoiceType)){
            return UN_SUPPORT;
        }
        for(InvoiceTypeEnum invoiceTypeEnum:DIGITAL.getInvoiceTypeCodeEnums()){
            if(allowInvoiceType.contains(invoiceTypeEnum.getCode())){
                return DIGITAL;
            }
        }
        for(InvoiceTypeEnum invoiceTypeEnum:TAX_DEVICE.getInvoiceTypeCodeEnums()){
            if(allowInvoiceType.contains(invoiceTypeEnum.getCode())){
                return TAX_DEVICE;
            }
        }
        return UN_SUPPORT;
    }
}
