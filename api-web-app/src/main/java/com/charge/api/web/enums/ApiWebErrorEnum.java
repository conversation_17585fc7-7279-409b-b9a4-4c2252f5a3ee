package com.charge.api.web.enums;


import com.charge.common.enums.common.BaseError;

/**
 * 异常
 *
 * <AUTHOR>
 * @date 2023/9/7
 */
public enum ApiWebErrorEnum implements BaseError {

    TOKEN_NOT_EXIST_OR_EXPIRE(-3,"token非法或者失效，请重新登录"),
    ACCOUNT_OR_PASSWORD_MISS(-1,"登录失败，账号或密码缺失"),
    NO_PERMISSION(-2,"登录失败，账号或密码缺失");


    final int code;
    final String value;

    ApiWebErrorEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return value;
    }
}
