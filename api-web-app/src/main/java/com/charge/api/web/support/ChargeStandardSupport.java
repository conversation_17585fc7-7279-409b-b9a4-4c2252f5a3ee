package com.charge.api.web.support;

import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.config.client.standard.StandardConfigClient;
import com.charge.config.dto.standard.StandardConfigDTO;
import com.charge.config.dto.standard.condition.StandardConfigBatchCondition;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ChargeStandardSupport
 * <p>
 * Description:
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/10
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChargeStandardSupport {

    private final StandardConfigClient standardConfigClient;

    public Map<Long, StandardConfigDTO> getStandardConfigMap(Long communityId, List<Long> standardConfigIds) throws ChargeBusinessException {
        StandardConfigBatchCondition standardConfigBatchCondition = new StandardConfigBatchCondition();
        standardConfigBatchCondition.setCommunityIds(Lists.newArrayList(communityId));
        standardConfigBatchCondition.setStandardConfigIds(standardConfigIds);
        standardConfigBatchCondition.setFillTaxPoint(false);
        ChargeResponse<List<StandardConfigDTO>> response = standardConfigClient.queryBatchDetailList(standardConfigBatchCondition);
        List<StandardConfigDTO> configDTOS = AppInterfaceUtil.getResponseDataThrowException(response);
        if (CollectionUtils.isEmpty(configDTOS)) {
            return new HashMap<>();
        }
        return configDTOS.stream().collect(Collectors.toMap(StandardConfigDTO::getId, Function.identity(), (k1, k2) -> k2));
    }
}