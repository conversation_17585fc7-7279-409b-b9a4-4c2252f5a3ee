package com.charge.api.web.vo.joylife;

import com.charge.api.web.vo.joylife.request.ZhaoXiAssertRequest;
import com.charge.api.web.vo.joylife.response.ChargeStandardVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2024/7/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssetChargeStandVO extends ZhaoXiAssertRequest {
    private List<ChargeStandardVO> chargeStandards;

}


