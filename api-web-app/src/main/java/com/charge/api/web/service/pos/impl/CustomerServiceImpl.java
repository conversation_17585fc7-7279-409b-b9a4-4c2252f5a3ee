package com.charge.api.web.service.pos.impl;

import com.charge.api.web.service.pos.CustomerService;
import com.charge.api.web.vo.pos.points.AssetCustomerQueryVO;
import com.charge.api.web.vo.pos.points.CustomerPointsVO;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.enums.DwellStateEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/11/3 14:14
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CustomerServiceImpl implements CustomerService {

    private final AssetClient assetClient;

    @Override
    public List<CustomerPointsVO> getAssetCustomerList(AssetCustomerQueryVO queryVO) throws ChargeBusinessException {
        AssetDTO assetDTO = AppInterfaceUtil.getResponseDataThrowException(assetClient.oneAsset(AssetCondition.builder().id(Long.parseLong(queryVO.getAssetId())).build()));
        if((Objects.isNull(assetDTO.getHouseDTO()) || CollectionUtil.isEmpty(assetDTO.getHouseDTO().getListCustomer()))
                && (Objects.isNull(assetDTO.getParkingSpaceDTO()) || CollectionUtil.isEmpty(assetDTO.getParkingSpaceDTO().getListCustomer()))){
            return Lists.newArrayList();
        }
        List<CustomerDTO> customerList = new ArrayList<>();
        if(AssetTypeEnum.HOUSE.getCode().equals(assetDTO.getType())) {
            customerList = assetDTO.getHouseDTO().getListCustomer();
        } else if (AssetTypeEnum.PARKING_SPACE.getCode().equals(assetDTO.getType())) {
            customerList = assetDTO.getParkingSpaceDTO().getListCustomer();
        } else {
            log.error("{}|未知的资产类型,assetId={}", LogCategoryEnum.BUSSINESS, queryVO.getAssetId());
            throw new ChargeBusinessException(ErrorInfoEnum.E2004.getCode(), "资产类型异常");
        }
        List<CustomerPointsVO> list = customerList.stream()
                .filter(e -> DwellStateEnum.STAY.getCode().equals(e.getDwellState()) || DwellStateEnum.STAY.getCode().equals(e.getUseStatus()))
                .map(e -> {
                    CustomerPointsVO customerPointsVO = new CustomerPointsVO();
                    customerPointsVO.setCustomerType(e.getCustomerType());
                    customerPointsVO.setCustomerName(e.getCustomerName());
                    //手机号返回格式不确定（可能+86-也可能不带），目前逻辑为截取最后11位
                    String phone = StringUtils.isNotBlank(e.getPhone()) && e.getPhone().length() >= 11 ? e.getPhone().substring(e.getPhone().length() - 11) : "";
                    customerPointsVO.setPhone(phone);
                    return customerPointsVO;
                }).filter(e -> {
                    //过滤掉手机号为空或者格式不合法的客户
                    String reg = "^1[3,4,5,6,7,8,9][0-9]{9}$";
                    Pattern pattern = Pattern.compile(reg);
                    Matcher matcher = pattern.matcher(e.getPhone());
                    return StringUtils.isNotBlank(e.getPhone()) && matcher.matches();
                }).collect(Collectors.toList());
        return list;
    }
}
