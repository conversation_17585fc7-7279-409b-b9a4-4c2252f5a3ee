package com.charge.api.web.service.pay.impl;

import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.core.enums.TraceContextKeyEnum;
import com.charge.core.util.JacksonConverter;
import com.charge.core.util.TraceContextUtil;
import com.charge.general.client.bff.PayCallbackClient;
import com.charge.general.dto.PayCallbackResultDTO;
import com.charge.general.dto.PayOrderResultDTO;
import com.charge.pay.PayBusinessCallback;
import com.charge.pay.client.CommunityMerchantClient;
import com.charge.pay.client.PayClient;
import com.charge.pay.domain.MerchantDO;
import com.charge.pay.domain.PayOrderResultDO;
import com.charge.pay.dto.condition.MerchantConditionDTO;
import com.charge.pay.dto.pay.MerchantDTO;
import com.charge.pay.dto.pay.PayAttachDTO;
import com.charge.pay.dto.pay.TradeResultDTO;
import com.charge.pay.enums.PayChannelEnum;
import com.charge.pay.enums.PayTradeStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 支付回调业务处理
 * <AUTHOR>
 * date 2023/06/02
 */
@Service
@Slf4j
public class PayBusinessCallbackImpl implements PayBusinessCallback {
    /**
     * 支付来源 电商=MALL 朝昔=（小程序：WECHAT_APPLET、app:YUEHOME_PAY）
     */
    private static ThreadLocal<String> PAYMENT_SOURCE_TL = new ThreadLocal<>();


    @Resource
    private PayClient payClient;

    @Resource
    private CommunityMerchantClient communityMerchantClient;
    @Resource
    private PayCallbackClient payCallbackClient;

    private PayAttachDTO getPayAttach(String attach) {
        if (StringUtils.isNotBlank(attach)) {
            try {
                return JacksonConverter.read(attach, PayAttachDTO.class);
            } catch (Exception ex) {
                log.info("attach error!", ex);
            }
        }
        return new PayAttachDTO();
    }

    @Override
    public MerchantDO getMerchantDO(PayOrderResultDO payOrderResultDO, String channelType) {

        //若为拉卡拉渠道，验签无需获取商户信息，直接返回
        String payChannelUpperCase = channelType.toUpperCase();
        if(payChannelUpperCase.equals(PayChannelEnum.LAKALA.getCode()) || payChannelUpperCase.equals(PayChannelEnum.UNIONPAY.getCode())){
            return new MerchantDO();
        }
        PayAttachDTO payAttach = getPayAttach(payOrderResultDO.getAttach());
        MerchantConditionDTO merchantConditionDTO = new MerchantConditionDTO();
        merchantConditionDTO.setMerchantNumber(payOrderResultDO.getMchId());
        merchantConditionDTO.setPayChannel(channelType);
        merchantConditionDTO.setCommunityId(payAttach.getCommunityId());
        merchantConditionDTO.setPaymentSource(payAttach.getPaymentSource());
        ChargeResponse<MerchantDTO> chargeResponse =
                communityMerchantClient.getMerchantByNumber(merchantConditionDTO);
        if (!chargeResponse.isSuccess()) {
            return null;
        }
        MerchantDTO merchantDTO = chargeResponse.getContent();
        ThreadContext.put(TraceContextKeyEnum.COMMUNITY_ID.getCode(), Objects.isNull(payAttach.getCommunityId()) ? null : payAttach.getCommunityId() + "");
        PAYMENT_SOURCE_TL.set(merchantDTO.getPaymentSource());
        return BeanCopierWrapper.copy(merchantDTO, MerchantDO.class);
    }

    @Override
    public boolean deal(PayOrderResultDO payOrderResultDO) {
        if (!Objects.equals(PaymentTerminalEnum.MALL.getCode(), PAYMENT_SOURCE_TL.get())) {
            boolean chargeSystemDeal = chargeSystemDeal(payOrderResultDO);
            if (!chargeSystemDeal) {
                PAYMENT_SOURCE_TL.remove();
                return Boolean.FALSE;
            }
        }
        TradeResultDTO tradeResultDTO = BeanCopierWrapper.copy(payOrderResultDO, TradeResultDTO.class);
        try {
            ChargeResponse chargeResponse = payClient.saveTradeResult(tradeResultDTO);
            PAYMENT_SOURCE_TL.remove();
            return chargeResponse.isSuccess();
        } catch (ChargeBusinessException e) {
            log.error("支付回调更新支付交易表异常：", e);
        }
        return Boolean.FALSE;
    }

    /**
     * description: 招行和威富通的回调销单处理
     * author: wuChao
     * date: 2023/7/5
     * param [payOrderResultDO]
     * return boolean
     **/
    private boolean chargeSystemDeal(PayOrderResultDO payOrderResultDO) {
        if (Objects.equals(payOrderResultDO.getTradeState(), PayTradeStateEnum.SUCCESS.getCode())){
            PayOrderResultDTO payOrderResultDTO = BeanCopierWrapper.copy(payOrderResultDO, PayOrderResultDTO.class);
            try {
                payOrderResultDTO.setCommunityId(TraceContextUtil.getCommunityId());
                ChargeResponse<PayCallbackResultDTO> chargeResponse = payCallbackClient.operate(payOrderResultDTO);
                if (!chargeResponse.isSuccess()) {
                    log.info("[支付下单回调操作],更新账单异常,{}", chargeResponse);
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            }catch (ChargeBusinessException ex){
                log.error("回调消单处理异常", ex);
            }
        }
        return Boolean.FALSE;
    }
}
