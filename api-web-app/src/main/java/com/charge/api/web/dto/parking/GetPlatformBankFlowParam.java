package com.charge.api.web.dto.parking;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: GetPlatformBankFlowParam
 * @Author: wangle
 * @Description: 获取云平台银行收款业务流水接口调用参数
 * @Date: 2021/11/11 15:44:27
 * @Version: 1.0
 */
@Data
public class GetPlatformBankFlowParam implements Serializable {

    private static final long serialVersionUID = -64905777243765398L;
    /**
     * D日数据总条数
     */
    private String totalNum;
    /**
     * 批次号，标识D日传输数据批次 ，需保证每日唯一性
     */
    private String batchId;
    /**
     * 本批次数据总条数，最大500条
     */
    private int currentNum;
    /**
     * 本批次是否为最后一批
     */
    private boolean isDone;

    /**
     * 停车场收费业务流水list
     */
    private List<PlatformBankFlowRecord> recordList;

}
