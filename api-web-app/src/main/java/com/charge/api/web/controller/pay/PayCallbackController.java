package com.charge.api.web.controller.pay;

import com.alibaba.fastjson.JSONObject;
import com.charge.common.constant.MetricConstants;
import com.charge.common.util.EnumUtil;
import com.charge.core.enums.StatusEnum;
import com.charge.pay.PayBase;
import com.charge.pay.PayBusinessCallback;
import com.charge.pay.PayFactory;
import com.charge.pay.enums.PayChannelEnum;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;

/**
 * 支付回调接口
 * <AUTHOR>
 * date 2023/06/02
 */
@RestController
@RequestMapping("/pay/v1/callback")
@Api(description = "支付回调接口")
@Slf4j
public class PayCallbackController {

    private static final String RESULT_SUCCESS ="success";

    @Resource
    private PayBusinessCallback payBusinessCallback;

    private String headStr(HttpServletRequest httpServletRequest){
        JSONObject jsonObject = new JSONObject();
        Enumeration<String> headerNames = httpServletRequest.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            jsonObject.put(headerName, httpServletRequest.getHeader(headerName));
        }
       return jsonObject.toJSONString();
    }

    @PostMapping(value = "/{channel}")
    public Object notify(@PathVariable String channel, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        if (!EnumUtil.exist(PayChannelEnum.values(), channel)) {
            //转为大写再验证一遍
            channel = channel.toUpperCase();
            if (!EnumUtil.exist(PayChannelEnum.values(), channel)) {
                httpServletResponse.setHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.FAIL.getCode());
                log.error("channel not exist：{}", channel);
                return null;
            }
        }
        PayBase callbackPay = (PayBase)PayFactory.getCallbackPay(channel);
        Object response = null;
        try {
            Object requestBody;
            if (channel.equals(PayChannelEnum.UNIONPAY.getCode()) || channel.toUpperCase().equals(PayChannelEnum.UNIONPAY.getCode())) {
                String context=httpServletRequest.getParameter("context");
                String mac = httpServletRequest.getParameter("mac");
                requestBody =new JSONObject().fluentPut("body",context).fluentPut("mac",mac);
                log.info("callback UNIONPAY context:{},mac:{}",context, mac);
            }else {
                String body = IOUtils.toString(httpServletRequest.getInputStream(), StandardCharsets.UTF_8);
                if (channel.equals(PayChannelEnum.LAKALA.getCode()) || channel.toUpperCase().equals(PayChannelEnum.LAKALA.getCode())) {
                    String authorization = httpServletRequest.getHeader("authorization");
                    requestBody =new JSONObject().fluentPut("body",body).fluentPut("authorization",authorization);
                }else {
                    requestBody = body;
                }
                log.info("callback {} body:{}",channel, requestBody);
            }
            response = callbackPay.callback(requestBody, payBusinessCallback);
            if (RESULT_SUCCESS.equals(response)) {
                httpServletResponse.setHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.SUCCESS.getCode());
            } else {
                httpServletResponse.setHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.FAIL.getCode());
            }
        } catch (IOException e) {
            log.error("{} notify error,{} ",channel,e.getMessage(), e);
            response=callbackPay.callbackResponse(false,null);
            httpServletResponse.setHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.FAIL.getCode());
        }
        return response;
    }
}
