package com.charge.api.web.service.joylife;

import com.charge.api.web.util.ResultPage;
import com.charge.api.web.vo.joylife.request.YueCardPayRequestVO;
import com.charge.api.web.vo.joylife.response.YueCardPayDetailVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;

/**
 * 月卡订单接口
 * Author: yjw
 * Date: 2023/4/12 13:42
 */
public interface YueAppParkingService {

    ResultPage getParkingBillList(String cardId, Integer currentPage,
                                  Integer pageSize) throws ChargeBusinessException;

    /**
     * 月卡支付
     *
     * @param payVO payVO
     * @return  成功/失败
     * @throws ChargeBusinessException
     */
    ChargeResponse createCardPayBills(YueCardPayRequestVO payVO) throws ChargeBusinessException;

    /**
     * 根据订单号获取停车月卡缴费详情
     *
     * @param orderNum 订单号
     * @return 成功/失败
     */
    ChargeResponse<YueCardPayDetailVO> cardPayDetail(String orderNum) throws ChargeBusinessException;
}
