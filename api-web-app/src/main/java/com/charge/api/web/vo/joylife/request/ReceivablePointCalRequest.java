package com.charge.api.web.vo.joylife.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-05 18:29
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceivablePointCalRequest implements Serializable {

    private static final long serialVersionUID = 1341336230111405345L;

    /**
     * 项目id
     */
    @NotEmpty(message = "项目id不能为空")
    private String communityId;

    /**
     * 积分抵扣金额（两位小数），默认0
     */
    private String pointsMoney;

    /**
     * 资产账单信息
     */
    List<ReceivableAssetBillInfo> assetBillInfos;




}
