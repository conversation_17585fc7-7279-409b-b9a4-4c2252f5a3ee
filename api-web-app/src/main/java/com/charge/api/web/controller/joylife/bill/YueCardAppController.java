package com.charge.api.web.controller.joylife.bill;

import com.charge.api.web.service.joylife.YueAppParkingService;
import com.charge.api.web.util.ResultPage;
import com.charge.api.web.vo.joylife.request.YueCardPayRequestVO;
import com.charge.api.web.vo.joylife.request.YueRefreshCardRequest;
import com.charge.api.web.vo.joylife.response.YueCardPayDetailVO;
import com.charge.bill.client.IncomeCarriedForwardClient;
import com.charge.bill.dto.income.IncomeCarriedForwardConditionDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.TraceContextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/11
 */
@Api(value = "月卡相关接口")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class YueCardAppController {

    private final YueAppParkingService yueAppParkingService;

    private final IncomeCarriedForwardClient incomeCarriedForwardClient;

    /**
     * 停车月卡支付
     *
     * @param yueCardPayRequest yueCardPayRequest
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/app/parking/createPayOrder")
    public ChargeResponse createCardPayBills(@RequestBody @Validated YueCardPayRequestVO yueCardPayRequest) throws ChargeBusinessException {
        return yueAppParkingService.createCardPayBills(yueCardPayRequest);
    }

    /**
     * 根据订单号获取停车月卡缴费详情
     *
     * @param orderNum 订单号
     * @return
     */
    @GetMapping("/app/parking/cardPayDetail")
    public ChargeResponse<YueCardPayDetailVO> cardPayDetail(@RequestParam("orderNum") String orderNum) throws ChargeBusinessException {
        log.info("{}|项目id|{}", LogCategoryEnum.BUSSINESS, TraceContextUtil.getCommunityId());
        return yueAppParkingService.cardPayDetail(orderNum);
    }


    @ApiOperation(value = "查询月卡缴费记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "cardId", value = "月卡ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "currentPage", value = "当前页数", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量", required = false, dataType = "Integer")
    })
    @GetMapping("/app/parking/billList")
    public ResultPage getParkingBillList(String cardId,
                                         @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                         @RequestParam(value = "pageSize", defaultValue = "9999") Integer pageSize) throws ChargeBusinessException{
        log.info("{}|项目id|{}", LogCategoryEnum.BUSSINESS, TraceContextUtil.getCommunityId());
        return yueAppParkingService.getParkingBillList(cardId, currentPage, pageSize);
    }

    @ApiOperation(value = "月卡开卡完成后更新月卡ID")
    @PostMapping("/api/zhaoxi/parking/refreshCard")
    public ChargeResponse refreshCard(@RequestBody @Validated YueRefreshCardRequest request) throws ChargeBusinessException {
        return incomeCarriedForwardClient.refreshCard(IncomeCarriedForwardConditionDTO.builder().orderNum(request.getOrderNum()).cardId(request.getCardId()).build());
    }

}
