package com.charge.api.web.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class DeliveryGroupItem {
    private Long id;

    private Long groupId;

    private Long itemId;

    private String itemName;

    private String preItemUuid;

    private String preItemName;

    private String billingId;

    private String billingName;

    private Integer month;

    private BigDecimal money;

    private Date createTime;

    private Date modifyTime;

}