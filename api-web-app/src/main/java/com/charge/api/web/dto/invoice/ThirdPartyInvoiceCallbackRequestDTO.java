package com.charge.api.web.dto.invoice;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/09/05/ 19:24
 * @description
 */
@Data
public class ThirdPartyInvoiceCallbackRequestDTO implements Serializable {

    private static final long serialVersionUID = 7139984812348274940L;

    /**
     * 发票主信息
     */
    private ThirdPartyInvoiceMainDTO invoiceMain;

    /**
     * 发票商品行信息
     */
    private List<ThirdPartyInvoiceDetailDTO> invoiceDetails;

    /**
     * 原始单据列表
     */
    private List<ThirdPartyInvoiceSalesBillRequestDTO> salesBills;

    /**
     * 预制发票信息
     */
    private List<ThirdPartyInvoiceRelationRequestDTO> relationList;
}
