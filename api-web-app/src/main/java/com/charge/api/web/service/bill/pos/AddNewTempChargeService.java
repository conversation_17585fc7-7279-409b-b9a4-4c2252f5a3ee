package com.charge.api.web.service.bill.pos;

import com.charge.api.web.vo.lakala.OrdersBillCreateRequest;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.domain.AssetPayDTO;
import com.charge.bill.dto.domain.AssetPaymentDetailDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.maindata.pojo.dto.AssetDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * pos小区多房间临时收费下单
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddNewTempChargeService {

    private final CreateBillFlowService createOrderService;

    public CreateBillResponse createBill(OrdersBillCreateRequest request, AssetDTO asset) throws ChargeBusinessException {
        return createOrderService.createOrder(buildAssetPayDTO(request, asset));
    }

    /**
     * 构建AssetPayDTO
     * @param request
     * @param asset
     * @return
     */
    private AssetPayDTO buildAssetPayDTO(OrdersBillCreateRequest request, AssetDTO asset) throws ChargeBusinessException {
        AssetPayDTO assetPayDTO = new AssetPayDTO();
        assetPayDTO.setAssetPayBaseDTO(buildAssetPayBaseDTO(request, asset));
        assetPayDTO.setAssetPaymentDetailDTO(buildAssetPaymentDetailDTO(request, asset));
        return assetPayDTO;
    }

    /**
     * 构建基础信息
     * @param request
     * @param asset
     * @return
     */
    private AssetPayBaseDTO buildAssetPayBaseDTO(OrdersBillCreateRequest request, AssetDTO asset) throws ChargeBusinessException {
        AssetPayBaseDTO assetPayBaseDTO = new AssetPayBaseDTO();
        FillCommonUtil.fillAssetPayBaseCommunityInfo(asset, assetPayBaseDTO);
        assetPayBaseDTO.setOrderNum(IdGeneratorSupport.getIstance().nextId());
        assetPayBaseDTO.setActualPrice(new BigDecimal(request.getAmount()));
        assetPayBaseDTO.setPaymentMethod(FillCommonUtil.toPaymentMethodEnum(Integer.parseInt(request.getPaymentMethod())).getPaymentCode());
        assetPayBaseDTO.setPayMember(request.getPayMember());
        assetPayBaseDTO.setCollectorId(request.getCollectorId());
        assetPayBaseDTO.setCollectorName(request.getCollectorName());
        assetPayBaseDTO.setMemo(request.getMemo());
        assetPayBaseDTO.setBankAccountNo(request.getBankTransactionNo());
        assetPayBaseDTO.setBankAccountUuid(request.getBankAccountUuid());
        assetPayBaseDTO.setPaymentTerminal(PaymentTerminalEnum.POS.getCode());
        assetPayBaseDTO.setClientSourceEnum(ClientSourceEnum.POS_BATCH_HOUSE_TEMP_PAY);
        assetPayBaseDTO.setPayHouseCount(1);
        return assetPayBaseDTO;
    }
    /**
     * 构建缴费信息
     * @param request
     * @param assetDTO
     * @return
     */
    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(OrdersBillCreateRequest request, AssetDTO assetDTO) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 资产信息
        assetPaymentDetailDTO.setBillAssetInfoDTO(FillCommonUtil.buildBillAssetInfo(assetDTO));
        // 临时订单信息
        assetPaymentDetailDTO.setOrderItems(FillCommonUtil.buildOrderItem(request.getTempChargeData()));
        return assetPaymentDetailDTO;
    }
}
