package com.charge.api.web.vo.joylife.response;

import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 欠费月份统计和明细
 */
@Data
public class ReceivableBillMonthVO implements Serializable {
	/**
	 * 费用月份
	 */
	@ApiModelProperty(value = "费用月份")
	private    String    month;
	/**
	 * 金额
	 */
	@ApiModelProperty(value = "金额")
	private    String    amount;
	@ApiModelProperty(value = "账单详情")
	/**
	 * 账单详情
	 */
	private List<ReceivableBillDetailVO> detailList;

	public static List<ReceivableBillMonthVO> from(List<ReceivableBillDTO> ReceivableBillDTOList) {
		List<ReceivableBillMonthVO> result=new ArrayList<>();
		if(!CollectionUtils.isEmpty(ReceivableBillDTOList)){
			Map<String,List<ReceivableBillDTO>> orderItemGroupByBelongYears=ReceivableBillDTOList.stream().filter(item-> item!=null&&StringUtils.isNotBlank(item.getBelongYears()))
					.collect(Collectors.groupingBy(ReceivableBillDTO::getBelongYears));
			orderItemGroupByBelongYears.forEach((key,value)->{
				ReceivableBillMonthVO monthOrderItemArrears=new ReceivableBillMonthVO();
				Optional<BigDecimal> amountOptional=value.stream()
						.filter(ReceivableBillDTO->ReceivableBillDTO.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode()))
						.map(ReceivableBillDTO->(ReceivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : ReceivableBillDTO.getArrearsAmount()).add(ReceivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : ReceivableBillDTO.getPenaltyArrearsAmount()))
						.reduce(BigDecimal::add);
				BigDecimal amount=amountOptional.isPresent()?amountOptional.get():BigDecimal.ZERO;
				List<ReceivableBillDetailVO> orderItemArrears=new ArrayList<>();
				value.forEach(item->orderItemArrears.add(ReceivableBillDetailVO.from(item)));
				orderItemArrears.sort(Comparator.comparing(ReceivableBillDetailVO::getEffectTime,Comparator.nullsLast(Comparator.naturalOrder())));
				monthOrderItemArrears.setMonth(key);
				monthOrderItemArrears.setAmount(amount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
				monthOrderItemArrears.setDetailList(orderItemArrears);
				result.add(monthOrderItemArrears);
			});
		}
		return result;
	}

	public static List<ReceivableBillMonthVO> fromHangup(List<ReceivableBillDTO> ReceivableBillDTOList) {
		List<ReceivableBillMonthVO> result=new ArrayList<>();
		if(!CollectionUtils.isEmpty(ReceivableBillDTOList)){
			Map<String,List<ReceivableBillDTO>> orderItemGroupByBelongYears=ReceivableBillDTOList.stream().filter(item-> item!=null&&StringUtils.isNotBlank(item.getBelongYears()))
					.collect(Collectors.groupingBy(ReceivableBillDTO::getBelongYears));
			orderItemGroupByBelongYears.forEach((key,value)->{
				ReceivableBillMonthVO monthOrderItemArrears=new ReceivableBillMonthVO();
				Optional<BigDecimal> amountOptional=value.stream()
						.map(ReceivableBillDTO->(ReceivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO : ReceivableBillDTO.getArrearsAmount()).add(ReceivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : ReceivableBillDTO.getPenaltyArrearsAmount()))
						.reduce(BigDecimal::add);
				BigDecimal amount=amountOptional.isPresent()?amountOptional.get():BigDecimal.ZERO;
				List<ReceivableBillDetailVO> orderItemArrears=new ArrayList<>();
				value.forEach(item->orderItemArrears.add(ReceivableBillDetailVO.from(item)));
				orderItemArrears.sort(Comparator.comparing(ReceivableBillDetailVO::getEffectTime,Comparator.nullsLast(Comparator.naturalOrder())));
				monthOrderItemArrears.setMonth(key);
				monthOrderItemArrears.setAmount(amount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
				monthOrderItemArrears.setDetailList(orderItemArrears);
				result.add(monthOrderItemArrears);
			});
		}
		return result;
	}
}
