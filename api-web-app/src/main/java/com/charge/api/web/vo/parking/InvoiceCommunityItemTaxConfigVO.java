package com.charge.api.web.vo.parking;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class InvoiceCommunityItemTaxConfigVO {

    /**
     * 税收分类编码
     */
    private String code;

    /**
     * 货物劳物名称
     */
    private String goodsName;
    /**
     *
     * 商品服务分类
     */
    private String goodsServiceCate;
    /**
     *
     * 税率
     */
    private BigDecimal taxRate;
    /**收费项ID  */
    private String  itemCode;

    /**收费项名称  */
    private String itemName;

    /**
     * 是否享受税收优惠政策
     */
    private String taxPre;

    /**
     * 税收优惠
     */
    private String taxPreCon;
}
