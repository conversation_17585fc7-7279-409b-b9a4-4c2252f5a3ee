package com.charge.api.web.constants;

/**
 * 支付相关常量
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/4/10.
 */
public class PayRelatedConstants {

    /*交易类型*/
    public static final String TRANSACTION_RECEIVALBLES = "0"; //收款
    public static final String TRANSACTION_REFUND = "1"; //退款
    public static final String TRANSACTION_CARRYOVER = "2"; //结转

    /*订单支付状态*/
    public static final String ORDER_NOT_PAY = "0"; //未支付
    public static final String ORDER_PAY_SUCCESS = "1"; //已支付
    public static final String ORDER_PAY_FAILED = "2"; //支付失败
    public static final String ORDER_COLLECTION = "3"; //托收中
    public static final String ORDER_PAY_HOLD = "4"; //挂起
    public static final String ORDER_NOT_EFFECTIVE = "5"; //未生效
    public static final String ORDER_PAY_WAIT = "6"; //待支付
    public static final String ORDER_PAY_DEVELOPING = "7"; //挂账中

    /*订单支付状态名称*/
    public static final String ORDER_NOT_PAY_CONTENT = "未支付"; //未支付
    public static final String ORDER_PAY_SUCCESS_CONTENT = "已支付"; //已支付
    public static final String ORDER_PAY_FAILED_CONTENT = "支付失败"; //支付失败
    public static final String ORDER_PAY_HOLD_CONTENT = "已挂起"; //已挂起
    public static final String ORDER_NOT_EFFECTIVE_CONTENT = "未生效"; //未生效
    public static final String ORDER_PAY_WAIT_CONTENT = "待支付"; //待支付
    public static final String ORDER_PAY_DEVELOPING_CONTENT = "挂账中"; //挂账中

    /*交易流水表业务交易类型*/
    public static final String BUSINESS_TYPE_NORMAL_PAY = "0"; //普通缴费
    public static final String BUSINESS_TYPE_GUARANTY_PAY = "1"; //交押金
    public static final String BUSINESS_TYPE_GUARANTY_BACK = "2"; //押金退还
    public static final String BUSINESS_TYPE_DEPOSIT_PAY = "3"; //交预存
    public static final String BUSINESS_TYPE_DEPOSIT_MINUS = "4"; //扣除预存
    public static final String BUSINESS_TYPE_DEPOSIT_BACK = "5"; //预存退还
    public static final String BUSINESS_TYPE_TEMP_PAY = "6"; //临时收费
    public static final String BUSINESS_TYPE_BANK_COLLECTION = "7"; //银行托收
    public static final String BUSINESS_TYPE_CARRY_FORWARD = "8"; //收待结转
    public static final String BUSINESS_TYPE_BUCKLE_CARRYOVER_PAY = "9";//扣款待结转
    public static final String BUSINESS_TYPE_RETREAT_CARRYOVER_PAY = "10";//退款待结转
    public static final String BUSINESS_TYPE_TRADE_DISCOUNT_DEPOSIT_PAY = "11"; //商业折扣优惠预存
    public static final String BUSINESS_TYPE_CARD_PAY = "12"; //月卡缴费

    /*交易流水表支付状态*/
    public static final String PAYMENT_ORDER_NOT_PAY = "0"; //未支付
    public static final String PAYMENT_ORDER_PAY_SUCCESS = "1"; //已支付
    public static final String PAYMENT_ORDER_PAY_FAILED = "2"; //支付失败
    public static final String PAYMENT_ORDER_REFUND_FAILED = "3"; //退款失败
    public static final String PAYMENT_ORDER_REFUND_SUCCESS = "4"; //退款成功
    public static final String PAYMENT_ORDER_REFUND_DOING = "5"; //退款中
    public static final String PAYMENT_TRANSACTION_NOT_EXIST = "6"; //交易流水不存在

    /*交易流水关于作废红冲状态*/
    public static final String PAYMENT_STATUS_NORMAL = "0"; //正常
    public static final String PAYMENT_STATUS_CANCEL = "1"; //已作废
    public static final String PAYMENT_STATUS_RED = "2"; //已红冲
    public static final String PAYMENT_STATUS_REFUND = "3"; //红冲退款中

    /* 批量账单*/
    public static final String BILL_STATUS_CANCELED = "3";

    /*账单类型*/
    public static final String BILL_TYPE_NORMAL = "0"; //正常
    public static final String BILL_TYPE_ORIGINAL = "1"; //原账单
    public static final String BILL_TYPE_NEGATIVE = "2"; //负数账单
    public static final String BILL_TYPE_ADJUST = "3"; //调整账单
    public static final String BILL_TYPE_TRADE_DISCOUNT = "15"; //优惠预存账单
    public static final String BILL_TYPE_EQUITY_ADJUST = "19"; //权益性调整账单（含积分抵扣）
    public static final String BILL_INCOME_CARRY_FORWARD = "20"; //收入结转账单


    public static final String CANCEL_METHOD_CANCEL = "0"; //直接作废
    public static final String CANCEL_METHOD_CANCEL_ADJUST = "1"; //作废调整
    public static final String RED_METHOD_RED = "2"; //直接红冲
    public static final String RED_METHOD_RED_REFUND = "3"; //红冲退款
    public static final String RED_METHOD_RED_ADJUST = "4"; //红冲调整

    /*交易流水是否已经入账*/
    public static final String ENTER_STATUS_NO = "0"; //未入账
    public static final String ENTER_STATUS_YES = "1"; //已入账

    /*支付方式（订单表存储）*/
    public static final String PAYMENT_METHOD_WEIXIN = "weixin"; //微信
    public static final String PAYMENT_METHOD_SWIFTPASS = "swiftpass"; //微信
    public static final String PAYMENT_METHOD_ALIPAY = "alipay"; //支付宝
    public static final String PAYMENT_METHOD_POS = "POS"; //刷卡
    public static final String PAYMENT_METHOD_EMPLOYEE = "EMPL"; //员工代付
    public static final String PAYMENT_METHOD_TRANSFER_AND_CHECK = "TRANSFER_AND_CHECK"; //转账与支票

    public static final String PAYMENT_TERMINAL_APP = "app";

    /*支付方式（业务消单表存储）*/
    public static final String PAYMENT_METHOD_POS_CODE = "0"; //pos
    public static final String PAYMENT_METHOD_TRANSFER_CODE = "1"; //转账
    public static final String PAYMENT_METHOD_CHEQUE_CODE = "2"; //支票
    public static final String PAYMENT_METHOD_ALIPAY_CODE = "3"; //支付宝
    public static final String PAYMENT_METHOD_WEIXIN_CODE = "4"; //微信


    /*支付方式-pos核销回调的支付方式编码）*/
    public static final String PAYMENT_METHOD_POS_CODE_CALL_BACK = "0"; //pos
    public static final String PAYMENT_METHOD_ALIPAY_CODE_CALL_BACK = "2"; //支付宝
    public static final String PAYMENT_METHOD_WEIXIN_CODE_CALL_BACK = "1"; //微信
    public static final String PAYMENT_METHOD_PRESTORE_CODE = "5"; //预存扣款
    public static final String PAYMENT_METHOD_EMPLOYEE_CODE = "6"; //员工代付
    public static final String PAYMENT_METHOD_BANK = "7"; //银行托收
    public static final String PAYMENT_METHOD_DERATE = "8"; //减免
    public static final String PAYMENT_METHOD_CRT_CODE = "9"; //  润钱包方式
    public static final String PAYMENT_METHOD_CRT_EQUITY_ADJUST = "10"; // 权益性调整（含华润通积分）
    public static final String PAYMENT_INCOME_CARRY_FORWARD = "11"; // 收入结转
    public static final String PAYMENT_METHOD_CANCEL_ADJUSTMENT = "21"; //作废调整
    public static final String PAYMENT_METHOD_RECEIVEABLE_ADJUSTED = "22"; //应收调整
    public static final String PAYMENT_METHOD_RESERVE_ADJUSTMENT = "23"; //直接红冲
    public static final String PAYMENT_METHOD_RESERVE_REFUND = "24"; //红冲退款
    public static final String PAYMENT_METHOD_RESERVE_ADJUST = "25"; //红冲调整
    public static final String PAYMENT_METHOD_REFUND_PRESTORE = "26"; //退款转预存
    public static final String PAYMENT_METHOD_PRESTORE_ADJUST = "27"; //预存调整

    /*金融平台交易结果状态码*/
    public static final String TRANSACTION_PAYMENT_STATUS_CODE = "90001"; //交易成功

    /*支付传递给支付平台的商品名称*/
    public static final String GOODSNAME_FOR_PROPERTY = "物业服务费(合并缴费)"; //物业缴费等
    public static final String GOODSNAME_FOR_TEMPCHARGE = "临时缴费"; //临时缴费
    public static final String GOODSNAME_FOR_DEPOSIT = "预存"; //预存
    public static final String GOODSNAME_FOR_GUARANTY = "押金"; //押金
    public static final String GOODSNAME_FOR_OFFER = "银行托收";
    public static final String GOODSNAME_FOR_PRESTORE = "预存充值";
    public static final String GOODSNAME_FOR_MERGE = "合并缴费";
    public static final String GOODSNAME_COMMON_PRESTORE = "通用预存";
    public static final String GOODSNAME_FOR_PRESTORE_DEDUCTION = "预存扣款";
    public static final String GOODSNAME_FOR_PRESTORE_REFUND = "预存退款";
    public static final String GOODSNAME_FOR_PRESTORE_ADJUST = "预存调整";
    public static final String GOODSNAME_FOR_TRADE_DISCOUNT_PRESTORE = "优惠预存";
    public static final String GOODSNAME_MONTH_PARKING = "月卡缴费";
    public static final String GOODSNAME_WORD_ORDER = "工单缴费";

    public static final String GOODSNAME_TEMP_PARKING = "车辆临停";

    public static final String GOODSNAME_EBUSINESS_ORDER = "电商订单";


    /*交易类型*/
    public static final Integer TRANSACTION_TYPE_CHARGE = 0; //收款
    public static final Integer TRANSACTION_TYPE_REFUND = 1; //退款
    public static final Integer TRANSACTION_TYPE_CARRYOVER = 1; //结转

    /*订单类型*/
    public static final String ORDER_TYPE_NORMAL_PAY = "0"; //普通缴费
    public static final String ORDER_TYPE_GUARANTY_PAY = "1"; //押金
    public static final String ORDER_TYPE_PRESTORE_PAY = "3"; //预存
    public static final String ORDER_TYPE_TEMP_PAY = "6"; //临时收费
    public static final String ORDER_TYPE_DEDUCT_PAY = "4"; //预存扣款
    public static final String ORDER_TYPE_SPECIAL_PRESTORE = "5"; //专项预存
    public static final String ORDER_TYPE_DISCOUNT_PRESTORE = "11";//优惠预存
    /*标的物类型*/
    public static final String SUBJECT_TYPE_HOUSE = "house"; //房屋
    public static final String SUBJECT_TYPE_COMMUNITY = "community"; //小区
    public static final String SUBJECT_TYPE_PARKING = "parking"; // 停车场parking

    /*缴费类型(0表示自主缴费；1表示员工代缴)*/
    public static final String PAYMENT_TYPE_OWNER = "0";
    public static final String PAYMENT_TYPE_EMPLOYEE = "1";

    /*交易流水状态常量*/
    public static final String FLOW_PAY_SUCCESS = "1"; //支付成功

    /*收费类型*/
    public static final String TEMPORARY_CHARGE = "temporaryCharge"; //临时收费类
    public static final String DEPOSIT_CHARGE = "deposit"; //押金类
    public static final String PROPERTY_SERVICE = "propertyService"; //物业服务类

    /*操作日志操作类型*/
    public static final String OPERATON_TYPE_CREATE = "create";//创建
    public static final String OPERATON_TYPE_MODIFY = "modify";//更改
    public static final String OPERATON_TYPE_FILE = "file";//归档
    public static final String OPERATON_TYPE_REDUCTION = "reduction";//还原
    public static final String OPERATON_TYPE_DELETE = "delete";//删除

    /*押金状态*/
    public static final String DEP_STATUS_SCARLET = "9"; //已红冲调整

    /*判断收费项是否有计费标准*/
    public static final String STANDARD_NO = "0";
    public static final String STANDARD_YES = "1";

    /**
     * 实收表：charge_type类型：默认0；1违约金
     */
    public static final String CHARGE_TYPE_PENALTY = "1";

    /**
     * 实收表：charge_type类型：默认0；1违约金
     */
    public static final String CHARGE_TYPE_NORMAL = "0";

    /*订单应收类型*/
    public static final String ITEM_TYPE_ORIGINAL = "0"; //原始应收
    public static final String ITEM_TYPE_NEW_ADD = "2"; //调整新增应收


    /* 预存类型 */
    public static final String CURRENCY_PRESTORE = "0";
    public static final String SPECIAL_PRESTORE = "1";

    /* 预存类型名称 */
    public static final String CURRENCY_PRESTORE_NAME = "通用抵扣";
    public static final String SPECIAL_PRESTORE_NAME = "专项抵扣";

    public static final String CURRENCY_PRESTORE_ID = "prestore";

    /*收费对象*/
    public static final String CHARGE_OBJ_OWNER = "0";//业户
    public static final String CHARGE_OBJ_DEVELOPER = "1";//开发商

    /*欠费是否可进行支付*/
    public static final Integer COMMUNITY_PAY_ENABLE = 0;//可以正常支付
    public static final Integer COMMUNITY_PAY_DISABLE = 1;//不可以正常支付

    /*欠费是否可进预存充值*/
    public static final Integer PRESTORE_PAY_ENABLE = 1;//可以正常支付
    public static final Integer PRESTORE_PAY_DISABLE = 0;//不可以正常支付

    /*交易流水扩展类型*/
    public static final String CARD_PAYMENT = "1";      //月卡缴费

    public static final String BILL_STATUS_CLOSED = "10";  //账单关闭
    public static final String BILL_STATUS_PAY_NORMAL = "0";

    //费项配置结转状态
    public static final Integer POINTS_DISCOUNT_STATUS_YES = 1;//允许积分抵扣
    public static final Integer POINTS_DISCOUNT_STATUS_NO = 0;//不允许积分抵扣

    /**
     * 通用预存费用项ID和Name
     */
    public static final Long PREDEPOSIT_ITEM_GENERAL_ID = 1L;
    public static final String PREDEPOSIT_ITEM_GENERAL_NAME = "通用预存";

    /**
     * 预存费用项类型 1有数值，2没有数值，2包含仪表类公摊类、同事包含无计费配置情况
     * 如果有每月金额则1展示，2不展示，如果没有每月金额则2展示，1不展示;
     */
    public static final Integer V1_PREDEPOSIT_PAY_TYPE = 1;
    public static final Integer V1_PREDEPOSIT_DISABLE_PAY_TYPE = 2;

    //预存用户类型
    public static final Integer PREDEPOSIT_CHARGE_OBJ_OWNER = 0;//业主

    public static final Long PREDEPOSIT_GENERAL_ID_V2 = 1L;

    public static final String PREDEPOSIT_GENERAL_NAME = "通用预存";

    //费项赠分状态
    public static final Integer POINTS_EARN_STATUS_YES = 1;//允许积分赠分
    public static final Integer POINTS_EARN_STATUS_NO = 0;//不允许积分赠分

    public static final String SPECIAL_PREDEPOSIT_PREFIX = "预存"; //预存

    /**
     *  '是否对账：0否，1是'
     */
    public static final Integer IS_BALANCE_NO = 0;//
    public static final Integer IS_BALANCE_YES = 1;//
}
