package com.charge.api.web.vo.joylife.response;

import com.charge.api.web.vo.joylife.OrderItemBS;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.config.dto.meter.MeterBookAssetDetailDTO;
import com.charge.config.dto.standard.StandardConfigDTO;
import com.charge.config.enums.ChangeStatusEnum;
import com.charge.config.enums.ChargeTypeEnum;
import com.charge.config.enums.MeterTypeEnum;
import com.charge.joylife.vo.ReceivableMeterDetailVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class OrderItemArrears {
	@ApiModelProperty(value = "账单ID")
	private    String    orderId;
	@ApiModelProperty(value = "费项ID")
	private    String    itemId;
	@ApiModelProperty(value = "费项名称")
	private    String    itemName;
	@ApiModelProperty(value = "费项金额")
	private    String    itemArrearsAmount;
	@ApiModelProperty(value = "违约金金额")
	private    String    itemPenaltyAmount;
	@ApiModelProperty(value = "订单状态")
	private    String    orderStatus;
	@ApiModelProperty(value = "订单状态")
	private    String    orderStatusId;
	@ApiModelProperty(value = "使用量")
	private    String    usage;
	@ApiModelProperty(value = "单位")
	private    String    unit;
	@ApiModelProperty(value = "订单生效时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date effectTime;

	@ApiModelProperty(value = "仪表数据")
	private List<ReceivableMeterDetailVO> meterDetailVOList;

	/**
	 * 计费周期类型(1:月度，2:季度，3:半年度，4:年度, 9无计费周期)
	 */
	private List<Integer> cycleList;

	public static OrderItemArrears from(OrderItemBS order) {
		OrderItemArrears orderItemArrears=new OrderItemArrears();
		orderItemArrears.setItemId(order.getGoodsId());
		orderItemArrears.setItemName(order.getGoodsName());
		orderItemArrears.setItemArrearsAmount((order.getArrearsAmount() == null ? BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP) : order.getArrearsAmount().setScale(2,BigDecimal.ROUND_HALF_UP)).toString());
		orderItemArrears.setItemPenaltyAmount((order.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP) : order.getPenaltyArrearsAmount().setScale(2,BigDecimal.ROUND_HALF_UP)).toString());
		orderItemArrears.setOrderId(order.getItemId());
		orderItemArrears.setOrderStatus(order.getStatusName());
		orderItemArrears.setOrderStatusId(order.getStatusId());
		orderItemArrears.setEffectTime(order.getCreate_ts());
		if(!StringUtils.isEmpty(order.getMemo())){
		//	memo = memo + mergeMeterDetail.getLastNum().stripTrailingZeros().toPlainString() + "-" + mergeMeterDetail.getCurrentNum().stripTrailingZeros().toPlainString() + "    " + meterCount.stripTrailingZeros().toPlainString() + ",";
			String[]  usageInfo=order.getMemo().split("    ");
			if(usageInfo.length>1){
				String usage=usageInfo[1];
				if(NumberUtils.isNumber(usage)){
					orderItemArrears.setUsage(usage);
					if(order.getGoodsName().contains("电费")){
						orderItemArrears.setUnit("度");
					}else{
						orderItemArrears.setUnit("m³");
					}

				}
			}
		}

		return orderItemArrears;
	}

	public static OrderItemArrears fromV2(ReceivableBillDTO receivableBillDTO, Map<Long, MeterBookAssetDetailDTO> meterMap,
										  List<StandardConfigDTO> itemStandardList) {
		OrderItemArrears orderItemArrears=new OrderItemArrears();
		orderItemArrears.setItemId(receivableBillDTO.getItemId().toString());
		orderItemArrears.setItemName(receivableBillDTO.getItemName());
		orderItemArrears.setItemArrearsAmount((receivableBillDTO.getArrearsAmount() == null ? BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP) : receivableBillDTO.getArrearsAmount().setScale(2,BigDecimal.ROUND_HALF_UP)).toString());
		orderItemArrears.setItemPenaltyAmount((receivableBillDTO.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP) : receivableBillDTO.getPenaltyArrearsAmount().setScale(2,BigDecimal.ROUND_HALF_UP)).toString());
		orderItemArrears.setOrderId(Long.toString(receivableBillDTO.getId()));
		//朝昔催缴专用，未核销及部分核销直接返回statusId=0，status=未核销
		orderItemArrears.setOrderStatus(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode().equals(receivableBillDTO.getPayStatus())?ReceivableBillPayStatusEnum.NOT_PAY.getValue():ReceivableBillPayStatusEnum.getValueByCode(receivableBillDTO.getPayStatus()));
		orderItemArrears.setOrderStatusId(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode().equals(receivableBillDTO.getPayStatus())?ReceivableBillPayStatusEnum.NOT_PAY.getCode().toString():receivableBillDTO.getPayStatus().toString());
		orderItemArrears.setEffectTime(new Date(receivableBillDTO.getEffectTime()));

		//计费周期
		List<Integer> cycleList = Optional.ofNullable(itemStandardList).orElse(new ArrayList<>()).stream().filter(s -> Objects.equals(ChargeTypeEnum.GENERAL_NORMAL.getType(), s.getChargeType()))
				.map(s -> s.getChargeCycleType()).distinct().collect(Collectors.toList());
		orderItemArrears.setCycleList(cycleList);

		String usage = "";
		String unit = "";
		if (Objects.nonNull(receivableBillDTO.getMeterBookDetailId()) && Objects.nonNull(meterMap.get(receivableBillDTO.getMeterBookDetailId()))) {
			//存在仪表数据
			MeterBookAssetDetailDTO meterDetail = meterMap.get(receivableBillDTO.getMeterBookDetailId());
			usage = meterDetail.getCurrentCount().stripTrailingZeros().toPlainString();
			unit = MeterTypeEnum.getByCode(meterDetail.getMeterType()).getUnit();
			Integer multiple = Objects.isNull(meterDetail.getMultiple())?1:meterDetail.getMultiple();
			Integer changeStatus = Objects.isNull(meterDetail.getChangeStatus()) ? ChangeStatusEnum.NO_TABLE_CHANGED.getCode() : meterDetail.getChangeStatus();
			ReceivableMeterDetailVO meterVO = ReceivableMeterDetailVO.builder().unit(unit).usage(usage).belongYears(meterDetail.getBelongYears())
					.lastNum(meterDetail.getLastNum().multiply(new BigDecimal(multiple)).stripTrailingZeros().toPlainString())
					.currentNum(meterDetail.getCurrentNum().multiply(new BigDecimal(multiple)).stripTrailingZeros().toPlainString())
					.meterDateBegin(meterDetail.getMeterBookDateBegin()).meterDateEnd(meterDetail.getMeterBookDateEnd())
					.multiple(multiple).changeStatus(changeStatus).build();
			orderItemArrears.setMeterDetailVOList(Lists.newArrayList(meterVO));
		}
		orderItemArrears.setUsage(usage);
		orderItemArrears.setUnit(unit);
		return orderItemArrears;
	}
}
