package com.charge.api.web.vo.joylife.response;

import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.vo.joylife.OrderItemBS;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.config.dto.meter.MeterBookAssetDetailDTO;
import com.charge.config.dto.standard.StandardConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
public class MonthOrderItemArrears {
	@ApiModelProperty(value = "费用月份")
	private    String    month;
	@ApiModelProperty(value = "金额")
	private    String    amount;
	@ApiModelProperty(value = "账单详情")
	private List<OrderItemArrears> detailList;

	public static List<MonthOrderItemArrears> from(List<OrderItemBS> orderItemBSList) {
		List<MonthOrderItemArrears> result=new ArrayList<>();
		if(!CollectionUtils.isEmpty(orderItemBSList)){
			Map<String,List<OrderItemBS>> orderItemGroupByBelongYears=orderItemBSList.stream().filter(item-> item!=null&&StringUtils.isNotBlank(item.getBelongYears()))
					.collect(Collectors.groupingBy(OrderItemBS::getBelongYears));
			orderItemGroupByBelongYears.forEach((key,value)->{
				MonthOrderItemArrears monthOrderItemArrears=new MonthOrderItemArrears();
				Optional<BigDecimal> amountOptional=value.stream()
						.filter(orderItemBS->orderItemBS.getStatusId().equals(PayRelatedConstants.ORDER_NOT_PAY))
						.map(orderItemBS->(orderItemBS.getArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getArrearsAmount()).add(orderItemBS.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getPenaltyArrearsAmount()))
						.reduce(BigDecimal::add);
				BigDecimal amount=amountOptional.isPresent()?amountOptional.get():BigDecimal.ZERO;
				List<OrderItemArrears> orderItemArrears=new ArrayList<>();
				value.forEach(item->orderItemArrears.add(OrderItemArrears.from(item)));
				orderItemArrears.sort(Comparator.comparing(OrderItemArrears::getEffectTime,Comparator.nullsLast(Comparator.naturalOrder())));
				monthOrderItemArrears.setMonth(key);
				monthOrderItemArrears.setAmount(amount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
				monthOrderItemArrears.setDetailList(orderItemArrears);
				result.add(monthOrderItemArrears);
			});
		}
		return result;
	}

	public static List<MonthOrderItemArrears> fromV2(List<ReceivableBillDTO> arrearsOrderList,
													 Map<Long, MeterBookAssetDetailDTO> meterMap,
													 Map<Long, List<StandardConfigDTO>> itemStandardsMap,
													 Integer chargeObject) {
		List<MonthOrderItemArrears> result = new ArrayList<>();

		if (CollectionUtils.isEmpty(arrearsOrderList)) {
			return result;
		}

		Map<String, List<ReceivableBillDTO>> orderItemGroupByBelongYears = arrearsOrderList.stream()
				.filter(item -> item != null && StringUtils.isNotBlank(item.getBelongYears()))
				.collect(Collectors.groupingBy(ReceivableBillDTO::getBelongYears));

		orderItemGroupByBelongYears.forEach((year, bills) -> {
			MonthOrderItemArrears monthOrderItemArrears = createMonthOrderItemArrears(year, bills, meterMap, itemStandardsMap, chargeObject);
			result.add(monthOrderItemArrears);
		});

		return result;
	}

	private static MonthOrderItemArrears createMonthOrderItemArrears(String year,
																	 List<ReceivableBillDTO> bills,
																	 Map<Long, MeterBookAssetDetailDTO> meterMap,
																	 Map<Long, List<StandardConfigDTO>> itemStandardsMap,
																	 Integer chargeObject) {
		MonthOrderItemArrears monthOrderItemArrears = new MonthOrderItemArrears();

		BigDecimal totalAmount = calculateTotalAmount(bills, chargeObject);
		List<OrderItemArrears> orderItemArrears = createOrderItemArrearsList(bills, meterMap, itemStandardsMap);

		monthOrderItemArrears.setMonth(year);
		monthOrderItemArrears.setAmount(totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
		monthOrderItemArrears.setDetailList(orderItemArrears);

		return monthOrderItemArrears;
	}

	private static BigDecimal calculateTotalAmount(List<ReceivableBillDTO> bills, Integer chargeObject) {
		Stream<ReceivableBillDTO> filteredBills = bills.stream()
				.filter(bill -> (bill.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
						|| bill.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()))
						&& ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(bill.getBillStatus())
						&& (chargeObject == null || chargeObject.equals(bill.getChargeObject())));

		return filteredBills.map(bill -> {
			BigDecimal arrearsAmount = Optional.ofNullable(bill.getArrearsAmount()).orElse(BigDecimal.ZERO);
			BigDecimal penaltyAmount = Optional.ofNullable(bill.getPenaltyArrearsAmount()).orElse(BigDecimal.ZERO);
			return arrearsAmount.add(penaltyAmount);
		}).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
	}

	private static List<OrderItemArrears> createOrderItemArrearsList(List<ReceivableBillDTO> bills,
																	 Map<Long, MeterBookAssetDetailDTO> meterMap,
																	 Map<Long, List<StandardConfigDTO>> itemStandardsMap) {
		List<OrderItemArrears> orderItemArrears = bills.stream()
				.map(bill -> OrderItemArrears.fromV2(bill, meterMap, itemStandardsMap.get(bill.getItemId())))
				.sorted(Comparator.comparing(OrderItemArrears::getEffectTime, Comparator.nullsLast(Comparator.naturalOrder())))
				.collect(Collectors.toList());

		return orderItemArrears;
	}


	public static List<MonthOrderItemArrears> fromHangup(List<OrderItemBS> orderItemBSList) {
		List<MonthOrderItemArrears> result=new ArrayList<>();
		if(!CollectionUtils.isEmpty(orderItemBSList)){
			Map<String,List<OrderItemBS>> orderItemGroupByBelongYears=orderItemBSList.stream().filter(item-> item!=null&&StringUtils.isNotBlank(item.getBelongYears()))
					.collect(Collectors.groupingBy(OrderItemBS::getBelongYears));
			orderItemGroupByBelongYears.forEach((key,value)->{
				MonthOrderItemArrears monthOrderItemArrears=new MonthOrderItemArrears();
				Optional<BigDecimal> amountOptional=value.stream()
						.map(orderItemBS->(orderItemBS.getArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getArrearsAmount()).add(orderItemBS.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO : orderItemBS.getPenaltyArrearsAmount()))
						.reduce(BigDecimal::add);
				BigDecimal amount=amountOptional.isPresent()?amountOptional.get():BigDecimal.ZERO;
				List<OrderItemArrears> orderItemArrears=new ArrayList<>();
				value.forEach(item->orderItemArrears.add(OrderItemArrears.from(item)));
				orderItemArrears.sort(Comparator.comparing(OrderItemArrears::getEffectTime,Comparator.nullsLast(Comparator.naturalOrder())));
				monthOrderItemArrears.setMonth(key);
				monthOrderItemArrears.setAmount(amount.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
				monthOrderItemArrears.setDetailList(orderItemArrears);
				result.add(monthOrderItemArrears);
			});
		}
		return result;
	}

	public static List<MonthOrderItemArrears> fromHangupV2(List<ReceivableBillDTO> hangupOrderList,
														   Map<Long, MeterBookAssetDetailDTO> meterMap,
														   Map<Long, List<StandardConfigDTO>> itemStandardsMap,
														   Integer chargeObject) {
		List<MonthOrderItemArrears> result = new ArrayList<>();

		if (CollectionUtils.isEmpty(hangupOrderList)) {
			return result;
		}

		Map<String, List<ReceivableBillDTO>> orderItemGroupByBelongYears = groupByBelongYears(hangupOrderList);

		orderItemGroupByBelongYears.forEach((year, bills) -> {
			MonthOrderItemArrears monthOrderItemArrears = createHangUpMonthOrderItemArrears(year, bills, meterMap, itemStandardsMap, chargeObject);
			result.add(monthOrderItemArrears);
		});

		return result;
	}

	private static Map<String, List<ReceivableBillDTO>> groupByBelongYears(List<ReceivableBillDTO> hangupOrderList) {
		return hangupOrderList.stream()
				.filter(item -> item != null && StringUtils.isNotBlank(item.getBelongYears()))
				.collect(Collectors.groupingBy(ReceivableBillDTO::getBelongYears));
	}

	private static MonthOrderItemArrears createHangUpMonthOrderItemArrears(String year,
																	 List<ReceivableBillDTO> bills,
																	 Map<Long, MeterBookAssetDetailDTO> meterMap,
																	 Map<Long, List<StandardConfigDTO>> itemStandardsMap,
																	 Integer chargeObject) {
		MonthOrderItemArrears monthOrderItemArrears = new MonthOrderItemArrears();

		BigDecimal totalAmount = calculateHangUpTotalAmount(bills, chargeObject);
		List<OrderItemArrears> orderItemArrears = createOrderItemArrearsList(bills, meterMap, itemStandardsMap);

		monthOrderItemArrears.setMonth(year);
		monthOrderItemArrears.setAmount(totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
		monthOrderItemArrears.setDetailList(orderItemArrears);

		return monthOrderItemArrears;
	}

	private static BigDecimal calculateHangUpTotalAmount(List<ReceivableBillDTO> bills, Integer chargeObject) {
		Stream<ReceivableBillDTO> filteredBills = bills.stream()
				.filter(bill -> chargeObject == null || chargeObject.equals(bill.getChargeObject()));

		return filteredBills.map(bill -> {
			BigDecimal arrearsAmount = Optional.ofNullable(bill.getArrearsAmount()).orElse(BigDecimal.ZERO);
			BigDecimal penaltyAmount = Optional.ofNullable(bill.getPenaltyArrearsAmount()).orElse(BigDecimal.ZERO);
			return arrearsAmount.add(penaltyAmount);
		}).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
	}

}
