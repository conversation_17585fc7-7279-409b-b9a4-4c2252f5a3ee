package com.charge.api.web.service.bill.joy;

import com.charge.api.web.adapter.PayOrderAdapter;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.vo.joylife.PrestoreChargeOrderInfo;
import com.charge.api.web.vo.joylife.request.PrestoreOrderRequest;
import com.charge.bill.client.flow.AssetPaymentClient;
import com.charge.bill.dto.BillAssetInfoDTO;
import com.charge.bill.dto.PayOrAdjustItemDTO;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.dto.domain.AssetPayDTO;
import com.charge.bill.dto.domain.AssetPaymentDetailDTO;
import com.charge.bill.dto.domain.response.CreateBillResponse;
import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.constant.PayRelatedConstants;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.joylife.dto.PayInfoDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.pay.dto.pay.PayOrderSubmitResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 朝昔预存下单
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AddPrestorePayService {

    private final AssetPaymentClient assetPaymentClient;

    public ChargeResponse<Object> createBill(PrestoreOrderRequest request, CommunityDTO communityDTO,
                                             PayOrderSubmitResponseDTO payReturnDTO, AssetAdapter assetAdapter,
                                             String orderNum, String ownerName, Long ownerId) throws ChargeBusinessException {
        AssetPayDTO assetPayDTO = new AssetPayDTO();
        assetPayDTO.setAssetPayBaseDTO(buildAssetPayBaseDTO(request, communityDTO, payReturnDTO.getOutTradeNo(), orderNum));
        assetPayDTO.setAssetPaymentDetailDTO(buildAssetPaymentDetailDTO(communityDTO, assetAdapter, request, ownerName, ownerId));
        CreateBillResponse response = AppInterfaceUtil.getResponseDataThrowException(assetPaymentClient.createBill(assetPayDTO));
        PayInfoDTO payInfoDTO = PayOrderAdapter.responseParamConvert(payReturnDTO, response.getIncomeBillId());
        log.info("{}|【朝昔】预存充值2.0，返回下单信息|{}", LogCategoryEnum.BUSSINESS, payInfoDTO);
        return new ChargeResponse<>(payInfoDTO);
    }

    /**
     * 构建基础信息
     * @param request
     * @param communityDTO
     * @param outTradeNo
     * @param orderNum
     * @return
     */
    private AssetPayBaseDTO buildAssetPayBaseDTO(PrestoreOrderRequest request, CommunityDTO communityDTO,
                                                    String outTradeNo, String orderNum) {
        AssetPayBaseDTO assetOrderBaseDTO = new AssetPayBaseDTO();
        assetOrderBaseDTO.setCommunityId(communityDTO.getId());
        assetOrderBaseDTO.setCommunityName(communityDTO.getName());
        assetOrderBaseDTO.setOrderNum(orderNum);
        assetOrderBaseDTO.setOutTransactionNo(outTradeNo);
        assetOrderBaseDTO.setGoodsName(PayRelatedConstants.GOODSNAME_FOR_PRESTORE);
        assetOrderBaseDTO.setActualPrice(new BigDecimal(request.getTotalPrice())); // 实收金额
        assetOrderBaseDTO.setPoints(StringUtils.isEmpty(request.getTotalPoints()) ? 0 : Integer.parseInt(request.getTotalPoints()));
        assetOrderBaseDTO.setEquityAccount(request.getEquityAccount());
        assetOrderBaseDTO.setPaymentTerminal(PaymentTerminalEnum.handleJoyLifeWechatApplet(request.getPaymentSource()));
        assetOrderBaseDTO.setPayMember(request.getUserName());
        assetOrderBaseDTO.setPayMemberId(request.getUserId());
        assetOrderBaseDTO.setPayMemberMobile(request.getPhone());
        assetOrderBaseDTO.setMemo(request.getMemo());
        assetOrderBaseDTO.setMsAssetOwnerId(request.getCustomId());
        assetOrderBaseDTO.setCreateUser(request.getUserName());
        CommonUtils.fillPaymentMethodInfo(request.getPaymentMethod(), assetOrderBaseDTO);
        if (Objects.equals(PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel(),request.getPaymentSource())) {
            assetOrderBaseDTO.setPaymentChannel(request.getPaymentSource());
        }
        assetOrderBaseDTO.setClientSourceEnum(ClientSourceEnum.JOY_PRESTORE_PAY);
        return assetOrderBaseDTO;
    }

    /**
     * 构建缴费信息
     * @param communityDTO
     * @param assetAdapter
     * @param request
     * @return
     */
    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(CommunityDTO communityDTO, AssetAdapter assetAdapter,
                                                             PrestoreOrderRequest request, String ownerName, Long ownerId) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 资产信息
        assetPaymentDetailDTO.setBillAssetInfoDTO(buildBillAssetInfoDTO(assetAdapter, communityDTO, ownerName, ownerId));
        // 预存信息
        assetPaymentDetailDTO.setPrestoreItems(buildPrestoreItems(request.getChargeOrderInfoList()));
        return assetPaymentDetailDTO;
    }
    private BillAssetInfoDTO buildBillAssetInfoDTO(AssetAdapter assetAdapter, CommunityDTO communityDTO, String ownerName, Long ownerId) {
        BillAssetInfoDTO billAssetInfoDTO = new BillAssetInfoDTO();
        billAssetInfoDTO.setCommunityId(communityDTO.getId());
        billAssetInfoDTO.setCommunityName(communityDTO.getName());
        billAssetInfoDTO.setAssetId(assetAdapter.getId());
        billAssetInfoDTO.setAssetType(assetAdapter.getType());
        billAssetInfoDTO.setAssetName(assetAdapter.getSubName());
        billAssetInfoDTO.setAssetCode(assetAdapter.getSubCode());
        billAssetInfoDTO.setBuildingId(assetAdapter.getBuildingId());
        billAssetInfoDTO.setBuildingName(assetAdapter.getBuildingName());
        billAssetInfoDTO.setUnitId(assetAdapter.getUnitId());
        billAssetInfoDTO.setUnitName(assetAdapter.getUnitName());
        billAssetInfoDTO.setOwnerId(ownerId); // 构建预收单明细中的UserId
        billAssetInfoDTO.setOwnerName(ownerName);
        billAssetInfoDTO.setAssetUseStatus(assetAdapter.getAssetUseStatus());
        return billAssetInfoDTO;
    }

    /**
     * 构建预存信息
     * @param chargeOrderInfoList
     * @return
     */
    private List<PayOrAdjustItemDTO> buildPrestoreItems(List<PrestoreChargeOrderInfo> chargeOrderInfoList) {
        if (CollectionUtils.isEmpty(chargeOrderInfoList)) {
            return Arrays.asList();
        }
        return chargeOrderInfoList.stream().map(prestoreChargeOrderInfo -> {
            PayOrAdjustItemDTO payOrAdjustItemDTO = new PayOrAdjustItemDTO();
            payOrAdjustItemDTO.setItemName(prestoreChargeOrderInfo.getItemName());
            payOrAdjustItemDTO.setItemId(Long.valueOf(prestoreChargeOrderInfo.getItemId()));
            payOrAdjustItemDTO.setAmount(new BigDecimal(prestoreChargeOrderInfo.getMoney()));
            payOrAdjustItemDTO.setPoints(prestoreChargeOrderInfo.getPoints());
            return payOrAdjustItemDTO;
        }).collect(Collectors.toList());
    }
}
