package com.charge.api.web.service.parking;

import com.charge.api.web.dto.parking.ParkingSpaceParam;
import com.charge.api.web.vo.ParkingPlaceBalanceVO;
import com.charge.api.web.vo.ParkingPlacePeriodVO;
import com.charge.common.exception.ChargeBusinessException;

import java.util.List;

/**
 * @Description 停车云平台产权车位余额及有效期间
 * @Author: yjw
 * @Date: 2024/3/7 15:51
 */
public interface PlatformParkingSpaceService {

    /**
     * 查询产权车位预存余额及可用余额期数（含专项预存、待结转）
     * @return
     */
    List<ParkingPlaceBalanceVO> getParkingPlaceBalance(ParkingSpaceParam parkingSpaceParam) throws ChargeBusinessException;

    /**
     * 查询产权车位授权期限
     * @return
     */
    List<ParkingPlacePeriodVO> getParkingPlacePeriod(ParkingSpaceParam parkingSpaceParam) throws ChargeBusinessException;

}
