package com.charge.api.web.service.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 账单业务
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class OrderBillBusiness extends BillBusiness {

    /**
     * 订单子类别id
     */
    private Long subsetId;

    /**
     * 分期id
     */
    private Long installmentId;


    /**
     * 电商子订单id
     */
    private Long eBusinessBaseOrderId;

}


