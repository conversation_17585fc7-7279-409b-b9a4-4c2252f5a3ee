package com.charge.api.web.controller.collection;

import com.charge.api.web.dto.collection.request.*;
import com.charge.api.web.dto.collection.request.BankCollectionSignConfirmRequestDTO;
import com.charge.api.web.dto.collection.response.*;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.apicloud.enums.SignSubmitStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ChargeStatusEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.config.client.item.CommunityChargeItemClient;
import com.charge.config.dto.item.CommunityChargeItemDTO;
import com.charge.config.dto.item.condition.CommunityChargeItemQueryConditionDTO;
import com.charge.config.enums.BusinessTypeEnum;
import com.charge.config.enums.StandardConfigStatusEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.StringUtil;
import com.charge.core.util.TraceContextUtil;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.pay.client.collection.BankCollectionCommunityConfigClient;
import com.charge.pay.dto.collection.request.*;
import com.charge.pay.dto.collection.request.BankCollectionSignAddRequestDTO;
import com.charge.pay.dto.collection.response.*;
import com.charge.pay.dto.enums.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping(value = "/bank/collection")
public class AppSignController {

    private final BankCollectionCommunityConfigClient bankCollectionCommunityConfigClient;

    private final CommunitySupport communitySupport;

    private final AssetSupport assetSupport;

    private final CommunityChargeItemClient communityChargeItemClient;

    /**
     * 查询项目的托收配置
     * @param communityMsId
     * @return
     * @throws ChargeBusinessException
     */
    @GetMapping(value = "/config")
    public ChargeResponse<BankCollectionConfigDTO> getConfig(@RequestParam String communityMsId) throws ChargeBusinessException {
        BankCollectionCommunityConfigQueryDTO requestDTO = new BankCollectionCommunityConfigQueryDTO();
        Long communityId = communitySupport.getCommunityIdByMsId(communityMsId);
        TraceContextUtil.setCommunityId(communityId);
        requestDTO.setCommunityId(communityId);
        List<BankCollectionCommunityConfigDTO> list = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.baseInfo(requestDTO));
        if (CollectionUtil.isNotEmpty(list)) {
            list = list.stream().filter(one-> BankCollectionCommunityConfigEnableEnum.ON.getCode().equals(one.getEnable())).collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(list)) {
            return new ChargeResponse<>(BankCollectionConfigDTO.builder()
                    .isConfig(false)
                    .build());
        }
        List<BankCollectionCommunityConfigDTO> szfsList = list.stream().filter(one -> BankCollectionCodeEnum.SZFS.getCode().equals(one.getPlanCode())).collect(Collectors.toList());
        // 优先返回深结算
        return new ChargeResponse<>(BankCollectionConfigDTO.builder()
                .isConfig(true)
                .configId(szfsList.isEmpty() ? list.get(0).getId() : szfsList.get(0).getId())
                .planCode(szfsList.isEmpty() ? list.get(0).getPlanCode() : szfsList.get(0).getPlanCode())
                .build());
    }

    /**
     * 查询项目的签约列表
     * @param requestDTO
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping(value = "/sign/list")
    public ChargeResponse<List<BankCollectionSignListResponseDTO>> listSignRecord(@RequestBody @Valid BankCollectionSignListRequestDTO requestDTO) throws ChargeBusinessException {
        BankCollectionAgreementQueryDTO agreementQueryDTO = new BankCollectionAgreementQueryDTO();
        Long communityId = communitySupport.getCommunityIdByMsId(requestDTO.getCommunityMsId());
        TraceContextUtil.setCommunityId(communityId);
        List<AssetAdapter> adapters = assetSupport.getAssetListByMsIds(communityId, requestDTO.getAssetMsIdList());
        if (CollectionUtil.isEmpty(adapters)) {
            return new ChargeResponse<>(new ArrayList<>());
        }
        Map<Long, List<AssetAdapter>> map = adapters.stream().collect(Collectors.groupingBy(AssetAdapter::getId));
        agreementQueryDTO.setCommunityId(communityId);
        agreementQueryDTO.setPageNum(1);
        agreementQueryDTO.setPageSize(1000);
        agreementQueryDTO.setAssetIdList(new ArrayList<>(map.keySet()));
        PagingDTO<BankCollectionAgreementDTO> pagingDTO = AppInterfaceUtil.getResponseDataThrowException(
                bankCollectionCommunityConfigClient.signedAgreementPage(agreementQueryDTO));
        List<BankCollectionSignListResponseDTO> list = pagingDTO.getList().stream().map(one ->
                BeanCopierWrapper.copyBean(one, BankCollectionSignListResponseDTO.class, (s,t) -> {
                    t.setCollectionChannel(s.getPlanCode());
                    t.setBankAccountName(s.getAccountName());
                    t.setEntrustNo(s.getAgreementCode());
                    t.setCreateTime(DateUtils.format(s.getCreateTime(), DateUtils.FORMAT_1));
                    t.setAgreementId(s.getId());
                    t.setMsId(map.get(s.getAssetId()).get(0).getMsId());
                    t.setAssetName(map.get(s.getAssetId()).get(0).getSubName());
                })).collect(Collectors.toList());
        return new ChargeResponse<>(list);
    }

    /**
     * 下发验证码、深结算招行签约、中金招行直接签约
     * @param requestDTO
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping(value = "/sign")
    public ChargeResponse<BankCollectionSignResponseDTO> sign(@RequestBody @Valid BankCollectionSignRequestDTO requestDTO) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(requestDTO.getCommunityMsId());
        TraceContextUtil.setCommunityId(communityId);
        List<AssetAdapter> adapters = assetSupport.getAssetListByMsIds(communityId, requestDTO.getAssetMsIdList());
        BankCollectionCommunityConfigDTO configDTO = this.getConfigDTO(communityId, requestDTO.getConfigId());
        if (BankCollectionCodeEnum.CICC.getCode().equals(configDTO.getPlanCode()) && Objects.equals(requestDTO.getCmbFlag(), 1)) {
            // 中金招行直接签约
            BankCollectionSignConfirmBatchRequestDTO batchRequestDTO = BeanCopierWrapper.copyBean(requestDTO, BankCollectionSignConfirmBatchRequestDTO.class, (s, t) -> {
                t.setSignTerminal(BankCollectionSignTerminalEnum.APP.getCode());
                t.setCommunityId(communityId);
                t.setAccountName(s.getBankAccountName());
                t.setCertificateType(s.getCertificateType());
                t.setCommunityCollectionId(s.getConfigId());
                t.setList(this.getBankCollectionAssetInfoList(adapters));
            });
            Boolean ret = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.signConfirmBatch(batchRequestDTO));
            return new ChargeResponse<>(BankCollectionSignResponseDTO.builder().status(ret ? BankCollectionSignSubmitStatusEnum.SUCCESS.getCode() :
                    BankCollectionSignSubmitStatusEnum.FAIL.getCode()).build());
        }
        // 其他两种
        BankCollectionPreAgreementBatchRequestDTO batchRequestDTO = BeanCopierWrapper.copyBean(requestDTO, BankCollectionPreAgreementBatchRequestDTO.class, (s,t) -> {
            t.setSignTerminal(BankCollectionSignTerminalEnum.APP.getCode());
            t.setCommunityId(communityId);
            t.setAccountName(s.getBankAccountName());
            t.setCertificateDict(s.getCertificateType());
            t.setCommunityCollectionId(s.getConfigId());
            t.setList(this.getBankCollectionAssetInfoList(adapters));
        });
        return bankCollectionCommunityConfigClient.signBatch(batchRequestDTO);
    }

    public BankCollectionCommunityConfigDTO getConfigDTO(Long communityId, Long configId) throws ChargeBusinessException {
        BankCollectionCommunityConfigQueryDTO configQueryDTO = new BankCollectionCommunityConfigQueryDTO();
        configQueryDTO.setCommunityId(communityId);
        configQueryDTO.setId(configId);
        List<BankCollectionCommunityConfigDTO> configList = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.baseInfo(configQueryDTO));
        if (CollectionUtil.isEmpty(configList)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "项目托收配置id不存在");
        }
        return configList.get(0);
    }

    /**
     * 确认验证码接口
     * @param requestDTO
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping(value = "/sign/confirm")
    public ChargeResponse<BankCollectionSignConfirmResponseDTO> signConfirm(@RequestBody @Valid BankCollectionSignConfirmRequestDTO requestDTO) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(requestDTO.getCommunityMsId());
        TraceContextUtil.setCommunityId(communityId);
        BankCollectionSignConfirmCodeRequestDTO codeRequestDTO = BeanCopierWrapper.copy(requestDTO, BankCollectionSignConfirmCodeRequestDTO.class);
        Boolean result = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.signConfirmByApplyNum(codeRequestDTO));
        if (result) {
            return new ChargeResponse<>(BankCollectionSignConfirmResponseDTO.builder().status(0).message("签约成功").build());
        }
        return new ChargeResponse<>(BankCollectionSignConfirmResponseDTO.builder().status(1).message("签约失败").build());
    }

    /**
     * 根据银行卡查询签约成功记录
     * @param communityMsId
     * @param bankAccount
     * @param configId
     * @return
     * @throws ChargeBusinessException
     */
    @GetMapping(value = "/sign/query/bank-account")
    public ChargeResponse<BankCollectionQueryBankAccountDTO> querySignByAccount(@RequestParam String communityMsId, @RequestParam String bankAccount,
                                                                                @RequestParam Long configId) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(communityMsId);
        TraceContextUtil.setCommunityId(communityId);
        BankBaseQueryDTO requestDTO = new BankBaseQueryDTO();
        requestDTO.setCommunityCollectionId(configId);
        requestDTO.setCommunityId(communityId);
        requestDTO.setBankAccount(bankAccount);
        BankCollectionCustomerDTO customerDTO = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.agreementCheck(requestDTO));
        if (Objects.isNull(customerDTO)) {
            return new ChargeResponse<>(BankCollectionQueryBankAccountDTO.builder().count(0).build());
        }
        return new ChargeResponse<>(BeanCopierWrapper.copyBean(customerDTO, BankCollectionQueryBankAccountDTO.class, (s,t) -> {
            t.setBankAccountName(s.getAccountName());
            t.setCount(StringUtil.isNotEmpty(s.getCertificateNum()) ? 1:0);
        }));
    }

    /**
     * 卡号已签约的情况下新增资产
     * @param requestDTO
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping(value = "/sign/add")
    public ChargeResponse<BankCollectionSignConfirmResponseDTO> signAdd(@RequestBody @Valid com.charge.api.web.dto.collection.request.BankCollectionSignAddRequestDTO requestDTO) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(requestDTO.getCommunityMsId());
        TraceContextUtil.setCommunityId(communityId);
        List<AssetAdapter> adapters = assetSupport.getAssetListByMsIds(communityId, requestDTO.getAssetMsIdList());
        BankCollectionSignAddRequestDTO addRequestDTO = BeanCopierWrapper.copyBean(requestDTO, BankCollectionSignAddRequestDTO.class, (s, t) -> {
                    t.setList(this.getBankCollectionAssetInfoList(adapters));
                });
        addRequestDTO.setCommunityId(communityId);
        Boolean result = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.signConfirmByBankAccount(addRequestDTO));
        if (result) {
            return new ChargeResponse<>(BankCollectionSignConfirmResponseDTO.builder().status(0).message("签约成功").build());
        }
        return new ChargeResponse<>(BankCollectionSignConfirmResponseDTO.builder().status(1).message("签约失败").build());
    }

    public List<BankCollectionAssetInfo> getBankCollectionAssetInfoList(List<AssetAdapter> adapters) {
        return adapters.stream().map(one -> {
            BankCollectionAssetInfo bankCollectionAssetInfo = new BankCollectionAssetInfo();
            bankCollectionAssetInfo.setAssetId(one.getId());
            bankCollectionAssetInfo.setAssetCode(one.getSubCode());
            bankCollectionAssetInfo.setAssetType(one.getType());
            bankCollectionAssetInfo.setBuildingName(one.getBuildingName());
            bankCollectionAssetInfo.setOwnerId(CollectionUtil.isNotEmpty(one.getListCustomer()) ? one.getListCustomer().get(0).getId() : null);
            bankCollectionAssetInfo.setOwnerName(CollectionUtil.isNotEmpty(one.getListCustomer()) ? one.getListCustomer().stream().map(CustomerDTO::getCustomerName).collect(Collectors.joining(",")) : "");
            bankCollectionAssetInfo.setBuildingId(one.getCommunityId());
            return bankCollectionAssetInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 查询托收的收费项列表
     * @param communityMsId
     * @param configId
     * @return
     * @throws ChargeBusinessException
     */
    @GetMapping(value = "/item/list")
    public ChargeResponse<List<BankCollectionItemConfigVO>> getItemList(@RequestParam String communityMsId, @RequestParam(required = false) Long configId) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(communityMsId);
        TraceContextUtil.setCommunityId(communityId);
        // 查询是否项目启用收费项
        BankCollectionCommunityConfigQueryDTO configQueryDTO = new BankCollectionCommunityConfigQueryDTO();
        configQueryDTO.setCommunityId(communityId);
        List<BankCollectionCommunityConfigDTO> configList = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.baseInfo(configQueryDTO));
        if (CollectionUtil.isEmpty(configList)) {
            return new ChargeResponse<>(ChargeStatusEnum.FAIL.getCode(), "项目托收配置id不存在");
        }
        List<BankCollectionItemConfigVO> ret = new ArrayList<>();
        List<String> waitPlanCodeList = new ArrayList<>();
        // 双渠道收费项分开返回
        for (BankCollectionCommunityConfigDTO configDTO : configList) {
            if (BankCollectionItemTypeEnum.SPECIAL.getCode().equals(configDTO.getCollectionItemType())) {
                BankCollectionItemRequestDTO requestDTO = new BankCollectionItemRequestDTO();
                requestDTO.setResourceId(configDTO.getId());
                requestDTO.setResourceType(BankCollectionItemTypeEnum.ALL.getCode());
                requestDTO.setCommunityId(communityId);
                List<BankCollectionItemConfigDTO> list = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.queryList(requestDTO));
                ret.addAll(list.stream().map(one -> BeanCopierWrapper.copyBean(one, BankCollectionItemConfigVO.class,
                        (s, t) -> t.setPlanCode(configDTO.getPlanCode()))).collect(Collectors.toList()));
            } else {
                waitPlanCodeList.add(configDTO.getPlanCode());
            }
        }
        if (CollectionUtil.isNotEmpty(waitPlanCodeList)) {
            // 查询项目启用的收费项
            CommunityChargeItemQueryConditionDTO condition = new CommunityChargeItemQueryConditionDTO();
            condition.setPageSize(1000);
            condition.setPageNum(1);
            condition.setBusinessTypes(Collections.singletonList(BusinessTypeEnum.CYCLICAL.getCode()));
            condition.setStatus(Collections.singletonList(StandardConfigStatusEnum.ENABLE.getCode()));
            condition.setCommunityId(communityId);
            PagingDTO<CommunityChargeItemDTO> pagingDTO = AppInterfaceUtil.getResponseDataThrowException(communityChargeItemClient.queryCommunityChargeItemPage(condition));
            for (String planCode : waitPlanCodeList) {
                ret.addAll(pagingDTO.getList().stream().map(one -> {
                    BankCollectionItemConfigVO bankCollectionItemConfigDTO = new BankCollectionItemConfigVO();
                    bankCollectionItemConfigDTO.setItemId(one.getItemId());
                    bankCollectionItemConfigDTO.setItemName(one.getItemName());
                    bankCollectionItemConfigDTO.setPlanCode(planCode);
                    return bankCollectionItemConfigDTO;
                }).collect(Collectors.toList()));
            }
        }
        return new ChargeResponse<>(ret);
    }

    /**
     * 查询深结算招行签约url接口
     * @param applyNum
     * @param configId
     * @return
     * @throws ChargeBusinessException
     */
    @GetMapping(value = "/sign/callback/status")
    public ChargeResponse<BankCollectionSignCallbackResponseDTO> signCallBackStatus(@RequestParam String applyNum, @RequestParam Long configId, @RequestParam String communityMsId) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(communityMsId);
        TraceContextUtil.setCommunityId(communityId);
        SzfsSignCallbackStatusRequestDTO statusRequestDTO = new SzfsSignCallbackStatusRequestDTO();
        statusRequestDTO.setCommunityCollectionId(configId);
        statusRequestDTO.setApplyNum(applyNum);
        SzfsBankSignCallbackInfoResponseDTO dto = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.signCallbackStatus(statusRequestDTO));
        if (SignSubmitStatusEnum.SEND.getCode().equals(dto.getStatus())) {
            return new ChargeResponse<>(BankCollectionSignCallbackResponseDTO.builder().status(1).message(dto.getMessage()).build());
        } else if (SignSubmitStatusEnum.BANK_WAIT.getCode().equals(dto.getStatus())) {
            return new ChargeResponse<>(BankCollectionSignCallbackResponseDTO.builder().status(0).message(dto.getMessage()).authUrl(dto.getAuthUrl()).build());
        }
        return new ChargeResponse<>(BankCollectionSignCallbackResponseDTO.builder().status(2).message(dto.getMessage()).build());
    }

    /**
     * 查询深结算银行编码、证件类型接口
     * @param communityMsId
     * @param configId
     * @param dictType
     * @return
     * @throws ChargeBusinessException
     */
    @GetMapping(value = "/dict")
    public ChargeResponse<List<BankCollectionBaseDictDTO>> getConfig(@RequestParam String communityMsId, @RequestParam Long configId, @RequestParam Integer dictType) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(communityMsId);
        TraceContextUtil.setCommunityId(communityId);
        BankCollectionCommunityDicQueryDTO requestDTO = new BankCollectionCommunityDicQueryDTO();
        requestDTO.setCommunityId(communityId);
        requestDTO.setCommunityCollectionId(configId);
        requestDTO.setDictType(dictType);
        List<BankCollectionBaseDictDTO> list = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.dicList(requestDTO));
        if (CollectionUtil.isNotEmpty(list)
                && BankCollectionCodeEnum.CICC.getCode().equals(list.get(0).getPlanCode())
                && CollectionDicTypeEnum.CERTIFICATE_DICT.getCode().equals(dictType)) {
            // 中金证件类型只返回身份证
            return new ChargeResponse<>(list.stream().filter(one-> CICCCertificateTypeEnum.ID_CARD.getCode().equals(one.getDictCode())).collect(Collectors.toList()));
        }
        return new ChargeResponse<>(list);
    }

    /**
     * 取消授权接口
     * @param requestDTO
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping(value = "/sign/cancel")
    public ChargeResponse<BankCollectionSignConfirmResponseDTO> getConfig(@RequestBody @Valid BankCollectionSignCancelRequestDTO requestDTO) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(requestDTO.getCommunityMsId());
        TraceContextUtil.setCommunityId(communityId);
        Long assetId = assetSupport.getV2AssetId(requestDTO.getAssetMsId());
        BankCollectionAgreementCancelRequestDTO cancelRequestDTO = new BankCollectionAgreementCancelRequestDTO();
        cancelRequestDTO.setCommunityId(communityId);
        cancelRequestDTO.setAssetId(assetId);
        cancelRequestDTO.setAgreementId(requestDTO.getAgreementId());
        cancelRequestDTO.setBankAccount(requestDTO.getBankAccount());
        cancelRequestDTO.setCommunityCollectionId(requestDTO.getConfigId());
        Boolean ret = AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.cancelAgreement(cancelRequestDTO));
        if (ret) {
            return new ChargeResponse<>(BankCollectionSignConfirmResponseDTO.builder().status(0).message("解约成功").build());
        }
        return new ChargeResponse<>(BankCollectionSignConfirmResponseDTO.builder().status(1).message("解约失败").build());
    }
}
