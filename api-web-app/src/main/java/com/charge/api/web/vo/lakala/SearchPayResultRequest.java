package com.charge.api.web.vo.lakala;

import com.charge.api.web.vo.pos.PayMethodChannel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchPayResultRequest {
    @NotBlank(message = "商户号不能为空")
    private String mercid;
    private String termid;
    private PayMethodChannel payMethodChannel;
    private String txntim;
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    private String tranSeqNo;
    @NotBlank(message = "交易结果不能为空")
    private String payResult;
    private String token;
    private Boolean doubleCheck;
    private Boolean retry;
    private String outTransactionId;

    /**
     * 支付时间格式 拉卡拉 20240321131745 需要转为 "yyyy-MM-dd HH:mm:ss"
     */
    private String payTime;

    private String amount;

}
