package com.charge.api.web.vo.joylife;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 预收退款
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class PredepositRefundParamVO extends BaseRefundReq {

    /**
     * 项目id
     */
    private Long communityId;

    /**
     * 朝昔项目id
     */
    @NotBlank(message = "项目id不能为空")
    private String communityMsId;

    /**
     * 退款 预收费项
     */
    @NotEmpty(message = "退款列表不能为空")
    @Valid
    private List<PredepositRefundItemVO> listItem;

    /**
     * 退款类型 0预存退款 1押金退款
     */
    private int type;

    /**
     * 扣除万象星数量
     */
    private Integer revertPoints;

    /**
     * 大会员账号pid
     */
    private String pid;

    /**
     * 会员手机号
     */
    private String phone;

    /**
     * 图片路径地址
     */
    private List<String> imagePathList;

    /**
     * 退款操作人id
     */
    private String operator;
}
