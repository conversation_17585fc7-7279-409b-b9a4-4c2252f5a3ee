package com.charge.api.web.support;

import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CustomerClient;
import com.charge.maindata.condition.CustomerCondition;
import com.charge.maindata.pojo.dto.CustomerDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class CustomerSupport {

    @Autowired
    private CustomerClient customerClient;

    public List<CustomerDTO> getCustomerListByMsId(Long communityId, String msId) throws ChargeBusinessException {
        if(Objects.isNull(communityId) || StringUtils.isEmpty(msId)){
            return Collections.EMPTY_LIST;
        }
        CustomerCondition condition = CustomerCondition.builder()
                .communityId(communityId)
                .msId(msId)
                .build();
        return  AppInterfaceUtil.getResponseDataThrowException(customerClient.listCustomer(condition));
    }
}
