package com.charge.api.web.convert;

import com.charge.api.web.vo.joylife.request.BatchPayDataRequest;
import com.charge.config.dto.points.PointsSignCommunityDTO;
import com.charge.pay.dto.point.ChargePointsTransDTO;
import com.charge.pay.dto.point.ChargePointsTransRecordDTO;
import com.charge.pay.dto.point.PointsTradeConfirmMqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description:
 * @date 2023/4/1817:41
 */
@Mapper(uses = {StringToLongMapper.class})
public interface PointsTradeConverter {
    PointsTradeConverter INSTANCE = Mappers.getMapper(PointsTradeConverter.class);

    @Mappings({
            @Mapping(target = "itemId", ignore = true ),
            @Mapping(target = "itemName", ignore = true ),
            @Mapping(target = "orgId", source = "config.orgId"),
            @Mapping(target = "communityId", source = "config.communityId"),
            @Mapping(target = "merchantCode", source = "config.merchantNumber"),
            @Mapping(target = "storeCode", source = "config.shopNumber"),
            @Mapping(target = "userId", source = "pay.userId"),
            @Mapping(target = "userName", source = "pay.userName"),
            @Mapping(target = "pid", source = "pay.equityAccount"),
            @Mapping(target = "phone", source = "pay.phone"),
            @Mapping(target = "points", source = "pay.points"),
            @Mapping(target = "equityMoney", source = "pay.pointsMoney"),
            @Mapping(target = "paymentTerminal", source = "pay.paymentSource")
    })
    ChargePointsTransDTO toPointsTransDTO(BatchPayDataRequest pay, PointsSignCommunityDTO config);

    PointsTradeConfirmMqDTO toConfirmDTO(ChargePointsTransDTO transDTO);

    PointsTradeConfirmMqDTO toConfirmDTO(ChargePointsTransRecordDTO recordDTO);
}
