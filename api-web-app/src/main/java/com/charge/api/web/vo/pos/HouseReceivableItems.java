package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 房屋应收项
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@NoArgsConstructor
@Data
public class HouseReceivableItems {

    @JsonProperty("houseId")
    private String houseId;
    @JsonProperty("arrearsPrice")
    private String arrearsPrice;
    @JsonProperty("itemList")
    private List<ChargeItemPrice> itemList;

}


