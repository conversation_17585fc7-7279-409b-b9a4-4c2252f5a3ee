package com.charge.api.web.dto.collection.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BankCollectionSignRequestDTO implements Serializable {
    private static final long serialVersionUID = -8360096699188199161L;

    @NotNull(message = "请输入项目ms id")
    private String communityMsId;
    @NotNull(message = "请输入要签约的资产ms id列表")
    private List<String> assetMsIdList;
    /**
     * 项目托收配置id
     */
    @NotNull(message = "请输入配置id")
    private Long configId;

    /**
     * 开户人
     */
    @NotNull(message = "请输入开户人")
    private String bankAccountName;

    /**
     * 银行账号
     */
    @NotNull(message = "请输入银行卡号")
    private String bankAccount;

    /**
     * 银行ID
     */
    private String bankCode;
    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 证件号码
     */
    @NotNull(message = "请输入证件号码")
    private String certificateNum;

    /**
     * 证件类型
     */
    @NotNull(message = "请输入证件类型")
    private String certificateType;

    /**
     * 手机
     */
    @NotNull(message = "请输入手机号码")
    private String phone;

    @NotNull(message = "请输入用户ms id")
    private String userMsId;

    private Integer cmbFlag;
}
