package com.charge.api.web.controller.pos;

import com.charge.api.web.service.pos.ChargeItemConfigService;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.config.enums.BusinessTypeEnum;
import com.charge.order.enums.OrderRuleCategoryEnum;
import com.charge.pos.dto.OrderRuleChargeItemDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;


@Api(value = "收费项2.0 ")
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChargeItemConfigController {

    private final ChargeItemConfigService chargeItemConfigService;

    /**
     * description:  收费项分页查询  订单品类 临时-通用
     * author: wuChao
     * date: 2023/2/28
     * param [communityId, currentPage, pageSize]
     * return com.charge.user.dto.common.ResultPage<com.charge.api.web.vo.pos.SystemChargeItem>
     **/
    @ApiOperation(value = "查询临时收费项列表（117和138）", notes = "查询临时收费项列表（117和138）")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "currentPage", value = "当前页数（默认为1）", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量（默认为10）", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getTempChargeItemList", method = {RequestMethod.GET})
    public ChargePageResponse<List<SystemChargeItemVO>> getOrderRuleChargeItemPage(
            String communityId, @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, String token) {
        OrderRuleChargeItemDTO dto = new OrderRuleChargeItemDTO();
        dto.setPageNum(currentPage);
        dto.setPageSize(pageSize);
        dto.setCommunityId(Long.valueOf(communityId));
        dto.setFirstClassificationId(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY.getCode());
        dto.setSecondClassificationId(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_300.getCode());
        return chargeItemConfigService.getOrderRuleChargeItemPage(dto);
    }

    /**
     * 收费项查询  订单品类 临时-临停
     *
     * @param token         token
     * @param communityUuid 小区id
     * @return list
     */
    @ApiOperation(value = "获取 已选收费项列表", notes = "获取 已选收费项列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String")
    })
    @RequestMapping(value = "/chargeItem/getTemporary", method = {RequestMethod.GET})
    public ChargeResponse<List<TemporaryChargeItemVO>> getOrderRuleChargeItemTemporaryStop(String token, String communityUuid) {
        OrderRuleChargeItemDTO dto = new OrderRuleChargeItemDTO();
        dto.setCommunityId(Long.valueOf(communityUuid));
        dto.setFirstClassificationId(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY.getCode());
        dto.setSecondClassificationId(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_301.getCode());
        return new ChargeResponse<>(chargeItemConfigService.getOrderRuleChargeItemTemporaryStop(dto));
    }


//    /**
//     * description: 收费项查询  订单品类 临时-押金
//     * author: wuChao
//     * date: 2023/3/1
//     * param [communityId, pageNum, pageSize, keyword, token]
//     * return com.charge.api.web.vo.ChargePageResponse<java.util.List<com.charge.api.web.vo.pos.SystemChargeItemVO>>
//     **/
//    @ApiOperation(value = "获取押金项列表(114)", notes = "获取押金项列表(114)")
//    @ApiImplicitParams({
//            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区id", required = true, dataType = "String"),
//            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "所要查询的页数,默认为1", required = false, dataType = "int"),
//            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量，默认为10", required = false, dataType = "int"),
//            @ApiImplicitParam(paramType = "query", name = "keyword", value = "关键字", required = false, dataType = "String"),
//            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
//    })
//    @RequestMapping(value = "/getDepositItem", method = {RequestMethod.GET})
//    public ChargePageResponse<List<SystemChargeItemVO>> getOrderRuleDepositItem(String communityId, @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
//                                                                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, String keyword, String token) {
//        OrderRuleChargeItemDTO dto = new OrderRuleChargeItemDTO();
//        dto.setPageNum(pageNum);
//        dto.setPageSize(pageSize);
//        dto.setCommunityId(Long.valueOf(communityId));
//        dto.setFirstClassificationId(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY.getCode());
//        dto.setSecondClassificationId(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_301.getCode());
//        return chargeItemConfigService.getOrderRuleChargeItemPage(dto);
//    }

    /**
     * description: 获取周期型（预存）收费项 分页查询
     * author: wuChao
     * date: 2023/3/1
     * param [communityUuid, prestoreItemName, pageNum, pageSize, token]
     * return com.charge.common.dto.ChargeResponse<java.util.List<com.charge.api.web.vo.pos.PrestoreItemVO>>
     **/
    @ApiOperation(value = "获取专项预存收费项列表(分页)", notes = "获取专项预存收费项列表(分页)")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "prestoreItemName", value = "专项预存名称", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "所要查询的页数,默认为1", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量，默认为10", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯秘串(来自鉴权微服务的access_token)", required = true, dataType = "String")
    })
    @RequestMapping(value = "/getPrestoreItem", method = {RequestMethod.GET})
    public ChargeResponse<List<PrestoreItemVO>> getPrestoreItem(
            String communityUuid, String prestoreItemName, @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, String token) {
        OrderRuleChargeItemDTO dto = new OrderRuleChargeItemDTO();
        dto.setPageNum(pageNum);
        dto.setPageSize(pageSize);
        dto.setCommunityId(Long.valueOf(communityUuid));
        dto.setItemName(prestoreItemName);
        dto.setBusinessTypes(Collections.singletonList(BusinessTypeEnum.CYCLICAL.getCode()));
        return chargeItemConfigService.getPrestoreItem(dto);
    }

    @ApiOperation(value = "获取押金项列表(114)", notes = "获取押金项列表(114)")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "所要查询的页数,默认为1", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量，默认为10", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "keyword", value = "关键字", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getDepositItem", method = {RequestMethod.GET})
    public ChargePageResponse<List<DepositItemVO>> pageQueryDepositItem(@RequestParam Long communityId, @RequestParam(defaultValue = "1",required = false) Integer pageNum,@RequestParam String token,
                                                                        @RequestParam( defaultValue = "10",required = false) Integer pageSize, @RequestParam(required = false) String keyword) throws ChargeBusinessException {
        return chargeItemConfigService.pageQueryDepositItem(communityId,pageNum,pageSize,keyword);
    }



    @ApiOperation(value = "查询收费细项列表及模糊搜索", notes = "查询收费细项列表及模糊搜索")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemUuid", value = "收费项id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "incomeDetailName", value = "收费项名称", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "当前页数（默认为1）", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量（默认为10）", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getTempetail", method = {RequestMethod.GET})
    public ChargeResponse<List<FinIncomeDetailVO>> getTempetail(String communityId, String itemUuid, String incomeDetailName,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, String token) {
        int num = BigDecimal.ZERO.intValue();
        return new ChargePageResponse<>(Lists.newArrayList(), pageSize, num, pageNum, num);
    }

}
