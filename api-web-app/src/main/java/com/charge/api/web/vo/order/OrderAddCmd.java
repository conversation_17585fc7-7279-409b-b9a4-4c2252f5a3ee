package com.charge.api.web.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单新增
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderAddCmd extends BaseOrderCmdReq {

    /**
     * 定金/意向金 600   佣金 601
     */
    @NotBlank(message = "二级分类不能为空")
    private String secondClassificationId;

    /**
     * 资产id(无指定资产则默认是项目虚拟房间)
     */
    private Long assetId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    @NotBlank(message = "用户名称不能为空")
    @Length(max = 200, message = "用户名称最大长度200")
    private String customerName;

    /**
     * 货物名称
     */
    @NotBlank(message = "货物名称不能为空")
    @Length(max = 255, message = "货物名称最大长度255")
    private String goodsName;


    /**
     * 订单初始金额
     */
    @NotNull(message = "订单初始金额不能为空")
    @DecimalMin(value = "0.01", message = "订单初始金额不能小于0.01")
    private BigDecimal originalAmount;


    /**
     * 支付单号 -新增已支付的订单时必传，是之前通过收费支付的收费的支付单号，用于查询关联之前的支付要素
     */
    private String payOrderNum;

    /**
     * 订单优惠类型
     */
    private String discountType;

    /**
     * 订单优惠金额
     */
    private BigDecimal discountAmount;


    /**
     * 支付模式：1-分期支付，2-全额支付
     */
    @NotNull(message = "支付模式不能为空")
    private Integer payMode;

    /**
     * 支付状态：0-待支付，1-已支付
     */
    @NotNull(message = "支付状态不能为空")
    private Integer payStatus;

    /**
     * 已支付金额（添加已支付订单时需要传）
     */
    private BigDecimal payAmount;

    /**
     * 订单下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "下单时间不能为空")
    private Date orderTime;

    /**
     * 子订单
     */
    @NotEmpty(message = "子单列表不能为空")
    @Valid
    private List<SubOrderAdd> subOrders;


    /**
     * 合同号
     */
    private String contractNo;


    /**
     * 扩展字段，支持业务自定义的字段
     */
    private Map<String, Object> extend;


}