package com.charge.api.web.dto.collection.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BankCollectionSignConfirmRequestDTO implements Serializable {
    private static final long serialVersionUID = -8360096699188199161L;

    @NotNull(message = "请输入项目ms id")
    private String applyNum;
    @NotNull(message = "请输入要签约的资产ms id列表")
    private String authCode;

    @NotNull(message = "请输入项目ms id")
    private String communityMsId;
}
