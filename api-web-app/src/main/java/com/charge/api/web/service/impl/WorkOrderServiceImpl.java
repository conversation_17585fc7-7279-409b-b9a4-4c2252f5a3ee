package com.charge.api.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.adapter.PayOrderAdapter;
import com.charge.api.web.service.joylife.WorkOrderService;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.support.PrePayLockSupport;
import com.charge.bill.dto.LockObjectDTO;
import com.charge.bill.enums.BillPrePaySceneEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.util.CollectionUtil;
import com.charge.joylife.dto.PayInfoDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.order.client.OrderClient;
import com.charge.order.dto.OrderDTO;
import com.charge.order.dto.WorkOrderPayCmd;
import com.charge.pay.client.PayClient;
import com.charge.pay.dto.pay.PayOrderSubmitResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 10:07
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WorkOrderServiceImpl implements WorkOrderService {

    private final OrderClient orderClient;
    private final PayClient payClient;

    private final PrePayLockSupport prePayLockSupport;

    private final CommunitySupport communitySupport;



    @Override
    public ChargeResponse<PayInfoDTO> orderPay(WorkOrderPayCmd workOrderPayCmd,String msid) throws ChargeBusinessException {
        CommunityDTO v2CommunityDTO = communitySupport.getCommunityByMsId(msid);
        workOrderPayCmd.setCommunityId(v2CommunityDTO.getId());
        // 校验订单重复性
        ChargeResponse<List<OrderDTO>> orderByOrderNumList = orderClient.getOrderByOrderNumList(Collections.singletonList(workOrderPayCmd.getOrderNum()),workOrderPayCmd.getCommunityId());
        List<OrderDTO> orderDTOList = AppInterfaceUtil.getDataThrowException(orderByOrderNumList);
        if (CollectionUtil.isEmpty(orderDTOList)) {
            log.info("订单不存在，orderNum:{}", workOrderPayCmd.getOrderNum());
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "订单不存在");
        }
        LockObjectDTO lockObjectDTO = LockObjectDTO.builder().mark(workOrderPayCmd.getOrderNum())
                .paymentMethod(workOrderPayCmd.getPaymentMethod()).paymentSource(workOrderPayCmd.getPaymentSource())
                .payMember(workOrderPayCmd.getPayMember()).businessScene(BillPrePaySceneEnum.ZHAOXI_ORDER.getCode())
                .orderIdList(new HashSet<>(Arrays.asList(orderDTOList.get(0).getId())))
                .build();
        prePayLockSupport.lockAndClose(lockObjectDTO);
        // 预支付下单

        ChargeResponse<PayOrderSubmitResponseDTO> response = payClient.tradeCreate(
                PayOrderAdapter.requestWorkOrderBuild(orderDTOList.get(0), workOrderPayCmd));
        if (!response.isSuccess() || response.getContent() == null) {
            log.error("支付调用异常,condition:{} response:{}", JSON.toJSONString(workOrderPayCmd), JSON.toJSONString(response));
            prePayLockSupport.unlock(lockObjectDTO);
            throw new ChargeBusinessException(response.getCode().toString(), response.getMessage());
        }
        try {
            // 更新有偿工单支付状态
            orderClient.orderPay(workOrderPayCmd);
        }catch (Exception ex){
            prePayLockSupport.unlock(lockObjectDTO);
            throw ex;
        }

        return new ChargeResponse<>(PayOrderAdapter.responseWorkOrderBuild(response.getContent()));
    }
}
