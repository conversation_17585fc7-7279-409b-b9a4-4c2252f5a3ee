package com.charge.api.web.config;

import com.charge.api.web.interceptor.PosLoginInterceptor;
import org.apache.catalina.connector.Connector;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.MappedInterceptor;

import javax.annotation.Resource;

/**
 * @Description
 * @Author: yjw
 * @Date: 2023/10/17 17:50
 */

@Configuration
public class MvcConfig implements WebMvcConfigurer {

    @Resource
    private PosLoginInterceptor posLoginInterceptor;

    @Bean
    public MappedInterceptor mappedInterceptor2(PosLoginInterceptor posLoginInterceptor) {
        return new MappedInterceptor(new String[]{"/**"}, new String[]{"/login","/payNotify","/getLostPayNum"},posLoginInterceptor);
    }

    /**
     * 请求接口URL支持带特殊符号 - 转义
     * @return
     */
    @Bean
    public TomcatServletWebServerFactory webServerFactory() {
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.addConnectorCustomizers((Connector connector) -> {
            connector.setProperty("relaxedPathChars", "\"<>[\\]^`{|}");
            connector.setProperty("relaxedQueryChars", "\"<>[\\]^`{|}");
        });
        return factory;
    }
}
