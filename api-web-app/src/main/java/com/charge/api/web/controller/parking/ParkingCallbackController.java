package com.charge.api.web.controller.parking;

import com.charge.api.web.dto.parking.*;
import com.charge.api.web.enums.EcsbEnums;
import com.charge.api.web.service.parking.PlatformBankFlowService;
import com.charge.api.web.service.parking.PlatformParkingSpaceService;
import com.charge.api.web.vo.ParkingPlaceBalanceVO;
import com.charge.api.web.vo.ParkingPlacePeriodVO;
import com.charge.api.web.vo.parking.InvoiceCommunityConfigVO;
import com.charge.api.web.vo.parking.InvoiceCommunityItemTaxConfigVO;
import com.charge.common.constant.MetricConstants;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * Author: yjw
 * Date: 2023/5/11 14:23
 */

@Api(value = "停车云平台相关接口")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ParkingCallbackController {

    private final PlatformBankFlowService platformBankFlowServiceImpl;

    private final PlatformParkingSpaceService platformParkingSpaceService;

    @ApiOperation(value = "获取停车云平台临停订单业务流水接口", notes = "获取停车云平台临停订单业务流水接口")
    @PostMapping({"/platformbankflow/parkingDataSync"})
    public ResultResponse getPlatformBankBusinessFlow(@RequestBody GetPlatformBankFlowParam getPlatformBankFlowParam, HttpServletResponse httpServletResponse) throws ChargeBusinessException {
        ResultResponse response = platformBankFlowServiceImpl.getPlatformBankBusinessFlow(getPlatformBankFlowParam);
        if(EcsbEnums.SUCCESS.getCode().equals(response.getRESPONSE().getRETURN_CODE())){
            httpServletResponse.setHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.SUCCESS.getCode());
        } else {
            httpServletResponse.setHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.FAIL.getCode());
        }
        return response;
    }

    @ApiOperation(value = "临停订单历史业务流水初始化（软删）", notes = "临停订单历史业务流水初始化（软删）")
    @PostMapping({"/platformbankflow/historyDataDelete"})
    public ResultResponse historyDataDelete(@RequestBody DeleteHistoryDataParam deleteHistoryDataParam, HttpServletResponse httpServletResponse) {
        ResultResponse response = platformBankFlowServiceImpl.historyDataDelete(deleteHistoryDataParam);
        if(EcsbEnums.SUCCESS.getCode().equals(response.getRESPONSE().getRETURN_CODE())){
            httpServletResponse.setHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.SUCCESS.getCode());
        } else {
            httpServletResponse.setHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.FAIL.getCode());
        }
        return response;
    }

    /**
     * 查询开票模式和销方信息 输入参数  communityId:小区id，返回：开票配置 （1）开票模式 （2）销方信息
     * @param request
     * @return
     */
    @PostMapping({"/platformbankflow/invoice/config"})
    public ChargeResponse<InvoiceCommunityConfigVO> getInvoiceConfig(@RequestBody @Validated InvoiceConfigRequestParam request) throws ChargeBusinessException {
        return platformBankFlowServiceImpl.getInvoiceCommunityConfig(request.getCommunityCode());

    }

    /**
     * 税收分类和税率
     * @param request
     * @return
     */
    @PostMapping({"/platformbankflow/invoice/item/config"})
    public ChargeResponse<InvoiceCommunityItemTaxConfigVO> getInvoiceItemConfig(@RequestBody  @Validated InvoiceItemConfigRequestParam request) throws ChargeBusinessException {
        return platformBankFlowServiceImpl.getInvoiceChargeItemConfig(request.getCommunityCode(), request.getItemCode());
    }

    @ApiOperation(value = "查询产权车位预存余额及可用余额期数（含专项预存、待结转）", notes = "查询产权车位预存余额及可用余额期数（含专项预存、待结转）")
    @PostMapping({"/platformPark/parkingPlace/balance"})
    public ChargeResponse<List<ParkingPlaceBalanceVO>> getParkingPlaceBalance(@Valid @RequestBody ParkingSpaceParam parkingSpaceParam) throws ChargeBusinessException {
        return new ChargeResponse<>(platformParkingSpaceService.getParkingPlaceBalance(parkingSpaceParam));
    }

    @ApiOperation(value = "查询产权车位授权期限", notes = "查询产权车位授权期限")
    @PostMapping({"/platformPark/parkingPlace/period"})
    public ChargeResponse<List<ParkingPlacePeriodVO>> getParkingPlacePeriod(@Valid @RequestBody ParkingSpaceParam parkingSpaceParam) throws ChargeBusinessException {
        return new ChargeResponse<>(platformParkingSpaceService.getParkingPlacePeriod(parkingSpaceParam));
    }
}
