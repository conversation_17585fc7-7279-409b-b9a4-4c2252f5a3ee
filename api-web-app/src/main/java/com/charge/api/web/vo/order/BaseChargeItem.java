package com.charge.api.web.vo.order;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * BaseChargeItem
 *
 * <AUTHOR>
 * @date 2024/12/12
 */
@Data
public class BaseChargeItem {
    /**
     * 收费项id：与收费项编码二选一传入
     */
    private Long itemId;

    /**
     * 收费项编码：与收费项id二选一传入
     */
    private String itemCode;

    /**
     * 收费项名称
     */
    @NotBlank(message = "收费项名称不能为空")
    private String itemName;
}