package com.charge.api.web.support;

import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.enums.redis.RedisKeyLockEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.starter.jedis.JedisManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 2023/08/08/ 11:26
 * @description
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BankCollectionCheckSupport {

    private final JedisManager jedisManager;

    /**
     * 根据资产校验是否处于托收，是的话抛出异常
     * @param assetId
     * @return
     * @throws ChargeBusinessException
     */
    public Boolean bankCollectingByAssetId(Long assetId) throws ChargeBusinessException {
        if (Objects.isNull(assetId)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002);
        }
        String bankCollectionLock = jedisManager.getString(RedisKeyLockEnum.BANK_COLLECTION_ASSET_ID.key(assetId));
        return StringUtils.isNotEmpty(bankCollectionLock) ? Boolean.TRUE : Boolean.FALSE;
    }

    /**
     * 根据资产列表校验是否处于托收，是的话抛出异常
     * @param assetIds
     * @return
     * @throws ChargeBusinessException
     */
    public Boolean bankCollectingByAssetIds(Collection<Long> assetIds) throws ChargeBusinessException {
        if (CollectionUtils.isEmpty(assetIds)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002);
        }
        for (Long assetId : assetIds) {
            String bankCollectionLock = jedisManager.getString(RedisKeyLockEnum.BANK_COLLECTION_ASSET_ID.key(assetId));
            if (StringUtils.isNotEmpty(bankCollectionLock)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
