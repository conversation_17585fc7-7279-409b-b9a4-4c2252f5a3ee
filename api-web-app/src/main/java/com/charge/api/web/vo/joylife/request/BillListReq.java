package com.charge.api.web.vo.joylife.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/8/26 11:27
 */
@Data
public class BillListReq extends ZhaoXiAssertRequest {

    /**
     * 订单号
     */
    private String orderNum;

    /**
     * 客户ID
     */
    private String customerMsId;

    /**
     * 交易日期开始时间（yyyy-MM-dd）
     */
    private String payTimeStart;

    /**
     * 交易日期截止时间（yyyy-MM-dd）
     */
    private String payTimeEnd;

    /**
     * id初始值
     */
    @NotNull(message = "id初始值不能为空")
    private Long idOffSet;

    /**
     * 单批次数量
     */
    @NotNull(message = "单批次数量不能为空")
    private Integer querySize;

}
