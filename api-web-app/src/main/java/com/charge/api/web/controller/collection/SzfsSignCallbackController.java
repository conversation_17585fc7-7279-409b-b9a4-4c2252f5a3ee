package com.charge.api.web.controller.collection;

import com.charge.apicloud.client.SzfsClient;
import com.charge.apicloud.dto.szfs.request.SzfsCallbackRequestDTO;
import com.charge.apicloud.dto.szfs.response.ContractCallbackResponseDTO;
import com.charge.apicloud.dto.szfs.response.SzfsSignCallbackResponseDTO;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.core.util.StringUtil;
import com.charge.pay.client.collection.BankCollectionCommunityConfigClient;
import com.charge.pay.dto.collection.request.SzfsSignCallbackRequestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Objects;

@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping(value = "/szfs/sign")
public class SzfsSignCallbackController {

    private final BankCollectionCommunityConfigClient bankCollectionCommunityConfigClient;

    private final SzfsClient szfsClient;

    @RequestMapping(value = "/callback", method = {RequestMethod.POST, RequestMethod.GET})
    public String callback(HttpServletRequest req,
                           HttpServletResponse response
                           ) throws ChargeBusinessException {
        Map<String, String[]> parameterMap = req.getParameterMap();
        for (Map.Entry<String, String[]> stringEntry : parameterMap.entrySet()) {
            log.info("map {} {}", stringEntry.getKey(), stringEntry.getValue());
        }
        String msgType = req.getParameter("msgtype");
        String corpNo  = req.getParameter("corpNo");
        String version = req.getParameter("version");
        String subNode = req.getParameter("subnode");
        String sendTime = req.getParameter("sendtime");
        String msgNo = req.getParameter("msgno");
        String respType = req.getParameter("resptype");
        String zipType = req.getParameter("ziptype");
        String signature = req.getParameter("signature");
        String msgBody = req.getParameter("msgbody");
        String gatewayVersion = req.getParameter("gatewayversion");
        log.info("深结算签约回调{}", corpNo);
        if (StringUtil.isEmpty(corpNo)) {
            log.error("corpNo为空");
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "corpNo为空");
        }
        SzfsSignCallbackResponseDTO signCallbackResponseDTO = AppInterfaceUtil.getResponseDataThrowException(szfsClient.signCallback(SzfsCallbackRequestDTO.builder()
                .gatewayVersion(gatewayVersion)
                .msgBody(msgBody)
                .msgNo(msgNo)
                .msgType(msgType)
                .respType(respType)
                .sendTime(sendTime)
                .signature(signature)
                .subNode(subNode)
                .corpNo(corpNo)
                .version(version)
                .zipType(zipType).build()));

        ContractCallbackResponseDTO.BizResult bizResult = signCallbackResponseDTO.getContractCallbackResponseDTO().getBizResult();
        AppInterfaceUtil.getResponseDataThrowException(bankCollectionCommunityConfigClient.signCallback(SzfsSignCallbackRequestDTO.builder()
                .authUrl(Objects.isNull(bizResult) ? null : bizResult.getAuthUrl())
                .status(signCallbackResponseDTO.getContractCallbackResponseDTO().getStatus())
                .message(signCallbackResponseDTO.getContractCallbackResponseDTO().getRemark())
                .subNode(signCallbackResponseDTO.getContractCallbackResponseDTO().getOsubNode())
                .entrustNo(Objects.isNull(bizResult) ? null : bizResult.getContractNo())
                .applyNum(signCallbackResponseDTO.getContractCallbackResponseDTO().getOseqId())
                .build()));
        response.setHeader("signature", signCallbackResponseDTO.getSignature());
        return signCallbackResponseDTO.getResponseXml();
    }
}
