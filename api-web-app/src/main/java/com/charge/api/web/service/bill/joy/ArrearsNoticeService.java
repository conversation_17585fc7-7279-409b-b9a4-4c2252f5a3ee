package com.charge.api.web.service.bill.joy;

import com.charge.api.web.convert.ArrearsNoticeConverter;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.service.order.CreateOrderContext;
import com.charge.api.web.service.pos.BillV2Service;
import com.charge.api.web.support.*;
import com.charge.api.web.vo.BillItem;
import com.charge.api.web.vo.arrearsnotice.*;
import com.charge.api.web.vo.pos.v3.AssetBill;
import com.charge.api.web.vo.pos.v3.CreateBillReq;
import com.charge.api.web.vo.pos.v3.CreateBillResp;
import com.charge.bill.client.ArrearsNoticeClient;
import com.charge.bill.client.AssetTransactionClient;
import com.charge.bill.client.IncomeBillClient;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.LockObjectDTO;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.arrearsnotice.ArrearsNoticeAssetReceivableDTO;
import com.charge.bill.dto.arrearsnotice.ArrearsNoticeDTO;
import com.charge.bill.dto.arrearsnotice.ArrearsNoticeQueryDTO;
import com.charge.bill.dto.income.AssetTransactionDTO;
import com.charge.bill.dto.income.AssetTransactionQueryDTO;
import com.charge.bill.dto.income.IncomeBillDTO;
import com.charge.bill.dto.income.IncomeBillRequestDTO;
import com.charge.bill.enums.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.UserSessionDTO;
import com.charge.common.enums.redis.RedisKeyBillEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.util.SessionUtil;
import com.charge.config.dto.standard.StandardConfigDTO;
import com.charge.general.client.message.MessageClient;
import com.charge.general.dto.message.MessageSearchRequestDTO;
import com.charge.general.enums.MessageType;
import com.charge.general.enums.SendChannel;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.pay.client.TransferPayClient;
import com.charge.pay.dto.CommunityBankAccountGroupDTO;
import com.charge.pay.dto.pay.TransferPayDTO;
import com.charge.pay.dto.pay.TransferPayQueryDTO;
import com.charge.pay.dto.reward.CommunityBankAccountPlusDTO;
import com.charge.starter.jedis.JedisManager;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ArrearsNoticeService
 * <p>
 * Description:缴费通知单实现服务
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ArrearsNoticeService {
    private final ArrearsNoticeClient arrearsNoticeClient;

    private final AssetSupport assetSupport;

    private final ReceivableBillClient receivableBillClient;

    private final IncomeBillClient incomeBillClient;

    private final TransferPayClient transferPayClient;

    private final CommunitySupport communitySupport;

    private final BankAccountSupport bankAccountSupport;

    private final AssetTransactionClient assetTransactionClient;

    private final MessageClient messageClient;

    private final ChargeStandardSupport chargeStandardSupport;

    private final JedisManager jedisManager;

    @Value("${arrearsNotice.url.pay:https://testcharge.crlandpm.com.cn/v2/h5/fee-payment-notification?communityId=%d&id=%d&token=%s}")
    private String transferH5PayUrl;

    @Value("${arrearsNotice.bankName:招商银行深圳分行}")
    private String bankName;
    @Value("${arrearsNotice.report.email:defaultEmail}")
    private String reportEmail;

    @Value("${arrearsNotice.report.phone:defaultPhone}")
    private String reportPhone;

    @Value("${arrearsNotice.token.times:200}")
    private Integer tokenTimes;
    @Value("${arrearsNotice.token.expire:5184000}")
    private Integer tokenExpire;

    @Value("${arrearsNotice.token.check:false}")
    private Boolean checkToken;

    private final BillV2Service billV2Service;

    private final PrePayLockSupport prePayLockSupport;

    private void validateToken(String token){
        if(!StringUtils.hasText(token)){
            throw new IllegalArgumentException("token为空");
        }
        if (jedisManager.decr(token)<=0){
            throw new IllegalArgumentException("token校验失败");
        }
    }

    public ArrearsNoticeCreateOrderResp createBill(ArrearsNoticeCreateOrderReq noticeCreateOrderReq) throws ChargeBusinessException {
        validateToken(noticeCreateOrderReq.getToken());
        List<TransferPayDTO> transferPayDTOS = getTransferPayDTOS(noticeCreateOrderReq);
        if(!CollectionUtils.isEmpty(transferPayDTOS)){
            //校验未支付
            transferPayDTOS.stream().filter(t -> Objects.equals(t.getPayStatus(), 1)).findAny().ifPresent(t -> {
                throw new IllegalArgumentException("缴费通知单已经支付:"+t.getBizId());
            });
            //有待支付的缴费通知单则直接返回
            Optional<TransferPayDTO> toPayDTOOptional = transferPayDTOS.stream().filter(t -> Objects.equals(t.getPayStatus(), 0)).findAny();
            if(toPayDTOOptional.isPresent()){
                TransferPayDTO transferPayDTO = toPayDTOOptional.get();
                CreateBillReq createBillReq = buildCreateBillReq(noticeCreateOrderReq);
                Set<Long> receivableIds = createBillReq.getBills().stream().flatMap(a -> a.getReceivables().stream().map(BillItem::getId)).collect(Collectors.toSet());
                lock(transferPayDTO.getOrderNum(),receivableIds,createBillReq.getPayMember());
                return new ArrearsNoticeCreateOrderResp(transferPayDTO.getBizId(),transferPayDTO.getAcceptorAccount()+transferPayDTO.getAccountSubUnitCode(),transferPayDTO.getAcceptorAccountName()
                ,transferPayDTO.getAcceptorAccountBank(),generateRandomUpperCaseString(4),noticeCreateOrderReq.getTotalAmount());
            }
        }
        CreateBillReq createOrderReq=buildCreateBillReq(noticeCreateOrderReq);
        CreateBillResp bill = billV2Service.createBill(createOrderReq);
        return buildArrearsNoticeCreateOrderResp(bill,noticeCreateOrderReq);

    }

    private void lock(String orderNum,Set<Long> receivableIds,String payMember) throws ChargeBusinessException {
        LockObjectDTO lockObjectDTO = LockObjectDTO.builder().mark(orderNum).paymentSource(PaymentTerminalEnum.CHARGE_SYSTEM.getCode())
                .paymentMethod(PaymentMethodEnum.TRANSFER_OFFLINE.getPaymentMethod()).businessScene(BillPrePaySceneEnum.CMB_TRANSFER.getCode())
                .receiveBillIdList(receivableIds)
                .payMember(payMember)
                .build();
        prePayLockSupport.lockAndClose(lockObjectDTO);
    }

    private List<TransferPayDTO> getTransferPayDTOS(ArrearsNoticeCreateOrderReq noticeCreateOrderReq) throws ChargeBusinessException {
        ChargeResponse<List<TransferPayDTO>> transferListResp = transferPayClient.list(TransferPayQueryDTO.builder().communityId(noticeCreateOrderReq.getCommunityId())
                .bizIds(Lists.newArrayList(noticeCreateOrderReq.getId())).build());
        List<TransferPayDTO> transferPayDTOS = AppInterfaceUtil.getResponseDataThrowException(transferListResp);
        return transferPayDTOS;
    }

    private ArrearsNoticeCreateOrderResp buildArrearsNoticeCreateOrderResp(CreateBillResp createBillResp,ArrearsNoticeCreateOrderReq req){
        ArrearsNoticeCreateOrderResp orderResp=new ArrearsNoticeCreateOrderResp();
        CreateOrderContext context = createBillResp.getContext();
        CommunityBankAccountGroupDTO communityBankAccount = context.getCommunityBankAccount();
        orderResp.setId(req.getId());
        orderResp.setTransferCode(generateRandomUpperCaseString(4));
        orderResp.setAcceptorAccount(communityBankAccount.getBankActualAccount()+createBillResp.getExternalOrderNum());
        orderResp.setAcceptorAccountName(communityBankAccount.getBankActualAccountName());
        orderResp.setAcceptorAccountBank(bankName);
        orderResp.setTotalAmount(context.getTotalAmount().setScale(2,RoundingMode.HALF_UP));
        return orderResp;
    }

    public static String generateRandomUpperCaseString(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            char c = (char) ('A' + random.nextInt(26)); // 'A' to 'Z'
            sb.append(c);
        }
        return sb.toString();
    }

    private CreateBillReq buildCreateBillReq(ArrearsNoticeCreateOrderReq noticeCreateOrderReq) throws ChargeBusinessException {
        ArrearsNoticeDTO arrearsNotice = AppInterfaceUtil.getResponseDataThrowException(arrearsNoticeClient.detail(noticeCreateOrderReq.getId()));
        Assert.notNull(arrearsNotice,"缴费通知单不存在");
        Assert.isTrue(arrearsNotice.getStatus()==1,"缴费通知单已失效");
        Assert.isTrue(!BillPayStatusEnum.SUCCESS.getCode().equals( arrearsNotice.getPayStatus()),"缴费通知单已经支付");
        List<ArrearsNoticeAssetReceivableDTO> details = arrearsNotice.getDetails();
        Assert.notEmpty(details,"缴费通知单明细不能为空");
        BigDecimal totalAmount = arrearsNotice.getArrearAmount();
        Assert.isTrue(totalAmount.compareTo(noticeCreateOrderReq.getTotalAmount())==0,"缴费通知单金额发生了变化");
        Map<Long, List<ArrearsNoticeAssetReceivableDTO>> assetDetailsMap = details.stream().collect(Collectors.groupingBy(ArrearsNoticeAssetReceivableDTO::getAssetId));
        List<Long> receivableIds = details.stream().map(ArrearsNoticeAssetReceivableDTO::getReceivableId).distinct().collect(Collectors.toList());
        List<ReceivableBillDTO> receivableBills = AppInterfaceUtil.getResponseDataThrowException(receivableBillClient.queryList(ReceivableConditionDTO.builder().ids(receivableIds)
                .communityId(noticeCreateOrderReq.getCommunityId()).build()));
        Assert.isTrue(receivableBills.size()==receivableIds.size(),"缴费通知单明细对应的应收单不存在");
        Map<Long, ReceivableBillDTO> receivableBillMap = receivableBills.stream().collect(Collectors.toMap(ReceivableBillDTO::getId, Function.identity()));
        for (ArrearsNoticeAssetReceivableDTO assetReceivableDTO : details){
            ReceivableBillDTO receivableBill = receivableBillMap.get(assetReceivableDTO.getReceivableId());
            Assert.isTrue(receivableBill.getBillStatus().equals(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode()),"缴费通知单明细对应的应收单状态不是已生效:"+receivableBill.getId());
            Assert.isTrue(receivableBill.getPayStatus().equals(ReceivableBillPayStatusEnum.NOT_PAY.getCode())
                    ||receivableBill.getPayStatus().equals(ReceivableBillPayStatusEnum.PAY_PARTIAL.getCode()),"缴费通知单明细对应的应收单不是待支付:"+receivableBill.getId());
            Assert.isTrue(receivableBill.getArrearsAmount().compareTo(assetReceivableDTO.getArrearsAmount())==0,"缴费通知单的金额发生了变化:"+receivableBill.getId());
        }
        CreateBillReq createBillReq=new CreateBillReq();
        createBillReq.setCommunityId(noticeCreateOrderReq.getCommunityId());
        createBillReq.setCollectorId(noticeCreateOrderReq.getCollectorId());
        createBillReq.setCollectorName(noticeCreateOrderReq.getCollectorName());
        createBillReq.setPayMember(arrearsNotice.getCustomerName());
        createBillReq.setTotalAmount(totalAmount);
        createBillReq.setArrearsNoticeId(noticeCreateOrderReq.getId());
        createBillReq.setPaymentTerminal(PaymentTerminalEnum.CHARGE_SYSTEM);
        createBillReq.setPaymentMethod(PaymentMethodEnum.TRANSFER_OFFLINE.getPaymentMethod());
        createBillReq.setPaymentChannel(PaymentChannelEnum.CMB_TRANSFER);
        List<AssetBill> assetBills = assetDetailsMap.entrySet().stream().map(entry -> buildAssetBill(entry.getValue(), entry.getKey(), receivableBillMap)).collect(Collectors.toList());
        createBillReq.setBills(assetBills);
        return createBillReq;
    }

    private AssetBill buildAssetBill(List<ArrearsNoticeAssetReceivableDTO> assetReceivables, Long assetId, Map<Long, ReceivableBillDTO> receivableBillMap) {
        AssetBill assetBill = new AssetBill();
        assetBill.setAssetId(assetId);
        BigDecimal totalAmount = assetReceivables.stream().map(a -> a.getArrearsAmount().add(a.getPenaltyArrearsAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assetBill.setAmount(totalAmount);
        assetBill.setReceivables(assetReceivables.stream().map(assetReceivableDTO -> buildBillItem(receivableBillMap, assetReceivableDTO)).collect(Collectors.toList()));
        return assetBill;
    }

    private static BillItem buildBillItem(Map<Long, ReceivableBillDTO> receivableBillMap, ArrearsNoticeAssetReceivableDTO assetReceivableDTO) {
        ReceivableBillDTO receivableBillDTO = receivableBillMap.get(assetReceivableDTO.getReceivableId());
        BillItem billItem = new BillItem();
        billItem.setId(receivableBillDTO.getId());
        billItem.setItemId(receivableBillDTO.getItemId());
        billItem.setItemName(receivableBillDTO.getItemName());
        billItem.setPoints(0L);
        billItem.setAmount(assetReceivableDTO.getArrearsAmount().add(assetReceivableDTO.getPenaltyArrearsAmount()));
        return billItem;
    }


    public List<ArrearsNoticeVO> list(ArrearsNoticeQuery query) throws ChargeBusinessException {
        ChargeResponse<List<Long>> listChargeResponse = messageClient.listBizIds(MessageSearchRequestDTO.builder().communityId(query.getCommunityId())
                .sendChannels(Lists.newArrayList(SendChannel.POS.name())).messageType(MessageType.ARREARS_NOTICE.getCode()).sentResult(1).build());
        List<Long> bizIds = AppInterfaceUtil.getResponseDataThrowException(listChargeResponse);
        if(CollectionUtils.isEmpty(bizIds)){
            return new ArrayList<>();
        }
        ArrearsNoticeQueryDTO requestDTO = ArrearsNoticeQueryDTO.builder().communityId(query.getCommunityId())
                .searchParam(query.getSearchParam()).querySize(query.getQuerySize()).ids(bizIds).idOffSet(query.getIdOffSet())
                .payStatusList(Lists.newArrayList(0)).statusList(Lists.newArrayList(1)).fillDetail(true).build();
        List<ArrearsNoticeDTO> arrearsNotices = AppInterfaceUtil.getResponseDataThrowException(arrearsNoticeClient.list(requestDTO));
        return convert(arrearsNotices, query.getCommunityId());
    }

    public ArrearsNoticeDetailVO detail(ArrearsNoticeDetailQuery query) throws ChargeBusinessException {
        if(StringUtils.hasText(query.getToken())){
            validateToken(query.getToken());
        }
        ArrearsNoticeDTO arrearsNotice = AppInterfaceUtil.getResponseDataThrowException(arrearsNoticeClient.detail(query.getId()));
        return convert(arrearsNotice, query);
    }

    public ArrearsNoticeDetailVO2 detailV2(ArrearsNoticeDetailQuery query) throws ChargeBusinessException {
        //pc-gateWay过来的校验sessionUser存在，apiGateWay过来的校验token
        Optional<UserSessionDTO> sessionUser = SessionUtil.getSessionUser();
        if(!sessionUser.isPresent()&&Boolean.TRUE.equals(checkToken)){
            validateToken(query.getToken());
        }else {
            log.info("detailV2 login,ignore token");
        }
        ArrearsNoticeDTO arrearsNotice = AppInterfaceUtil.getResponseDataThrowException(arrearsNoticeClient.detail(query.getId()));
        return convertV2(arrearsNotice,query);
    }

    public ArrearsNoticePayResultVO getPayResult(ArrearsNoticeDetailQuery query) throws ChargeBusinessException {
        ArrearsNoticeDTO arrearsNotice = AppInterfaceUtil.getResponseDataThrowException(arrearsNoticeClient.detail(query.getId()));
        Assert.notNull(arrearsNotice, "通知单不存在");
        ArrearsNoticePayResultVO payResultVO = ArrearsNoticePayResultVO.builder().payStatus(arrearsNotice.getPayStatus())
                .orderNum(arrearsNotice.getOrderNum()).build();
        if (payResultVO.getPayStatus().equals(BillPayStatusEnum.SUCCESS.getCode())) {
            Set<String> assetCodes = arrearsNotice.getDetails().stream().map(ArrearsNoticeAssetReceivableDTO::getAssetCode).collect(Collectors.toSet());
            Assert.hasText(arrearsNotice.getOrderNum(), "已支付的缴费通知单支付单号未空");
            IncomeBillRequestDTO incomeBillRequestDTO = new IncomeBillRequestDTO();
            incomeBillRequestDTO.setCommunityId(arrearsNotice.getCommunityId());
            incomeBillRequestDTO.setOrderNum(arrearsNotice.getOrderNum());
            ChargeResponse<IncomeBillDTO> incomeBillResponse =incomeBillClient .getIncomeBillByOrderNum(incomeBillRequestDTO);
            IncomeBillDTO incomeBillDTO = AppInterfaceUtil.getResponseDataThrowException(incomeBillResponse);
            AssetTransactionQueryDTO assetTransactionQuery  =new AssetTransactionQueryDTO();
            assetTransactionQuery.setCommunityId(arrearsNotice.getCommunityId());
            assetTransactionQuery.setIncomeIds(Lists.newArrayList(incomeBillDTO.getId()));
            ChargeResponse<List<AssetTransactionDTO>> listChargeResponse = assetTransactionClient.listAssetTransaction(assetTransactionQuery);
            List<AssetTransactionDTO> assetTransactionDTOS = AppInterfaceUtil.getResponseDataThrowException(listChargeResponse);
            payResultVO.setPayMethod(incomeBillDTO.getPaymentMethod());
            payResultVO.setPayMember(incomeBillDTO.getPayMember());
            payResultVO.setPayTime(incomeBillDTO.getPaymentTime());
            payResultVO.setCollectorName(incomeBillDTO.getCollectorName());
            payResultVO.setAssetNumber(assetCodes.size());
            payResultVO.setCommunityName(incomeBillDTO.getCommunityName());
            payResultVO.setTotalAmount(arrearsNotice.getArrearAmount());
            payResultVO.setAssetTransactionId(assetTransactionDTOS.get(0).getId());
            payResultVO.setExternalOrderNum(incomeBillDTO.getOutTransactionNo());
        }
        return payResultVO;

    }

    private ArrearsNoticeDetailVO2 convertV2(ArrearsNoticeDTO arrearsNoticeDTO, ArrearsNoticeDetailQuery query) throws ChargeBusinessException {
        Assert.isTrue(arrearsNoticeDTO!=null&&!CollectionUtils.isEmpty(arrearsNoticeDTO.getDetails()), "通知单不存在或者明细为空");
        CommunityDTO communityDTO = communitySupport.getCommunityById(arrearsNoticeDTO.getCommunityId());
        Assert.notNull(communityDTO,"项目不存在或者已失效");
        CommunityBankAccountGroupDTO communityBankAccount = bankAccountSupport.getByCommunityId(arrearsNoticeDTO.getCommunityId());
        CommunityBankAccountPlusDTO bankAccount = communityBankAccount.getBankAccounts().get(0);
        List<Long> assetIds = arrearsNoticeDTO.getDetails().stream().map(ArrearsNoticeAssetReceivableDTO::getAssetId)
                .distinct().collect(Collectors.toList());
        Map<Long, AssetAdapter> assetMap = assetSupport.listAssetByIds(arrearsNoticeDTO.getCommunityId(), assetIds).stream()
                .collect(Collectors.toMap(AssetAdapter::getId, Function.identity(), (a, b) -> a));
        Assert.isTrue(assetMap.size() == assetIds.size(), "部分资产不存在");

        List<Long> receivableIds = arrearsNoticeDTO.getDetails().stream().map(ArrearsNoticeAssetReceivableDTO::getReceivableId)
                .distinct().collect(Collectors.toList());
        ChargeResponse<List<ReceivableBillDTO>> receivableResponse = receivableBillClient.queryList(ReceivableConditionDTO.builder()
                .communityId(arrearsNoticeDTO.getCommunityId()).ids(receivableIds).build());
        List<ReceivableBillDTO> receivableBillDTOS = AppInterfaceUtil.getResponseDataThrowException(receivableResponse);
        Assert.isTrue(receivableBillDTOS.size() == receivableIds.size(), "部分应收不存在");
        Map<Long, ReceivableBillDTO> receivableMap = receivableBillDTOS.stream().collect(Collectors.toMap(ReceivableBillDTO::getId, Function.identity(), (a, b) -> a));

        List<ArrearsNoticeReceivableVO2> arrearsNoticeReceivableVO2s = arrearsNoticeDTO.getDetails().stream()
                .map(assetReceivableDTO -> {
                    ArrearsNoticeReceivableVO2 assetMonthReceivableVO = ArrearsNoticeConverter.INSTANCE.mapV2(assetReceivableDTO);
                    AssetAdapter assetAdapter = assetMap.get(assetReceivableDTO.getAssetId());
                    ReceivableBillDTO receivableBill = receivableMap.get(assetReceivableDTO.getReceivableId());
                    assetMonthReceivableVO.setCustomerNames(assetAdapter.getOwnerName());
                    assetMonthReceivableVO.setHouseHoldNames(assetAdapter.getHouseHoldName());
                    assetMonthReceivableVO.setItemName(receivableBill.getItemName());
                    assetMonthReceivableVO.setTotalArrearsAmount(assetReceivableDTO.getArrearsAmount().add(assetReceivableDTO.getPenaltyArrearsAmount()));
                    return assetMonthReceivableVO;
                }).sorted(Comparator.comparing(ArrearsNoticeReceivableVO2::getAssetCode)
                        .thenComparing(ArrearsNoticeReceivableVO2::getBelongYear)
                        .thenComparing(ArrearsNoticeReceivableVO2::getItemName))
                .collect(Collectors.toList());

        ArrearsNoticeDetailVO2 arrearsNoticeDetail = new ArrearsNoticeDetailVO2();
        arrearsNoticeDetail.setOrgName(communityDTO.getLegalCompanyCname());
        arrearsNoticeDetail.setCommunityName(communityDTO.getName());
        arrearsNoticeDetail.setTotalAmount(arrearsNoticeDTO.getArrearAmount());
        arrearsNoticeDetail.setCreateTime(DateUtils.format(new Date(arrearsNoticeDTO.getCreateTime().getTime()),DateUtils.FORMAT_8));
        arrearsNoticeDetail.setCustomerName(arrearsNoticeDTO.getCustomerName());
        arrearsNoticeDetail.setReportPhone(reportPhone);
        arrearsNoticeDetail.setReportEmail(reportEmail);
        arrearsNoticeDetail.setAcceptorAccountName(communityBankAccount.getBankActualAccountName());
        arrearsNoticeDetail.setVirtualSettlementAccount(bankAccount.getBankVirtualAccount());
        arrearsNoticeDetail.setPosMerchantNum(bankAccount.getLakalaAccount());
        arrearsNoticeDetail.setAcceptorMerchantNumAccount(bankAccount.getMerchantNum());
        arrearsNoticeDetail.setPayUrl(getPayUrl(arrearsNoticeDTO));
        arrearsNoticeDetail.setDetails(arrearsNoticeReceivableVO2s);
        if (Boolean.TRUE.equals(query.getWithPayUrl())) {
            arrearsNoticeDetail.setPayUrl(String.format(transferH5PayUrl, arrearsNoticeDTO.getCommunityId(),arrearsNoticeDTO.getId(),generateToken(arrearsNoticeDTO.getId())) );
        }
        fillTotal(arrearsNoticeDetail);
        return arrearsNoticeDetail;
    }

    private void fillTotal(ArrearsNoticeDetailVO2 arrearsNoticeDetail){
        List<ArrearsNoticeReceivableVO2> details = arrearsNoticeDetail.getDetails();
        arrearsNoticeDetail.setTotalReceivableAmount(details.stream().map(ArrearsNoticeReceivableVO2::getReceivableAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        arrearsNoticeDetail.setTotalIncomeAmount(details.stream().map(ArrearsNoticeReceivableVO2::getIncomeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        arrearsNoticeDetail.setTotalArrearsAmount(details.stream().map(ArrearsNoticeReceivableVO2::getArrearsAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        arrearsNoticeDetail.setTotalPenaltyArrearsAmount(details.stream().map(ArrearsNoticeReceivableVO2::getPenaltyArrearsAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
    }


    private ArrearsNoticeDetailVO convert(ArrearsNoticeDTO arrearsNoticeDTO, ArrearsNoticeDetailQuery query) throws ChargeBusinessException {
        Assert.isTrue(arrearsNoticeDTO!=null&&!CollectionUtils.isEmpty(arrearsNoticeDTO.getDetails()), "通知单不存在或者明细为空");
        List<Long> assetIds = arrearsNoticeDTO.getDetails().stream().map(ArrearsNoticeAssetReceivableDTO::getAssetId)
                .distinct().collect(Collectors.toList());
        Map<Long, AssetAdapter> assetMap = assetSupport.listAssetByIds(arrearsNoticeDTO.getCommunityId(), assetIds).stream()
                .collect(Collectors.toMap(AssetAdapter::getId, Function.identity(), (a, b) -> a));
        Assert.isTrue(assetMap.size() == assetIds.size(), "部分资产不存在");

        List<Long> receivableIds = arrearsNoticeDTO.getDetails().stream().map(ArrearsNoticeAssetReceivableDTO::getReceivableId)
                .distinct().collect(Collectors.toList());

        ChargeResponse<List<ReceivableBillDTO>> receivableResponse = receivableBillClient.queryList(ReceivableConditionDTO.builder()
                .communityId(arrearsNoticeDTO.getCommunityId()).ids(receivableIds).build());


        List<ReceivableBillDTO> receivableBillDTOS = AppInterfaceUtil.getResponseDataThrowException(receivableResponse);
        Assert.isTrue(receivableBillDTOS.size() == receivableIds.size(), "部分应收不存在");
        Map<Long, ReceivableBillDTO> receivableMap = receivableBillDTOS.stream().collect(Collectors.toMap(ReceivableBillDTO::getId, Function.identity(), (a, b) -> a));

        List<Long> standardIds = receivableBillDTOS.stream().map(ReceivableBillDTO::getBillingStandardId).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        Map<Long, StandardConfigDTO> standardConfigMap =new HashMap<>(standardIds.size());
        if(!CollectionUtils.isEmpty(standardIds)){
            standardConfigMap.putAll(chargeStandardSupport.getStandardConfigMap(arrearsNoticeDTO.getCommunityId(), standardIds));
        }
        List<AssetMonthReceivableVO> assetMonthReceivables = arrearsNoticeDTO.getDetails().stream().collect(Collectors.groupingBy(ArrearsNoticeAssetReceivableDTO::getAssetId)).entrySet().stream()
                .map(entry1 -> {
                    AssetMonthReceivableVO assetMonthReceivableVO = new AssetMonthReceivableVO();
                    AssetAdapter assetAdapter = assetMap.get(entry1.getKey());
                    assetMonthReceivableVO.setAssetId(assetAdapter.getId());
                    assetMonthReceivableVO.setAssetName(assetAdapter.getAssetName());
                    List<MonthReceivablesVO> monthReceivables = entry1.getValue().stream().collect(Collectors.groupingBy(ArrearsNoticeAssetReceivableDTO::getBelongYear)).entrySet().stream().map(entry2 -> {
                        MonthReceivablesVO monthReceivablesVO = new MonthReceivablesVO();
                        fillMonth(monthReceivablesVO, entry2.getKey());
                        List<ArrearsNoticeReceivableVO> noticeReceivables = entry2.getValue().stream().map(dto -> {
                            ReceivableBillDTO receivableBillDTO = receivableMap.get(dto.getReceivableId());
                            StandardConfigDTO standardConfigDTO = standardConfigMap.get(receivableBillDTO.getBillingStandardId());
                            ArrearsNoticeReceivableVO noticeReceivableVO = ArrearsNoticeConverter.INSTANCE.map(dto);
                            noticeReceivableVO.setItemName(receivableBillDTO.getItemName());
                            noticeReceivableVO.setTotalArrearsAmount(dto.getArrearsAmount().add(dto.getPenaltyArrearsAmount()));
                            noticeReceivableVO.fillExtra(standardConfigDTO==null?"":standardConfigDTO.getChargeType());
                            return noticeReceivableVO;
                        }).collect(Collectors.toList());
                        monthReceivablesVO.setAssetReceivables(noticeReceivables);
                        BigDecimal monthTotalArrearsAmount = noticeReceivables.stream().map(ArrearsNoticeReceivableVO::getTotalArrearsAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        monthReceivablesVO.setAssetReceivables(noticeReceivables);
                        monthReceivablesVO.setMonthTotalArrearsAmount(roundingMode(monthTotalArrearsAmount));
                        return monthReceivablesVO;
                    }).sorted((a, b) -> b.getMonthDate().compareTo(a.getMonthDate())).collect(Collectors.toList());
                    assetMonthReceivableVO.setMonthReceivables(monthReceivables);
                    return assetMonthReceivableVO;
                }).collect(Collectors.toList());
        BigDecimal totalArrearsAmount = assetMonthReceivables.stream().flatMap(a -> a.getMonthReceivables().stream().map(MonthReceivablesVO::getMonthTotalArrearsAmount))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        ArrearsNoticeDetailVO arrearsNoticeDetail = new ArrearsNoticeDetailVO();
        assetMonthReceivables.sort(Comparator.comparing(AssetMonthReceivableVO::getAssetName));
        arrearsNoticeDetail.setAssetMonthReceivables(assetMonthReceivables);
        arrearsNoticeDetail.setTotalAmount(roundingMode(totalArrearsAmount));
        if (Boolean.TRUE.equals(query.getWithPayUrl())) {
            arrearsNoticeDetail.setPayUrl(getPayUrl(arrearsNoticeDTO));
        }
        arrearsNoticeDetail.setCommunityName(receivableBillDTOS.get(0).getCommunityName());
        return arrearsNoticeDetail;
    }

    private String getPayUrl(ArrearsNoticeDTO arrearsNoticeDTO) {
        return String.format(transferH5PayUrl, arrearsNoticeDTO.getCommunityId(),arrearsNoticeDTO.getId(),generateToken(arrearsNoticeDTO.getId()));
    }

    private String generateToken(Long id){
        String token = RedisKeyBillEnum.ARREARS_NOTICE.key(id, System.currentTimeMillis());
        jedisManager.incrBy(tokenExpire,token,tokenTimes);
        return token;
    }

    public static void fillMonth(MonthReceivablesVO monthReceivablesVO,String belongYears){
        Date month = DateUtils.parse(belongYears, DateUtils.FORMAT_14);
        monthReceivablesVO.setMonthDate(month);
        String format = DateUtils.format(month, DateUtils.FORMAT_15);
        monthReceivablesVO.setMonth(format);
    }

    private static BigDecimal roundingMode(BigDecimal bigDecimal) {
        return bigDecimal.setScale(2, RoundingMode.HALF_UP);
    }


    private List<ArrearsNoticeVO> convert(List<ArrearsNoticeDTO> arrearsNotices, Long communityId) throws ChargeBusinessException {
        if (CollectionUtils.isEmpty(arrearsNotices)) {
            return Lists.newArrayList();
        }
        List<Long> assetIds = arrearsNotices.stream().filter(a -> !CollectionUtils.isEmpty(a.getDetails())).flatMap(a -> a.getDetails().stream()
                .map(ArrearsNoticeAssetReceivableDTO::getAssetId)).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, AssetAdapter> assetMap = assetSupport.listAssetByIds(communityId, assetIds).stream()
                .collect(Collectors.toMap(AssetAdapter::getId, Function.identity(), (a, b) -> a));
        return arrearsNotices.stream().filter(a -> !CollectionUtils.isEmpty(a.getDetails())).map(arrearsNoticeDTO -> {
            ArrearsNoticeVO arrearsNotice = ArrearsNoticeConverter.INSTANCE.map(arrearsNoticeDTO);
            String assetNames = arrearsNoticeDTO.getDetails().stream().filter(d -> assetMap.containsKey(d.getAssetId())).map(d ->
                    assetMap.get(d.getAssetId()).getAssetName()
            ).distinct().collect(Collectors.joining("、"));
            arrearsNotice.setAssetNames(assetNames);
            return arrearsNotice;
        }).collect(Collectors.toList());

    }

}