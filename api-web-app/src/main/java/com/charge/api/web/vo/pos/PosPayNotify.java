package com.charge.api.web.vo.pos;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/5/29
 */
@NoArgsConstructor
@Data
public class PosPayNotify {

    private String mchId;

    private String deviceInfo;

    private String resultCode;

    private String tradeType;

    private String totalFee;

    private String transactionId;

    private String outTradeNo;

    private String timeEnd;

    private String payType;

    private String cardNo;

    private String payAmt;

    private String batchbillno;

    private String systraceno;

    private String orderidScan;

    private String bankType;

    private String refernumber;

    private String couponFee;

    private String attach;

    private String openid;
}


