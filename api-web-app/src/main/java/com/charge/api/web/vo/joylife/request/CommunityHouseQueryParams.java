package com.charge.api.web.vo.joylife.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 小区+房间ID组合
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommunityHouseQueryParams implements Serializable {
	/**
	 * 小区主数据ID
	 */
	@ApiModelProperty(value = "小区主数据ID")
	private String communityMsId;
	@ApiModelProperty(value = "房屋主数据ID")
	/**
	 * 房屋主数据ID列表
	 */
	private List<String> houseMsIdList;

}
