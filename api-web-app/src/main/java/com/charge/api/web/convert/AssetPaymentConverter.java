package com.charge.api.web.convert;

import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.vo.pos.v3.CreateBillReq;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.income.AssetPayReceivableBillDTO;
import com.charge.bill.dto.income.AssetPaymentDTO;
import com.charge.common.util.DateMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/03/04
 */
@Mapper(uses = {DateMapper.class})
public interface AssetPaymentConverter {
    AssetPaymentConverter INSTANCE = Mappers.getMapper(AssetPaymentConverter.class);

    @Mappings({
            @Mapping(target = "communityId", source = "createOrderReq.communityId"),
            @Mapping(target = "communityName", source = "createOrderReq.communityName"),
            @Mapping(target = "totalPrice", source = "createOrderReq.totalAmount"),
            @Mapping(target = "actualPrice", source = "createOrderReq.totalAmount"),
            @Mapping(target = "paymentMethod", constant ="0"),
            @Mapping(target = "paymentChannel", constant = "CASH"),
            @Mapping(target = "paymentTerminal", constant = "POS"),
            @Mapping(target = "paymentType", constant = "0"),
            @Mapping(target = "balanceStatus", constant = "0"),
            @Mapping(target = "operationStatus", constant = "2"),
            @Mapping(target = "payMember", source = "createOrderReq.payMember")
    })
    AssetPaymentDTO buildAssetPaymentDTO(CreateBillReq createOrderReq, List<AssetAdapter> assetAdapterList);

    @Mapping(target = "receivableBillId", source = "id")
    AssetPayReceivableBillDTO toAssetPayReceivableBillDTO(ReceivableBillDTO receivableBillDTO);

    List<AssetPayReceivableBillDTO> toAssetPayReceivableBillDTOS(List<ReceivableBillDTO> receivableBillDTOS);

}
