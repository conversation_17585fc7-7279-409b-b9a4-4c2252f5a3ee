package com.charge.api.web.controller.ebusiness;

import com.charge.api.web.service.ebusiness.EBusinessOrderService;
import com.charge.api.web.vo.ebusiness.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.starter.web.annotation.Idempotent;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/13 16:09
 */
@Api(value = "电商订单相关接口")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class EBusinessOrderController {

    private final EBusinessOrderService eBusinessOrderService;

    /**
     * 订单首次收款接口
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/ebusiness/order/payment")
    @Idempotent
    public ChargeResponse<Void> paymentOrders(@Valid @RequestBody EBusinessPaymentOrderCondition condition) throws ChargeBusinessException {
        eBusinessOrderService.paymentOrders(condition);
        return new ChargeResponse();
    }

    /**
     * 订单退款接口
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/ebusiness/order/refund")
    @Idempotent
    public ChargeResponse<Void> refundOrders(@Valid @RequestBody EBusinessRefundOrderCondition condition) throws ChargeBusinessException {
        eBusinessOrderService.refundOrders(condition);
        return new ChargeResponse();
    }

    /**
     * 订单核销接口（到家服务）
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/ebusiness/order/write-off")
    @Idempotent
    public ChargeResponse<Void> writeOffOrders(@Valid @RequestBody EBusinessWriteOffOrderConditon condition) throws ChargeBusinessException {
        eBusinessOrderService.writeOffOrders(condition);
        return new ChargeResponse();
    }

    /**
     * 订单完结接口
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/ebusiness/order/finish")
    @Idempotent
    public ChargeResponse<Void> finishOrders(@Valid @RequestBody EBusinessFinishOrderCondition condition) throws ChargeBusinessException {
        eBusinessOrderService.finishOrders(condition);
        return new ChargeResponse();
    }

    /**
     * 装修订单同步接口
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/ebusiness/order/furnish/payment")
    @Idempotent
    public ChargeResponse<EBusinessPayBankInfo> paymentFurnishOrders(@Valid @RequestBody EBusinessFurnishPaymentOrderCondition condition) throws ChargeBusinessException {
        return new ChargeResponse(eBusinessOrderService.paymentFurnishOrders(condition));
    }

    /**
     * 装修订单分期验收接口
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/ebusiness/order/furnish/acceptance")
    @Idempotent
    public ChargeResponse<Void> acceptanceOrders(@Valid @RequestBody EBusinessAcceptanceOrderConditon condition) throws ChargeBusinessException {
        eBusinessOrderService.acceptanceOrders(condition);
        return new ChargeResponse();
    }

    /**
     * POS、银行卡转账订单退款接口(报账系统退款)
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @PostMapping("/ebusiness/order/ems/refund")
    @Idempotent
    public ChargeResponse<String> emsRefundOrders(@Valid @RequestBody EBusinessEmsRefundOrderCondition condition) throws ChargeBusinessException {
        return new ChargeResponse<>(eBusinessOrderService.emsRefundOrders(condition));
    }

}
