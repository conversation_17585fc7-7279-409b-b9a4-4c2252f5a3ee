package com.charge.api.web.vo.joylife.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/03/04 14:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChargeItemSimpleVO {

    /**
     * 收费项id
     */
    private Long itemId;
    /**
     * 收费项名称
     */
    private String itemName;
    /**
     * 收费项编码
     */
    private String itemCode;

}


