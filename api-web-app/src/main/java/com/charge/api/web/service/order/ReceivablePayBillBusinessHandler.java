package com.charge.api.web.service.order;

import com.charge.bill.client.BillNumGeneratorClient;
import com.charge.bill.client.WriteOffBillClient;
import com.charge.bill.dto.income.WriteOffBillDTO;
import com.charge.bill.enums.BusinessTypeEnum;
import com.charge.bill.enums.OrderNumPrefixEnum;
import com.charge.bill.enums.WriteOffTypeEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.maindata.pojo.dto.CustomerDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.charge.api.web.service.order.BaseCreateOrderService.getCustomer;

/**
 * 订单账单处理
 *
 * <AUTHOR>
 * @date 2023/2/27
 */
@Component
@Slf4j
public class ReceivablePayBillBusinessHandler implements BillBusinessHandler {

    @Resource
    private WriteOffBillClient writeOffBillClient;

    @Resource
    private BillNumGeneratorClient billNumGeneratorClient;


    @Override
    public BusinessTypeEnum type() {
        return BusinessTypeEnum.NORMAL_PAY;
    }

    @Override
    public void validate(BillBusiness billBusiness) {
        Assert.isTrue(billBusiness instanceof ReceivablePayBillBusiness, "billBusiness 类型是 应收支付 的类型错误 ");
        ReceivablePayBillBusiness receivablePayBillBusiness = (ReceivablePayBillBusiness) billBusiness;
        Assert.hasText(receivablePayBillBusiness.getBelongYears(), "receivablePayBillBusiness 所属年月不能为空");
        Assert.notNull(receivablePayBillBusiness.getChargeItemId(), "receivablePayBillBusiness 收费项id不能为空");
        Assert.notNull(receivablePayBillBusiness.getReceivableBillId(), "receivablePayBillBusiness receivableBillId不能为空");
        Assert.notNull(receivablePayBillBusiness.getChargeType(), "receivablePayBillBusiness chargeType不能为空");
        Assert.notNull(receivablePayBillBusiness.getChargeObject(), "receivablePayBillBusiness chargeObject不能为空");

        Assert.hasText(receivablePayBillBusiness.getName(), "receivablePayBillBusiness 收费项name不能为空");
        Assert.notNull(receivablePayBillBusiness.getMoney(), "receivablePayBillBusiness 金额不能为空");
    }

    @Override
    public void handle(CreateOrderContext context, AssetBillBusiness assetBillBusiness, List<BillBusiness> billBusinesses) {
        CustomerDTO customerDto = getCustomer(assetBillBusiness.getAsset());
        if (customerDto == null) {
            customerDto = new CustomerDTO();
            customerDto.setId(context.getPayMemberId());
            customerDto.setCustomerName(context.getPayMember());
        }
        CustomerDTO customerDto1 = customerDto;
        List<WriteOffBillDTO> writeOffBills= new ArrayList<>(billBusinesses.size());
        billBusinesses.forEach(billBusiness -> {
            ReceivablePayBillBusiness business = (ReceivablePayBillBusiness) billBusiness;
            String writeBillNum = billNumGeneratorClient.generatorBillNum(OrderNumPrefixEnum.WRITE_OFF_BILL.getCode()).getContent();
            business.setBillNum(writeBillNum);
            WriteOffBillDTO writeOff = WriteOffBillDTO.builder().assetId(assetBillBusiness.getAsset().getId()).userId(customerDto1.getId())
                    .assetTransactionId(assetBillBusiness.getAssetTransactionId()).receivableBillId(business.getReceivableBillId()).billNum(writeBillNum).itemId(business.getChargeItemId())
                    .itemName(business.getName()).memo(context.getMemo()).actualAmount(business.getMoney()).chargeType(business.getChargeType().getCode())
                    .writeOffType(WriteOffTypeEnum.PAY.getCode()).belongYears(business.getBelongYears()).isBalance(1)
                    .chargeObject(business.getChargeObject().getCode()).assetUseStatus(business.getAssetUseStatus()).communityId(context.getCommunity().getId()).build();
            writeOff.setCreateUser(context.getCollectorId());
            writeOff.setCreateTime(new Timestamp(context.getCreateTime().getTime()));
            writeOffBills.add(writeOff);
        });

        ChargeResponse<List<WriteOffBillDTO>> response = writeOffBillClient.batchCreate(writeOffBills);
        Assert.isTrue(response.isSuccess(), "创建核销单异常" + response.getMessage());
        Assert.notEmpty(response.getContent(), "创建核销单返回为空");
        Map<Long, List<WriteOffBillDTO>> receivableBillIdToWriteOffBillMap = response.getContent().stream().collect(Collectors.groupingBy(WriteOffBillDTO::getReceivableBillId));
        billBusinesses.forEach(billBusiness -> {
            ReceivablePayBillBusiness business = (ReceivablePayBillBusiness) billBusiness;
            List<WriteOffBillDTO> bills = receivableBillIdToWriteOffBillMap.get(business.getReceivableBillId());
            if (!CollectionUtils.isEmpty(bills)) {
                bills.stream().filter(bill -> bill.getChargeType().equals(business.getChargeType().getCode())&&bill.getBillNum().equals(business.getBillNum()))
                        .findFirst().ifPresent(bill -> business.setId(bill.getId()));
            }
        });
    }
}


