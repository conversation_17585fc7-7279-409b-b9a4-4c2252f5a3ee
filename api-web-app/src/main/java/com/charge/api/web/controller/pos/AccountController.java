package com.charge.api.web.controller.pos;

import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 账户控制器
 *
 * <AUTHOR>
 * @date 2023/7/18
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(value = "收费2.0 pos账户相关接口")
public class AccountController {

    public static final String FUNCTION_OFFLINE = "2.0系统已下线该功能，请知悉";

    @RequestMapping(value = "/getDefaultFinBankAccount", method = RequestMethod.GET)
    public ChargeResponse<String> getDefaultFinBankAccount(String communityUuid, String token) {
        return new ChargeResponse<>(Integer.parseInt(ErrorInfoEnum.E1011.getCode()), FUNCTION_OFFLINE);
    }

    @RequestMapping(value = "/selectBankAccountAll", method = RequestMethod.GET)
    public ChargeResponse<String> selectBankAccount(String communityUuid, @RequestParam(defaultValue = "1") int pageNum,
                                        @RequestParam(defaultValue = "10") int pageSize, @RequestParam(defaultValue = "0") String isAll, String token) {
        return new ChargeResponse<>(Integer.parseInt(ErrorInfoEnum.E1011.getCode()), FUNCTION_OFFLINE);
    }


}


