package com.charge.api.web.controller.pos;

import com.charge.api.web.service.pay.LakalaService;
import com.charge.api.web.vo.pos.PosPayNotify;
import com.charge.api.web.vo.pos.PosResponse;
import com.charge.common.constant.MetricConstants;
import com.charge.common.dto.ChargeResponse;
import com.charge.core.enums.StatusEnum;
import com.charge.pos.dto.AccountInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Author: yjw
 * Date: 2023/3/1 10:57
 */

@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(value = "pos登录相关接口")
public class PosLoginController {

    private final LakalaService lakalaService;

    @ApiOperation(value = "登录接口", notes = "登录接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "username", value = "用户名", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "password", value = "密码", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "url", value = "地址", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "systemType", value = "系统类型(0表示h5,1表示安卓,2表示ios)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "timeStamp", value = "时间戳(v4.0.0及以后版本必须传入) ", required = false, dataType = "String")
    })
    @RequestMapping(value = "/login", method = {RequestMethod.POST})
    public ChargeResponse<AccountInfo> login(String username, String password, String url, String systemType, String timeStamp) throws Exception {

        return lakalaService.login(username, password, url, systemType, timeStamp);
    }

    @ApiOperation(value = "通知服务系统消息推送接口", notes = "通知服务系统消息推送接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "mch_id", value = "商户号(支付分配的商户号)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "device_info", value = "设备号(支付分配的终端设备号)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "nonce_str", value = "随机字符串(随机字符串，不长于32位)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "sign", value = "签名(密钥及签名验证准备：\n" +
                    "准备好拉卡拉公钥、待验证签名的字符串、从拉卡拉返回时得到的参数sign的值。\n" +
                    "签名验证函数：\n" +
                    "使用各自语言对应的SHA1WithRSA签名验证函数，传入待验签字段、拉卡拉公钥、参数sign对应的值（该参数为拉卡拉返回）进行验签，根据返回结果判定是否验签通过)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "result_code", value = "业务结果(SUCCESS/FAIL)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "err_code", value = "错误代码(见错误码说明)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "err_code_des", value = "错误代码描述(见错误码说明)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "openid", value = "用户标识(用户在商户appid下的唯一标识)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "trade_type", value = "交易类型(见交易类型说明)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "total_fee", value = "总金额(以分为单位)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "transaction_id", value = "商户支付订单号\n" +
                    "(流水号/参考号)\n(拉卡拉唯一交易标识\n" +
                    "POSP交易系统参考号)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "out_trade_no", value = "商户订单号(商户系统的订单号，与请求一致)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "attach", value = "商家数据包(商家数据包，原样返回)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "time_end", value = "支付完成时间(支付完成时间，格式为yyyyMMddHHmmss，如2009年12月25日9点10分10秒表示为20091225091010)", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pay_type", value = "支付方式(卡类型标识)(00 借记卡\n" +
                    "01 贷记卡\n" +
                    "91 微信\n" +
                    "92 支付宝\n" +
                    "93 百度钱包\n" +
                    "96 拉卡拉钱包\n" +
                    "04 未知卡类型)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "card_no", value = "交易卡号(前六后四，中间用星号替换\n" +
                    "扫码即为二维码(以收单数据库值为准))", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pay_amt", value = "实际支付金额(商户系统的订单号，与请求一致)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "batchbillno", value = "批次号(与请求传过来的报文一致)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "systraceno", value = "凭证号(与请求传过来的报文一致)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "orderid_scan", value = "扫码订单号(银行卡支付时该字段请忽略，扫码支付时该字段为扫码订单号)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "refernumber", value = "系统参考号", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "bank_type", value = "付款银行(银行类型，采用字符串类型的银行标识)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "fee_type", value = "货币种类(货币类型，符合ISO4217标准的三位字母代码，默认人民币：CNY，其他值列表详见货币类型)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "cash_fee", value = "现金支付金额(现金支付金额订单现金支付金额，详见支付金额)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "cash_fee_type", value = "现金支付货币类型(货币类型，符合ISO4217标准的三位字母代码，默认人民币：CNY，其他值列表详见货币类型)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "coupon_fee", value = "代金券或立减优惠金额(代金券或立减优惠金额<=订单总金额，订单总金额-代金券或立减优惠金额=现金支付金额，详见支付金额)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "coupon_count", value = "代金券或立减优惠使用数量(代金券或立减优惠使用数量)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "coupon_id_$n", value = "代金券或立减优惠ID(代金券或立减优惠ID,$n为下标，从0开始编号)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "coupon_fee_$n", value = "单个代金券或立减优惠支付金额(单个代金券或立减优惠支付金额,$n为下标，从0开始编号)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "is_subscribe", value = "是否关注公众账号(用户是否关注公众账号，Y-关注，N-未关注，仅在公众账号类型支付有效)", required = false, dataType = "String"),
    })
    @RequestMapping(value = "/payNotify", method = {RequestMethod.POST})
    public PosResponse payNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String data = request.getParameter("data");
        log.info("payNotify param {}",request.getParameterMap());
        PosPayNotify posPayNotify = com.alibaba.fastjson.JSONObject.parseObject(data, PosPayNotify.class);
        PosResponse posResponse = lakalaService.payNotify(posPayNotify);
        if("SUCCESS".equals(posResponse.getReturn_code())){
            response.addHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.SUCCESS.getCode());
        }else {
            log.warn("payNotify fail PosPayNotify {}",posPayNotify);
            response.addHeader(MetricConstants.TAG_BIZ_CODE, StatusEnum.FAIL.getCode());
        }
        return posResponse;

    }
}
