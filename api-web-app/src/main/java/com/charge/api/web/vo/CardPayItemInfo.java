package com.charge.api.web.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Classname CardPayItemInfo
 * @Description 月卡缴费明细
 * @Date 2022/5/12 15:44
 */
@Data
public class CardPayItemInfo implements Serializable {
    /**
     * id
     */
    private Long itemId;
    /**
     * 名称
     */
    private String itemName;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 日金额
     */
    private BigDecimal dayPrice;
}
