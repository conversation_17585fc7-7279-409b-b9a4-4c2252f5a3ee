package com.charge.api.web.controller.pos;

import com.charge.api.web.service.pos.BillV2Service;
import com.charge.api.web.vo.pos.v3.CreateBillReq;
import com.charge.api.web.vo.pos.v3.CreateBillResp;
import com.charge.api.web.vo.pos.v3.PayResultVO;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.order.client.OrderInstallmentClient;
import com.charge.starter.web.annotation.Idempotent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * 拉卡拉新接口
 * @Description
 * @Author: yjw
 * @Date: 2023/10/16 17:25
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping(value = "pos")
public class BillController {

    private final BillV2Service billV2Service;

    private OrderInstallmentClient orderInstallmentClient;

    /**
     * 下单
     * @param createOrderReq 下单请求
     * @return ChargeResponse<CreateBillResp> 下单结果
     *
     */
    @PostMapping(value = "createBill")
    @Idempotent(tips = "正在处理您的请求，请勿重复点击")
    public ChargeResponse<CreateBillResp> createBill(@RequestBody @Validated CreateBillReq createOrderReq,@RequestHeader String device) throws ChargeBusinessException {
        createOrderReq.setDevice(device);
        if (createOrderReq.getPaymentTerminal()==null){
            createOrderReq.setPaymentTerminal(PaymentTerminalEnum.POS);
        }
        return new ChargeResponse<>(billV2Service.createBill(createOrderReq));
    }

    /**
     * 支付结果统一查询接口
     * @param communityId 项目id
     * @param orderNum 订单号
     * @return 支付结果
     */
    @GetMapping(value = "payResult")
    public ChargeResponse<PayResultVO> getPayResult(@RequestParam @NotBlank(message = "订单号不能为空") String orderNum, @RequestHeader Long communityId) throws ChargeBusinessException {
        return new ChargeResponse<>(billV2Service.getPayResult(communityId,orderNum));
    }

}
