package com.charge.api.web.service.pos;

import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.DepositDetailVO;
import com.charge.api.web.vo.pos.DepositVO;
import com.charge.common.exception.ChargeBusinessException;

import java.util.List;

public interface DepositService {

    ChargePageResponse<List<DepositVO>> getDeposits(String communityUuid, Long houseUuid, String depositStatus, Integer pageNum, Integer pageSize) throws ChargeBusinessException;

    ChargePageResponse<DepositDetailVO> getDepositDetail(String depositId, String communityUuid, String houseId, String pageNum, String pageSize) throws ChargeBusinessException;

    ChargePageResponse<List<DepositVO>> getDepositSearch(String communityUuid, String houseId, String content, String depositStatus, Integer pageNum, Integer pageSize) throws ChargeBusinessException;
}
