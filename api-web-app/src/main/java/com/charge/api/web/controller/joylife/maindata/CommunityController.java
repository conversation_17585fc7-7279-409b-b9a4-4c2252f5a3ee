package com.charge.api.web.controller.joylife.maindata;

import com.charge.api.web.service.maindata.CommunityService;
import com.charge.api.web.vo.joylife.request.ListCommunityReq;
import com.charge.api.web.vo.joylife.response.CommunityVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/8/27 9:08
 */
@Api(value = "项目相关接口")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class CommunityController {

    private final CommunityService communityService;

    /**
     * 根据 LDAP 账号分页查询虚拟项目列表
     * @param reqCondition
     * @return
     * @throws ChargeBusinessException
     */
    @ApiOperation(value = "根据 LDAP 账号分页查询虚拟项目列表")
    @PostMapping(value = "/community/by-account")
    public ChargeResponse<PagingDTO<CommunityVO>> listCommunity(@Valid @RequestBody ListCommunityReq reqCondition) throws ChargeBusinessException {
        return new ChargeResponse<>(communityService.listCommunityByUser(reqCondition));
    }
}
