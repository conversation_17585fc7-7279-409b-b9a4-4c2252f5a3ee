package com.charge.api.web.convert;

import com.charge.api.web.vo.order.*;
import com.charge.api.web.vo.pos.EBusinessBaseOrderVO1;
import com.charge.api.web.vo.pos.EBusinessMasterOrderVO;
import com.charge.api.web.vo.pos.OrderInstallmentVO;
import com.charge.bill.dto.certificate.CertificateEBusiessOrderDTO;
import com.charge.common.util.DateMapper;
import com.charge.order.dto.OrderDTO;
import com.charge.order.dto.ebuiness.*;
import com.charge.order.dto.rent.ExtendBaseOrderDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 缴费通知单转换器
 * <AUTHOR>
 * @description
 * @date 2023/02/28
 */
@Mapper(uses = {DateMapper.class,OrderEnumMapper.class })
public interface EBusinessOrderConverter {


    @Mapping(target = "goods.",source = "subOrderAdd")
    SubOrderAddDTO map(SubOrderAdd subOrderAdd);

    @Mapping(target = "subOrderNo",source = "extendOrderId")
    @Mapping(target = "originalAmount",source = "amount")
    @Mapping(target = "paidAmount",source = "paymentAmount")
    SubOrderVO map(ExtendBaseOrderDTO subOrderAdd);


    EBusinessSubOrderAdjustFrom map(SubOrderAdjustFrom subOrderAdjustFrom);

    EBusinessSubOrderAdjustCancelCmd map(SubOrderAdjustCancelCmd subOrderAdjustFrom);


    @Mapping(target = "assetCustomer.",source = "subOrderAdjustTo")
    EBusinessSubOrderAdjustTo map(SubOrderAdjustTo subOrderAdjustTo);

    @Mapping(target = "goods.",source = "subOrderAdjustToItem")
    @Mapping(target = "originalAmount",source = "amount")
    @Mapping(target = "payAmount",source = "amount")
    SubOrderAddDTO map(SubOrderAdjustToItem subOrderAdjustToItem);


    List<SubOrderAddDTO> map(List<SubOrderAdd> subOrderAdds);


    EBusinessOrderConverter INSTANCE = Mappers.getMapper(EBusinessOrderConverter.class);

    OrderInstallmentVO map(OrderInstallmentDTO1 installmentDTO1);

    EBusinessBaseOrderVO1 mapV1(EBusinessBaseOrderDTO baseOrderDTO);

    OrderInstallmentVO map(OrderInstallmentDTO installmentDTO);

    @Mapping(target = "assetCustomer.",source = "orderAddCmd")
    EBusinessOrderAddCmd map(OrderAddCmd orderAddCmd);

    @Mapping(target = "assetCustomer.",source = "orderAddCmd")
    EBusinessSubOrderAdjustAddCmd map(SubOrderAdjustAddCmd orderAddCmd);

    EBusinessMasterOrderVO map(EBusinessMasterOrderDTO masterOrderDTO);

    List<CertificateEBusiessOrderDTO> convertCertificateOrderDTO(List<OrderDTO> orderDTOS);

    List<CertificateEBusiessOrderDTO> convertEBusinessMasterOrderDTO(List<EBusinessMasterOrderDTO> eBusinessMasterOrderDTOS);
}
