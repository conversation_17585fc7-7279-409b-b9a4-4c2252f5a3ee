package com.charge.api.web.vo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * 订单子单查询条件
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SubOrderQuery extends BaseOrderReq {


    /**
     * 子订单号
     */
    @NotBlank(message ="子订单号不能为空")
    private String subOrderNo;


}