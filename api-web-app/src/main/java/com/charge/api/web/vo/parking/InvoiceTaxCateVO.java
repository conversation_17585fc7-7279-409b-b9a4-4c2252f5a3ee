package com.charge.api.web.vo.parking;

import lombok.Data;

import java.io.Serializable;

/**
 */
@Data
public class InvoiceTaxCateVO implements Serializable {
    private static final long serialVersionUID = -12485949255347306L;

    /**
     * 税收分类编码
     */
    private String code;

    /**
     * 货物劳物名称
     */
    private String goodsName;
    /**
     *
     * 商品服务分类
     */
    private String goodsServiceCate;
    /**
     *
     * 税率
     */
    private String taxRate;

}
