package com.charge.api.web.vo.joylife;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2017/5/8.
 *
 * 备注：1、根据状态值statusId，查询订单项返回的实体类型
 */
public class OrderItemBS implements Serializable{

    private static final long serialVersionUID = -8265769918052903036L;

    private String itemId;

    private String ownerId;

    private Integer dr;

    private String goodsId;

    private String goodsName;

    private String goodsType;

    private String goodsPic;

    private String memo;

    private String orderId;

    private Double originPrice;

    private Integer amount;

    private String statusId;

    private String statusName;

    private Date create_ts;

    private Date update_ts;

    private String cropId;

    private String appId;

    private BigDecimal receivableAmount;

    private BigDecimal receivableMoney;

    private BigDecimal receivableTax;

    private BigDecimal actualAmount;

    private BigDecimal arrearsAmount;

    private String belongYears;

    private Date receivableTime;

    private BigDecimal penaltyAmount;

    private BigDecimal penaltyMoney;

    private BigDecimal penaltyTax;

    private BigDecimal penaltyActualAmount;

    private BigDecimal penaltyArrearsAmount;

    private Date chargeStartTime;

    private Date chargeEndTime;

    private String itemType;

    private String changeYears;

    private String penaltyStatus;

    private BigDecimal penaltyReceivable;

    private BigDecimal penaltyDerate;

    private String penaltyConfig;

    private Date penaltyUpdateTs;

    private String costSource;

    private String enterStatus;

    private Date effectTime;

    private BigDecimal discountMoney;

    private String discountTime;

    private BigDecimal reductionsMoney;

    private String originalOrderId;

    private String costGenerationWay;

    private String chargeObj;

    private BigDecimal carryForwardMoney;

    public String getCostSource() {
        return costSource;
    }

    public void setCostSource(String costSource) {
        this.costSource = costSource;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getChangeYears() {
        return changeYears;
    }

    public void setChangeYears(String changeYears) {
        this.changeYears = changeYears;
    }

    public String getCropId() {
        return cropId;
    }

    public void setCropId(String cropId) {
        this.cropId = cropId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Date getCreate_ts() {
        return create_ts;
    }

    public void setCreate_ts(Date create_ts) {
        this.create_ts = create_ts;
    }

    public Date getUpdate_ts() {
        return update_ts;
    }

    public void setUpdate_ts(Date update_ts) {
        this.update_ts = update_ts;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }



    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getDr() {
        return dr;
    }

    public void setDr(Integer dr) {
        this.dr = dr;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }

    public String getGoodsPic() {
        return goodsPic;
    }

    public void setGoodsPic(String goodsPic) {
        this.goodsPic = goodsPic;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Double getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Double originPrice) {
        this.originPrice = originPrice;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }


    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    public BigDecimal getReceivableMoney() {
        return receivableMoney;
    }

    public void setReceivableMoney(BigDecimal receivableMoney) {
        this.receivableMoney = receivableMoney;
    }

    public BigDecimal getReceivableTax() {
        return receivableTax;
    }

    public void setReceivableTax(BigDecimal receivableTax) {
        this.receivableTax = receivableTax;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public BigDecimal getArrearsAmount() {
        return arrearsAmount;
    }

    public void setArrearsAmount(BigDecimal arrearsAmount) {
        this.arrearsAmount = arrearsAmount;
    }

    public String getBelongYears() {
        return belongYears;
    }

    public void setBelongYears(String belongYears) {
        this.belongYears = belongYears;
    }

    public Date getReceivableTime() {
        return receivableTime;
    }

    public void setReceivableTime(Date receivableTime) {
        this.receivableTime = receivableTime;
    }

    public BigDecimal getPenaltyAmount() {
        return penaltyAmount;
    }

    public void setPenaltyAmount(BigDecimal penaltyAmount) {
        this.penaltyAmount = penaltyAmount;
    }

    public BigDecimal getPenaltyMoney() {
        return penaltyMoney;
    }

    public void setPenaltyMoney(BigDecimal penaltyMoney) {
        this.penaltyMoney = penaltyMoney;
    }

    public BigDecimal getPenaltyTax() {
        return penaltyTax;
    }

    public void setPenaltyTax(BigDecimal penaltyTax) {
        this.penaltyTax = penaltyTax;
    }

    public BigDecimal getPenaltyActualAmount() {
        return penaltyActualAmount;
    }

    public void setPenaltyActualAmount(BigDecimal penaltyActualAmount) {
        this.penaltyActualAmount = penaltyActualAmount;
    }

    public BigDecimal getPenaltyArrearsAmount() {
        return penaltyArrearsAmount;
    }

    public void setPenaltyArrearsAmount(BigDecimal penaltyArrearsAmount) {
        this.penaltyArrearsAmount = penaltyArrearsAmount;
    }

    public Date getChargeStartTime() {
        return chargeStartTime;
    }

    public void setChargeStartTime(Date chargeStartTime) {
        this.chargeStartTime = chargeStartTime;
    }

    public Date getChargeEndTime() {
        return chargeEndTime;
    }

    public void setChargeEndTime(Date chargeEndTime) {
        this.chargeEndTime = chargeEndTime;
    }

    public String getStatusId() {
        return statusId;
    }

    public void setStatusId(String statusId) {
        this.statusId = statusId;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public OrderItemBS() {
    }

    public OrderItemBS(String itemId, String ownerId, Integer dr, String goodsId, String goodsName, String goodsType, String goodsPic, String memo, String orderId, Double originPrice, Integer amount, String statusId, String statusName, Date create_ts, Date update_ts, String cropId, String appId, BigDecimal receivableAmount, BigDecimal receivableMoney, BigDecimal receivableTax, BigDecimal actualAmount, BigDecimal arrearsAmount, String belongYears, Date receivableTime, BigDecimal penaltyAmount, BigDecimal penaltyMoney, BigDecimal penaltyTax, BigDecimal penaltyActualAmount, BigDecimal penaltyArrearsAmount, Date chargeStartTime, Date chargeEndTime, String itemType, String changeYears) {
        this.itemId = itemId;
        this.ownerId = ownerId;
        this.dr = dr;
        this.goodsId = goodsId;
        this.goodsName = goodsName;
        this.goodsType = goodsType;
        this.goodsPic = goodsPic;
        this.memo = memo;
        this.orderId = orderId;
        this.originPrice = originPrice;
        this.amount = amount;
        this.statusId = statusId;
        this.statusName = statusName;
        this.create_ts = create_ts;
        this.update_ts = update_ts;
        this.cropId = cropId;
        this.appId = appId;
        this.receivableAmount = receivableAmount;
        this.receivableMoney = receivableMoney;
        this.receivableTax = receivableTax;
        this.actualAmount = actualAmount;
        this.arrearsAmount = arrearsAmount;
        this.belongYears = belongYears;
        this.receivableTime = receivableTime;
        this.penaltyAmount = penaltyAmount;
        this.penaltyMoney = penaltyMoney;
        this.penaltyTax = penaltyTax;
        this.penaltyActualAmount = penaltyActualAmount;
        this.penaltyArrearsAmount = penaltyArrearsAmount;
        this.chargeStartTime = chargeStartTime;
        this.chargeEndTime = chargeEndTime;
        this.itemType = itemType;
        this.changeYears = changeYears;
    }

    public String getPenaltyStatus() {
        return penaltyStatus;
    }

    public void setPenaltyStatus(String penaltyStatus) {
        this.penaltyStatus = penaltyStatus;
    }

    public BigDecimal getPenaltyReceivable() {
        return penaltyReceivable;
    }

    public void setPenaltyReceivable(BigDecimal penaltyReceivable) {
        this.penaltyReceivable = penaltyReceivable;
    }

    public BigDecimal getPenaltyDerate() {
        return penaltyDerate;
    }

    public void setPenaltyDerate(BigDecimal penaltyDerate) {
        this.penaltyDerate = penaltyDerate;
    }

    public String getPenaltyConfig() {
        return penaltyConfig;
    }

    public void setPenaltyConfig(String penaltyConfig) {
        this.penaltyConfig = penaltyConfig;
    }

    public Date getPenaltyUpdateTs() {
        return penaltyUpdateTs;
    }

    public void setPenaltyUpdateTs(Date penaltyUpdateTs) {
        this.penaltyUpdateTs = penaltyUpdateTs;
    }

    public String getEnterStatus() {
        return enterStatus;
    }

    public void setEnterStatus(String enterStatus) {
        this.enterStatus = enterStatus;
    }

    public Date getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(Date effectTime) {
        this.effectTime = effectTime;
    }

    public BigDecimal getDiscountMoney() {
        return discountMoney;
    }

    public void setDiscountMoney(BigDecimal discountMoney) {
        this.discountMoney = discountMoney;
    }

    public String getDiscountTime() {
        return discountTime;
    }

    public void setDiscountTime(String discountTime) {
        this.discountTime = discountTime;
    }

    public BigDecimal getReductionsMoney() {
        return reductionsMoney;
    }

    public void setReductionsMoney(BigDecimal reductionsMoney) {
        this.reductionsMoney = reductionsMoney;
    }

    public String getOriginalOrderId() {
        return originalOrderId;
    }

    public void setOriginalOrderId(String originalOrderId) {
        this.originalOrderId = originalOrderId;
    }

    public String getCostGenerationWay() {
        return costGenerationWay;
    }

    public void setCostGenerationWay(String costGenerationWay) {
        this.costGenerationWay = costGenerationWay;
    }

    public String getChargeObj() {
        return chargeObj;
    }

    public void setChargeObj(String chargeObj) {
        this.chargeObj = chargeObj;
    }

    public BigDecimal getCarryForwardMoney() {
        return carryForwardMoney;
    }

    public void setCarryForwardMoney(BigDecimal carryForwardMoney) {
        this.carryForwardMoney = carryForwardMoney;
    }
}
