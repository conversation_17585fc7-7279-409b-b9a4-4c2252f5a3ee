package com.charge.api.web.controller.joylife.work;

import com.charge.api.web.service.joylife.WorkOrderService;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.joylife.dto.PayInfoDTO;
import com.charge.order.dto.WorkOrderPayCmd;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 从order 中迁移
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 9:49
 */
@RestController
@RequestMapping(value = "/app/workOrderPay")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class WorkOrderController {


    private final WorkOrderService workOrderService;

    /**
     * 有偿工单支付订单
     * @param workOrderPayCmd
     * @return
     */
    @PostMapping(value = "/orderPay")
    public ChargeResponse<PayInfoDTO> orderPay(@Validated @RequestBody WorkOrderPayCmd workOrderPayCmd, @RequestHeader(value="X-CHARGE-MSID") String msid) throws ChargeBusinessException {

        return workOrderService.orderPay(workOrderPayCmd,msid);

    }

}
