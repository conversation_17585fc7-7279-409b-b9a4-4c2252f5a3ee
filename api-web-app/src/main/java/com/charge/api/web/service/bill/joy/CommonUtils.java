package com.charge.api.web.service.bill.joy;

import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.controller.joylife.bill.request.CreateOrderRequest;
import com.charge.api.web.controller.joylife.bill.request.PayOrAdjustItemVO;
import com.charge.api.web.vo.joylife.AssetBillItem;
import com.charge.api.web.vo.joylife.AssetBillReq;
import com.charge.api.web.vo.joylife.CreateBatchAssetBillReq;
import com.charge.bill.dto.domain.AssetPayBaseDTO;
import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentMethodEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class CommonUtils {

    public static void fillPaymentMethodInfo(String paymentMethod, final AssetPayBaseDTO assetOrderBaseDTO) {
        assetOrderBaseDTO.setPaymentMethod(Objects.equals("3",paymentMethod) ? PaymentMethodEnum.ALIPAY.getPaymentCode() : PaymentMethodEnum.WECHAT.getPaymentCode());
        assetOrderBaseDTO.setPaymentChannel(Objects.equals("3",paymentMethod) ? PaymentChannelEnum.ALI_PAY.getPaymentChannel() : PaymentChannelEnum.WECHAT_PAY.getPaymentChannel());
    }

    public static String buildGoodsName(CreateOrderRequest createOrderRequest) {
        List<PayOrAdjustItemVO> list = getAllBillItems(createOrderRequest);
        return list.size() == 1 ? PayRelatedConstants.GOODSNAME_FOR_MERGE : list.get(0).getItemName();
    }

    public static List<PayOrAdjustItemVO> getAllBillItems(CreateOrderRequest createOrderRequest) {
        List<PayOrAdjustItemVO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(createOrderRequest.getOrderItems())) {
            list.addAll(createOrderRequest.getOrderItems());
        }
        if (CollectionUtils.isNotEmpty(createOrderRequest.getDepositItems())) {
            list.addAll(createOrderRequest.getDepositItems());
        }
        return list;
    }

    public static List<AssetBillItem> getAllBillItems(CreateBatchAssetBillReq createBatchAssetBillReq) {
        List<AssetBillItem> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(createBatchAssetBillReq.getAssetBillReqs())) {
            List<AssetBillReq> assetBillReqs = createBatchAssetBillReq.getAssetBillReqs();
            assetBillReqs.forEach(e -> {
                if(CollectionUtils.isNotEmpty(e.getReceivables())){
                    list.addAll(e.getReceivables());
                }
                if(CollectionUtils.isNotEmpty(e.getPrestores())){
                    list.addAll(e.getPrestores());
                }
            });
        }
        return list;
    }

}
