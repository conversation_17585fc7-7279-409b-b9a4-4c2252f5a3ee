package com.charge.api.web.vo.joylife.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量朝昔计算预存请求
 *
 * @Description
 * @Author: yjw
 * @Date: 2024/6/11 10:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class BatchZhaoXiAssetReq extends ZhaoXiCommunityReq {

    /**
     * 资产信息列表
     */
    @NotEmpty(message = "资产信息列表不能为空")
    private List<ZhaoXiAssertRequest> assetList;

}
