package com.charge.api.web.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 子订单vo
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SubOrderVO extends BaseOrderVO {

    private static final long serialVersionUID = 8175996290471580937L;

    /**
     * 子订单号
     */
    private String subOrderNo;

    /**
     * 资产id
     */
    private Long assetId;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 收费项id
     */
    private Long itemId;

    /**
     * 收费项名称
     */
    private String itemName;
    /**
     * 订单初始金额
     */
    private BigDecimal originalAmount;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 订单支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 开票金额
     */
    private BigDecimal invoicedAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 支付模式：1-分期支付，2-全额支付
     */
    private Integer payMode;

    /**
     * 支付完成时间
     */
    private Date paidAt;

    /**
     * 货物名称
     */
    private String goodsName;


    /**
     * 支付状态：0-待支付，1-已支付，2-部分支付，3-全额退款，4-部分退款
     */
    private Integer payStatus;

    /**
     * 订单下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;

}
