package com.charge.api.web.dto.invoice;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Wen
 * @date 2023/9/15 20:12
 */
@Data
public class InvoiceApplyResultDTO implements Serializable {
    private static final long serialVersionUID = 780811493231122026L;
    
    /** 发票ID */
    private Long invoiceApplyId;

    /** 发票ID */
    private List<Long> invoiceApplyIds;

    private String buyerName;

    private String message;

    private Integer code;
}
