package com.charge.api.web.support;

import com.charge.bill.client.OrderBillDetailClient;
import com.charge.bill.dto.income.OrderBillDetailDTO;
import com.charge.bill.dto.income.OrderBillDetailQueryDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 订单工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderSupport {
    private final OrderBillDetailClient orderBillDetailClient;

    public List<OrderBillDetailDTO> listByTransactionId(Long communityId , List<Long> transactionIds) throws ChargeBusinessException {
        ChargeResponse<PagingDTO<OrderBillDetailDTO>> page = orderBillDetailClient.page(OrderBillDetailQueryDTO.builder()
                .assetTransactionIds(transactionIds)
                .communityId(communityId)
                .pageNum(1)
                .pageSize(500).build());
        return Optional.of(AppInterfaceUtil.getResponseDataThrowException(page).getList()).orElse(Lists.newArrayList());
    }

}


