package com.charge.api.web.util;


import com.charge.api.web.dto.parking.EcsbResponse;
import com.charge.api.web.dto.parking.ResultResponse;
import com.charge.api.web.enums.EcsbEnums;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

/**
 * <AUTHOR>
 * @Classname EcsbResponseUtils
 * @Date 2021/9/10 10:53 上午
 */
public class EcsbResponseUtils {

    public static ResultResponse generatorResponseSuccess(String code, Object obj){
        ResultResponse resultResponse = new ResultResponse();
        EcsbResponse<Object> ecsbResponse = new EcsbResponse();
        if (StringUtils.isBlank(code)){
            ecsbResponse.setRETURN_CODE(EcsbEnums.SUCCESS.getCode());
        }else {
            ecsbResponse.setRETURN_CODE(code);
        }
        ecsbResponse.setRETURN_STAMP(DateTime.now().toString("yyyy-MM-dd HH:mm:ss:sss"));
        ecsbResponse.setRETURN_DATA(obj);
        ecsbResponse.setRETURN_DESC("返回成功");
        resultResponse.setRESPONSE(ecsbResponse);
        return resultResponse;
    }

    public static ResultResponse generatorResponseError(String code,Object obj){
        ResultResponse resultResponse = new ResultResponse();
        EcsbResponse<Object> ecsbResponse = new EcsbResponse();
        if (StringUtils.isBlank(code)){
            ecsbResponse.setRETURN_CODE(EcsbEnums.FAILED.getCode());
        }else {
            ecsbResponse.setRETURN_CODE(code);
        }
        ecsbResponse.setRETURN_STAMP(DateTime.now().toString("yyyy-MM-dd HH:mm:ss:sss"));
        ecsbResponse.setRETURN_DATA(obj);
        ecsbResponse.setRETURN_DESC("返回失败");
        resultResponse.setRESPONSE(ecsbResponse);
        return resultResponse;
    }

}
