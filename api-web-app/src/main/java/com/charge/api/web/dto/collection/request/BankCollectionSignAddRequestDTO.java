package com.charge.api.web.dto.collection.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BankCollectionSignAddRequestDTO implements Serializable {
    private static final long serialVersionUID = -8360096699188199161L;

    @NotBlank(message = "请输入项目ms id")
    private String communityMsId;
    @NotNull(message = "请输入要签约的资产ms id列表")
    private List<String> assetMsIdList;
    /**
     * 项目托收配置id
     */
    @NotNull(message = "请输入配置id")
    private Long configId;

    /**
     * 开户人
     */
    @NotBlank(message = "请输入卡号")
    private String bankAccount;

    @NotBlank(message = "请输入开通人")
    private String userMsId;

}
