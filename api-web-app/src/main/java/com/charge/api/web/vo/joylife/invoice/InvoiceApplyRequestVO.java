package com.charge.api.web.vo.joylife.invoice;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import lombok.Data;

@Data
public class InvoiceApplyRequestVO implements Serializable {
    
    private static final long serialVersionUID = 54327552341170385L;

    /**项目ID-朝夕侧 */
    private String communityMsId;

    /**资产ID-朝夕侧 */
    private String assetMsId;
    
    /**资产ID-朝夕侧 */
    private String customerMsId;

    /**业务单据类型(RECEIVABLE-应收单,INCOME-实收单,PREDEPOSIT-预收单,ORDER-订单 */
    @NotBlank(message = "业务单据类型不能为空")
    private String businessBillType;

    /**税控设备id */
    @NotNull(message = "税控设备id不能为空")
    private Long deviceId;

    /**销方id */
    @NotNull(message = "销方id不能为空")
    private Long sellerId;

    /**发票类型 */
    @NotBlank(message = "发票类型不能为空")
    private String invoiceType;

    /**发票抬头类型，1是个人，2是企业 */
    @NotNull(message = "发票抬头类型不能为空")
    private Integer objectType;

    /**发票抬头 */
    @NotBlank(message = "发票抬头不能为空")
    @Length(max=255, message = "发票抬头最大长度{max}")
    private String buyerName;

    /**企业纳税识别号 */
    @Length(max=100, message = "纳税识别号最大长度{max}")
    private String sellerTaxIdNo;

    /**地址 */
    @Length(max = 200, message = "地址长度最大{max}")
    private String buyerAddress;

    /**电话 */
    @Length(max = 20, message = "电话长度最大{max}")
    private String buyerTel;

    /**开户行 */
    @Length(max = 100, message = "开户行长度最大{max}")
    private String buyerBankName;

    /**开户行账号 */
    @Length(max = 100, message = "开户行账号长度最大{max}")
    private String buyerBankAccount;

    /**备注 */
    @Length(max = 255, message = "备注长度最大{max}")
    private String memo;

    /**收件人邮箱 */
    @Email(message = "email格式不正确")
    @Length(max = 64, message = "邮箱最大{max}")
    @NotBlank(message = "收件人邮箱不能为空")
    private String buyerSendEmail;

    /**收件人手机 */
    @Length(min = 11, max = 11, message = "手机长度{max}")
    @NotBlank(message = "收件人手机不能为空")
    private String buyerSendTel;

    /**收件人 */
    @Length(max = 10, message = "收件人长度最大{max}")
    @NotBlank(message = "收件人不能为空")
    private String buyerRecipient;

    /**开票详情列表 */
    @NotEmpty
    private List<InvoiceDetailRequestDTO> detailList;

    @Data
    public static class InvoiceDetailRequestDTO implements Serializable{

        /** 业务单据标识 (单据id) */
        @NotNull(message = "业务单据标识不能为空")
        private Long businessBillId;

        /**业务单据号 */
        private String businessBillNo;

        /**业务单据类型 */
        @NotBlank(message = "业务单据类型不能为空")
        private String businessBillType;
        
        /**业务单据所属日期*/
        private String belongYears;

        /**税率 */
        @NotNull(message = "税率不能为空")
        private BigDecimal taxRate;

        /**金额 */
        @NotNull(message = "金额不能为空")
        private BigDecimal goodsAmount;

        /**价税合计(金额+税额) */
        private BigDecimal taxGoodsAllAmount;

        /**税收分类编码 */
        @NotBlank(message = "税收分类编码不能为空")
        private String taxCateCode;

        /**税额 */
        private BigDecimal taxAmount;

        /**货物劳务名称 */
        private String goodsName;
        
        /**收费项id */
        private Long itemId;

        /**收费项名 */
        private String itemName;
        
        /**收费项税目 */
        private String itemTaxName;
    }
}
