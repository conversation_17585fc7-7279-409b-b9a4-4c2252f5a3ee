package com.charge.api.web.vo.pos.points;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 赠分款余额变得 扣除积分 数据
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PredepositDeductPointsCountVO implements Serializable {

    private static final long serialVersionUID = -2442905748128101650L;

    /**
     * 项目id
     */
    private Long communityId;

    /**
     * 扣除积分总额
     */
    private Integer totalPoints;

    /**
     * 总计扣除赠分款余额
     */
    private BigDecimal totalMoney;

    /**
     * 积分扣除明细
     */
    private List<DeductPointsItemDetailVO> deductPointsList;

}
