package com.charge.api.web.util;

import java.time.LocalDate;
import java.time.Period;


/**
 * Author: yjw
 * Date: 2023/3/17 16:19
 */
public class YueXinDateUtils {

    /**
     * 与当前时间相差的月数
     *
     * @param time              时间 yyyy-MM
     * @param reportBelongYears 时间 yyyy-MM
     * @return 月数
     */
    public static int getMonth(String time, String reportBelongYears)  {
        if ("".equals(time)) {
            throw new IllegalArgumentException("计算欠费账龄时间格式异常");
        }
        if ("".equals(reportBelongYears)) {
            throw new IllegalArgumentException("计算欠费账龄时间格式异常");
        }
        String timesYear = time.substring(0, 4);
        String timesMonth = time.substring(5, 7);

        String reportYear = reportBelongYears.substring(0, 4);
        String reportMonth = reportBelongYears.substring(5, 7);
        LocalDate belongYears = LocalDate.of(Integer.valueOf(timesYear), Integer.valueOf(timesMonth), 1);
        LocalDate reportDay = LocalDate.of(Integer.valueOf(reportYear), Integer.valueOf(reportMonth), 1);
        Period periodToNextJavaRelease = Period.between(belongYears, reportDay);
        return (periodToNextJavaRelease.getMonths() + periodToNextJavaRelease.getYears() * 12 + 1);
    }
}
