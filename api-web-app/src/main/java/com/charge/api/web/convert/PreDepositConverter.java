package com.charge.api.web.convert;

import com.charge.api.web.vo.joylife.response.PredepositBillVO;
import com.charge.bill.dto.income.PredepositBillDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: wangs
 */
@Mapper
public interface PreDepositConverter {
    PreDepositConverter INSTANCE = Mappers.getMapper(PreDepositConverter.class);

    List<PredepositBillVO> toBillVOS(List<PredepositBillDTO> accountDTOS);

}
