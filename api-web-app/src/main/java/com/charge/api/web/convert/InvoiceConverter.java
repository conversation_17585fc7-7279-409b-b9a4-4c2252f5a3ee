package com.charge.api.web.convert;

import com.charge.api.web.dto.invoice.*;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.util.DateMapper;
import com.charge.invoice.dto.thirdparty.*;
import com.charge.invoice.vo.InvoiceApplyResultVO;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/09/11/ 21:15
 * @description
 */
@Mapper(uses = DateMapper.class, builder = @Builder(disableBuilder = true))
public interface InvoiceConverter {

    InvoiceConverter INSTANCE = Mappers.getMapper(InvoiceConverter.class);

    InvoiceCallbackRequestDTO convert2InvoiceCallbackRequestDTO(ThirdPartyInvoiceCallbackRequestDTO requestDTO);

    InvoiceCallbackMainDTO convert2InvoiceCallbackMainDTO(ThirdPartyInvoiceMainDTO requestDTO);

    InvoiceCallbackDetailDTO convert2InvoiceCallbackDetailDTO(ThirdPartyInvoiceDetailDTO requestDTO);

    InvoiceApplyCallbackRequestDTO convert2InvoiceApplyCallbackRequestDTO(ThirdPartyInvoiceApplyCallbackRequestDTO requestDTO);

    InvoiceFailCallbackRequestDTO convert2InvoiceFailCallbackRequestDTO(ThirdPartyInvoiceFailCallbackRequestDTO requestDTO);

    InvoiceFailProcessDTO convert2InvoiceFailProcessDTO(ThirdPartyInvoiceFailProcessDTO requestDTO);

    List<InvoiceApplyResultDTO> convert2InvoiceApplyResultDTO(List<InvoiceApplyResultVO> result);
}
