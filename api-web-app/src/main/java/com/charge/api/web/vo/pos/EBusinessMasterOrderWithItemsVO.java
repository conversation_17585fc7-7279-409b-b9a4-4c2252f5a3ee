package com.charge.api.web.vo.pos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 电商主单vo
 *
 * <AUTHOR>
 * @date 2024/11/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EBusinessMasterOrderWithItemsVO {
    /**
     * 电商主单
     */
    private EBusinessMasterOrderVO order;
    /**
     * 电商进度单
     */
    private List<OrderInstallmentWithItemsVO> items;

}