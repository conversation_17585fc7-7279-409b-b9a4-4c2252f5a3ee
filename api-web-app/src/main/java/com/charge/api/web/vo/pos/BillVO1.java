package com.charge.api.web.vo.pos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * pos账单详情
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillVO1 {
    
    private String payMember;
    
    private String communityName;
    
    private String buildingName;
    
    private String unitName;
    
    private String houseName;
    
    private BigDecimal totalPrice;
    
    private String paymentMethod;
    
    private String paymentName;
    
    private String payStatus;
    
    private String payTime;
    
    private String orderId;
    
    private String orderName;
    
    private String tno;
    
    private List<BillItemVO> itemList;
    
    private String memo;
    
    private String collectorId;
    
    private String collectorName;
    
    private String operatorId;
    
//    private Object adjustBillId;
    
    private CancelVO cancelInfo;
    
    private String transactionFlowId;
    
//    private Object basicInfo;
    
    private String bankTransactionNo;
    
    private String arrivalTime;
    
    private String paymentSource;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CancelVO {
        
        private String adjustRoom;
        
        private String bankCard;
        
        private String negativeBillId;
        
        private String billType;
        
        private String originalBillId;
        
        private String operatorName;
        
        private String creatBillTypeName;
        
        private String cancelTime;
        
        private String adjustBillId;
        
        private String billStatus;
        
        private String cancelMethod;
        
        private String createBillType;
        
        private String cancelMemo;
        
        private String operatorId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BillItemVO {
        
        private String itemName;
        
        private String chargeTypeId;
        
        private String createTime;
        
        private BigDecimal price;
        
        private String itemCode;
        
        private String prestoreName;
        
        private Integer businessType;
        
        private Integer itemUuid;
        
        private String incomeDetailUuid;
        
        private String incomeDetailName;
    }
}


