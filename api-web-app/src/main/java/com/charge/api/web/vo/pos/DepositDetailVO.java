package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@NoArgsConstructor
@Data
@AllArgsConstructor
public class DepositDetailVO {

    private String itemId;
    private String draweeName;
    private String itemName;
    private int money;
    private int balance;
    private String payTime;
    private String title;
    private String householdName;
    private String receiptNumber;
    private List<DepositRefundVO> detail;

    @NoArgsConstructor
    @Data
    public static class DepositRefundVO {
        /**
         * 主键id
         */
        private String id;

        /**
         * 住户id
         */
        private String householdId;

        /**
         * 住户姓名
         */
        private String householdName;

        /**
         * 房屋id
         */
        private String houseId;

        /**
         * 房屋名称
         */
        private String houseName;

        /**
         * 小区id
         */
        private String areaId;

        /**
         * 小区名称
         */
        private String areaName;

        /**
         * 押金项(收费项)id
         */
        private String itemId;

        /**
         * 押金项(收费项)名称
         */
        private String itemName;

        /**
         * 金额
         */
        private BigDecimal money;

        /**
         * 退款类型(0标识银行卡退款, 1表示业主app退款)
         */
        private String refundType;

        /**
         * 业主app账号
         */
        private String appAccount;

        /**
         * 银行信息
         */
        private String bankInfo;

        /**
         * 银行卡号
         */
        private String bankCard;

        /**
         * 押金付款记录
         */
        private String paymentId;

        /**
         * 退款工单
         */
        private String caseId;

        /**
         * 押金状态(0审核中，1表示审核通过，2表示审核不通过)
         */
        private String depositStatus;

        /**
         * 创建时间
         */
        @JsonFormat(
                pattern = "yyyy-MM-dd HH:mm:ss",
                timezone = "GMT+8"
        )
        private Date createTime;
        /**
         * 退款结果时间
         */
        @JsonFormat(
                pattern = "yyyy-MM-dd HH:mm:ss",
                timezone = "GMT+8"
        )
        private Date resultTime;
        /**
         * 租户id
         */
        private String cropId;
        /**
         * 退款操作人
         */
        private String operator;
        /**
         * 退款操作人id
         */
        private String operatorId;
        /**
         * 退款说明
         */
        private String remark;
        /**
         * 退款失败说明
         */
        private String rejected;
        /**
         * 订单号
         */
        private String orderNum;
        /**
         * 垫付人
         */
        private String employeeName;
        /**
         * 垫付人id
         */
        private String employeeId;
        /**
         * 员工是否垫付(0:否，1：是,默认0)
         */
        private String advanced;
        /**
         * 确认退款人
         */
        private String confirmName;
        /**
         * 确认退款人id
         */
        private String confirmId;
        /**
         * 标的物类型，默认房屋“house”
         */
        private String houseType;

        /**
         * 退款方式
         */
        private String refundStyle;

        private String incomeDetailUuid;

        private String incomeDetailName;
    }
}
