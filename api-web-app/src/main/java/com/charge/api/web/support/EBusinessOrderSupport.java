package com.charge.api.web.support;

import com.charge.api.web.convert.EBusinessMasterOrderConverter;
import com.charge.api.web.vo.order.OrderQuery;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.order.client.EBusinessOrderQueryClient;
import com.charge.order.dto.ebuiness.EBusinessOrderDetailDTO;
import com.charge.order.dto.ebuiness.EBusinessOrderQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * EBusinessOrderSupport
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EBusinessOrderSupport {

    private final CommunityParamSupport communitySupport;

    private final EBusinessOrderQueryClient orderQueryClient;

    public EBusinessOrderDetailDTO getEBusinessOrderDetail(OrderQuery orderQuery) throws ChargeBusinessException {
        communitySupport.fillCommunityId(orderQuery);
        EBusinessOrderQuery query = EBusinessMasterOrderConverter.INSTANCE.map(orderQuery);
        query.setQueryAdjust(orderQuery.getQueryAdjust());
        query.setQueryPay(orderQuery.getQueryPay());
        query.setQueryRefund(orderQuery.getQueryRefund());
        ChargeResponse<EBusinessOrderDetailDTO> chargeResponse = orderQueryClient.orderDetail(query);
        return AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
    }
}