package com.charge.api.web.vo.arrearsnotice;

import com.charge.common.serializer.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * ArrearsNoticeAssetReceivableVO
 * <p>
 * Description:缴费通知单详情
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/22
 */
@Data
public class ArrearsNoticeDetailVO {
    private List<AssetMonthReceivableVO> assetMonthReceivables;
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalAmount;
    private String payUrl;
    private String communityName;
}