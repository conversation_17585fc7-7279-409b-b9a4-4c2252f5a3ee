package com.charge.api.web.service.pay.impl;

import com.alibaba.fastjson.JSONArray;
import com.charge.api.web.adapter.PayOrderAdapter;
import com.charge.api.web.config.AssetPayGrayConfig;
import com.charge.api.web.constants.IncomeBillConstants;
import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.constants.YueConstants;
import com.charge.api.web.convert.PointsTradeConverter;
import com.charge.api.web.enums.ChargeObjTypeEnum;
import com.charge.api.web.enums.CheckErrorEnum;
import com.charge.api.web.service.bill.joy.AddBatchNormalService;
import com.charge.api.web.service.joylife.IncomeBillService;
import com.charge.api.web.service.joylife.PointsService;
import com.charge.api.web.service.pay.SwiftPassService;
import com.charge.api.web.support.BankCollectionCheckSupport;
import com.charge.api.web.support.PrePayLockSupport;
import com.charge.api.web.util.ShardingUtil;
import com.charge.api.web.vo.joylife.BatchArrearsOrderList;
import com.charge.api.web.vo.joylife.request.BatchPayDataRequest;
import com.charge.api.web.vo.joylife.request.ReceivableAssetBillInfo;
import com.charge.api.web.vo.joylife.request.ReceivableBillInfo;
import com.charge.api.web.vo.joylife.request.ReceivablePointCalRequest;
import com.charge.api.web.vo.joylife.response.MonthOrderItemArrears;
import com.charge.api.web.vo.joylife.response.OrderItemArrears;
import com.charge.api.web.vo.joylife.response.ReceivablePointCalResultItemVo;
import com.charge.api.web.vo.joylife.response.ReceivablePointCalResultVo;
import com.charge.apicloud.enums.CrmixcTransTypeEnum;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.LockObjectDTO;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.domain.response.CreateBillsResponse;
import com.charge.bill.dto.income.AssetArrearsListDTO;
import com.charge.bill.dto.income.AssetReceivalbeBillListDTO;
import com.charge.bill.dto.income.IncomeOrderInfosDTO;
import com.charge.bill.enums.*;
import com.charge.common.constant.CommonConstant;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.config.client.points.PointsConfigClient;
import com.charge.config.dto.points.PointsConfigConditionDTO;
import com.charge.config.dto.points.PointsSignCommunityDTO;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.general.client.bff.PayCallbackClient;
import com.charge.general.dto.PayCallbackResultDTO;
import com.charge.general.dto.PayOrderResultDTO;
import com.charge.joylife.dto.PayInfoDTO;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.pay.client.PayClient;
import com.charge.pay.client.PointClient;
import com.charge.pay.dto.ChargePayResponse;
import com.charge.pay.dto.pay.PayOrderSubmitResponseDTO;
import com.charge.pay.dto.pay.PaySubOrderResponseDTO;
import com.charge.pay.dto.point.ChargePointsTransDTO;
import com.charge.pay.dto.point.ChargePointsTransRecordDTO;
import com.charge.pay.dto.point.PointsTradeConfirmMqDTO;
import com.charge.pay.enums.PayTradeStateEnum;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 威富通订单处理
 * 
 * <AUTHOR>
 * @date 2023/3/17
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class SwiftPassServiceImpl implements SwiftPassService {

    private final IncomeBillService incomeBillService;
    private final CommunityClient communityClient;
    private final ReceivableBillClient receivableBillClient;
    private final PointClient pointClient;
    private final PointsConfigClient pointsConfigClient;
    private final PayClient payClient;
    private final BankCollectionCheckSupport bankCollectionCheckSupport;
    private final AssetPayGrayConfig assetPayGrayConfig;
    private final AddBatchNormalService addBatchNormalService;

    private final PayCallbackClient payCallbackClient;

    private final PrePayLockSupport prePayLockSupport;
    private final PointsService pointsService;

    @Override
    public ChargeResponse<Long> createIncomeBill(BatchPayDataRequest pay, Long communityId, BigDecimal penaltyMoney, BigDecimal principalMoney,
        List<AssetArrearsListDTO> assetArrearsLists, String deadLine, int houseCount, String orderNum,boolean disAllFlag,Long pointsTransRecordId,
                                                 JSONObject jsonObject) throws ChargeBusinessException {
        // 开启灰度
        if (assetPayGrayConfig.isOpenGray(communityId,"/new/chargeAPI/createBatchPay")) {
            CreateBillsResponse response = addBatchNormalService.createBills(pay, communityId, penaltyMoney, principalMoney, assetArrearsLists, deadLine, houseCount, orderNum, disAllFlag, pointsTransRecordId);
            return new ChargeResponse<>(response.getIncomeBillId());
        }
        IncomeOrderInfosDTO incomeOrderInfos = new IncomeOrderInfosDTO();
        incomeOrderInfos.setOrderNum(orderNum);
        incomeOrderInfos.setPayHouseCount(houseCount);
        incomeOrderInfos.setActualPrice(new BigDecimal(pay.getActualPrice()));
        incomeOrderInfos.setCommunityId(communityId);
        incomeOrderInfos.setCommunityName(pay.getCommunityName());
        incomeOrderInfos.setArrearsOrderList(assetArrearsLists);
        incomeOrderInfos.setMemo(pay.getMemo());
        incomeOrderInfos.setEndYear(deadLine);
        incomeOrderInfos.setBalanceStatus(IncomeBillConstants.BALANCE_STATUS_NOT_DONE);
        incomeOrderInfos.setPayStatus(BillPayStatusEnum.UN_PAY.getCode());
        incomeOrderInfos.setTotalPrice(new BigDecimal(pay.getTotalPrice()));
        incomeOrderInfos.setPrincipalMoney(principalMoney);
        incomeOrderInfos.setPenaltyMoney(penaltyMoney);
        incomeOrderInfos.setEquityMoney(new BigDecimal(StringUtils.isEmpty(pay.getPointsMoney())?"0":pay.getPointsMoney()));
        incomeOrderInfos.setEquityAccount(pay.getEquityAccount());
        String paymentChannel = Objects.equals(PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel(),pay.getPaymentSource())?PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel():fillPaymentChannel(pay.getPaymentMethod());
        incomeOrderInfos.setPaymentChannel(disAllFlag? "":paymentChannel);
        incomeOrderInfos.setPaymentMethod(disAllFlag? PaymentMethodEnum.EQUITY.getPaymentCode():fillPaymentMethod(pay.getPaymentMethod()));

        incomeOrderInfos.setPaymentType(disAllFlag?PaymentTypeEnum.POINTS.getCode():PaymentTypeEnum.CASH.getCode());
        //修改朝昔小程序终端来源，将WECHAT_APPLET=>PaymentTerminalEnum.ZHAO_XI_APP
        incomeOrderInfos.setPaymentTerminal(PaymentTerminalEnum.handleJoyLifeWechatApplet(pay.getPaymentSource()));
        incomeOrderInfos.setIsInvoiced(0);
        incomeOrderInfos.setPayMember(pay.getUserName());
        incomeOrderInfos.setPayMemberId(pay.getUserId());
        incomeOrderInfos.setPoints(Integer.parseInt(pay.getPoints()));
        incomeOrderInfos.setGoodsName(PayRelatedConstants.GOODSNAME_FOR_PROPERTY);
        incomeOrderInfos.setPayMemberMobile(pay.getPhone());
        incomeOrderInfos.setReceiptType(0);
        incomeOrderInfos.setCustomId(pay.getCustomId());
        incomeOrderInfos.setPointsTransTd(pointsTransRecordId);
        if(Objects.nonNull(jsonObject)){
            incomeOrderInfos.setIsMergeBill(Objects.nonNull(jsonObject.get("isMergeBill"))?(Integer) jsonObject.get("isMergeBill"):null);
            incomeOrderInfos.setSubBills(Objects.nonNull(jsonObject.get("subItems"))?PayOrderAdapter.fillSubBills(convertPaySubOrderDTO(jsonObject)):null);
        }
        return incomeBillService.createIncomeBills(incomeOrderInfos);
    }



    public List<PaySubOrderResponseDTO> convertPaySubOrderDTO(JSONObject jsonObject){

        String subItemsJson = JSONArray.toJSONString(jsonObject.get("subItems"));

        return JSONArray.parseArray(subItemsJson, PaySubOrderResponseDTO.class);
    }

    private Integer fillPaymentMethod(String paymentMethod){
        if (Objects.equals("3",paymentMethod)) {
            return PaymentMethodEnum.ALIPAY.getPaymentCode();
        }
        return PaymentMethodEnum.WECHAT.getPaymentCode();
    }


    private String fillPaymentChannel(String paymentMethod){
        if (Objects.equals("3",paymentMethod)) {
            return  PaymentChannelEnum.ALI_PAY.getPaymentChannel();
        }
        return PaymentChannelEnum.WECHAT_PAY.getPaymentChannel();
    }
    @Override
    public AssetArrearsListDTO buildAssetArrears(BatchArrearsOrderList batchArrearsOrders, List<AssetReceivalbeBillListDTO> assetReceivalbeBillLists, String paymentMethod) {
        AssetArrearsListDTO assetArrearsListDTO = new AssetArrearsListDTO();
        assetArrearsListDTO.setAssetId(Long.parseLong(batchArrearsOrders.getHouseId()));
        assetArrearsListDTO.setAssetName(batchArrearsOrders.getHouseName());
        assetArrearsListDTO.setAssetTotalAmount(new BigDecimal(batchArrearsOrders.getHouseTotalAmount()));
        assetArrearsListDTO.setAssetCode(batchArrearsOrders.getHouseCode());
        assetArrearsListDTO.setHouseType(batchArrearsOrders.getHouseType());
        assetArrearsListDTO.setIsBalance(CommonConstant.ONE);
        assetArrearsListDTO.setDetailList(assetReceivalbeBillLists);
        assetArrearsListDTO.setPaymentTerminal(PaymentTerminalEnum.ZHAO_XI_APP.getCode());
        assetArrearsListDTO.setPaymentChannel(fillPaymentChannel(paymentMethod));
        assetArrearsListDTO.setPaymentMethod(fillPaymentMethod(paymentMethod));
        assetArrearsListDTO.setGiftStarCount(batchArrearsOrders.getGiftStarCount());

        return assetArrearsListDTO;
    }

    @Override
    public Long fetchCommunityId(String communityMsId) throws ChargeBusinessException {
        ChargeResponse<CommunityDTO> communityResponse = communityClient.oneByCondition(CommunityCondition.builder().msId(communityMsId).build());
        if (!communityResponse.isSuccess()) {
            log.error("查询主数据服务异常");
            throw new ChargeBusinessException(String.valueOf(YueConstants.CODE_FAILED), "查询主数据服务异常");
        }
        CommunityDTO communityDTO = AppInterfaceUtil.getResponseData(communityResponse);
        if (Objects.isNull(communityDTO)) {
            log.error("通过ms id：{}查询不到对应小区", communityMsId);
            throw new ChargeBusinessException(String.valueOf(YueConstants.CODE_FAILED), "查询不到对应小区");
        }
        return communityDTO.getId();
    }

    @Override
    public JSONObject createOrder(BatchPayDataRequest pay, Long communityId, String orderNum) throws ChargeBusinessException {
        ChargeResponse<PayOrderSubmitResponseDTO> payOrderResponseData = payClient.tradeCreate(PayOrderAdapter.requestParamConvert(pay, communityId, orderNum));
        if (!payOrderResponseData.isSuccess()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", payOrderResponseData.getCode());
            jsonObject.put("errorMessage", payOrderResponseData.getMessage());
            return jsonObject;
        }
        log.info("支付预下单响应:{}", payOrderResponseData);
        return PayOrderAdapter.responseParamConvert(payOrderResponseData.getContent(), pay.getPaymentSource());
    }

    @Override
    public void checkReceivableBillStatus(List<ReceivableBillDTO> effectiveByIdsReceivableBillDTOS,List<ReceivableBillDTO> checkingReceivableBillDTOS,List<ReceivableBillDTO> effectiveAllReceivableBills) throws ChargeBusinessException {

        Map<Long, String> effectiveMaxBelongYearsByItemId =
                effectiveByIdsReceivableBillDTOS.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getItemId,
                        Collectors.mapping(ReceivableBillDTO::getBelongYears, Collectors.collectingAndThen(Collectors.maxBy(String::compareTo),Optional::get))));


        Map<Long, List<String>> itemIdToBelongYearsList = effectiveByIdsReceivableBillDTOS.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getItemId,
                Collectors.mapping(ReceivableBillDTO::getBelongYears, Collectors.toList())));


        List<String> effectiveItemNames = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(effectiveByIdsReceivableBillDTOS)) {
            Map<Long, List<ReceivableBillDTO>> itemIdToEffectiveReceivableBillMap = effectiveAllReceivableBills.stream().filter(t-> effectiveMaxBelongYearsByItemId.get(t.getItemId()) != null && t.getBelongYears().compareTo(effectiveMaxBelongYearsByItemId.get(t.getItemId())) < 0).collect(Collectors.groupingBy(t -> t.getItemId()));
            Map<Long, List<ReceivableBillDTO>> itemIdToEffectiveByIdsReceivableBillMap = effectiveByIdsReceivableBillDTOS.stream().filter(t-> effectiveMaxBelongYearsByItemId.get(t.getItemId()) != null && t.getBelongYears().compareTo(effectiveMaxBelongYearsByItemId.get(t.getItemId())) != 0).collect(Collectors.groupingBy(t -> t.getItemId()));
            itemIdToBelongYearsList.forEach((itemId,belongYearsList)->{
                List<String> distinctBelongYearsList = belongYearsList.stream().distinct().collect(Collectors.toList());
                if (distinctBelongYearsList.size() > 1
                        && org.apache.commons.collections.CollectionUtils.isNotEmpty(itemIdToEffectiveByIdsReceivableBillMap.get(itemId))
                        && itemIdToEffectiveReceivableBillMap.get(itemId).size() > itemIdToEffectiveByIdsReceivableBillMap.get(itemId).size()){
                    effectiveItemNames.add(itemIdToEffectiveByIdsReceivableBillMap.get(itemId).get(0).getItemName());
                }else if (distinctBelongYearsList.size() == 1 && org.apache.commons.collections.CollectionUtils.isNotEmpty(itemIdToEffectiveReceivableBillMap.get(itemId))){
                    effectiveItemNames.add(itemIdToEffectiveReceivableBillMap.get(itemId).get(0).getItemName());
                }
            });

        }
        Assert.isTrue(CollectionUtil.isEmpty(effectiveItemNames),"所选缴费期间存在生效中数据，请审核后再操作缴费。");

        List<String> checkingItemNames = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(checkingReceivableBillDTOS)) {
            Map<Long, List<ReceivableBillDTO>> itemIdToCheckingReceivableBillMap = checkingReceivableBillDTOS.stream().collect(Collectors.groupingBy(t -> t.getItemId()));
            itemIdToCheckingReceivableBillMap.forEach((itemId,list)->{

                String checkingMinBelongYears = list.stream().map((ReceivableBillDTO::getBelongYears)).min(String::compareTo).get();
                String effectiveMaxBelongYears = effectiveMaxBelongYearsByItemId.get(itemId);
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(effectiveMaxBelongYears) && effectiveMaxBelongYears.compareTo(checkingMinBelongYears) > 0){
                    checkingItemNames.add(list.get(0).getItemName());
                }
            });
        }
        Assert.isTrue(CollectionUtil.isEmpty(checkingItemNames),"所选缴费期间存在审核中数据，请审核后再操作缴费。");
    }

    @Override
    public ChargeResponse createBatchPay(BatchPayDataRequest pay) throws ChargeBusinessException {
        BigDecimal totalMoney = new BigDecimal(pay.getTotalPrice());
        BigDecimal actualPrice = new BigDecimal(pay.getActualPrice());
        BigDecimal pointsMoney = new BigDecimal(StringUtils.isEmpty(pay.getPointsMoney())? "0":pay.getPointsMoney());

        if (totalMoney.compareTo(actualPrice.add(pointsMoney)) != 0) {
            return new ChargeResponse(YueConstants.CODE_FAILED, "订单总金额!=实付金额+积分抵扣金额");
        }
        if(CollectionUtil.isEmpty( pay.getArrearsOrderList())){
            return new ChargeResponse(YueConstants.CODE_FAILED, "缴费单为空");
        }
        BigDecimal totalMoneySum=pay.getArrearsOrderList().stream().map(item -> new BigDecimal(item.getHouseTotalAmount()).setScale(2, BigDecimal.ROUND_HALF_UP)).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(totalMoney.compareTo(totalMoneySum) != 0){
            return new ChargeResponse(YueConstants.CODE_FAILED, "订单总金额!=明细单金额之和");
        }
        // t校验参数（房间数量、收费项）
        Long communityId = fetchCommunityId(pay.getCommunityId());
        ShardingUtil.addCommunityId2ThreadContext(communityId);
        // 总违约金
        BigDecimal penaltyMoney = BigDecimal.ZERO;
        // 不含违约金总欠费
        BigDecimal principalMoney = BigDecimal.ZERO;

        //抵扣费项
        List<AssetArrearsListDTO> disList = new ArrayList<>();
        //实收费项
        List<AssetArrearsListDTO> actualList = new ArrayList<>();

        //欠费信息转换
        List<AssetArrearsListDTO> assetArrearsLists = new ArrayList<>();

        List<String> houseIds = new ArrayList<>();
        List<Long> recBillIds = new ArrayList<>();
        List<OrderItemArrears> allDetail = new ArrayList<>();
        Set<Long> itemIdSet = new HashSet<>();
        String deadLine = YueConstants.MONTH_LEAST;
        Long pointsTransRecordId = null;
        boolean pointsSuccessRedeem = false;
        LockObjectDTO lockObjectDTO = null;
        PointsTradeConfirmMqDTO confirmMqDTO = new PointsTradeConfirmMqDTO();

        try {
            List<Long> tempReceivableIds = pay.getArrearsOrderList().stream().flatMap(a -> a.getBillList().stream()).flatMap(a -> a.getDetailList().stream()).map(a -> Long.parseLong(a.getOrderId())).collect(Collectors.toList());

            ReceivableConditionDTO receivableCondition = ReceivableConditionDTO.builder().communityId(communityId)
                    .billStatuses(org.assertj.core.util.Lists.newArrayList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),ReceivableBillStatusEnum.BILL_CHECKING.getCode()))
                    .payStatuses(org.assertj.core.util.Lists.newArrayList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(), ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()))
                    .assetIdList(pay.getArrearsOrderList().stream().map(t->Long.valueOf(t.getHouseId())).collect(Collectors.toList()))
                    .billType(ReceivableBillTypeEnum.RECEIVABLE.getCode()).build();
            ChargeResponse<List<ReceivableBillDTO>> receivablesResp = receivableBillClient.queryList(receivableCondition);
            List<ReceivableBillDTO> receivableBillsResponseData = AppInterfaceUtil.getResponseDataThrowException(receivablesResp);
            Assert.notEmpty(receivableBillsResponseData,"查询应收单失败");

            List<ReceivableBillDTO> recBillByIdsList = receivableBillsResponseData.stream().filter(t->tempReceivableIds.contains(t.getId())).collect(Collectors.toList());
            Assert.isTrue(recBillByIdsList.size() == tempReceivableIds.size(),"部分应收不存在");

            Map<Long, List<ReceivableBillDTO>> assetIdToReceivableBillDTOs = receivableBillsResponseData.stream().collect(Collectors.groupingBy(t -> t.getAssetId()));

            Map<Long, List<ReceivableBillDTO>> effectiveByIdsReceivableBillDTOSGroupByAssetId = recBillByIdsList.stream().collect(Collectors.groupingBy(t -> t.getAssetId()));
            for (Map.Entry<Long, List<ReceivableBillDTO>> entry : effectiveByIdsReceivableBillDTOSGroupByAssetId.entrySet()){
                List<Integer> chargeObjectList = entry.getValue().stream().map(t -> t.getChargeObject()).distinct().collect(Collectors.toList());
                Assert.isTrue(chargeObjectList.size() <=1,"账单既包括了开发商又包括了业主欠费，请重新选择。");

                List<ReceivableBillDTO> checkingReceivableBills = assetIdToReceivableBillDTOs.get(entry.getKey()).stream().filter(t->ReceivableBillStatusEnum.BILL_CHECKING.getCode().equals(t.getBillStatus()) && chargeObjectList.get(0).equals(t.getChargeObject())).collect(Collectors.toList());
                List<ReceivableBillDTO> effectiveReceivableBills = assetIdToReceivableBillDTOs.get(entry.getKey()).stream().filter(t->ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode().equals(t.getBillStatus()) && chargeObjectList.get(0).equals(t.getChargeObject())).collect(Collectors.toList());

                checkReceivableBillStatus(entry.getValue(),checkingReceivableBills,effectiveReceivableBills);
            }

            Map<Long, ReceivableBillDTO> receivableBillMap = recBillByIdsList.stream().collect(Collectors.toMap(ReceivableBillDTO::getId, Function.identity(), (a, b) -> b));
            for (BatchArrearsOrderList batchArrearsOrder : pay.getArrearsOrderList()) {
                if (CollectionUtil.isNotEmpty(batchArrearsOrder.getBillList())) {
                    BigDecimal billMoneySum = batchArrearsOrder.getBillList().stream().map(item -> new BigDecimal(item.getAmount()).setScale(2, BigDecimal.ROUND_HALF_UP)).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (new BigDecimal(batchArrearsOrder.getHouseTotalAmount()).setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(billMoneySum) != 0) {
                        return new ChargeResponse(YueConstants.CODE_FAILED, "房间订单总金额!=明细单金额之和");
                    }
                    BigDecimal houseAmount = new BigDecimal(StringUtils.isEmpty(batchArrearsOrder.getHouseTotalAmount()) ? "0" : batchArrearsOrder.getHouseTotalAmount());
                    if (Objects.isNull(batchArrearsOrder) || BigDecimal.ZERO.compareTo(houseAmount) == 0) {
                        continue;
                    }
                    List<AssetReceivalbeBillListDTO> assetReceivalbeBillLists = new ArrayList<>();
                    for (MonthOrderItemArrears arrears : batchArrearsOrder.getBillList()) {
                        List<OrderItemArrears> orderItemsArrears = arrears.getDetailList();
                        if (CollectionUtil.isNotEmpty(orderItemsArrears)) {
                            if (DateUtils.parse(arrears.getMonth(), DateUtils.FORMAT_14).after(DateUtils.parse(deadLine, DateUtils.FORMAT_14))) {
                                deadLine = arrears.getMonth();
                            }
                            for (OrderItemArrears orderItemArrears : orderItemsArrears) {
                                long id = Long.parseLong(orderItemArrears.getOrderId());
                                ReceivableBillDTO receivableBillDTO = receivableBillMap.get(id);
                                Assert.notNull(receivableBillDTO,"欠费不存在"+id);
                                recBillIds.add(Long.parseLong(orderItemArrears.getOrderId()));
                                allDetail.add(orderItemArrears);
                                itemIdSet.add(Long.parseLong(orderItemArrears.getItemId()));
                                principalMoney = principalMoney.add(new BigDecimal(orderItemArrears.getItemArrearsAmount()));
                                penaltyMoney = penaltyMoney.add(new BigDecimal(orderItemArrears.getItemPenaltyAmount()));
                                AssetReceivalbeBillListDTO assetReceivalbeBillListDTO = new AssetReceivalbeBillListDTO();
                                assetReceivalbeBillListDTO.setBillId(id);
                                assetReceivalbeBillListDTO.setItemId(Long.parseLong(orderItemArrears.getItemId()));
                                assetReceivalbeBillListDTO.setItemName(orderItemArrears.getItemName());
                                assetReceivalbeBillListDTO.setBelongYears(arrears.getMonth());
                                assetReceivalbeBillListDTO.setItemArrearsAmount(new BigDecimal(orderItemArrears.getItemArrearsAmount()));
                                assetReceivalbeBillListDTO.setItemPenaltyAmount(new BigDecimal(orderItemArrears.getItemPenaltyAmount()));
                                assetReceivalbeBillListDTO.setChargeObject(receivableBillDTO.getChargeObject());
                                assetReceivalbeBillListDTO.setAssetUseStatus(receivableBillDTO.getAssetUseStatus());
                                assetReceivalbeBillLists.add(assetReceivalbeBillListDTO);
                            }
                        }
                    }

                    if (CollectionUtil.isNotEmpty(assetReceivalbeBillLists)) {
                        houseIds.add(batchArrearsOrder.getHouseId());
                        AssetArrearsListDTO assetArrearsListDTO = buildAssetArrears(batchArrearsOrder, assetReceivalbeBillLists, pay.getPaymentMethod());
                        assetArrearsLists.add(assetArrearsListDTO);
                    }
                }
            }

            log.info("{}|生活缴费，房间IDs:{},订单IDs:{}", LogCategoryEnum.BUSSINESS, houseIds, recBillIds);
            houseIds = houseIds.stream().distinct().collect(Collectors.toList());
            int houseCount = houseIds.size();
            if (houseCount == 0) {
                return new ChargeResponse(YueConstants.CODE_FAILED, "房屋为空");
            }
            if (CollectionUtil.isEmpty(recBillIds)) {
                return new ChargeResponse(YueConstants.CODE_FAILED, "收费项为空");
            }


            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(recBillByIdsList)) {
                // 校验是否托收中
                if (bankCollectionCheckSupport.bankCollectingByAssetIds(recBillByIdsList.stream().map(ReceivableBillDTO::getAssetId).distinct().collect(Collectors.toList()))) {
                    return new ChargeResponse<>(YueConstants.CODE_FAILED, ErrorInfoEnum.E3042.getValue());
                }
                for (ReceivableBillDTO receivableBill : recBillByIdsList) {
                    if (Objects.equals(receivableBill.getPayStatus(), ReceivableBillPayStatusEnum.COLLECTION.getCode())) {
                        return new ChargeResponse(YueConstants.CODE_FAILED, ErrorInfoEnum.E3044.getValue());
                    }
                }
            }
            if (!checkMoney(recBillByIdsList, allDetail, actualPrice, pointsMoney)) {
                return new ChargeResponse(YueConstants.CODE_FAILED, "欠费金额与缴费金额不一致");
            }
            if (checkBillStatusNoEffective(recBillByIdsList)) {
                return new ChargeResponse<>(YueConstants.CODE_FAILED, "应收单单据状态存在不是生效的数据，请重新获取列表再进行下单");
            }

            //生成唯一订单号
            String orderNum = IdGeneratorSupport.getIstance().nextId();

            lockObjectDTO = LockObjectDTO.builder().mark(orderNum).paymentSource(pay.getPaymentSource())
                    .paymentMethod(pay.getPaymentMethod()).businessScene(BillPrePaySceneEnum.ZHAOXI_LIFE.getCode())
                    .receiveBillIdList(recBillByIdsList.stream().map(ReceivableBillDTO::getId).collect(Collectors.toSet()))
                    .payMember(pay.getUserId())
                    .build();
            prePayLockSupport.lockAndClose(lockObjectDTO);

            //积分抵扣flag
            boolean disPointsFlag = StringUtils.isEmpty(pay.getPointsMoney()) || BigDecimal.ZERO.compareTo(new BigDecimal(pay.getPointsMoney())) >= 0 ? false :true;
            if (disPointsFlag) {
                //积分抵扣
                log.info("{}|生活缴费：进入积分抵扣：points:{},pointsToMoney:{}",LogCategoryEnum.BUSSINESS, pay.getPoints(), pay.getPointsMoney());
                //进入积分抵扣环节
                pointsMoney  = new BigDecimal(pay.getPointsMoney());
                validatePointsPayParam(pay.getPoints(), pointsMoney, pay.getEquityAccount());

                //查询可抵扣项信息
                ChargeResponse<List<PointsSignCommunityDTO>> communityConfig = pointsConfigClient.getCommunityConfig(PointsConfigConditionDTO.builder().communityId(communityId).enableConfig(true).build());
                List<PointsSignCommunityDTO> configList = AppInterfaceUtil.getResponseData(communityConfig);
                log.info("{}|可积分抵扣费项：{}",LogCategoryEnum.BUSSINESS,configList);
                if (CollectionUtils.isEmpty(configList) || CollectionUtils.isEmpty(configList.get(0).getPointsDiscountItemIdList())) {
                    prePayLockSupport.unlock(lockObjectDTO);
                    return new ChargeResponse(-1, "无可抵扣积分费项");
                }
                List<Long> disItemIdList =configList.get(0).getPointsDiscountItemIdList();
                PointsSignCommunityDTO config = configList.get(0);

                //获取抵扣和缴费账单
                createEquityTransactionBillList(assetArrearsLists, disItemIdList, pointsMoney,disList,actualList);

                //通过actualList排除违约金构建actualListsForGift效验赠送积分正确性
                if (checkGiftStarCount(pay.getCommunityId(), actualList, pay.getPointsMoney())){
                    throw new ChargeBusinessException(CheckErrorEnum.POINTS_ERROR.getCode(), CheckErrorEnum.POINTS_ERROR.getMsg());
                }

                //积分通兑
                ChargePointsTransDTO transDTO = PointsTradeConverter.INSTANCE.toPointsTransDTO(pay,config);
                transDTO.setOrderNum(orderNum);
                transDTO.setItemName(PayRelatedConstants.GOODSNAME_FOR_PROPERTY);
                transDTO.setTradeType(CrmixcTransTypeEnum.MIXC_POINTS_TYPE_REDEEM.getTypeCode());
                confirmMqDTO = PointsTradeConverter.INSTANCE.toConfirmDTO(transDTO);
                ChargeResponse<ChargePointsTransRecordDTO> tradeResp = pointClient.redeemPoints(transDTO);
                if (!tradeResp.isSuccess()) {
                    log.info("{}|生活缴费-积分抵扣交易失败|{}", LogCategoryEnum.BUSSINESS, tradeResp);
                    prePayLockSupport.unlock(lockObjectDTO);
                    return new ChargeResponse(tradeResp.getCode(),"无法使用万象星，请稍后重试或联系管家处理！");
                }
                ChargePointsTransRecordDTO transRecord = AppInterfaceUtil.getResponseDataThrowException(tradeResp);
                log.info("{}|生活缴费-积分抵扣交易成功|{}", LogCategoryEnum.BUSSINESS, transRecord);
                pointsTransRecordId = transRecord.getId();
                pointsSuccessRedeem = true;
            }

            //通过actualList排除违约金构建actualListsForGift效验赠送积分正确性(没有积分抵扣时的校验)
            if (checkGiftStarCount(pay.getCommunityId(), assetArrearsLists, null)){
                throw new ChargeBusinessException(CheckErrorEnum.POINTS_ERROR.getCode(), CheckErrorEnum.POINTS_ERROR.getMsg());
            }

            //返回数据
            Map<String, String> map = Maps.newHashMap();

            boolean allDisFlag = false;
            if (actualPrice.compareTo(BigDecimal.ZERO) == 0 && CollectionUtils.isEmpty(actualList)) {
                //全额积分抵扣
                log.info("{}|生活缴费-全额积分抵扣|{}", LogCategoryEnum.BUSSINESS, disList);
                allDisFlag = true;
                // 创建批量预支付单
                ChargeResponse<Long> incomeBillResponse =
                        createIncomeBill(pay, communityId, penaltyMoney, principalMoney, disList, deadLine, houseCount, orderNum,allDisFlag,pointsTransRecordId,null);
                Long billId = AppInterfaceUtil.getResponseData(incomeBillResponse);
                if (billId == null) {
                    prePayLockSupport.unlock(lockObjectDTO);
                    return new ChargeResponse(YueConstants.CODE_FAILED, "创建实收单失败");
                }
                log.info("{}|生活缴费-全额积分抵扣下单成功，实收单id为{}", LogCategoryEnum.BUSSINESS, billId);
                map.put("id", String.valueOf(billId));
                //实时销单
                PayOrderResultDTO payOrderResultDTO = PayOrderResultDTO.builder().outTradeNo(orderNum)
                        .communityId(communityId).tradeState(PayTradeStateEnum.SUCCESS.getCode())
                        .totalFee(actualPrice+"").build();
                ChargeResponse<PayCallbackResultDTO> payResponse = payCallbackClient.operate(payOrderResultDTO);
                if (!payResponse.isSuccess()) {
                    log.info("{}|积分全额抵扣交易成功，更新账单异常:{}" ,LogCategoryEnum.BUSSINESS, payResponse.getMessage());
                    prePayLockSupport.unlock(lockObjectDTO);
                    return new ChargePayResponse(YueConstants.CODE_FAILED, "积分全额抵扣交易成功，更新账单异常，请联系管家处理！" + payResponse.getMessage());
                }
            } else {
                pay.setActualList(actualList);
                // 创建威富通订单
                JSONObject jsonObject = createOrder(pay, communityId,orderNum);
                if (jsonObject.containsKey("errorCode")) {
                    if (pointsSuccessRedeem) {
                        //下单异常：实时退分
                        log.error("{}|积分通兑成功，但支付渠道下单异常，实时退分：{}", LogCategoryEnum.BUSSINESS, jsonObject);
                        pointClient.pointsTradeMq(confirmMqDTO,5);
                    }
                    prePayLockSupport.unlock(lockObjectDTO);
                    return new ChargeResponse<>(jsonObject.getInt("errorCode"), jsonObject.getString("errorMessage"));
                }
                log.info("{}|生活缴费-威富通下单结果|{}", LogCategoryEnum.BUSSINESS, jsonObject);
                if (!CollectionUtils.isEmpty(disList)) {
                    assetArrearsLists = new ArrayList<>();
                    assetArrearsLists.addAll(disList);
                    assetArrearsLists.addAll(actualList);
                }
                // json object bug, 需要确保value值为string类型
                jsonObject.forEach((k, v) -> {
                    map.put(k.toString(), v.toString());
                });

                // 创建批量预支付单
                ChargeResponse<Long> incomeBillResponse =
                        createIncomeBill(pay, communityId, penaltyMoney, principalMoney, assetArrearsLists, deadLine, houseCount, orderNum,allDisFlag,pointsTransRecordId,jsonObject);
                Long billId = AppInterfaceUtil.getResponseData(incomeBillResponse);
                if (billId == null) {
                    log.info("{}|生活缴费-创建实收单失败：", LogCategoryEnum.BUSSINESS, incomeBillResponse);
                    throw  new ChargeBusinessException(ErrorInfoEnum.E1013);
                }
                log.info("{}|生活缴费-威富通支付下单成功，实收单id为{}", LogCategoryEnum.BUSSINESS, billId);
                map.put("id", String.valueOf(billId));
                // 写入交易记录表 ==应该去异步操作
                // TODO 新的下单以统一写入
                /*PayRecord payRecord = new PayRecord().setOrderNum(orderNum).setPayMember(pay.getUserName()).setPayMemberId(pay.getUserId()).setPayStatus(0).setPaymentMethod(0)
                        .setPaymentChannel(PayRelatedConstants.PAYMENT_METHOD_SWIFTPASS).setPaymentTerminal(PayRelatedConstants.PAYMENT_TERMINAL_APP)
                        .setMoney(new BigDecimal(pay.getActualPrice())).setMemo(pay.getMemo()).setCommunityId(communityId).setCommunityName(pay.getCommunityName())
                        .setGoodsName(PayRelatedConstants.GOODSNAME_FOR_PROPERTY).setBalanceStatus(0);
                ChargeResponse recordResponse = payRecordClient.insert(payRecord);
                if (!recordResponse.isSuccess()) {
                    log.info("{}|生活缴费-写入交易记录异常", LogCategoryEnum.BUSSINESS, recordResponse.getMessage());
                    return new ChargePayResponse(YueConstants.CODE_FAILED, "写入交易记录异常" + recordResponse.getMessage());
                }*/
            }

            map.put("out_trade_no", orderNum);
            map.put("orderNum", orderNum);
            // 返回结果
            if (!allDisFlag && disPointsFlag) {
                //异步发送积分订单确认mq
                pointClient.pointsTradeMq(confirmMqDTO,7200);
            }
            //统一转对象类型
            PayInfoDTO payInfoDTO = BeanCopierWrapper.copy(map, PayInfoDTO.class);
            payInfoDTO.setPayInfo(map.get("pay_info"));
            payInfoDTO.setOutTradeNo(orderNum);
            payInfoDTO.setFullyDeductFlag(allDisFlag);
            PayOrderAdapter.buildCmb(payInfoDTO);
            return new ChargeResponse<>(payInfoDTO);
        } catch (Exception e) {
            if(e instanceof IllegalArgumentException){
                log.warn("下单异常:{}",  e.getMessage(),e);
            }else {
                log.error("下单异常:{}", e.getMessage(),e);
            }
            if (pointsSuccessRedeem) {
                //下单异常：实时退分
                log.error("积分通兑成功，但下单异常，实时退分,errMsg：{}",e.getMessage(), e);
                pointClient.pointsTradeMq(confirmMqDTO,5);
            }
            prePayLockSupport.unlock(lockObjectDTO);
            String msg = "交易异常，请稍后再试";
            if (e instanceof ChargeBusinessException||e instanceof IllegalArgumentException){
                msg = e.getMessage();
            }
            return new ChargeResponse(YueConstants.CODE_FAILED, msg);
        }
    }

    private boolean checkGiftStarCount(String communityId, List<AssetArrearsListDTO> actualList, String pointsMoney) throws ChargeBusinessException {
        if (CollectionUtils.isEmpty(actualList)) {
            //不需要效验赠分
            return false;
        }
        //actualList转换为List<ReceivableAssetBillInfo> assetBillInfos;
        List<ReceivableAssetBillInfo> assetBillInfos = new ArrayList<>();
        actualList.forEach(assetArrearsListDTO -> {
            List<ReceivableBillInfo> receivableBillInfoList = new ArrayList<>();
            ReceivableAssetBillInfo assetBillInfo = new ReceivableAssetBillInfo();
            assetBillInfo.setAssetId(String.valueOf(assetArrearsListDTO.getAssetId()));
            assetArrearsListDTO.getDetailList().stream().filter(assetArrearsDetailDTO -> Objects.equals(assetArrearsDetailDTO.getChargeObject(), ChargeObjTypeEnum.OWNER.getCode()))
                    .forEach(assetArrearsDetailDTO -> {
                ReceivableBillInfo receivableBillInfo = new ReceivableBillInfo();
                receivableBillInfo.setBillId(String.valueOf(assetArrearsDetailDTO.getBillId()));
                receivableBillInfo.setItemId(String.valueOf(assetArrearsDetailDTO.getItemId()));
                receivableBillInfo.setBelongYears(assetArrearsDetailDTO.getBelongYears());
                receivableBillInfo.setItemArrearsAmount(assetArrearsDetailDTO.getItemArrearsAmount());
                receivableBillInfo.setItemPenaltyAmount(assetArrearsDetailDTO.getItemPenaltyAmount());
                receivableBillInfoList.add(receivableBillInfo);
            });
            assetBillInfo.setReceivableBillInfos(receivableBillInfoList);
            assetBillInfos.add(assetBillInfo);
        });
        //调用赠分计算接口获取系统算分结果和giftStarCount比较
        ReceivablePointCalRequest receivablePointCalRequest = new ReceivablePointCalRequest();
        receivablePointCalRequest.setCommunityId(communityId);
        receivablePointCalRequest.setPointsMoney(pointsMoney);
        receivablePointCalRequest.setAssetBillInfos(assetBillInfos);
        ReceivablePointCalResultVo receivablePointCalResultVo = pointsService.calPreStorePointsByReceivableBill(receivablePointCalRequest);
        //将receivablePointCalResultVo转成Map，key是资产id，value是万象星数量
        Map<Long, Integer> assetIdToGiftStarMap = receivablePointCalResultVo.getItems().stream()
                .filter(item -> item.getAssetId() != null && !item.getAssetId().trim().isEmpty())
                .collect(Collectors.toMap(
                        item -> Long.valueOf(item.getAssetId()),
                        ReceivablePointCalResultItemVo::getGiftStarCount
                ));
        Map<Long, Integer> paramStarMap = actualList.stream().collect(Collectors.toMap(AssetArrearsListDTO::getAssetId, AssetArrearsListDTO::getGiftStarCount));
        if (paramStarMap.size() != assetIdToGiftStarMap.size()){
            log.info("万象星入参和计算资产数量不等,paramStarMap={}, assetIdToGiftStarMap={}", paramStarMap.size(), assetIdToGiftStarMap.size());
            return true;
        }

        for (Long assertId : paramStarMap.keySet()) {
            if (!Objects.equals(paramStarMap.get(assertId), assetIdToGiftStarMap.get(assertId))){
                log.info("checkGiftStarCount param:{}, calResult:{}",paramStarMap.get(assertId),assetIdToGiftStarMap.get(assertId));
                return true;
            }
        }
        return false;
    }

    /**
     * description:
     * author: wuChao
     * date: 2023/7/26
     * param [recBillList=应收单信息, allDetail=缴费信息, actualPrice=实际支付金额, pointsMoney=积分抵扣金额]
     * return boolean
     **/
    private boolean checkMoney(List<ReceivableBillDTO> recBillList, List<OrderItemArrears> allDetail, BigDecimal actualPrice, BigDecimal pointsMoney) {
        BigDecimal recArrearsTotal ;
        BigDecimal payAmount;
        BigDecimal recArrears = Optional.ofNullable(recBillList)
                .orElse(new ArrayList<>())
                .stream()
                .map(ReceivableBillDTO::getArrearsAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal recPenaltyArrears = Optional.ofNullable(recBillList)
                .orElse(new ArrayList<>())
                .stream()
                .map(ReceivableBillDTO::getPenaltyArrearsAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal payArrears = Optional.ofNullable(allDetail)
                .orElse(new ArrayList<>())
                .stream()
                .map(e->new BigDecimal(StringUtils.isEmpty(e.getItemArrearsAmount())?"0":e.getItemArrearsAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal payPenaltyArrears = Optional.ofNullable(allDetail)
                .orElse(new ArrayList<>())
                .stream()
                .map(e->new BigDecimal(StringUtils.isEmpty(e.getItemPenaltyAmount())?"0":e.getItemPenaltyAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        recArrearsTotal = recArrears.add(recPenaltyArrears);
        payAmount = payArrears.add(payPenaltyArrears);
        if (recArrears.compareTo(payArrears) != 0) {
            log.info("{}|[生活缴费->金额检验],欠收金额不一致，应支付欠收金额：{},支付欠收金额：{}", LogCategoryEnum.BUSSINESS, recArrears, payArrears);
            return Boolean.FALSE;
        }
        if (recPenaltyArrears.compareTo(payPenaltyArrears) != 0) {
            log.info("{}|[生活缴费->金额检验],欠收金额不一致，应支付违约金欠收金额：{},支付违约金欠收金额：{}", LogCategoryEnum.BUSSINESS, recPenaltyArrears, payPenaltyArrears);
            return Boolean.FALSE;
        }
        BigDecimal paidMoney = actualPrice.add(pointsMoney) ;
        if (recArrearsTotal.compareTo(paidMoney) != 0) {
            log.info("{}|[生活缴费->金额检验],欠收金额不一致，应支付总金额：{},支付总金额：{}", LogCategoryEnum.BUSSINESS, recArrearsTotal, paidMoney);
            return Boolean.FALSE;
        }
        return payAmount.compareTo(recArrearsTotal) == 0;
    }

    public void validatePointsPayParam(String points, BigDecimal pointsToMoney, String pId) throws ChargeBusinessException {
        if (StringUtils.isEmpty(points)) {
            throw  new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "积分抵扣缺失分数值，请重试");
        }
        if (StringUtils.isEmpty(pId)) {
            throw  new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "积分用户ID缺失，请重试");
        }
        //根据积分配置计算是否与抵扣金额一致，目前统一项目层级250分抵扣1块
        BigDecimal point = new BigDecimal(points);
        BigDecimal calPoints = pointsToMoney.multiply(new BigDecimal(250)).setScale(0, RoundingMode.UP);
        if (calPoints.compareTo(point) != 0) {
            throw  new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "积分可抵扣金额与传入抵扣金额不一致");
        }

    }

    /**
     * 积分缴费流水及明细记录
     * @param assetArrearsLists
     * @param disItemIdList   可抵扣的积分费项
     * @param pointsToMoney   积分抵扣金额
     * @param disList
     * @param actualList
     * @throws ParseException
     */
    public void createEquityTransactionBillList(List<AssetArrearsListDTO> assetArrearsLists,List<Long> disItemIdList, BigDecimal pointsToMoney,
                                                                List<AssetArrearsListDTO> disList,List<AssetArrearsListDTO> actualList) throws ParseException {

        //遍历抵扣
        BigDecimal disMoney = pointsToMoney;
        Iterator<AssetArrearsListDTO> it = assetArrearsLists.iterator();
        while (it.hasNext()) {
            AssetArrearsListDTO dto = it.next();
            List<AssetReceivalbeBillListDTO> detailList = dto.getDetailList();
            detailList = detailList.stream().sorted(Comparator.comparing(AssetReceivalbeBillListDTO::getBelongYears,Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
            BigDecimal houseTotalAmount  = dto.getAssetTotalAmount();
            BigDecimal pointsDisHouseAmount = BigDecimal.ZERO;
            BigDecimal leftHouseAmount = houseTotalAmount;
            List<AssetReceivalbeBillListDTO> disDTOList = new ArrayList<>(detailList.size()+1);
            List<AssetReceivalbeBillListDTO> leftDTOList = new ArrayList<>(detailList.size()+1);
            for (int i = 0, length = detailList.size(); i < length; i++) {
                AssetReceivalbeBillListDTO recDTO  = detailList.get(i);
                AssetReceivalbeBillListDTO dis = BeanCopierWrapper.copy(recDTO,AssetReceivalbeBillListDTO.class);
                AssetReceivalbeBillListDTO left = BeanCopierWrapper.copy(recDTO,AssetReceivalbeBillListDTO.class);
                if (disItemIdList.contains(recDTO.getItemId())) {
                    //可抵扣金额
                    BigDecimal arrearsAmount = recDTO.getItemArrearsAmount();
                    if (BigDecimal.ZERO.compareTo(disMoney) == 0 || BigDecimal.ZERO.compareTo(arrearsAmount) >=0) {
                        //没有积分/欠费可抵扣
                        leftDTOList.add(left);
                    }else if (disMoney.compareTo(arrearsAmount) < 0) {
                        //欠费部分抵扣
                        pointsDisHouseAmount = pointsDisHouseAmount.add(disMoney);
                        dis.setItemArrearsAmount(disMoney);
                        dis.setItemPenaltyAmount(BigDecimal.ZERO);
                        disDTOList.add(dis);
                        left.setItemArrearsAmount(arrearsAmount.subtract(disMoney));
                        leftHouseAmount = leftHouseAmount.subtract(disMoney);
                        leftDTOList.add(left);
                        disMoney = BigDecimal.ZERO;
                        continue;
                    } else {
                        //欠费全部抵扣
                        pointsDisHouseAmount = pointsDisHouseAmount.add(arrearsAmount);
                        dis.setItemArrearsAmount(arrearsAmount);
                        dis.setItemPenaltyAmount(BigDecimal.ZERO);
                        disDTOList.add(dis);
                        leftHouseAmount = leftHouseAmount.subtract(arrearsAmount);
                        if (BigDecimal.ZERO.compareTo(recDTO.getItemPenaltyAmount()) < 0) {
                            left.setItemArrearsAmount(BigDecimal.ZERO);
                            leftDTOList.add(left);
                        }
                        disMoney = disMoney.subtract(arrearsAmount);

                    }

                } else {
                    //不能积分抵扣
                    leftDTOList.add(left);
                }

            }
            //组装数据
            AssetArrearsListDTO disAssetDTO = BeanCopierWrapper.copy(dto,AssetArrearsListDTO.class);
            AssetArrearsListDTO leftAssetDTO = BeanCopierWrapper.copy(dto,AssetArrearsListDTO.class);
            if (!CollectionUtils.isEmpty(disDTOList)) {
                disAssetDTO.setPaymentMethod(PaymentMethodEnum.EQUITY.getPaymentCode());
                disAssetDTO.setDetailList(disDTOList);
                disAssetDTO.setIsBalance(IsBalanceEnum.NO.getCode());
                disAssetDTO.setAssetTotalAmount(pointsDisHouseAmount);
                disAssetDTO.setPaymentType(PaymentTypeEnum.POINTS.getCode());
                disList.add(disAssetDTO);
            }
            leftDTOList = leftDTOList.stream().filter(i->i.getItemArrearsAmount().add(i.getItemPenaltyAmount()).compareTo(BigDecimal.ZERO) > 0
                    && i.getItemArrearsAmount().compareTo(BigDecimal.ZERO)>=0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(leftDTOList)) {
                leftAssetDTO.setDetailList(leftDTOList);
                leftAssetDTO.setAssetTotalAmount(leftHouseAmount);
                leftAssetDTO.setPaymentType(PaymentTypeEnum.CASH.getCode());
                actualList.add(leftAssetDTO);
            }

        }
        log.info("{}|生活缴费-添加积分账单流水明细：积分抵扣disList:{} ",LogCategoryEnum.BUSSINESS, disList);
        log.info("{}|生活缴费-添加积分账单流水明细：实收actualList:{} ",LogCategoryEnum.BUSSINESS,actualList);
    }

    /**
     * description: 查询应收单 单据状态不为已生效的数据
     * author: wuChao
     * date: 2023/4/28
     * param [receivableBillDTOS]
     * return boolean
     **/
    private boolean checkBillStatusNoEffective(List<ReceivableBillDTO> receivableBillDTOS){
        long count = receivableBillDTOS.stream().filter(x -> !Objects.equals(x.getBillStatus(), ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode())).count();
        return count > 0;
    }

}
