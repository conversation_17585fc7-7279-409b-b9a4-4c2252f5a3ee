package com.charge.api.web.vo.ebusiness;

import com.charge.api.web.vo.BaseCommunityQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/13 17:44
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EBusinessItemCondition extends BaseCommunityQuery {

    /**
     * 业务类型（1-自营线上销售服务，2-到家服务，3-装修服务，4 意向金/定金  5/佣金）
     */
    private Integer type;
}
