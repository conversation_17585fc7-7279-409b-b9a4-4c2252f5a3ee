package com.charge.api.web.vo.pos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 费项价格
 *
 * <AUTHOR>
 * @date 2023/3/7
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class ChargeItemPriceV2 {
    /**
     * 应收单id
     */
    @NotNull(message = "收费项id不能为空")
    private String itemID;
    @NotBlank
    private String itemName;
    @NotNull(message = "收费项金额不能为空")
    private String price;
}


