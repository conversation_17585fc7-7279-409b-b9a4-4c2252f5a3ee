package com.charge.api.web.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单进度单vo
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderPayVO extends BaseOrderVO {

    private static final long serialVersionUID = 3984622926506892690L;


    /**
     * 子订单号
     */
    private String subOrderNo;


    /**
     * 应付金额
     */
    private BigDecimal amount;

    /**
     * 已付金额
     */
    private BigDecimal paidAmount;


    /**
     * 支付方式 1转账，2支付宝，3微信
     *
     */
    private Integer payMethod;

    /**
     * 支付渠道（BANK银行、TRANSFER_OFFLINE线下转帐、ALI支付宝、
     * WECHAT微信）
     */
    private String payChannel;

    /**
     * 支付终端（收费系统、拉卡拉、朝昔、停车场等）
     */
    private String payTerminal;

    /**
     * 支付状态, 0-未支付，1-已支付，2-部分支付
     */
    private Integer payStatus;

    /**
     * 支付模式：1-分期支付，2-全额支付
     */
    private Integer payMode;

    /**
     * 支付完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    /**
     * 付款客户名称
     */
    private String customerName;

}
