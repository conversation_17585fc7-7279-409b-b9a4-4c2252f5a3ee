package com.charge.api.web.controller.arrearsnotice;

import com.charge.api.web.service.bill.joy.ArrearsNoticeService;
import com.charge.api.web.vo.arrearsnotice.*;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.starter.web.annotation.Idempotent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 缴费通知单控制器
 * 缴费通知单相关功能
 *
 * <AUTHOR>
 * @date 2024/8/21
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping(value = "arrears-notice")
public class ArrearsNoticeController {


    private final ArrearsNoticeService arrearsNoticeService;


    /**
     * 查询缴费通知单
     * 用于滚动查询通知单，
     *
     * @param query
     * @return com.charge.common.dto.ChargeResponse<java.util.List < com.charge.api.web.vo.arrearsnotice.ArrearsNoticeVO>>
     * <AUTHOR>
     * @date 2024/8/21
     */
    @PostMapping("list")
    public ChargeResponse<List<ArrearsNoticeVO>> list(@RequestBody @Valid ArrearsNoticeQuery query) throws ChargeBusinessException {
        return new ChargeResponse<>(arrearsNoticeService.list(query));
    }

    /**
     * 查询缴费通知单详情,
     * withPayUrl是ture时还会返回缴费二维码的url
     *
     * @param query
     * @return com.charge.common.dto.ChargeResponse<com.charge.api.web.vo.arrearsnotice.ArrearsNoticeDetailVO>
     * <AUTHOR>
     * * @date 2024/8/21
     */
    @PostMapping("detail")
    public ChargeResponse<ArrearsNoticeDetailVO> detail(@RequestBody @Valid ArrearsNoticeDetailQuery query) throws ChargeBusinessException {
        return new ChargeResponse<>(arrearsNoticeService.detail(query));
    }

    /**
     * 查询缴费通知单详情,
     * withPayUrl是ture时还会返回缴费二维码的url
     *
     * @param query
     * @return com.charge.common.dto.ChargeResponse<com.charge.api.web.vo.arrearsnotice.ArrearsNoticeDetailVO>
     * <AUTHOR>
     * * @date 2024/8/21
     */
    @PostMapping("detail/v2")
    public ChargeResponse<ArrearsNoticeDetailVO2> detailV2(@RequestBody @Valid ArrearsNoticeDetailQuery query) throws ChargeBusinessException {
        return new ChargeResponse<>(arrearsNoticeService.detailV2(query));
    }

    /**
     * 缴费通知单下单
     *
     * @param noticeCreateOrderReq
     * @return com.charge.common.dto.ChargeResponse<com.charge.api.web.vo.arrearsnotice.ArrearsNoticeCreateOrderResp>   下单响应
     * @throws com.charge.common.exception.ChargeBusinessException
     * <AUTHOR>
     * @date 2024/8/21
     */
    @PostMapping("create-order")
    @Idempotent
    public ChargeResponse<ArrearsNoticeCreateOrderResp> createOrder(@RequestBody @Valid ArrearsNoticeCreateOrderReq noticeCreateOrderReq) throws ChargeBusinessException {
        ArrearsNoticeCreateOrderResp createOrderResp = arrearsNoticeService.createBill(noticeCreateOrderReq);
        return new ChargeResponse<>(createOrderResp);
    }

    /**
     * 缴费通知单支付结果查询
     *
     * @param query
     * @return com.charge.common.dto.ChargeResponse<com.charge.api.web.vo.arrearsnotice.ArrearsNoticePayResultVO>
     * @throws com.charge.common.exception.ChargeBusinessException
     * <AUTHOR>
     * @date 2024/8/21
     */
    @PostMapping("pay-result")
    public ChargeResponse<ArrearsNoticePayResultVO> getPayResult(@RequestBody @Valid ArrearsNoticeDetailQuery query) throws ChargeBusinessException {
        return new ChargeResponse<>(arrearsNoticeService.getPayResult(query));
    }

}