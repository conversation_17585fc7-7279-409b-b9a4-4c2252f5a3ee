package com.charge.api.web.vo.arrearsnotice;

import com.charge.common.serializer.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 缴费通知单详情
 *
 * <AUTHOR>
 * @date 2024/8/22
 */
@Data
public class ArrearsNoticeDetailVO2 {

    /**
     * 城市公司
     */
    private String orgName;

    /**
     * 项目名称
     */
    private String communityName;

    /**
     * 总金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalAmount;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 用户名称
     */
    private String customerName;

    /**
     * 举报电话
     */
    private String reportPhone;

    /**
     * 举报邮件
     */
    private String reportEmail;

    /**
     * 收款账户
     */
    private String acceptorAccountName = "默认收款账户";

    /**
     * 虚拟结算账户
     */
    private String virtualSettlementAccount = "默认虚拟结算账户";

    /**
     * pos商户号
     */
    private String posMerchantNum = "默认pos商户";

    /**
     * 收款商户号
     */
    private String acceptorMerchantNumAccount = "默认收款商户号";

    /**
     * 详情
     */
    private List<ArrearsNoticeReceivableVO2> details;

    /**
     * 支付链接
     */
    private String payUrl;

    /**
     * 应收金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalReceivableAmount;

    /**
     * 已收金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalIncomeAmount;

    /**
     * 欠收金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalArrearsAmount;

    /**
     * 违约金欠费金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalPenaltyArrearsAmount;

}