package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
public class SystemChargeItemVO implements Serializable {
    private static final long serialVersionUID = -7064020898680071328L;

    public static final String ENABLED="1";//启用状态

    public static final String DISABLE="0";//禁用状态

    private String id;

    private String itemUuid;
    /**
     * 收费项编码
     */
    private String itemCode;
    /**
     * 收费名称
     */
    private String itemName;
    /**
     * 收费类型  deposit=押金类   propertyService=物业服务类(周期型)   temporaryCharge=临时收费类
     */
    private String typeUuid;
    /**
     * 收费类型名称
     */
    private String typeName;
    /**
     * 计费类别id（计费模型ChargeType）   general=常规类   meter=仪表类   pool=分摊类（公摊）
     */
    private String billingTypeId;
    /**
     * 计费类别名称
     */
    private String billingTypeName;

    /**
     * 计费模型id  fixed=固定型   meter=仪表型（阶梯、自然月）  routine=常规型(普通)
     */
    private String billingId;
    /**
     * 计费模型
     */
    private String billingName;

    /**
     * 备注
     */
    private String memo;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date effectiveTime;
    /**
     * 税点
     */
    private BigDecimal taxPoint;
    /**
     * 税率小数点 默认两位小数点 2
     */
    private Integer taxDec;
    /**
     * 仪表类型（需要确认）
     */
    private String meterType;
    /**
     * 仪表类型id  e7422c50-e941-4fc6-8df1-34d0883948af=水表   c9d10e2a-130e-4178-9e91-7cd7af97afaa=燃气表   a95effae-7fc8-4e2c-b9fa-fd70233eea23=电表
     */
    private String meterTypeId;
    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 删除标识,0正常,1删除
     */
    private String dr;
    /**
     * 时间戳，每次更新数据自动更新
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTs;
    /**
     * 数据创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTs;
    /**
     * 租户id
     */
    @JsonIgnore
    private String cropId;
    /**
     * 应用id
     */
    @JsonIgnore
    private String appId;
    /**
     * 仪表数值单位
     */
    private String unit;

    /**
     * 禁用启用: 0禁用,1启用
     */
    private String status;
    /**
     * 小区id
     */
    private String communityUuid;
    /**
     * 总部收费项关联税率配置id（2.0有两个配置）
     */
    private String commonConfigUuid;
    /**
     * 总部收费项关联税率
     */
    private BigDecimal commonTaxRate;
    /**
     * 项目收费项关联税率配置id
     */
    private String communityConfigUuid;
    /**
     * 项目收费项关联税率
     */
    private BigDecimal communityTaxRate;
    /**
     * 税率名称
     */
    private String taxHeadName;
    /**
     * 可否折扣：0=允许折扣，1=禁止折扣
     */
    private String isDiscount;
    /**
     * 结转状态 null 未知，1 允许，0不允许
     */
    private Integer forwardStatus;
    /**
     * 是否积分抵扣 null 未知，1 允许，0不允许
     */
    private Integer pointsDiscountStatus;
    /**
     * 是否积分抵扣规则json：默认{"price":"1","points":"250"}
     */
    private String pointsDiscountRule;

}