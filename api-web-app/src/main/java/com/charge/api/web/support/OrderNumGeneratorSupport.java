package com.charge.api.web.support;

import com.charge.common.support.IdGeneratorSupport;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/01/05
 */
public class OrderNumGeneratorSupport {

    private static ThreadLocal<String> ORDER_NUM_TL = new ThreadLocal<>();

    /**
     * 生成
     * @return
     */
    public static String gen() {
        String orderNum = IdGeneratorSupport.getIstance().nextId();
        ORDER_NUM_TL.set(orderNum);
        return orderNum;
    }

    /**
     * 存在则获取，否则生成
     * @return
     */
    public static String getOrGen() {
        String orderNum = ORDER_NUM_TL.get();
        if (StringUtils.isNotBlank(orderNum)) {
            return orderNum;
        }
        return gen();
    }

    public static void clear() {
        ORDER_NUM_TL.remove();
    }
}
