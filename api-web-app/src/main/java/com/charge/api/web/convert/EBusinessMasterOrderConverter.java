package com.charge.api.web.convert;

import com.charge.api.web.vo.order.*;
import com.charge.common.util.DateMapper;
import com.charge.order.dto.ebuiness.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;


/**
 * 缴费通知单转换器
 * <AUTHOR>
 * @description
 * @date 2023/02/28
 */
@Mapper(uses = {DateMapper.class,OrderEnumMapper.class })
public interface EBusinessMasterOrderConverter {

    EBusinessMasterOrderConverter INSTANCE = Mappers.getMapper(EBusinessMasterOrderConverter.class);

    EBusinessOrderQuery map(OrderQuery orderQuery);

    @Mapping(target = "order",source = "masterOrder")
    @Mapping(target = "pays",source = "baseOrders")
    @Mapping(target = "adjusts",ignore = true)
    OrderDetailVO map(EBusinessOrderDetailDTO detailDTO);

    @Mapping(target = "orderNo",source = "extendOrderNo")
    @Mapping(target = "paidAmount",source = "totalPaidAmount")
    @Mapping(target = "payMode",source = "paymentMode")
    OrderVO map(EBusinessMasterOrderDTO masterOrderDTO);

    @Mapping(target = "subOrderNo",source = "extendOrderId")
    @Mapping(target = "orderNo",source = "extendOrderNo")
    @Mapping(target = "paidAmount",source = "totalPaidAmount")
    @Mapping(target = "amount",expression = "java(order.getTotalAmount().add(order.getDiscountAmount()))")
    @Mapping(target = "payMode",source = "paymentMode")
    @Mapping(target = "payTime",source = "paidAt")
    OrderPayVO map(EBusinessBaseOrderDTO order);

    @Mapping(target = "amount",source = "adjustAmount")
    OrderAdjustVO map(EBusinessOrderAdjustDTO adjustDTO);







}
