package com.charge.api.web.controller.joylife.bill;

import com.charge.api.web.service.joylife.PrestoreGroupService;
import com.charge.api.web.vo.DeliveryGroupAssetCondition;
import com.charge.api.web.vo.DeliveryGroupCommunityCondition;
import com.charge.api.web.vo.DeliveryGroupResponse;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.joylife.dto.DeliveryPayDataRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * Author: yjw
 * Date: 2023/3/7 16:23
 */

@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(value = "专项预存组合（含云交付）相关接口")
public class PrestoreGroupController {

    private final PrestoreGroupService prestoreGroupService;

    @ApiOperation(value = "专项预存组合-云交付获取标的物（房屋、⻋位、商铺）预存订单详情", notes = "JAVA类:com.chargeProject.consumer.interfaceX.DeliveryService" +
            "函数签名:ResultContent getOrderDetail(String communityUuid, String propertyType);")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityMsId", value = "项目ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseMsId", value = "标的物ID（房屋、车位、商铺）", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "propertyType", value = "资产类型（house-房屋，parking-车位，shop-商铺）", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/delivery/orderDetail", method = {RequestMethod.GET})
    public ChargeResponse getOrderDetail(String communityMsId, String houseMsId, String propertyType) throws ChargeBusinessException{

        return prestoreGroupService.getOrderDetail(communityMsId, houseMsId, propertyType);
    }

    @ApiOperation(value = "云交付-专项预存组合预支付")
    @RequestMapping(value = "/delivery/createPay", method = {RequestMethod.POST})
    public ChargeResponse createPay(@RequestBody @Validated DeliveryPayDataRequest payRequest) throws ChargeBusinessException {
        return prestoreGroupService.createPay(payRequest);
    }

    /**
     * 专项预存组合-判断批量项目是否配置云交付组合
     * @param deliveryGroupCommunityCondition
     * @return
     * @throws ChargeBusinessException
     */
    @RequestMapping(value = "/delivery/community/batchQuery", method = {RequestMethod.POST})
    public ChargeResponse<DeliveryGroupResponse> batchQueryGroupByCommunity(@RequestBody @Validated DeliveryGroupCommunityCondition deliveryGroupCommunityCondition) throws ChargeBusinessException{

        return prestoreGroupService.batchQueryGroupByCommunity(deliveryGroupCommunityCondition);
    }

    /**
     * 专项预存组合-判断批量资产是否配置云交付组合(即资产是否有相应计费配置)
     * @param deliveryGroupAssetCondition
     * @return
     * @throws ChargeBusinessException
     */
    @RequestMapping(value = "/delivery/asset/batchQuery", method = {RequestMethod.POST})
    public ChargeResponse<DeliveryGroupResponse> batchQueryGroupByAsset(@RequestBody @Validated DeliveryGroupAssetCondition deliveryGroupAssetCondition) throws ChargeBusinessException{

        return prestoreGroupService.batchQueryGroupByAsset(deliveryGroupAssetCondition);
    }
}
