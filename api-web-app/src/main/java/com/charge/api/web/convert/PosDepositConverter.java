package com.charge.api.web.convert;

import com.charge.api.web.vo.pos.DepositDetailVO;
import com.charge.api.web.vo.pos.DepositVO;
import com.charge.bill.dto.predeposit.pos.PosDepositDetailDTO;
import com.charge.bill.dto.predeposit.pos.PosDepositInfoDTO;
import com.charge.bill.dto.predeposit.pos.PosDepositRefundDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/05/18
 */
@Mapper
public interface PosDepositConverter {

    PosDepositConverter INSTANCE = Mappers.getMapper(PosDepositConverter.class);

    @Mappings({
            @Mapping(target = "id", source = "dto.predepositAccountId"),
            @Mapping(target = "balance", source = "dto.availableBalance"),
            @Mapping(target = "money", source = "dto.totalBalance"),
            @Mapping(target = "itemId", source = "dto.predepositItemId"),
            @Mapping(target = "itemName", source = "dto.predepositItemName"),
            @Mapping(target = "payMember", source = "dto.payMember"),
            @Mapping(target = "createTime", source = "dto.payTime"),
            @Mapping(target = "arrivalTime", source = "dto.payTime"),
            @Mapping(target = "receiptNumber", source = "dto.orderNum"),
            @Mapping(target = "householdId", source = "dto.userId"),
            @Mapping(target = "householdName", constant = "*"),
            @Mapping(target = "houseUuid", source = "dto.assetId"),
            @Mapping(target = "houseName", source = "dto.assetName"),
            @Mapping(target = "communityUuid", source = "dto.communityId"),
            @Mapping(target = "communityName", source = "dto.communityName"),
            @Mapping(target = "paymentMethod", source = "dto.paymentMethod"),
            @Mapping(target = "payStatus", source = "dto.payStatus"),
            @Mapping(target = "cropId", source = "dto.assetId"),
            @Mapping(target = "url", constant = ""),
            @Mapping(target = "collectorName", source = "dto.payMember", defaultValue = ""),
            @Mapping(target = "draweeName", source = "dto.payMember", defaultValue = ""),
            @Mapping(target = "houseType", constant = "house"),
            @Mapping(target = "paymentType", constant = "0"),
            @Mapping(target = "bankTransactionNo", source = "dto.bankTransactionNo", defaultValue = ""),
            @Mapping(target = "billStatus", constant = "0"),
            @Mapping(target = "enterStatus", source = "dto.enterStatus"),
            @Mapping(target = "memo", source = "dto.memo", defaultValue = ""),
            @Mapping(target = "customerCode", constant = ""),
            @Mapping(target = "incomeDetailUuid", constant = ""),
            @Mapping(target = "incomeDetailName", constant = ""),
    })
    DepositVO toDepositVO(PosDepositInfoDTO dto);

    List<DepositVO> toDepositVOList(List<PosDepositInfoDTO> dto);

    @Mappings({
            @Mapping(target = "itemId", source = "dto.predepositItemId"),
            @Mapping(target = "draweeName", source = "dto.payMember"),
            @Mapping(target = "itemName", source = "dto.predepositItemName"),
            @Mapping(target = "money", source = "dto.totalBalance"),
            @Mapping(target = "balance", source = "dto.availableBalance"),
            @Mapping(target = "payTime", source = "dto.payTime"),
            @Mapping(target = "receiptNumber", source = "dto.orderNum"),
            @Mapping(target = "detail", source = "dto.refundDetailDTOList"),
    })
    DepositDetailVO toDepositDetailVO(PosDepositDetailDTO dto);

    @Mappings({
            @Mapping(target = "cropId", constant = ""),
            @Mapping(target = "householdId", constant = ""),
            @Mapping(target = "householdName", constant = ""),
            @Mapping(target = "houseId", source = "dto.assetId"),
            @Mapping(target = "houseName", source = "dto.assetName"),
            @Mapping(target = "areaId", source = "dto.communityId"),
            @Mapping(target = "areaName", source = "dto.communityName"),
            @Mapping(target = "itemId", source = "dto.itemId"),
            @Mapping(target = "itemName", source = "dto.itemName"),
            @Mapping(target = "orderNum", source = "dto.refundBillNo"),
            @Mapping(target = "houseType", constant = "house"),
            @Mapping(target = "refundType", constant = "0"),
            @Mapping(target = "refundStyle", constant = "0"),
            @Mapping(target = "appAccount", constant = ""),
            @Mapping(target = "bankInfo", constant = ""),
            @Mapping(target = "paymentId", constant = ""),
            @Mapping(target = "rejected", constant = ""),
            @Mapping(target = "employeeName", source = "dto.employeeName"),
            @Mapping(target = "employeeId", constant = ""),
            @Mapping(target = "advanced", constant = ""),
            @Mapping(target = "confirmName", constant = "报账平台"),
            @Mapping(target = "confirmId", constant = ""),
            @Mapping(target = "caseId", constant = ""),
            @Mapping(target = "incomeDetailUuid", constant = ""),
            @Mapping(target = "incomeDetailName", constant = ""),
            @Mapping(target = "id", source = "dto.predepositRefundId"),
            @Mapping(target = "money", source = "dto.refundAmount"),
            @Mapping(target = "operator", source = "dto.createUser"),
            @Mapping(target = "operatorId", source = "dto.createUser"),
            @Mapping(target = "createTime", source = "dto.refundApplyTime"),
            @Mapping(target = "resultTime", source = "dto.refundSuccessTime"),
            @Mapping(target = "depositStatus", expression = "java(com.charge.api.web.service.pos.impl.DepositServiceImpl.convertRefundStatus(dto.getStatus()))"),
            @Mapping(target = "bankCard", source = "dto.payeeBankAccount"),
            @Mapping(target = "remark", source = "dto.refundRemark"),
    })
    DepositDetailVO.DepositRefundVO toDepositRefundVO(PosDepositRefundDetailDTO dto);

    List<DepositDetailVO.DepositRefundVO> toDepositRefundVOList(List<PosDepositRefundDetailDTO> dtoList);

}
