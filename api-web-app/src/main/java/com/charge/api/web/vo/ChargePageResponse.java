package com.charge.api.web.vo;

import com.charge.common.dto.ChargeResponse;
import lombok.Data;

@Data
public class ChargePageResponse<T> extends ChargeResponse<T> {
    private Integer pageSize;
    private Integer totalPage;
    private Integer currentPage;
    private Integer totalRecord;

    public ChargePageResponse(T content, Integer pageSize, Integer totalPage, Integer currentPage, Integer totalRecord) {
        super(content);
        this.pageSize = pageSize;
        this.totalPage = totalPage;
        this.currentPage = currentPage;
        this.totalRecord = totalRecord;
    }

    public ChargePageResponse(Integer code, String message) {
        super(code, message);
    }
}
