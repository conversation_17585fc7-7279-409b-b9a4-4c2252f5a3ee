package com.charge.api.web.config;

import java.io.Serializable;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/24
 */
@ConfigurationProperties(prefix = "parking")
@Configuration
@RefreshScope
@Data
public class ParkingPlatformConfig implements Serializable {

    private static final long serialVersionUID = 193190513164336584L;

    private ParkingPlatformItemConfig v1;

    private ParkingPlatformItemConfig v2;

}
