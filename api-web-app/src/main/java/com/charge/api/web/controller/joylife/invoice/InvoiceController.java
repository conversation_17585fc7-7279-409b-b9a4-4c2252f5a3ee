package com.charge.api.web.controller.joylife.invoice;

import com.alibaba.fastjson.JSONObject;
import com.charge.api.web.convert.InvoiceConverter;
import com.charge.api.web.dto.invoice.InvoiceApplyResultDTO;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.support.CustomerSupport;
import com.charge.api.web.vo.joylife.CustomerSimpleVO;
import com.charge.api.web.vo.joylife.invoice.*;
import com.charge.api.web.vo.joylife.request.ZhaoXiAssertRequest;
import com.charge.common.constant.CommonConstant;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.core.util.CollectionUtil;
import com.charge.invoice.client.InvoiceApplyClient;
import com.charge.invoice.dto.*;
import com.charge.invoice.enums.BusinessBillTypeEnum;
import com.charge.invoice.service.InvoiceAppService;
import com.charge.invoice.support.ChargeItemTaxSupport;
import com.charge.invoice.vo.InvoiceApplyResultVO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.order.enums.OrderRuleCategoryEnum;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 朝夕-发票接口
 *
 * <AUTHOR> Wen
 * @date 2023/9/11 18:47
 */
@Slf4j
@Api(tags = {"invoiceController-app"})
@RestController
@RequestMapping(value = "/app")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InvoiceController {

    private final InvoiceAppService invoiceAppService;
    private final InvoiceApplyClient invoiceApplyClient;
    private final CommunitySupport communitySupport;
    private final AssetSupport assetSupport;
    private final CustomerSupport customerSupport;
    private final ThreadPoolTaskExecutor chargeCommonExecutor;
    private final  ChargeItemTaxSupport chargeItemTaxSupport;


    /**
     * 应收单-可开票账单查询-单资产
     */
    @PostMapping("invoice/receivable/bill/list")
    public ChargeResponse<List<InvoiceBillDTO>> findReceivableBillList(@Valid @RequestBody InvoiceBillQueryDTO invoiceBillQueryDTO) throws ChargeBusinessException {
        return new ChargeResponse<>(invoiceAppService.findReceivableBillList(invoiceBillQueryDTO));
    }

    /**
     * 应收单-可开票账单查询-多资产
     */
    @PostMapping("invoice/receivable/multi-asset/bill/list")
    public ChargeResponse<List<InvoiceMultiAssetReceivableBillVO>> findMultiAssetReceivableBillList(@Valid @RequestBody InvoiceMultiAssetReceivableBillRequestVO invoiceBillQueryDTO) throws ChargeBusinessException {
        ImmutablePair<CommunityDTO, Map<Long, AssetAdapter>> checkedParam= checkAssetParam(invoiceBillQueryDTO.getCommunityMsId(),invoiceBillQueryDTO.getAssetList());
        CommunityDTO communityInfo=checkedParam.getLeft();
        Map<Long, AssetAdapter> assetAdapterMap=checkedParam.getRight();
        InvoiceMultiAssetQueryDTO condition = new InvoiceMultiAssetQueryDTO();
        condition.setCommunityId(communityInfo.getId());
        condition.setAssetIds(new ArrayList<>(assetAdapterMap.keySet()));
        condition.setBeginTime(invoiceBillQueryDTO.getBeginTime());
        condition.setEndTime(invoiceBillQueryDTO.getEndTime());
        List<InvoiceBillDTO> invoiceBillDTOList = invoiceAppService.findMultiAssetReceivableBillList(condition);
        if (CollectionUtil.isEmpty(invoiceBillDTOList)) {
            return new ChargeResponse<>(Collections.EMPTY_LIST);
        }
        Map<Long, List<InvoiceBillDTO>> assetBillMap = invoiceBillDTOList.stream().collect(Collectors.groupingBy(InvoiceBillDTO::getAssetId));
        List<InvoiceMultiAssetReceivableBillVO> result = new ArrayList<>();
        for (Map.Entry<Long, List<InvoiceBillDTO>> entry : assetBillMap.entrySet()) {
            Long key = entry.getKey();
            List<InvoiceBillDTO> value = entry.getValue();
            InvoiceMultiAssetReceivableBillVO assetBill = new InvoiceMultiAssetReceivableBillVO();
            assetBill.setAssetId(key);
            AssetAdapter assetInfo = assetAdapterMap.get(key);
            assetBill.setInvoiceBillDTOList(value);
            assetBill.setAssetMsId(assetInfo.getMsId());
            assetBill.setAssetType(assetInfo.getType());
            assetBill.setHouseName(assetInfo.getAssetName());
            assetBill.setCommunityId(communityInfo.getId());
            assetBill.setCommunityName(communityInfo.getName());
            assetBill.setCommunityMsId(communityInfo.getMsId());
            assetBill.setCustomerSimpleVOS(BeanCopierWrapper.copy(assetInfo.getListCustomer(), CustomerSimpleVO.class));
            result.add(assetBill);
        }
        return new ChargeResponse<>(result);
    }

    private ImmutablePair<CommunityDTO,  Map<Long, AssetAdapter>> checkAssetParam(String communityMsId, List<ZhaoXiAssertRequest>  assetList) throws ChargeBusinessException {
        CommunityDTO communityInfo = communitySupport.getCommunityByMsId(communityMsId);
        if (Objects.isNull(communityInfo)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "查无该小区");
        }
        Map<Long, AssetAdapter> assetAdapterMap = assetSupport.fillAssetIds(assetList, communityInfo.getId(),false);
        if (CollectionUtil.isEmpty(assetAdapterMap.values())) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "查询资产信息为空");
        }
        Set<Long> assetIds = assetAdapterMap.keySet();
        Set<ZhaoXiAssertRequest> assetRequests=new HashSet<>(assetList);
        if(!Objects.equals(assetIds.size(),assetRequests.size())){
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "输入资产个数部分查询为空");
        }
        return new  ImmutablePair(communityInfo,assetAdapterMap);
    }


    /**
     *所有单据-可开票多类账单查询-多资产
     */
    @PostMapping("invoice/multi-asset/bill/list")
    public ChargeResponse<List<InvoiceMultiAssetAllBillVO>> findMultiAssetAllBillList(@Valid @RequestBody InvoiceMultiAssetReceivableBillRequestVO invoiceBillQueryDTO) throws ChargeBusinessException {
        if(CollectionUtil.isEmpty(invoiceBillQueryDTO.getBusinessBillTypeList())){
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "可开票单据类型不能为空");
        }
        ImmutablePair<CommunityDTO, Map<Long, AssetAdapter>> checkedParam= checkAssetParam(invoiceBillQueryDTO.getCommunityMsId(),invoiceBillQueryDTO.getAssetList());
        CommunityDTO communityInfo=checkedParam.getLeft();
        Map<Long, AssetAdapter> assetAdapterMap=checkedParam.getRight();
        InvoiceMultiAssetQueryDTO condition=buildMultiAssetConditionDTO(invoiceBillQueryDTO,communityInfo,assetAdapterMap);
        Map<Long, List<InvoiceBillDTO>> receivableBillAssetMap= Maps.newHashMap();
        if(invoiceBillQueryDTO.getBusinessBillTypeList().contains(BusinessBillTypeEnum.RECEIVABLE.getCode())){
            List<InvoiceBillDTO> invoiceBillDTOListReceivable = invoiceAppService.findMultiAssetReceivableBillList(condition);
            if (CollectionUtil.isNotEmpty(invoiceBillDTOListReceivable)) {
                receivableBillAssetMap = invoiceBillDTOListReceivable.stream().filter(item->Objects.nonNull(item.getAssetId())).collect(Collectors.groupingBy(InvoiceBillDTO::getAssetId));
            }
        }
        Map<Long, List<InvoiceBillDTO>> predepositBillAssetMap= Maps.newHashMap();
        if(invoiceBillQueryDTO.getBusinessBillTypeList().contains(BusinessBillTypeEnum.PREDEPOSIT.getCode())){
            List<InvoiceBillDTO> invoiceBillDTOListPrestore = invoiceAppService.findMultiAssetPredpositBillList(condition);
            if (CollectionUtil.isNotEmpty(invoiceBillDTOListPrestore)) {
                predepositBillAssetMap = invoiceBillDTOListPrestore.stream().filter(item->Objects.nonNull(item.getAssetId())).collect(Collectors.groupingBy(InvoiceBillDTO::getAssetId));
            }
        }
        Map<Long, List<InvoiceOrderBillDTO>> orderBillAssetMap= Maps.newHashMap();
        if(invoiceBillQueryDTO.getBusinessBillTypeList().contains(BusinessBillTypeEnum.ORDER.getCode())){
            List<InvoiceOrderBillDTO> invoiceBillDTOListOrder = invoiceAppService.findMultiAssetOrderBillList(condition);
            if (CollectionUtil.isNotEmpty(invoiceBillDTOListOrder)) {
                orderBillAssetMap = invoiceBillDTOListOrder.stream().filter(item->Objects.nonNull(item.getAssetId())).collect(Collectors.groupingBy(InvoiceOrderBillDTO::getAssetId));
            }
        }
        List<InvoiceMultiAssetAllBillVO> result = new ArrayList<>();
        for ( Map.Entry<Long, AssetAdapter> entry : assetAdapterMap.entrySet()) {
            Long key = entry.getKey();
            List<InvoiceBillDTO> receivableList =receivableBillAssetMap.getOrDefault(key, new ArrayList<>());
            List<InvoiceBillDTO> predepositList =predepositBillAssetMap.getOrDefault(key, new ArrayList<>());
            List<InvoiceOrderBillDTO> orderList =orderBillAssetMap.getOrDefault(key, new ArrayList<>());
            InvoiceMultiAssetAllBillVO assetBill = this.buildMultiAssetAllBillVO(communityInfo,entry.getValue(),receivableList, predepositList, orderList);
            result.add(assetBill);
        }
        return new ChargeResponse<>(result);

    }

    private InvoiceMultiAssetAllBillVO buildMultiAssetAllBillVO(CommunityDTO communityInfo,AssetAdapter assetInfo, List<InvoiceBillDTO> receivableList, List<InvoiceBillDTO> predepositList, List<InvoiceOrderBillDTO> orderList) {
        InvoiceMultiAssetAllBillVO assetBill = new InvoiceMultiAssetAllBillVO();
        assetBill.setAssetId(assetInfo.getId());
        assetBill.setReceivableBillList(receivableList);
        if(CollectionUtil.isNotEmpty(orderList)){
            Map<Integer,List<InvoiceOrderBillDTO>> orderMap= orderList.stream().filter(item->Objects.nonNull(item.getSubOrderType())).collect(Collectors.groupingBy(InvoiceOrderBillDTO::getSubOrderType));
            if(Objects.nonNull(orderMap)){
                assetBill.setTempOrderBillDTOS(orderMap.get(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY.getCode()));
                assetBill.setPaidOrderBillDTOS(orderMap.get(OrderRuleCategoryEnum.ONE_LEVEL_PAID.getCode()));
                assetBill.setYueCardOrderBillDTOS(orderMap.get(OrderRuleCategoryEnum.ONE_LEVEL_PARK.getCode()));
            }
        }
        assetBill.setPreBillList(predepositList);
        assetBill.setAssetMsId(assetInfo.getMsId());
        assetBill.setAssetType(assetInfo.getType());
        assetBill.setHouseName(assetInfo.getAssetName());
        assetBill.setCommunityId(communityInfo.getId());
        assetBill.setCommunityName(communityInfo.getName());
        assetBill.setCommunityMsId(communityInfo.getMsId());
        assetBill.setCustomerSimpleVOS(BeanCopierWrapper.copy(assetInfo.getListCustomer(), CustomerSimpleVO.class));
        return  assetBill;
    }

    private InvoiceMultiAssetQueryDTO buildMultiAssetConditionDTO(InvoiceMultiAssetReceivableBillRequestVO invoiceBillQueryDTO, CommunityDTO communityInfo, Map<Long, AssetAdapter> assetAdapterMap) {
        InvoiceMultiAssetQueryDTO condition = new InvoiceMultiAssetQueryDTO();
        condition.setCommunityId(communityInfo.getId());
        condition.setAssetIds(new ArrayList<>(assetAdapterMap.keySet()));
        condition.setBeginTime(invoiceBillQueryDTO.getBeginTime());
        condition.setEndTime(invoiceBillQueryDTO.getEndTime());
        condition.setOrderTypeFirstLevelList(invoiceBillQueryDTO.getSubOrderTypeList());
        condition.setOrderNumList(invoiceBillQueryDTO.getOrderNumList());
        return condition;
    }


    /**
     * 预存单-可开票账单查询
     */
    @PostMapping("invoice/predeposit/bill/list")
    public ChargeResponse<List<InvoiceBillDTO>> findPedepositBillList(@RequestBody InvoiceBillQueryDTO invoiceBillQueryDTO) throws ChargeBusinessException {
        return new ChargeResponse<>(invoiceAppService.findPedepositBillList(invoiceBillQueryDTO));
    }

    /**
     * 月卡订单-可开票账单查询
     */
    @PostMapping("invoice/yue/car/bill/list")
    public ChargeResponse<List<InvoiceOrderBillDTO>> findYueCardOrderBillList(@Valid @RequestBody InvoiceOrderBillQueryDTO invoiceOrderBillQueryDTO) throws ChargeBusinessException {
        return new ChargeResponse<>(invoiceAppService.findYueCardOrderBillList(invoiceOrderBillQueryDTO));
    }

    /**
     * 有偿订单-可开票账单查询
     */
    @PostMapping("invoice/paid/car/bill/list")
    public ChargeResponse<List<InvoiceOrderBillDTO>> findPaidOrderBillList(@Valid @RequestBody InvoiceOrderNumQueryDTO invoiceOrderNumQueryDTO) throws ChargeBusinessException {
        return new ChargeResponse<>(invoiceAppService.findPaidOrderBillList(invoiceOrderNumQueryDTO));
    }

    /**
     * 临时订单-可开票账单查询
     */
    @PostMapping("invoice/order/temp/bill/list")
    public ChargeResponse<List<InvoiceOrderBillDTO>> findTempOrderBillList(@Valid @RequestBody InvoiceOrderBillQueryVO invoiceOrderBillQueryVO) throws ChargeBusinessException {
        InvoiceOrderBillQueryDTO invoiceOrderBillQueryDTO = BeanCopierWrapper.copy(invoiceOrderBillQueryVO, InvoiceOrderBillQueryDTO.class);
        return new ChargeResponse<>(invoiceAppService.findTempOrderBillList(invoiceOrderBillQueryDTO));
    }


    /**
     * 开具发票提交-跨资产
     */
    @PostMapping("invoice/apply/batch")
    public ChargeResponse<List<InvoiceApplyResultDTO>> invoiceApplyBatch(@Valid @NotEmpty @RequestBody InvoiceBatchCreateRequestVO condition) throws ChargeBusinessException {
         batchInvoiceCheckAndFillAsset(condition);
        List<InvoiceApplyResultVO> result=AppInterfaceUtil.getResponseDataThrowException(invoiceAppService.invoiceApplyBatch(condition.getCommunityId(),condition.getInvoiceRequestDTOS()));
        return new ChargeResponse(InvoiceConverter.INSTANCE.convert2InvoiceApplyResultDTO(result));
    }

    private void batchInvoiceCheckAndFillAsset(InvoiceBatchCreateRequestVO condition) throws ChargeBusinessException {
        List<CreateInvoiceRequestDTO> invoiceRequestDTOS=condition.getInvoiceRequestDTOS();
        if (invoiceRequestDTOS.size() > 10) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "发票数目超过10条");
        }
        CommunityDTO communityInfo=checkCommunityValid(invoiceRequestDTOS,condition);
        condition.setCommunityId(communityInfo.getId());
        List<ZhaoXiAssertRequest>  assetMsAllList=new ArrayList<>();
        for (CreateInvoiceRequestDTO createInvoiceRequest : invoiceRequestDTOS) {
            if(CollectionUtils.isEmpty(createInvoiceRequest.getDetailList())){
                throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "缺少发票明细");
            }
            List<CustomerDTO> customerDTOList=checkCustomerValid(createInvoiceRequest,communityInfo.getId());
            createInvoiceRequest.setInvoiceApplicant(customerDTOList.get(0).getId());
            List<ZhaoXiAssertRequest>  assetMsDetailList=this.getAssetMsList(createInvoiceRequest);
            if(CollectionUtil.isNotEmpty(assetMsDetailList)){
                assetMsAllList.addAll(assetMsDetailList);
            }

        }
        assetSupport.fillAssetIds(assetMsAllList, communityInfo.getId(),false);
        Map<String,Long> msIdMap=assetMsAllList.stream().collect(Collectors.toMap(v->v.getAssetMsId()+ CommonConstant.DASH+v.getAssetType(),ZhaoXiAssertRequest::getAssetId,(v1,v2)->v2));
        fillInvoiceRequstDTOWithAssetId(invoiceRequestDTOS,msIdMap);

    }

    private void fillInvoiceRequstDTOWithAssetId(List<CreateInvoiceRequestDTO> invoiceRequestDTOS, Map<String, Long> msIdMap) throws ChargeBusinessException {
        for (CreateInvoiceRequestDTO invoiceApplyResultDTO : invoiceRequestDTOS) {
            for (CreateInvoiceRequestDTO.InvoiceDetailRequestDTO detail : invoiceApplyResultDTO.getDetailList()) {
                if(Objects.isNull(msIdMap.get(detail.getAssetMsId()+ CommonConstant.DASH+detail.getAssetType()))){
                    throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "发票明细缺少查询资产为空");
                }
                detail.setAssetId(msIdMap.get(detail.getAssetMsId()+ CommonConstant.DASH+detail.getAssetType()));
            }
        }
    }

    private List<ZhaoXiAssertRequest> getAssetMsList(CreateInvoiceRequestDTO createInvoiceRequest) throws ChargeBusinessException {
        List<ZhaoXiAssertRequest>  assetMsList=new ArrayList<>();
        for (CreateInvoiceRequestDTO.InvoiceDetailRequestDTO detail : createInvoiceRequest.getDetailList()) {
            if(Objects.isNull(detail.getAssetMsId()) || Objects.isNull(detail.getAssetType())){
                throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "发票明细缺少资产id");
            }

            ZhaoXiAssertRequest assetMsInfo= new ZhaoXiAssertRequest();
            assetMsInfo.setAssetMsId(detail.getAssetMsId());
            assetMsInfo.setAssetType(detail.getAssetType());
            assetMsList.add(assetMsInfo);
        }
        return assetMsList;
    }

    private List<CustomerDTO> checkCustomerValid(CreateInvoiceRequestDTO createInvoiceRequest,Long communityId) throws ChargeBusinessException {
        if(Objects.isNull(createInvoiceRequest.getInvoiceApplicantMsId())){
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "缺少申请人id");
        }
        List<CustomerDTO> customerDTOList = customerSupport.getCustomerListByMsId(communityId, createInvoiceRequest.getInvoiceApplicantMsId());
        if (CollectionUtils.isEmpty(customerDTOList)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1107.getCode(),"开票对象不存在"+createInvoiceRequest.getInvoiceApplicantMsId());
        }
       return customerDTOList;
    }

    private CommunityDTO checkCommunityValid(List<CreateInvoiceRequestDTO> invoiceRequestDTOS, InvoiceBatchCreateRequestVO condition) throws ChargeBusinessException {
        Set<String> communityMsIds= invoiceRequestDTOS.stream().map(item->item.getCommunityMsId()).collect(Collectors.toSet());
        if(CollectionUtil.isEmpty(communityMsIds) || communityMsIds.size()>1){
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "小区参数错误，请检查");
        }
        if(!Objects.equals(communityMsIds.toArray()[0],condition.getCommunityMsId())){
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "小区参数错误，请检查,不允许跨小区开票");
        }

        CommunityDTO communityInfo = communitySupport.getCommunityByMsId(condition.getCommunityMsId());
        if (Objects.isNull(communityInfo)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(), "查无该小区");
        }
        return communityInfo;
    }


    /**
     * 开具发票提交
     */
    @PostMapping("invoice/apply")
    public ChargeResponse<InvoiceApplyResultDTO> invoiceApply(@Valid @RequestBody CreateInvoiceRequestDTO requestDTO) throws ChargeBusinessException {

        InvoiceApplyResultDTO resultDTO = new InvoiceApplyResultDTO();
        ChargeResponse response = invoiceAppService.invoiceApply(requestDTO);
        Long invoiceApplyId = (Long) AppInterfaceUtil.getResponseDataThrowException(response);
        resultDTO.setInvoiceApplyId(invoiceApplyId);
        return new ChargeResponse<>(resultDTO);
    }

    /**
     * 电商开具发票提交
     */
    @PostMapping("invoice/eBusiness/apply")
    public ChargeResponse<InvoiceApplyResultDTO> invoiceEBusinessApply(@Valid @RequestBody CreateInvoiceRequestDTO requestDTO) throws ChargeBusinessException {

        InvoiceApplyResultDTO resultDTO = new InvoiceApplyResultDTO();
        ChargeResponse<List<Long>> response = invoiceAppService.invoiceEbusinessApply(requestDTO);
        List<Long> invoiceApplyIds =AppInterfaceUtil.getResponseDataThrowException(response);
        resultDTO.setInvoiceApplyIds(invoiceApplyIds);
        return new ChargeResponse<>(resultDTO);
    }
    /**
     * 电商开具发票提交
     */
    @PostMapping("invoice/order/apply")
    public ChargeResponse<InvoiceApplyResultDTO> invoiceOrderApply(@Valid @RequestBody CreateOrderInvoiceRequestDTO requestDTO) throws ChargeBusinessException {

        InvoiceApplyResultDTO resultDTO = new InvoiceApplyResultDTO();
        ChargeResponse<List<Long>> response = invoiceAppService.invoiceOrderApply(requestDTO);
        List<Long> invoiceApplyIds =AppInterfaceUtil.getResponseDataThrowException(response);
        resultDTO.setInvoiceApplyIds(invoiceApplyIds);
        return new ChargeResponse<>(resultDTO);
    }

    /**
     * 开票记录列表
     */
    @PostMapping("/invoice/apply/list")
    public ChargeResponse<PagingDTO<TinyInvoiceApplyDTO>> findInvoiceApplyList(@Valid @RequestBody InvoiceQueryDTO invoiceQueryDTO) throws ChargeBusinessException {

        if (CollectionUtils.isEmpty(invoiceQueryDTO.getHouseMsIdList())
                && CollectionUtils.isEmpty(invoiceQueryDTO.getParkingMsIdList())) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1006.getCode(), "房间资产ID列表,车位资产ID列表,不能全为空");
        }
        return invoiceApplyClient.findInvoiceApplyList(invoiceQueryDTO);
    }

    /**
     * 查看开票详情
     */
    @PostMapping("/invoice/apply/detail")
    public ChargeResponse<SimpleInvoiceApplyDTO> getInvoiceApplyDetail(@Valid @RequestBody InvoiceApplyDetaillQueryDTO invoiceApplyDetaillQueryDTO) throws ChargeBusinessException {
        return invoiceApplyClient.getInvoiceApplyDetail(invoiceApplyDetaillQueryDTO);
    }

    /**
     * 查询用户最后一次发票失败的记录
     */
    @PostMapping("/invoice/last/failed/record")
    public ChargeResponse<TinyInvoiceApplyDTO> getLastFailedRecord(@Valid @RequestBody InvoiceApplyFailQueryDTO invoiceApplyFailQueryDTO) throws ChargeBusinessException {
        return invoiceApplyClient.getLastFailedRecord(invoiceApplyFailQueryDTO);
    }

}