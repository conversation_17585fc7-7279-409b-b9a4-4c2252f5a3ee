package com.charge.api.web.vo.joylife;

import com.charge.api.web.vo.joylife.request.ZhaoXiCommunityReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/6/7 17:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class CreateBatchAssetBillReq extends ZhaoXiCommunityReq {

    private static final long serialVersionUID = 3723043916054519934L;

    /**
     * 支付来源(微信小程序：WECHAT_APPLET，悦家APP：YUEHOME_PAY，润钱包：CRT_PAY)
     */
    @NotBlank(message = "支付来源参数缺失,请重试")
    private String paymentSource;

    /**
     * 支付总金额
     */
    @NotNull(message = "支付总金额参数丢失,请重试")
    @DecimalMin(value = "0.01",message = "金额必须大于0")
    private BigDecimal totalPrice;

    /**
     * 商品名称描述
     */
    private String desc;

    /**
     * 终端IP
     */
    @NotBlank(message = "终端IP参数缺失,请重试")
    private String mchCreateIp;

    /**
     * 备注
     */
    private String memo;

    /**
     * AppID
     */
    @NotBlank(message = "AppID参数缺失,请重试")
    private String subAppid;

    /**
     * 微信用户小程序关注商家的openid(小程序缴费必传)
     */
    private String subOpenid;

    /**
     * 支付人(当前登录用户姓名)
     */
    @NotNull(message = "支付人参数缺失,请重试")
    private String userName;

    /**
     * 客户ID（当前登录用户唯一标识，记录用户缴费历史)
     */
    @NotBlank(message = "客户ID参数缺失,请重试")
    private String userId;

    /**
     * 积分账户
     */
    private String equityAccount;

    /**
     * 电话号码
     */
    @NotBlank(message = "电话号码参数缺失,请重试")
    private String phone;

    /**
     * 抵扣总积分
     */
    private Integer totalPoints;

    /**
     * 朝昔用户msId
     */
    private String customId;


    /**
     * 买家支付宝账号
     */
    private String buyerLogonId;

    /**
     * 支付方式（支付宝：3,微信支付：4）
     */
    private String paymentMethod;

    @NotEmpty(message = "账单不能为空")
    @Valid
    private List<AssetBillReq> assetBillReqs;

    /**
     * 是否是app支付，使用其他渠道拉起支付宝app（非支付宝小程序）的需要传该值为true
     */
    private Boolean payNative;

    /**
     * 支付成功后跳转地址
     */
    private String returnUrl;

}
