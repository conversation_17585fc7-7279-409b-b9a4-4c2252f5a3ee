package com.charge.api.web.adapter;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.vo.pos.AssetVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.enums.common.JoyLifeErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.enums.CustomerTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import com.charge.maindata.pojo.dto.ParkingSpaceDTO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class HouseClientAdapter {

    private final AssetClient assetClient;

    public ChargeResponse<HouseDTO> getHouseById(long assetId) {
        ChargeResponse<PagingDTO<AssetDTO>> chargeResponse = assetClient.pageAsset(AssetCondition.builder().ids(Collections.singletonList(assetId)).pageNum(1).pageSize(1).build());
        if (!chargeResponse.isSuccess()) {
            return new ChargeResponse(JoyLifeErrorInfoEnum.FAIL.getCode(),"查询基础信息服务异常");
        }
        PagingDTO<AssetDTO> pagingDTO = chargeResponse.getContent();
        if (pagingDTO == null || pagingDTO.getList().size() == 0) {
            return new ChargeResponse(JoyLifeErrorInfoEnum.FAIL.getCode(),"查询不到房间信息");
        }
        HouseDTO houseDTO = pagingDTO.getList().get(0).getHouseDTO();
        return new ChargeResponse<>(houseDTO);
    }

    public ChargeResponse<List<AssetDTO>> getAssetByCommunityIdAndType(long id, int type, String keyword) {
        ChargeResponse<List<AssetDTO>> chargeResponse = assetClient.listAsset(AssetCondition.builder()
                .communityIds(Collections.singletonList(id)).type(type).searchParam(keyword).build());
        if (!chargeResponse.isSuccess()) {
            return new ChargeResponse(JoyLifeErrorInfoEnum.FAIL.getCode(),"查询基础信息服务异常");
        }
        List<AssetDTO> assetDTOList = chargeResponse.getContent();
        return new ChargeResponse<>(assetDTOList);
    }

    public ChargeResponse<AssetDTO> getAssetById(long id) {
        ChargeResponse<List<AssetDTO>> response = assetClient.listAsset(AssetCondition.builder().ids(Collections.singletonList(id))
                .excludeMoveOut(true).customerTypes(Lists.newArrayList(CustomerTypeEnum.OWNER.getCode())).build());
        if (!response.isSuccess()) {
            return new ChargeResponse(JoyLifeErrorInfoEnum.FAIL.getCode(),"查询基础信息服务异常");
        }
        List<AssetDTO> assetDTOList = response.getContent();
        if (assetDTOList == null || assetDTOList.size() == 0) {
            return new ChargeResponse(JoyLifeErrorInfoEnum.FAIL.getCode(),"查询不到房间信息");
        }
        AssetDTO assetDTO = assetDTOList.get(0);
        return new ChargeResponse<>(assetDTO);
    }

    public AssetVO getAssetVoById(Long id) throws ChargeBusinessException {
        AssetDTO assetDTO = getAssetById(id).getContent();
        if(assetDTO.getHouseDTO()!=null){
            HouseDTO house = assetDTO.getHouseDTO();
            return AssetVO.builder().id(assetDTO.getId()).name(house.getHouseName()).code(house.getHouseCode())
                    .buildingId(house.getBuildingId()).buildingName(house.getBuildingName()).unitId(house.getUnitId()).unitName(house.getUnitName())
                    .communityId(house.getCommunityId()).communityName(house.getCommunityName()).build();
        }else if(assetDTO.getParkingSpaceDTO()!=null){
            ParkingSpaceDTO parkingSpaceDTO = assetDTO.getParkingSpaceDTO();
            return AssetVO.builder().id(assetDTO.getId()).name(parkingSpaceDTO.getParkingSpaceName()).code(parkingSpaceDTO.getParkingSpaceCode())
                    .communityId(parkingSpaceDTO.getCommunityId()).communityName(parkingSpaceDTO.getCommunityName()).build();
        }else {
            throw new ChargeBusinessException(ErrorInfoEnum.E1013.getCode(),"资产的房间和车位信息都是空的");
        }
    }

    public AssetVO getCommunityVirtualHouse(Long communityId) throws ChargeBusinessException {
        ChargeResponse<AssetDTO> response = assetClient.getCommunityVirtualHouse(communityId);
        AssetDTO assetDTO = AppInterfaceUtil.getDataThrowException(response);
        HouseDTO house = assetDTO.getHouseDTO();
        if(house!=null){
            return AssetVO.builder().id(assetDTO.getId()).name(house.getHouseName()).code(house.getHouseCode())
                    .communityId(house.getCommunityId()).communityName(house.getCommunityName()).build();
        }else {
            throw new ChargeBusinessException(ErrorInfoEnum.E1013.getCode(),"虚拟资产的房间信息是空的");
        }

    }

    public AssetDTO getCommunityVirtualDto(Long communityId) throws ChargeBusinessException {
        ChargeResponse<AssetDTO> response = assetClient.getCommunityVirtualHouse(communityId);
        return  AppInterfaceUtil.getDataThrowException(response);
    }

    public List<AssetDTO> getAssetByIds(List<Long> ids) throws ChargeBusinessException {
        ChargeResponse<List<AssetDTO>> response = assetClient.listAsset(AssetCondition.builder().ids(ids).build());
        if(Boolean.FALSE.equals(response.isSuccess())){
            log.error("资产房屋基础数据服务调用异常,houseCondition:{}", JSON.toJSONString(ids));
            throw new ChargeBusinessException(ErrorInfoEnum.E1003.getCode(),"资产房屋基础数据服务调用异常");
        }

        return Optional.ofNullable(response.getContent()).orElse(Collections.emptyList());
    }
}
