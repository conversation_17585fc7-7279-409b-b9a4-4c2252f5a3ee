package com.charge.api.web.vo.joylife.deposit;

import com.charge.bill.dto.predeposit.PredepositAccountDTO;
import com.charge.config.dto.points.PointsSignCommunityDTO;
import com.charge.feecalculte.dto.ReceivableBillDTO;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrestoreVO {
    private BigDecimal availableMoney;
    private BigDecimal arrearsMoney;
    private Long itemId;
    private String itemName;
    private List<PrestoreDetail> chargeInfoDetailList;
    private BigDecimal chargeMoneyPerMonth;
    private List<Integer> monthList;
    private Integer pointsEarnStatus;
    /**
     * 收费面积
     */
    private BigDecimal chargeArea;
    /**
     * chargeModel
     */
//    private String chargeModel;
    /**
     * 收费金额类型（用于周期指定型
     */
//    private Integer amountType;
    /**
     * 单价
     */
    private String standardStr;

    public static List<PrestoreVO> of(Map<Long,String> bindItemMap,List<ReceivableBillDTO> preStores, List<com.charge.bill.dto.ReceivableBillDTO> arrears,
                                      List<PredepositAccountDTO> preDepositAccounts, PointsSignCommunityDTO pointsSignCommunityDTO, Integer chargeObjectType){
        if(CollectionUtils.isEmpty(bindItemMap)){
            return Lists.newArrayList();
        }
        Map<Long, List<com.charge.bill.dto.ReceivableBillDTO>> itemArrearsMap= Optional.ofNullable(arrears).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.groupingBy(com.charge.bill.dto.ReceivableBillDTO::getItemId));
        Map<Long,ReceivableBillDTO> itempreStoreMap=Optional.ofNullable(preStores).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(ReceivableBillDTO::getChargeItemId, Function.identity(),(a,b)->b));
        Map<Long, List<PredepositAccountDTO>> itempreDepositAccountMap=Optional.ofNullable(preDepositAccounts).orElse(Lists.newArrayList()).stream().collect(Collectors.groupingBy(PredepositAccountDTO::getPredepositItemId));
        return bindItemMap.keySet().stream().map(itemId->{
            String itemName = bindItemMap.get(itemId);
            ReceivableBillDTO preStore = itempreStoreMap.get(itemId);
            PrestoreVO prestoreVO;
            List<com.charge.bill.dto.ReceivableBillDTO> receivableBillDTOS=itemArrearsMap.get(itemId);;
            if(preStore==null){
                prestoreVO=PrestoreVO.builder().itemId(itemId).itemName(itemName).chargeMoneyPerMonth(BigDecimal.valueOf(0L))
                        .chargeInfoDetailList(PrestoreDetail.fromDefault()).monthList(Arrays.asList(3, 6, 12)).build();
            }else {
                prestoreVO=PrestoreVO.builder().itemId(preStore.getChargeItemId()).itemName(preStore.getChargeItemName()).chargeMoneyPerMonth(preStore.getReceivableAmount())
                        .standardStr(preStore.getStandardStr()).chargeInfoDetailList(PrestoreDetail.from(preStore.getReceivableAmount())).monthList(Arrays.asList(3, 6, 12)).chargeArea(preStore.getChargeArea()).build();
            }
            if(pointsSignCommunityDTO!=null&&pointsSignCommunityDTO.getPointsEarnItemIdList().contains(prestoreVO.getItemId())){
                prestoreVO.setPointsEarnStatus(1);
            }else {
                prestoreVO.setPointsEarnStatus(0);
            }
            if(!CollectionUtils.isEmpty(receivableBillDTOS)){
                BigDecimal arrearsAmount = receivableBillDTOS.stream().filter(a -> chargeObjectType.equals(a.getChargeObject())).map(a -> a.getArrearsAmount().add(a.getPenaltyArrearsAmount())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
                prestoreVO.setArrearsMoney(arrearsAmount);
            }else {
                prestoreVO.setArrearsMoney(BigDecimal.ZERO);
            }
            List<PredepositAccountDTO> preDepositAccount = itempreDepositAccountMap.get(itemId);
            if(!CollectionUtils.isEmpty(preDepositAccount)){
                BigDecimal availableAmount = preDepositAccount.stream().filter(a -> chargeObjectType.equals(a.getChargeObj())).map(PredepositAccountDTO::getAvailableBalance).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
                prestoreVO.setAvailableMoney(availableAmount);
            }else {
                prestoreVO.setAvailableMoney(BigDecimal.ZERO);
            }
            return prestoreVO;
        }).collect(Collectors.toList());
    }

}
