package com.charge.api.web.service.pos.impl;

import static com.charge.api.web.service.pos.impl.ChargeBillServiceImpl.nullStr;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.service.pos.ChargeItemConfigService;
import com.charge.api.web.util.PageUtil;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.DepositItemVO;
import com.charge.api.web.vo.pos.PrestoreItemVO;
import com.charge.api.web.vo.pos.SystemChargeItemVO;
import com.charge.api.web.vo.pos.TemporaryChargeItemVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.Paging;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.config.client.item.CommunityChargeItemClient;
import com.charge.config.client.item.CommunityPreStoreItemClient;
import com.charge.config.dto.item.CommunityChargeItemDTO;
import com.charge.config.dto.item.SpecialPreStoreItemDTO;
import com.charge.config.dto.item.condition.CommunityChargeItemQueryConditionDTO;
import com.charge.config.dto.item.condition.QuerySpecialPreStoreConditionDTO;
import com.charge.config.enums.BusinessTypeEnum;
import com.charge.config.enums.StandardConfigStatusEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.finance.client.SegmentsClient;
import com.charge.finance.dto.TaxInfoDTO;
import com.charge.order.client.OrderRuleClient;
import com.charge.order.dto.rule.CommunityBaseOrderRuleDTO;
import com.charge.order.dto.rule.CommunityOrderRuleQueryDTO;
import com.charge.order.enums.EnableEnum;
import com.charge.pos.dto.OrderRuleChargeItemDTO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * [收费项2.0 服务接口]
 *
 * <AUTHOR> wuChao
 * @version : [v1.0]
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChargeItemConfigServiceImpl implements ChargeItemConfigService {

    private final OrderRuleClient orderRuleClient;
    private final CommunityChargeItemClient communityChargeItemClient;
    private final SegmentsClient segmentsClient;
    private final CommunityPreStoreItemClient communityPreStoreItemClient;

    @Override
    public ChargePageResponse<List<SystemChargeItemVO>> getOrderRuleChargeItemPage(OrderRuleChargeItemDTO dto) {
        getOrderRuleCategory(dto);
        if (CollectionUtil.isEmpty(dto.getCommunityOrderRuleItemIds())) {
            int num = BigDecimal.ZERO.intValue();
            return new ChargePageResponse<>(new ArrayList<>(), dto.getPageSize(), num, dto.getPageNum(), num);
        }
        PagingDTO<CommunityChargeItemDTO> chargeItemPage = getChargeItem(dto);
        if (chargeItemPage == null) {
            int num = BigDecimal.ZERO.intValue();
            return new ChargePageResponse<>(new ArrayList<>(), dto.getPageSize(), num, dto.getPageNum(), num);
        }
        Map<Long, TaxInfoDTO> taxMap = getTaxInfoMap(chargeItemPage);
        List<SystemChargeItemVO> list = getOrderRuleChargeItemValues(chargeItemPage, taxMap);
        return new ChargePageResponse<>(list, dto.getPageSize(), (int) Math.ceil(chargeItemPage.getTotalCount()/ dto.getPageSize().doubleValue()), dto.getPageNum(), chargeItemPage.getTotalCount());
    }

    @Override
    public List<TemporaryChargeItemVO> getOrderRuleChargeItemTemporaryStop(OrderRuleChargeItemDTO dto) {
        getOrderRuleCategory(dto);
        if (CollectionUtil.isEmpty(dto.getCommunityOrderRuleItemIds())) {
            return new ArrayList<>();
        }
        PagingDTO<CommunityChargeItemDTO> chargeItemPage = getChargeItem(dto);
        if (chargeItemPage == null) {
            return new ArrayList<>();
        }
        List<TemporaryChargeItemVO> list = new ArrayList<>();
        for (CommunityChargeItemDTO itemDTO : chargeItemPage.getList()) {
            TemporaryChargeItemVO vo = new TemporaryChargeItemVO();
            vo.setItemUuid(String.valueOf(itemDTO.getItemId()));
            vo.setItemName(itemDTO.getItemName());
            vo.setSelected(1);
            vo.setDetailStatus("1");
            vo.setCommunityUuid(String.valueOf(dto.getCommunityId()));
            list.add(vo);
        }
        return list;
    }

    @Override
    public ChargeResponse<List<PrestoreItemVO>> getPrestoreItem(OrderRuleChargeItemDTO dto) {
        QuerySpecialPreStoreConditionDTO condition = new QuerySpecialPreStoreConditionDTO();
        condition.setCommunityId(dto.getCommunityId());
        condition.setStatus(StandardConfigStatusEnum.ENABLE.getCode());
        condition.setKeyWord(dto.getItemName());
        condition.setPaging(new Paging(dto.getPageNum(),dto.getPageSize()));
        ChargeResponse<PagingDTO<SpecialPreStoreItemDTO>> pagingDTOChargeResponse = communityPreStoreItemClient.queryCommunitySpecialPreStorePage(condition);
        if (!pagingDTOChargeResponse.isSuccess()) {
            int num = BigDecimal.ZERO.intValue();
            return new ChargePageResponse<>(new ArrayList<>(), dto.getPageSize(), num, dto.getPageNum(), num);
        }
        List<PrestoreItemVO> list = new ArrayList<>();
        for (SpecialPreStoreItemDTO itemDTO : pagingDTOChargeResponse.getContent().getList()) {
            Date createTime = itemDTO.getCreateTime() == null ? null : DateUtils.parse(DateUtils.formatToDateStr(DateUtils.FORMAT_0, itemDTO.getCreateTime()), DateUtils.FORMAT_0);
            Date modifyTime = itemDTO.getModifyTime() == null ? null : DateUtils.parse(DateUtils.formatToDateStr(DateUtils.FORMAT_0, itemDTO.getModifyTime()), DateUtils.FORMAT_0);
            PrestoreItemVO vo = new PrestoreItemVO();
            vo.setPreItemUuid(String.valueOf(itemDTO.getId()));
            vo.setPreItemName(itemDTO.getItemName());
            vo.setCommunityUuid(String.valueOf(dto.getCommunityId()));
            vo.setItemUuid(String.valueOf(itemDTO.getItemId()));
            vo.setItemName(itemDTO.getItemName());
            vo.setItemCode(itemDTO.getItemCode());
            vo.setUpdateTs(modifyTime);
            vo.setCreateTs(createTime);
            vo.setEnableStatus(String.valueOf(itemDTO.getStatus()));
            vo.setBillState("0");
            vo.setPointsEarnStatus(0);
            list.add(vo);
        }
        return new ChargePageResponse<>(list, dto.getPageSize(), (int) Math.ceil(pagingDTOChargeResponse.getContent().getTotalCount() / dto.getPageSize().doubleValue()), dto.getPageNum(), pagingDTOChargeResponse.getContent().getTotalCount());
    }

    /**
     * description: 订单品类收费项 分页查询 vo赋值
     * author: wuChao
     * date: 2023/3/1
     * param [chargeItemPage, taxMap]
     * return java.util.List<com.charge.api.web.vo.pos.SystemChargeItemVO>
     **/
    private List<SystemChargeItemVO> getOrderRuleChargeItemValues(PagingDTO<CommunityChargeItemDTO> chargeItemPage, Map<Long, TaxInfoDTO> taxMap) {
        //税率小数点
        int taxDec = 2;
        List<SystemChargeItemVO> itemVOS = new ArrayList<>();
        for (CommunityChargeItemDTO itemDTO : chargeItemPage.getList()) {
            String itemId = String.valueOf(itemDTO.getItemId());
            Date createTime = itemDTO.getCreateTime() == null ? null : DateUtils.parse(DateUtils.formatToDateStr(DateUtils.FORMAT_0, itemDTO.getCreateTime()), DateUtils.FORMAT_0);
            Date modifyTime = itemDTO.getModifyTime() == null ? null : DateUtils.parse(DateUtils.formatToDateStr(DateUtils.FORMAT_0, itemDTO.getModifyTime()), DateUtils.FORMAT_0);

            SystemChargeItemVO itemVO = SystemChargeItemVO.builder().id(itemId).itemUuid(itemId).itemCode(itemDTO.getItemCode()).itemName(itemDTO.getItemName())
                    .createUser(itemDTO.getCreateUser()).createTs(createTime).updateTs(modifyTime)
                    .status(String.valueOf(itemDTO.getStatus())).communityUuid(String.valueOf(itemDTO.getCommunityId()))
                    .isDiscount(String.valueOf(itemDTO.getSupportDiscount())).pointsDiscountStatus(itemDTO.getSupportPoints())
                    .pointsDiscountRule(itemDTO.getPointsDiscountRule()).build();
            if (taxMap.containsKey(itemDTO.getTaxId())) {
                BigDecimal taxPoint = new BigDecimal(taxMap.get(itemDTO.getTaxId()).getTaxPoint());
                itemVO.setTaxPoint(taxPoint);
                itemVO.setTaxDec(taxDec);
                itemVO.setCommunityTaxRate(taxPoint);
                itemVO.setTaxHeadName(taxMap.get(itemDTO.getTaxId()).getName());
            }
            itemVOS.add(itemVO);
        }
        return itemVOS;
    }

    @Override
    public ChargePageResponse<List<DepositItemVO>> pageQueryDepositItem(Long communityId, Integer pageNum, Integer pageSize, String keyword) throws ChargeBusinessException {
        CommunityChargeItemQueryConditionDTO conditionDTO = new CommunityChargeItemQueryConditionDTO();
        conditionDTO.setCommunityId(communityId);
        conditionDTO.setBusinessTypes(Lists.newArrayList(BusinessTypeEnum.DEPOSIT.getCode()));
        conditionDTO.setStatus(Collections.singletonList(StandardConfigStatusEnum.ENABLE.getCode()));
        if(StringUtils.hasText(keyword)){
            conditionDTO.setItemName(keyword);
        }
        PagingDTO<CommunityChargeItemDTO> pagingDTO = AppInterfaceUtil.getResponseDataThrowException(communityChargeItemClient.queryCommunityChargeItemPage(conditionDTO));
        pagingDTO.setPageNum(pageNum);
        pagingDTO.setPageSize(pageSize);
        return toChargePageResponse(pagingDTO);
    }

    private ChargePageResponse<List<DepositItemVO>> toChargePageResponse(PagingDTO<CommunityChargeItemDTO> pagingDTO){
        List<DepositItemVO> depositItems = pagingDTO.getList().stream().map(dto ->
                //todo 目前看不需要税率，冒烟时看需要再补
                DepositItemVO.builder()
                        .id(nullStr(dto.getId()))
                        .itemUuid(nullStr(dto.getItemId()))
                        .itemName(nullStr(dto.getItemName()))
                        .typeUuid("deposit")
                        .typeName("押金类")
                        .memo(dto.getMemo())
                        .effectiveTime(dto.getCreateTime())
                        .itemCode(dto.getItemCode())
                        .updateTs(dto.getCreateTime())
                        .createTs(dto.getModifyTime())
                        .dr("0")
                        .communityUuid(nullStr(dto.getCommunityId()))
                        .isDiscount(nullStr(dto.getSupportDiscount()))
                        .build()
        ).collect(Collectors.toList());
        ChargePageResponse<List<DepositItemVO>> response = PageUtil.toChargePageResponse(pagingDTO);
        response.setContent(depositItems);
        return response;

    }


    /**
     * description: 获取税率信息
     * author: wuChao
     * date: 2023/3/1
     * param [chargeItemPage]
     * return java.util.Map<java.lang.Long,com.charge.finance.dto.TaxInfoDTO>
     **/
    private Map<Long, TaxInfoDTO> getTaxInfoMap(PagingDTO<CommunityChargeItemDTO> chargeItemPage) {
        Map<Long, TaxInfoDTO> taxMap = new HashMap<>();
        String taxIds = chargeItemPage.getList().stream().filter(x -> x.getTaxId() != null).distinct().map(x -> x.getTaxId() + "").collect(Collectors.joining());
        ChargeResponse<List<TaxInfoDTO>> taxes = segmentsClient.getTaxesByIds(taxIds);
        if (taxes.isSuccess() && CollectionUtil.isNotEmpty(taxes.getContent())) {
            taxMap = taxes.getContent().stream().collect(Collectors.toMap(TaxInfoDTO::getId, x -> x));
        }
        return taxMap;
    }

    /**
     * description: 获取收费项信息
     * author: wuChao
     * date: 2023/3/1
     * param [dto, communityOrderRuleItemIds]
     * return com.charge.common.dto.PagingDTO<com.charge.config.dto.item.CommunityChargeItemDTO>
     **/
    private PagingDTO<CommunityChargeItemDTO> getChargeItem(OrderRuleChargeItemDTO dto) {
        CommunityChargeItemQueryConditionDTO communityItemQuery = BeanCopierWrapper.copy(dto, CommunityChargeItemQueryConditionDTO.class);
        if (CollectionUtil.isNotEmpty(dto.getCommunityOrderRuleItemIds())) {
            communityItemQuery.setItemIds(dto.getCommunityOrderRuleItemIds());
        }
        communityItemQuery.setStatus(Collections.singletonList(StandardConfigStatusEnum.ENABLE.getCode()));
        ChargeResponse<PagingDTO<CommunityChargeItemDTO>> communityChargeItemPage = communityChargeItemClient.queryCommunityChargeItemPage(communityItemQuery);
        if (communityChargeItemPage.isSuccess() && CollectionUtil.isNotEmpty(communityChargeItemPage.getContent().getList())) {
            return communityChargeItemPage.getContent();
        }
        return null;
    }

    /**
     * description: 获取制定订单规则品类的收费项集合
     * author: wuChao
     * date: 2023/3/1
     * param [dto]
     * return void
     **/
    private void getOrderRuleCategory(OrderRuleChargeItemDTO dto) {
        CommunityOrderRuleQueryDTO orderRuleQueryDTO = BeanCopierWrapper.copy(dto, CommunityOrderRuleQueryDTO.class);
        orderRuleQueryDTO.setStatus(Collections.singletonList(EnableEnum.ENABLE.getCode()));
        ChargeResponse<PagingDTO<CommunityBaseOrderRuleDTO>> orderRuleCommunityPage = orderRuleClient.getCommunityPage(orderRuleQueryDTO);
        if (orderRuleCommunityPage.isSuccess() && CollectionUtil.isNotEmpty(orderRuleCommunityPage.getContent().getList())) {
            CommunityBaseOrderRuleDTO communityBaseOrderRuleDTO = orderRuleCommunityPage.getContent().getList().stream()
                    .filter(getOrderRuleForPos(dto.getFirstClassificationId(),dto.getSecondClassificationId())).findFirst().orElse(null);
            if(Objects.nonNull(communityBaseOrderRuleDTO)){
                List<Long> itemIds = JSON.parseArray(communityBaseOrderRuleDTO.getChargeItemId(), Long.class);
                dto.setCommunityOrderRuleItemIds(itemIds);
            }
        }
    }

    private Predicate<CommunityBaseOrderRuleDTO> getOrderRuleForPos(final Integer firstClassificationId, final Integer secondClassificationId) {
        return communityBaseOrderRuleDTO -> Objects.equals(communityBaseOrderRuleDTO.getFirstClassificationId(), firstClassificationId.toString()) &&
                Objects.equals(communityBaseOrderRuleDTO.getSecondClassificationId(), secondClassificationId.toString());
    }
}
