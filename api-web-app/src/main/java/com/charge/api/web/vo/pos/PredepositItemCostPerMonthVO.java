package com.charge.api.web.vo.pos;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: zhengcan<PERSON>o
 */
@Data
@Builder
public class PredepositItemCostPerMonthVO {
    /**
     * 是否可进行支付 0 可以
     */
    private Integer type;
    /**
     *
     */
    private BigDecimal value = BigDecimal.ZERO;
    /**
     *
     */
    private String info;
    /**
     * 异常信息
     */
    private String message;
}
