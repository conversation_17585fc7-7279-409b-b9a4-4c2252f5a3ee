package com.charge.api.web.vo.pos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/4
 */
@NoArgsConstructor
@Data
public class ReceivablePayStatisticsDTO {
    @JsonProperty("total")
    private ReceivablePayStatisticDTO total;
    @JsonProperty("billCount")
    private Integer billCount;
    @JsonProperty("property")
    private ReceivablePayStatisticDTO property;
    @JsonProperty("power")
    private ReceivablePayStatisticDTO power;
    @JsonProperty("water")
    private ReceivablePayStatisticDTO water;
    @JsonProperty("others")
    private ReceivablePayStatisticDTO others;

    @NoArgsConstructor
    @Data
    public static class ReceivablePayStatisticDTO {
        @JsonProperty("receipts")
        private Double receipts;
        @JsonProperty("arrRate")
        private Double arrRate;
        @JsonProperty("recRate")
        private Double recRate;
        @JsonProperty("arrearage")
        private Double arrearage;
        @JsonProperty("receivable")
        private Double receivable;
    }


}


