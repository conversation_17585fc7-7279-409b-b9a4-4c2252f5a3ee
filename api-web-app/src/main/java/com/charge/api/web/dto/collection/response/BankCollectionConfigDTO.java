package com.charge.api.web.dto.collection.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class BankCollectionConfigDTO implements Serializable {
    private static final long serialVersionUID = -8360096699188199161L;

    private Boolean isConfig;
    private Long configId;
    private String planCode;
}
