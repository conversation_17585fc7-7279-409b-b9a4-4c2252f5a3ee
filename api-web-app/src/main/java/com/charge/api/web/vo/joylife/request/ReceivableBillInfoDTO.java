package com.charge.api.web.vo.joylife.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-08-07 14:47
 */

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceivableBillInfoDTO extends ReceivableBillInfo implements Serializable {


    private static final long serialVersionUID = 3936519416052017883L;

    /**
     * 收费对象：0=业户，1=开发商
     */
    private Integer chargeObject;
}
