package com.charge.api.web.controller.joylife.yuexin;

import com.charge.api.web.vo.joylife.response.AssetStandardConfigRuleVO;
import com.charge.api.web.vo.joylife.response.ChargeItemSimpleVO;
import com.charge.api.web.vo.joylife.response.TempChargeItemVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.PagingDTO;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.config.client.assets.AssetsChargeConfigClient;
import com.charge.config.client.item.ChargeItemClient;
import com.charge.config.client.item.CommunityChargeItemClient;
import com.charge.config.dto.assets.AssetStandardConfigRuleDTO;
import com.charge.config.dto.assets.AssetStandardConfigRuleQueryConditionDTO;
import com.charge.config.dto.item.ChargeItemDTO;
import com.charge.config.dto.item.CommunityChargeItemDTO;
import com.charge.config.dto.item.condition.ChargeItemQueryConditionDTO;
import com.charge.config.dto.item.condition.CommunityChargeItemQueryConditionDTO;
import com.charge.config.enums.BusinessTypeEnum;
import com.charge.config.enums.StandardConfigStatusEnum;
import com.charge.joylife.dto.AssetStandardConfigRuleQueryDTO;
import com.charge.joylife.dto.ChargeItemQueryDTO;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.order.client.OrderRuleClient;
import com.charge.order.dto.rule.CommunityBaseOrderRuleDTO;
import com.charge.order.dto.rule.CommunityOrderRuleQueryDTO;
import com.charge.order.enums.EnableEnum;
import com.charge.order.enums.OrderRuleCategoryEnum;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 收费项接口
 *
 * <AUTHOR>
 * @date 2024/03/04 14:19
 */
@RestController
@RequestMapping(value = "/yuexin/order")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ChargeItemController {
    private final CommunityChargeItemClient communityChargeItemClient;
    private final CommunityClient communityClient;
    private final OrderRuleClient orderRuleClient;
    private final ChargeItemClient chargeItemClient;
    private final AssetsChargeConfigClient assetsChargeConfigClient;

    /**
     * 获取押金项列表
     * @param communityMsId
     * @return
     * @throws ChargeBusinessException
     */
    @RequestMapping(value = "/item/getDepositItemList", method = {RequestMethod.GET})
    public ChargeResponse<List<ChargeItemSimpleVO>> getDepositItemList(@RequestParam String communityMsId) throws ChargeBusinessException {
        CommunityDTO communityDTO = communityClient.oneByCondition(CommunityCondition.builder().msId(communityMsId).build()).getContent();
        Long communityId = communityDTO.getId();

        CommunityChargeItemQueryConditionDTO conditionDTO = new CommunityChargeItemQueryConditionDTO();
        conditionDTO.setCommunityId(communityId);
        conditionDTO.setBusinessTypes(Lists.newArrayList(BusinessTypeEnum.DEPOSIT.getCode()));
        conditionDTO.setStatus(Collections.singletonList(StandardConfigStatusEnum.ENABLE.getCode()));
        PagingDTO<CommunityChargeItemDTO> pagingDTO = AppInterfaceUtil.getResponseDataThrowException(communityChargeItemClient.queryCommunityChargeItemPage(conditionDTO));
        List<ChargeItemSimpleVO> depositItemVOS = pagingDTO.getList().stream().map(dto -> ChargeItemSimpleVO.builder()
                .itemId(dto.getItemId())
                .itemName(dto.getItemName())
                .itemCode(dto.getItemCode())
                .build()).collect(Collectors.toList());
        depositItemVOS.sort(Comparator.comparing(ChargeItemSimpleVO::getItemCode));
        return new ChargeResponse<>(depositItemVOS);
    }

    /**
     * 获取临时收费项列表
     * @param communityMsId
     * @return
     * @throws ChargeBusinessException
     */
    @RequestMapping(value = "/item/getTempItemList", method = {RequestMethod.GET})
    public ChargeResponse<List<TempChargeItemVO>> getTempItemList(@RequestParam String communityMsId) throws ChargeBusinessException {
        CommunityDTO communityDTO = communityClient.oneByCondition(CommunityCondition.builder().msId(communityMsId).build()).getContent();
        Long communityId = communityDTO.getId();
        //临时类收费项查询
        CommunityOrderRuleQueryDTO orderRuleQueryDTO = new CommunityOrderRuleQueryDTO();
        orderRuleQueryDTO.setCommunityId(communityId);
        orderRuleQueryDTO.setStatus(Collections.singletonList(EnableEnum.ENABLE.getCode()));
        orderRuleQueryDTO.setFirstClassificationId(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY.getCode());
        orderRuleQueryDTO.setSecondClassificationIds(Arrays.asList(String.valueOf(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_300.getCode()), String.valueOf(OrderRuleCategoryEnum.ONE_LEVEL_TEMPORARY_301.getCode())));
        PagingDTO<CommunityBaseOrderRuleDTO> baseOrderRulePaging = AppInterfaceUtil.getResponseDataThrowException(orderRuleClient.getCommunityPage(orderRuleQueryDTO));
        if (CollectionUtils.isEmpty(baseOrderRulePaging.getList())) {
            return new ChargeResponse<>();
        }

        //项目启用收费项查询
        List<Long> itemIdList = Lists.newArrayList();
        baseOrderRulePaging.getList().forEach(e->{
            if (!CollectionUtils.isEmpty(e.getCommunityChargeItemIds())) {
                itemIdList.addAll(e.getCommunityChargeItemIds().stream().map(item -> item.getId()).collect(Collectors.toList()));
            }
        });
        CommunityChargeItemQueryConditionDTO conditionDTO = new CommunityChargeItemQueryConditionDTO();
        conditionDTO.setCommunityId(communityId);
        conditionDTO.setItemIds(itemIdList);
        conditionDTO.setStatus(Collections.singletonList(StandardConfigStatusEnum.ENABLE.getCode()));
        PagingDTO<CommunityChargeItemDTO> communityItemPaging = AppInterfaceUtil.getResponseDataThrowException(communityChargeItemClient.queryCommunityChargeItemPage(conditionDTO));
        Map<Long, CommunityChargeItemDTO> communityChargeItemDTOMap = CollectionUtils.isEmpty(communityItemPaging.getList()) ? Collections.emptyMap() :
          communityItemPaging.getList().stream().collect(Collectors.toMap(CommunityChargeItemDTO::getItemId, Function.identity(), (k1, k2) -> k1));

        List<TempChargeItemVO> result = Lists.newArrayList();
        baseOrderRulePaging.getList().forEach(e -> {
            TempChargeItemVO tempChargeItemVO = TempChargeItemVO.builder().secondClassificationId(e.getSecondClassificationId())
                    .secondClassificationName(e.getSecondClassificationName())
                    .chargeItemSimpleVOList(Lists.newArrayList()).build();
            result.add(tempChargeItemVO);
            if (CollectionUtils.isEmpty(e.getCommunityChargeItemIds())) {
                return;
            }
            e.getCommunityChargeItemIds().forEach(item -> {
                if (communityChargeItemDTOMap.containsKey(item.getId())) {
                    CommunityChargeItemDTO communityChargeItemDTO = communityChargeItemDTOMap.get(item.getId());
                    tempChargeItemVO.getChargeItemSimpleVOList().add(ChargeItemSimpleVO.builder().itemId(communityChargeItemDTO.getItemId()).itemName(communityChargeItemDTO.getItemName()).itemCode(communityChargeItemDTO.getItemCode()).build());
                }
            });
            tempChargeItemVO.getChargeItemSimpleVOList().sort(Comparator.comparing(ChargeItemSimpleVO::getItemCode));
        });

        result.sort(Comparator.comparing(TempChargeItemVO::getSecondClassificationId));
        return new ChargeResponse<>(result);
    }

    /**
     * 查询收费项信息
     * @param queryDTO
     * @return
     * @throws ChargeBusinessException
     */
    @ApiOperation(value = "查询收费项信息")
    @PostMapping(value = "/item/batchQuery")
    public ChargeResponse<List<ChargeItemSimpleVO>> batchQueryItemInfo(@Valid @RequestBody ChargeItemQueryDTO queryDTO) throws ChargeBusinessException {
        ChargeItemQueryConditionDTO conditionDTO = new ChargeItemQueryConditionDTO();
        conditionDTO.setItemIds(queryDTO.getItemIdList());
        PagingDTO<ChargeItemDTO> chargeItemPagingDTO = AppInterfaceUtil.getResponseDataThrowException(chargeItemClient.queryChargeItemList(conditionDTO));
        if (CollectionUtils.isEmpty(chargeItemPagingDTO.getList())) {
            return new ChargeResponse<>();
        }

        List<ChargeItemSimpleVO> result = chargeItemPagingDTO.getList().stream().map(e -> ChargeItemSimpleVO.builder().itemId(e.getId()).itemName(e.getItemName()).itemCode(e.getItemCode()).build()).collect(Collectors.toList());
        return new ChargeResponse<>(result);
    }

    /**
     * 资产绑定计费规则查询
     * @param queryDTO
     * @return
     * @throws ChargeBusinessException
     */
    @ApiOperation(value = "资产绑定计费规则查询")
    @PostMapping(value = "/asset/standard/config/rule")
    public ChargeResponse<List<AssetStandardConfigRuleVO>> batchQueryAssetStandardConfigRule(@Valid @RequestBody AssetStandardConfigRuleQueryDTO queryDTO) throws ChargeBusinessException {
        CommunityDTO communityDTO = communityClient.oneByCondition(CommunityCondition.builder().msId(queryDTO.getCommunityMsId()).build()).getContent();
        AssetStandardConfigRuleQueryConditionDTO conditionDTO = BeanCopierWrapper.copy(queryDTO, AssetStandardConfigRuleQueryConditionDTO.class);
        conditionDTO.setCommunityId(communityDTO.getId());
        ChargeResponse<List<AssetStandardConfigRuleDTO>> listChargeResponse = assetsChargeConfigClient.batchQueryAssetStandardConfigRule(conditionDTO);
        List<AssetStandardConfigRuleDTO> assetStandardConfigRuleDTOS = AppInterfaceUtil.getResponseDataThrowException(assetsChargeConfigClient.batchQueryAssetStandardConfigRule(conditionDTO));
        return new ChargeResponse<>(BeanCopierWrapper.copy(assetStandardConfigRuleDTOS, AssetStandardConfigRuleVO.class));
    }
}
