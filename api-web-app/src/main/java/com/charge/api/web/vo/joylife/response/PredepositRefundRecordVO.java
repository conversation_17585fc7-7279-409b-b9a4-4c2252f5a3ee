package com.charge.api.web.vo.joylife.response;

import com.charge.common.util.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预收退款记录
 *
 * <AUTHOR>
 */
@Data
public class PredepositRefundRecordVO  {

    /**
     * 退款单号
     */
    private String refundBillNum;

    /**
     * 退款时间
     */
    @JsonFormat(pattern = DateUtils.FORMAT_0, timezone = "GMT+8")
    private Date refundTime;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 收款账号
     */
    private String receiveAccount;

    /**
     * 退款说明
     */
    private String remark;

    /**
     * 退款状态：1 报账审核中, 2报账审核通过, 3报账已驳回, 4退款成功, 5退款失败
     */
    private Integer refundStatus;

    /**
     * 退款人
     */
    private String refundUser;

    /**
     * 退款单id
     */
    private Long refundBillId;


}
