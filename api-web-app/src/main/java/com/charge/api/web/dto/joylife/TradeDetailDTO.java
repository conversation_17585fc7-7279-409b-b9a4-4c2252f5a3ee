package com.charge.api.web.dto.joylife;

import com.charge.api.web.config.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/20
 */
@Data
public class TradeDetailDTO implements Serializable {

    private static final long serialVersionUID = -972544118078947656L;

    private String communityName;
    private String buildingName;
    private String unitName;
    private String houseId;
    private String houseName;
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalPay;
    private List<TradeDetailMonthInfo> billList;
}
