package com.charge.api.web.vo.joylife.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/10 13:57
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PrestorePayStatusVO implements Serializable {

    /**
     * 交易id
     */
    private String transactionNo;
    /**
     * 订单号
     */
    private String orderNum;
    /**
     * 支付状态
     */
    private String payStatus;
    /**
     * 赠送积分数
     */
    private Integer points;
    /**
     * 支付金额
     */
    private BigDecimal amount;
    /**
     * 支付id
     */
    private String payId;
}
