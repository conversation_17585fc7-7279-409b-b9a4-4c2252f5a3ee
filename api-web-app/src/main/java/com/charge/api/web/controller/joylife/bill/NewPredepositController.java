package com.charge.api.web.controller.joylife.bill;

import com.charge.api.web.service.joylife.YueXinAppService;
import com.charge.common.constant.CommonConstant;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.core.util.TraceContextUtil;
import com.charge.joylife.vo.AssetPredepositAccountListVO;
import com.charge.joylife.vo.AssetPredepositQueryVO;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.CommunityCondition;
import com.charge.maindata.enums.StatusEnum;
import com.charge.maindata.pojo.dto.CommunityDTO;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Api(tags = {"zhaoxi_api"}, value = "朝昔新接口")
@RestController
@RequestMapping(value = "/api/zhaoxi")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class NewPredepositController {

    private final CommunityClient communityClient;

    private final YueXinAppService yueXinAppService;


    /**获取资产预存余额明细
     * @return
     */
    @PostMapping("/asset/predepositList")
    public ChargeResponse<List<AssetPredepositAccountListVO>> getAssetPredepositList (@Valid @RequestBody AssetPredepositQueryVO conditionVO) throws ChargeBusinessException {
        ChargeResponse<CommunityDTO> communityDTOChargeResponse = communityClient.oneByCondition(CommunityCondition.builder().msId(conditionVO.getCommunityMsId()).status(StatusEnum.USING.getCode()).build());
        if (communityDTOChargeResponse == null || !communityDTOChargeResponse.isSuccess() || communityDTOChargeResponse.getContent() == null) {
            return new ChargeResponse<>(CommonConstant.FAIL,"查询不到该小区数据");
        }
        Long communityId = communityDTOChargeResponse.getContent().getId();
        conditionVO.setCommunityId(communityId);
        TraceContextUtil.setCommunityId(communityId);
        return new ChargeResponse<>(yueXinAppService.getAssetPredepositList(conditionVO));
    }
}
