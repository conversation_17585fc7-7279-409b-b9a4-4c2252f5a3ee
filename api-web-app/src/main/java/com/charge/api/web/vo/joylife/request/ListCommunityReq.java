package com.charge.api.web.vo.joylife.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/8/26 10:50
 */
@Data
public class ListCommunityReq {

    /**
     * 员工ldap账号
     */
    private String user;

    /**
     * 收费系统项目id
     */
    private Long communityId;

    private List<String> communityMsIds ;

    /**
     * 收费系统项目名称（模糊搜索）
     */
    private String communityName;

    /**
     * 是否虚拟项目
     */
    private Boolean isVirtual;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    /**
     * 每页数量
     */
    @NotNull(message = "每页数量不能为空")
    private Integer pageSize;

}
