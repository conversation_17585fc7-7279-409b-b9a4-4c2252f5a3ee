package com.charge.api.web.vo.arrearsnotice;

import com.charge.common.serializer.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>
 * 缴费通知下单响应
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ArrearsNoticeCreateOrderResp {

    /**
     * 缴费通知单id
     */
    @NotNull
    private Long id;

    /**
     * 收款账户
     */
    private String acceptorAccount;

    /**
     * 收款账户名
     */
    private String acceptorAccountName;

    /**
     * 收款账户名
     */
    private String acceptorAccountBank;


    /**
     * 转账码 招行app下单用
     */
    private String transferCode;

    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalAmount;

}
