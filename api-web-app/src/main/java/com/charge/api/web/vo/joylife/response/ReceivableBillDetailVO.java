package com.charge.api.web.vo.joylife.response;

import com.charge.bill.dto.ReceivableBillDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ReceivableBillDetailVO  implements Serializable {
	/**
	 * 账单ID
	 */
	@ApiModelProperty(value = "账单ID")
	private    Long    orderId;
	/**
	 * 费项ID
	 */
	@ApiModelProperty(value = "费项ID")
	private    Long    itemId;
	/**
	 * 费项名称
	 */
	@ApiModelProperty(value = "费项名称")
	private    String    itemName;
	/**
	 * 费项金额本金
	 */
	@ApiModelProperty(value = "费项金额")
	private    String    itemArrearsAmount;
	/**
	 * 费项金额违约金
	 */
	@ApiModelProperty(value = "费项金额")
	private    String    itemPenaltyAmount;
	/**
	 * 订单状态名称
	 */
	@ApiModelProperty(value = "订单状态")
	private    String    orderStatus;
	/**
	 * 订单状态id
	 */
	@ApiModelProperty(value = "订单状态")
	private    Integer    orderStatusId;
	/**
	 * 使用量
	 */
	@ApiModelProperty(value = "使用量")
	private    String    usage;
	/**
	 * 单位
	 */
	@ApiModelProperty(value = "单位")
	private    String    unit;
	/**
	 * 订单生效时间
	 */
	@ApiModelProperty(value = "订单生效时间")
	private Long effectTime;

	public static ReceivableBillDetailVO from(ReceivableBillDTO order) {
		ReceivableBillDetailVO orderItemArrears=new ReceivableBillDetailVO();
		orderItemArrears.setItemId(order.getItemId());
		orderItemArrears.setItemName(order.getItemName());
		orderItemArrears.setItemArrearsAmount((order.getArrearsAmount() == null ? BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP) : order.getArrearsAmount().setScale(2,BigDecimal.ROUND_HALF_UP)).toString());
		orderItemArrears.setItemPenaltyAmount((order.getPenaltyArrearsAmount() == null ? BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP) : order.getPenaltyArrearsAmount().setScale(2,BigDecimal.ROUND_HALF_UP)).toString());
		orderItemArrears.setOrderId(order.getId());
		orderItemArrears.setOrderStatus(order.getStatusName());
		orderItemArrears.setOrderStatusId(order.getPayStatus().intValue());
		orderItemArrears.setEffectTime(order.getEffectTime());
//		if(!StringUtils.isEmpty(order.getMemo())){
//		//	memo = memo + mergeMeterDetail.getLastNum().stripTrailingZeros().toPlainString() + "-" + mergeMeterDetail.getCurrentNum().stripTrailingZeros().toPlainString() + "    " + meterCount.stripTrailingZeros().toPlainString() + ",";
//			String[]  usageInfo=order.getMemo().split("    ");
//			if(usageInfo.length>1){
//				String usage=usageInfo[1];
//				if(NumberUtils.isNumber(usage)){
//					orderItemArrears.setUsage(usage);
//					if(order.getItemName().contains("电费")){
//						orderItemArrears.setUnit("度");
//					}else{
//						orderItemArrears.setUnit("m³");
//					}
//
//				}
//			}
//		}

		return orderItemArrears;
	}
}
