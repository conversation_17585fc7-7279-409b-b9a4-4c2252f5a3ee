package com.charge.api.web.service.joylife;

import com.charge.api.web.dto.joylife.PayResultV1;
import com.charge.bill.dto.income.IncomeOrderInfosDTO;
import com.charge.bill.dto.income.WorkOrderInfosDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;

/**
 * <AUTHOR>
 * @Description:实收单接口
 * @date 2022/12/1416:22
 */
public interface IncomeBillService {


    ChargeResponse<Long> createIncomeBills(IncomeOrderInfosDTO incomeOrderInfosDTO) throws ChargeBusinessException;

    ChargeResponse<Long> createWorkOrderBills(WorkOrderInfosDTO workOrderInfosDTO) throws ChargeBusinessException;

    /**
     * 根据交易订单号查询支付状态
     *
     * @param orderNum 交易订单号
     * @return
     */
    ChargeResponse<PayResultV1> getPayResult(String orderNum) throws ChargeBusinessException;
}
