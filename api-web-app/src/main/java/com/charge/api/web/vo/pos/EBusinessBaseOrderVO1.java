package com.charge.api.web.vo.pos;

import com.charge.common.serializer.BigDecimalSerializer;
import com.charge.common.serializer.IdSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;

/**
 * EBusinessBaseOrderVO1
 *
 * <AUTHOR>
 * @date 2024/11/14
 */
@Data
public class EBusinessBaseOrderVO1 {

    @JsonSerialize(using = IdSerializer.class)
    private Long id;
    /**
     * 费项id
     */
    private Long itemId;
    /**
     * 费项名称
     */
    private String itemName;
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 外部商品订单id
     */
    private String extendOrderId;
    /**
     * 欠费金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal arrearsAmount;
    /**
     * 总金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalAmount;
    /**
     * 已支付金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalPaidAmount;

    public void fillAmount(){
     this.arrearsAmount=this.totalAmount.subtract(this.totalPaidAmount);
    }
}