package com.charge.api.web.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

/**
 * 收费1.0-悦心返回对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResultPage {

    public static final int CODE_SUCCESS = 0;
    public static final int CODE_FAILED = -1;

    private int code;
    private String message;
    private Object content;
    private Integer pageSize;
    private Integer totalPage;
    private Integer currentPage;
    private Integer totalRecord;

}
