package com.charge.api.web.dto.joylife;

import com.charge.api.web.config.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/20
 */
@Data
public class TradeDetailItemInfo implements Serializable {

    private static final long serialVersionUID = -1651508338304693993L;
    private String itemId;
    private String itemName;
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal actualMoney;
    private String memo;
}
