package com.charge.api.web.service.pos;

import com.charge.api.web.dto.pos.AddPredepositRequestDTO;
import com.charge.api.web.vo.joylife.request.PreStoreCalReq;
import com.charge.api.web.vo.pos.AddPredepositResultVo;
import com.charge.api.web.vo.pos.PreDepositAccountPosDTOV1;
import com.charge.api.web.vo.pos.PredepositItemCostPerMonthVO;
import com.charge.bill.dto.predeposit.WaitingCarryForwardDTO;
import com.charge.bill.dto.predeposit.pos.PredepositRefundDetailPosDTO;
import com.charge.bill.dto.predeposit.pos.PredepositRefundSearchDetailDTO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.config.dto.item.PreStoreItemConfigDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/2/28 15:03
 */
public interface PreDepositService {
    ChargeResponse<WaitingCarryForwardDTO> getWaitingCarryForward(String communityUuid, String houseUuid);

    ChargeResponse<PreDepositAccountPosDTOV1> getPreStoreDetail(String communityUuid, String houseUuid);

    ChargeResponse<PredepositRefundDetailPosDTO> getCheckDetail(String id, String communityUuid, String houseId) throws ChargeBusinessException;

    ChargePageResponse<List<PredepositRefundSearchDetailDTO>> refundSearch(String communityUuid, String receiptNo, String paymentName, String houseNo, String payStart, String payEnd, String payMonth, Integer pageNum, Integer pageSize);

    /**
     * 预收充值
     * @return
     */
    AddPredepositResultVo addNewDepositInfo4House(AddPredepositRequestDTO addPredepositRequestDTO) throws ChargeBusinessException;

    PreStoreItemConfigDTO getCommunityPreStoreItem(Long communityId) throws ChargeBusinessException;

    List<Long> listChargeItemIdsNotBind(List<Long> chargeItemIds, Long communityId, Long assetId) throws ChargeBusinessException;

    PredepositItemCostPerMonthVO calPreStore(PreStoreCalReq calRequest) throws ChargeBusinessException;


    PredepositItemCostPerMonthVO getPredepositItemCostPerMonth(Long communityId, Long assetId, Long predepositItemId) throws ChargeBusinessException;
}
