package com.charge.api.web.vo.ebusiness;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Author: yjw
 * @Date: 2024/9/18 14:50
 */
@Data
public class EBusinessPayment {

    /**
     * 支付id
     */
    @NotBlank(message = "支付id不能为空")
    private String payId;
    /**
     * 支付方式(微信：WECHAT 支付宝：ALIPAY 线下支付：OFFLINE)
     */
    @NotBlank(message = "支付方式不能为空")
    private String payType;
    /**
     * 支付成功时间 格式：yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "支付成功时间不能为空")
    private String payTime;
    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空")
    private BigDecimal payPrice;
    /**
     * 支付流水号
     */
    @NotBlank(message = "支付流水号不能为空")
    private String tradeNo;
    /**
     * 支付商品明细
     */
    @Valid
    private List<EBusinessPayItem> payItems;
}
