package com.charge.api.web.vo.joylife.deposit;

import com.charge.config.dto.item.CommunityPrestoreGroupDetailDTO;
import com.charge.config.dto.item.CommunitySpecialPrestoreGroupItemDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GroupPrestoreChargeItemVO  implements Serializable {

	private static final long serialVersionUID = 4298355523920569733L;

	/**
	 * 预存组合ID
	 */
	private Long groupItemId;

	/**
	 * 预存组合名称
	 */
	private String groupItemName;

	/**
	 * 缴费月数列表
	 */
	private List<Integer> monthList;

	/**
	 * 每月金额
	 */
	private BigDecimal chargeMoneyPerMonth;

	/**
	 * 收费项预存信息列表
	 */
	private List<PrestoreVO> prestoreChargeItemDetailList;

	public static List<PrestoreChargeInfoVO> fromPrestoreVo(List<PrestoreChargeInfoVO> chargeInfoVOList, CommunityPrestoreGroupDetailDTO group) {
		if(CollectionUtils.isEmpty(chargeInfoVOList) || CollectionUtils.isEmpty(group.getPrestoreItemList())){
			return null;
		}
		List<PrestoreChargeInfoVO> result=new ArrayList<>();
		for(CommunitySpecialPrestoreGroupItemDTO item:group.getPrestoreItemList()){
			for(PrestoreChargeInfoVO chargeItem:chargeInfoVOList){
				if(item.getItemId() == null) continue;
				if(chargeItem.getItemId().equals(item.getItemId().toString()) && !ObjectUtils.isEmpty(chargeItem.getChargeMoneyPerMonth())){
					result.add(chargeItem);
				}
			}
		}
		if(result.size() == group.getPrestoreItemList().size()){
			return result;
		}
		return null;
	}

	public static GroupPrestoreChargeItemVO from(CommunityPrestoreGroupDetailDTO group, List<PrestoreVO> voList) {
		Optional<BigDecimal> moneyOptional=voList.stream().map(item->(item.getChargeMoneyPerMonth() == null ? BigDecimal.ZERO : item.getChargeMoneyPerMonth()))
				.reduce(BigDecimal::add);
		BigDecimal money= moneyOptional.orElse(BigDecimal.ZERO);
		return GroupPrestoreChargeItemVO.builder().groupItemId(group.getId())
				.groupItemName(group.getGroupName())
				.monthList(Arrays.asList(3,6,12))
				.chargeMoneyPerMonth(money)
				.prestoreChargeItemDetailList(voList).build();

	}
}
