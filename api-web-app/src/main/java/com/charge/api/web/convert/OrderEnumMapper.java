package com.charge.api.web.convert;

import com.charge.order.enums.EBusinessOrderPayStatusEnum;
import com.charge.order.enums.OrderSourceEnum;
import com.charge.order.enums.PaymentModeEnum;

/**
 * EnumMapper
 *
 * <AUTHOR>
 * @date 2024/12/13
 */
public class OrderEnumMapper {
    public OrderEnumMapper() {
    }

    public PaymentModeEnum asPaymentModeEnum(Integer payMode) {
        return PaymentModeEnum.ofCode(payMode);
    }

    public EBusinessOrderPayStatusEnum asEBusinessOrderPayStatusEnum(Integer status){
        return EBusinessOrderPayStatusEnum.ofCode(status);
    }

    public OrderSourceEnum asOrderSourceEnum(String source){
        return OrderSourceEnum.of(source);
    }


}