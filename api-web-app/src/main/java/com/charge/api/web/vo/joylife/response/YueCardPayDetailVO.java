package com.charge.api.web.vo.joylife.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023/4/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class YueCardPayDetailVO implements Serializable {
    /**
     * 资产流水id
     */
    private String payOrderId;

    /**
     * 车牌号
     */
    private String carNo;
    /**
     * 月卡名称
     */
    private String yardName;
    /**
     * 开始时间
     */
    private String startDay;
    /**
     * 结束时间
     */
    private String endDay;
    /**
     * 支付方式
     */
    private String paymentMethod;
    /**
     * 支付时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;
}
