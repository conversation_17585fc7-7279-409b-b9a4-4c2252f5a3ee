package com.charge.api.web.controller.joylife.yuexin;

import com.charge.api.web.config.SwitchConfig;
import com.charge.api.web.constants.YueConstants;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.service.joylife.YueXinAppService;
import com.charge.api.web.support.*;
import com.charge.api.web.vo.joylife.CommunityMasterData;
import com.charge.api.web.vo.joylife.PredepositDepositAccountQueryVO;
import com.charge.api.web.vo.joylife.PredepositDepositDetailQueryVO;
import com.charge.api.web.vo.joylife.PredepositRefundParamVO;
import com.charge.api.web.vo.joylife.response.*;
import com.charge.bill.client.PredepositAccountClient;
import com.charge.bill.client.PredepositRefundClient;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.client.UploadFileRecordClient;
import com.charge.bill.dto.AssetArrearsCountQueryDTO;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.UploadFileRecordDTO;
import com.charge.bill.dto.assetoverview.AssetOverViewDTO;
import com.charge.bill.dto.predeposit.AsssetPredepositCountDTO;
import com.charge.bill.dto.predeposit.PredepositDepositAccountQueryDTO;
import com.charge.bill.dto.predeposit.PredepositRefundDTO;
import com.charge.bill.enums.ReceivableBillStatusEnum;
import com.charge.bill.enums.ReceivalbleBillPayStatusEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.dto.Paging;
import com.charge.common.dto.PagingDTO;
import com.charge.common.enums.common.AliBucketEnum;
import com.charge.common.enums.common.ChargeObjEnum;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.common.util.OssUtil;
import com.charge.common.wrapper.BeanCopierWrapper;
import com.charge.config.client.item.ChargeItemClient;
import com.charge.config.dto.item.ChargeItemDTO;
import com.charge.config.dto.item.condition.ChargeItemQueryConditionDTO;
import com.charge.config.dto.meter.MeterBookAssetDetailDTO;
import com.charge.config.dto.meter.condition.MeterBookDetailConditionDTO;
import com.charge.core.enums.LogCategoryEnum;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.TraceContextUtil;
import com.charge.joylife.dto.ArrearsAssetInfo;
import com.charge.joylife.dto.CommunityArrearsAssetsReq;
import com.charge.joylife.dto.ListAssetsArrearsReq;
import com.charge.maindata.client.AssetClient;
import com.charge.maindata.client.CommunityClient;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.pay.client.BankAccountClient;
import com.charge.pay.dto.OrgBankInfoDTO;
import com.charge.pay.dto.bank.OrgBankConditionDTO;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.charge.api.web.support.AssetSupport.ofAssetTypeEnum;
import static com.charge.common.util.DateUtils.FORMAT_14;

/**
 * 悦心员工APP催缴相关接口
 * Author: yjw
 * Date: 2023/3/17 14:08
 */

@RestController
@RequestMapping(value = "/app/yuexin")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class YueXinAppController {

    private final AssetClient assetClient;

    private final CommunityClient communityClient;

    private final ReceivableBillClient receivableBillClient;

    private final PredepositAccountClient predepositAccountClient;

    private final YueXinAppService yueXinAppService;

    private final AssetSupport assetSupport;

    private final MeterBookSupport meterBookSupport;

    private final PredepositRefundClient predepositRefundClient;

    private final BankAccountClient bankAccountClient;

    private final CommunityParamSupport communityParamSupport;

    private final CommunitySupport communitySupport;

    private final AssetOverviewSupport assetOverviewSupport;

    private final ChargeItemClient chargeItemClient;

    private final UploadFileRecordClient uploadFileRecordClient;

    private final SwitchConfig switchConfig;

    /**
     * 查询房屋欠费详情
     */
    @ApiOperation(value = "查询房屋欠费详情")
    @GetMapping(value = "/getArrearsItemAmount/detail")
    public ChargeResponse<HouseOrderItemArrears> getArrearsItemAmountYueXin(@RequestParam(name = "houseMsId") String houseMsId) throws ChargeBusinessException {
        AssetCondition assetCondition = AssetCondition.builder().msIds(Arrays.asList(houseMsId)).build();
        ChargeResponse<List<AssetDTO>> assetResponse = assetClient.listAsset(assetCondition);
        List<AssetDTO> assetDTOS = AppInterfaceUtil.getResponseData(assetResponse);
        if (CollectionUtils.isEmpty(assetDTOS)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E8002);        }
        if (assetDTOS.size() > 1) {
            return new ChargeResponse(YueConstants.CODE_FAILED, "根据房间运营平台id收费系统存在" + assetDTOS.size() + "房屋");
        }
        Long communityId = assetSupport.getCommunityId(assetDTOS.get(0));
        //获取订单状态为部分核销、未核销，单据状态为生效中、已挂起、审核中的应收单
        ChargeResponse<List<ReceivableBillDTO>> billResponse = receivableBillClient.queryList(ReceivableConditionDTO.builder()
                .communityId(communityId)
                .assetIdList(Arrays.asList(assetDTOS.get(0).getId()))
                .payStatuses(Arrays.asList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(),
                        ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()))
                .billStatuses(Arrays.asList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),
                        ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                .chargeObject(ChargeObjEnum.CHARGE_OBJ_OWNER.getCode())
                .build());
        List<ReceivableBillDTO> receivableBillDTOS = AppInterfaceUtil.getResponseDataThrowException(billResponse);
        //查询仪表数据
        List<MeterBookAssetDetailDTO> meterDetails = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(receivableBillDTOS)) {
            List<Long> meterDetailIds = receivableBillDTOS.stream().filter(rec->Objects.nonNull(rec.getMeterBookDetailId()))
                    .map(ReceivableBillDTO::getMeterBookDetailId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(meterDetailIds)) {
                meterDetails = meterBookSupport.getMeterDetailByCondition(MeterBookDetailConditionDTO.builder()
                        .communityId(communityId).meterBookDetailIdList(meterDetailIds).build());
            }
        }
        Map<Long,MeterBookAssetDetailDTO> meterMap = CollectionUtil.isEmpty(meterDetails)?new HashMap<>():meterDetails.stream().collect(Collectors.toMap(m->m.getId(),m->m));
        HouseOrderItemArrears result = HouseOrderItemArrears.fromV2(assetDTOS.get(0), receivableBillDTOS, meterMap, new HashMap<>(), new ArrayList<>(), ChargeObjEnum.CHARGE_OBJ_OWNER.getCode());

        PredepositDepositAccountQueryDTO predepositDepositAccountQueryDTO = new PredepositDepositAccountQueryDTO();
        predepositDepositAccountQueryDTO.setCommunityId(communityId);
        predepositDepositAccountQueryDTO.setPaging(new Paging(1, 500));
        predepositDepositAccountQueryDTO.setAssetId(assetDTOS.get(0).getId());
        List<PredepositDepositAccountDTO> deposit = BeanCopierWrapper.copy(AppInterfaceUtil.getResponseDataThrowException(predepositAccountClient.queryDepositPage(predepositDepositAccountQueryDTO)).getList(), PredepositDepositAccountDTO.class);
        Double depositMoney = deposit.stream().collect(Collectors.summingDouble(t -> t.getAvailableBalance().doubleValue()));
        result.setDepositMoney(depositMoney.toString());
        return new ChargeResponse(result);
    }

    /**
     * 资产订单-预收押金列表
     */
    @PostMapping("/predeposit/account/deposit/list")
    public ChargeResponse<List<PredepositDepositAccountDTO>> assetDepositList(@Valid @RequestBody PredepositDepositAccountQueryVO vo) throws ChargeBusinessException {
        communityParamSupport.fillCommunityId(vo);
        assetSupport.fillAssetId(vo);
        PredepositDepositAccountQueryDTO copy = BeanCopierWrapper.copy(vo, PredepositDepositAccountQueryDTO.class);
        copy.setPaging(new Paging(1, 200));
        List<PredepositDepositAccountDTO> result = BeanCopierWrapper.copy(AppInterfaceUtil.getResponseDataThrowException(predepositAccountClient.queryDepositPage(copy)).getList(), PredepositDepositAccountDTO.class);

        result.forEach(item->{
            item.setAssetMsId(vo.getAssetMsId());
            item.setPredepositAccountId(item.getId());
        });
        return new ChargeResponse<>(result);
    }

    /**
     * 资产订单-预收押金详情
     */
    @PostMapping("/predeposit/account/deposit/detail")
    public ChargeResponse<PredepositDepositDetailDTO> assetDepositDetail(@Valid @RequestBody PredepositDepositDetailQueryVO vo) throws ChargeBusinessException {
        communityParamSupport.fillCommunityId(vo);
        assetSupport.fillAssetId(vo);
        TraceContextUtil.setCommunityId(vo.getCommunityId());

        //项目id
        PredepositDepositDetailDTO result = new PredepositDepositDetailDTO();
        //支付类型
        PredepositBillVO predepositBillVO = yueXinAppService.queryBillList(vo.getAssetId(), vo.getCommunityId(), vo.getPredepositAccountId());
        result.setPaymentType(predepositBillVO.getPayType());
        result.setReceiptNumber(predepositBillVO.getBillRelateInfoList().get(0).getRelatedNo());

        //详情
        PredepositDepositAccountQueryDTO copy = BeanCopierWrapper.copy(vo, PredepositDepositAccountQueryDTO.class);
        copy.setPaging(new Paging(1, 200));
        List<PredepositDepositAccountDTO> detailList = BeanCopierWrapper.copy(AppInterfaceUtil.getResponseDataThrowException(predepositAccountClient.queryDepositPage(copy)).getList(), PredepositDepositAccountDTO.class);
        result.setEmployeeName(detailList.get(0).getEmployeeName());
        result.setPaymentTime(detailList.get(0).getPaymentTime());
        result.setPredepositItemName(detailList.get(0).getPredepositItemName());
        result.setPredepositItemId(detailList.get(0).getPredepositItemId());
        ChargeItemQueryConditionDTO chargeItemQueryConditionDTO = new ChargeItemQueryConditionDTO();
        chargeItemQueryConditionDTO.setItemIds(Collections.singletonList(detailList.get(0).getPredepositItemId()));
        List<ChargeItemDTO> chargeItemDTOs = AppInterfaceUtil.getResponseDataThrowException(chargeItemClient.getBaseItemByIds(chargeItemQueryConditionDTO));
        if (CollectionUtil.isNotEmpty(chargeItemDTOs)){
            result.setPredepositItemCode(chargeItemDTOs.get(0).getItemCode());
        }
        result.setAvailableBalance(detailList.get(0).getAvailableBalance());
        result.setPayPrice(detailList.get(0).getDepositMoney());
        //调整
        result.setDepositAdjustRecordVO(yueXinAppService.listDepositAdjustRecord(vo.getCommunityId(),vo.getAssetId(), vo.getPredepositAccountId(), 1, 200));
        //退款
        result.setPredepositRefundRecordVO(yueXinAppService.listRefundRecord(vo.getPredepositAccountId(),  1, 200));
        //付款账户
        OrgBankConditionDTO orgBankConditionDTO = new OrgBankConditionDTO();
        orgBankConditionDTO.setCommunityIdList(Lists.newArrayList(vo.getCommunityId()));
        orgBankConditionDTO.setStatusList(Lists.newArrayList(1));
        orgBankConditionDTO.setPaging(new Paging(1, 10));
        ChargeResponse<PagingDTO<OrgBankInfoDTO>> orgBankPageResp = bankAccountClient.pagingByCondition(orgBankConditionDTO);
        result.setPayBankAccount(AppInterfaceUtil.getResponseDataThrowException(orgBankPageResp).getList().get(0).getBankAcc());
        return new ChargeResponse<>(result);
    }

    /**
     * 预收退款-提交
     * @param predepositRefundVO 退款请求
     * @return 退款结果
     * @throws ChargeBusinessException 业务异常
     */
    @PostMapping("/predeposit/account/deposit/refund")
    public ChargeResponse<PredepositRefundVO> refund(@Valid @RequestBody PredepositRefundParamVO predepositRefundVO) throws ChargeBusinessException {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(predepositRefundVO.getImagePathList()) && predepositRefundVO.getImagePathList().size() > 6) {
            return new ChargeResponse(YueConstants.CODE_FAILED, "图片最多传输6张");
        }

        communityParamSupport.fillCommunityId(predepositRefundVO);
        assetSupport.fillAssetId(predepositRefundVO);
        PredepositRefundDTO dto = BeanCopierWrapper.copy(predepositRefundVO, PredepositRefundDTO.class);
        dto.setCreateUser(predepositRefundVO.getOperator());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(predepositRefundVO.getImagePathList())){
            List<UploadFileRecordDTO> uploadFileRecordDTOS = saveRefundImages(predepositRefundVO.getImagePathList());
            List<Long> fileIds = uploadFileRecordDTOS.stream().map(t -> t.getId()).collect(Collectors.toList());
            dto.setFileIds(fileIds);
        }

        PredepositRefundVO result = new PredepositRefundVO();
        result.setRefundBillNum(AppInterfaceUtil.getResponseDataThrowException(predepositRefundClient.refundReturnBillNo(dto)));
        return new ChargeResponse<>(result);
    }

    private void fillThisMonthArrearsMap(List<Long> assetIds, Long communityId, String currentBelongYear,Map<Long, BigDecimal> thisMonthArrearsMap) throws ChargeBusinessException {
        ChargeResponse<List<ReceivableBillDTO>> chargeResponse = receivableBillClient.queryList(ReceivableConditionDTO.builder()
                .belongYears(currentBelongYear)
                .assetIdList(assetIds)
                .payStatuses(Arrays.asList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(), ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()
                        , ReceivalbleBillPayStatusEnum.COLLECTION.getCode(), ReceivalbleBillPayStatusEnum.PAY_WAIT.getCode()))
                //朝昔只需要生效的欠费
                .billStatuses(Collections.singletonList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode()))
                .communityId(communityId).build());
        List<ReceivableBillDTO> receivableBillDTOS = AppInterfaceUtil.getResponseDataThrowException(chargeResponse);
        receivableBillDTOS.stream().collect(Collectors.groupingBy(ReceivableBillDTO::getAssetId)).forEach((key, value) -> {
            BigDecimal totalArrears = value.stream().map(ReceivableBillDTO::getArrearsAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            thisMonthArrearsMap.put(key, totalArrears);
        });

    }

    @ApiOperation(value = "获取批量房间的欠费统计和预存可用余额")
    @PostMapping(value = "/getArrearsPrestoreItemAmount/house")
    public ChargeResponse<List<AssetArrearsDetailVO>> getArrearsPrestoreItemAmountYueXinNoPage(@RequestBody @Valid CommunityMasterData masterData) throws ChargeBusinessException {
        Long communityId = communitySupport.getCommunityIdByMsId(masterData.getCommunityMsId());
        List<AssetAdapter> assetDTOS = assetSupport.getAssetListByMsIds(communityId, masterData.getHouseMsIdList());
        if (CollectionUtils.isEmpty(assetDTOS)) {
            return new ChargeResponse<>(new ArrayList<>());
        }
        Map<Long, AssetAdapter> assetAdapterMap = assetDTOS.stream().collect(Collectors.toMap(AssetAdapter::getId, Function.identity(), (k1, k2) -> k1));
        List<Long> assetIdList = Lists.newArrayList(assetAdapterMap.keySet());
        if (switchConfig.isUseNewAssetOverview(communityId)) {
            List<AssetOverViewDTO> assetOverViews = assetOverviewSupport.listAssetOverview(communityId, null, null, ofAssetTypeEnum(masterData.getAssetType()), null, false, assetIdList, null);
            if (CollectionUtil.isEmpty(assetOverViews)) {
                return new ChargeResponse<>(new ArrayList<>());
            }
            String currentBelongYear = DateUtils.formatLocalDate(LocalDate.now(), FORMAT_14);
            Map<Long, BigDecimal> thisMonthArrearsMap = new HashMap<>();
            fillThisMonthArrearsMap(assetIdList, communityId, currentBelongYear, thisMonthArrearsMap);
            List<AssetArrearsDetailVO> assetArrearsDetailVOS = AssetArrearsDetailVO.ofList(assetOverViews, masterData.getCommunityMsId(), thisMonthArrearsMap, assetAdapterMap);
            return new ChargeResponse<>(assetArrearsDetailVOS);
        }
        //获取订单状态为部分核销、未核销，单据状态为生效中、已挂起、审核中的应收单
        ChargeResponse<List<ReceivableBillDTO>> billResponse = receivableBillClient.queryList(ReceivableConditionDTO.builder()
                .assetIdList(assetIdList).payStatuses(Arrays.asList(ReceivalbleBillPayStatusEnum.NOT_PAY.getCode(),
                        ReceivalbleBillPayStatusEnum.PAY_PARTIAL.getCode()))
                .billStatuses(Arrays.asList(ReceivableBillStatusEnum.BILL_EFFECTIVE.getCode(),
                        ReceivableBillStatusEnum.BILL_HOLD.getCode()))
                .communityId(communityId).build());
        List<ReceivableBillDTO> receivableBillDTOS = AppInterfaceUtil.getResponseDataThrowException(billResponse);
        Map<Long,List<AsssetPredepositCountDTO>> predepositMap = Maps.newHashMapWithExpectedSize(assetDTOS.size());
        List<List<Long>> partition = com.google.common.collect.Lists.partition(assetIdList, 100);
        //批量查预存余额=bill接口有100个资产限制，朝昔员工端页面分页客户资产查询，一般少于100个
        for (int i = 0,size = partition.size(); i < size; i++) {
            AssetArrearsCountQueryDTO assetArrearsCountQueryDTO = new AssetArrearsCountQueryDTO();
            assetArrearsCountQueryDTO.setCommunityId(communityId);
            assetArrearsCountQueryDTO.setAssetIdList(partition.get(i));
            try{
                ChargeResponse<List<AsssetPredepositCountDTO>> listChargeResponse = predepositAccountClient.assetPredepositCountList(assetArrearsCountQueryDTO);
                List<AsssetPredepositCountDTO> predepositCountDTOS = CollectionUtils.isEmpty(AppInterfaceUtil.getResponseData(listChargeResponse)) ? Collections.emptyList() : listChargeResponse.getContent();
                Map<Long, List<AsssetPredepositCountDTO>> collectMap = predepositCountDTOS.stream().collect(Collectors.groupingBy(AsssetPredepositCountDTO::getAssetId));
                predepositMap.putAll(collectMap);
                log.info("{}|【朝昔】查询用户所有房间:{},预收账号余额：{}", LogCategoryEnum.BUSSINESS,assetIdList,predepositMap);
            } catch (Exception exception) {
                log.warn("【预存余额查询】异常 assetIdList：{},msg:{}", assetIdList, exception.getMessage());
            }
        }
        return new ChargeResponse(AssetArrearsDetailVO.fromV2(receivableBillDTOS, masterData.getCommunityMsId(), assetDTOS, predepositMap));
    }

    /**
     * 查询所有欠费资产id集合
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @ApiOperation(value = "查询所有欠费资产id集合")
    @PostMapping(value = "/getArrearsAssetInfoByCondition")
    public ChargeResponse<List<ArrearsAssetInfo>> getArrearsAssetInfoByCondition(@Valid @RequestBody CommunityArrearsAssetsReq condition) throws ChargeBusinessException {
        return yueXinAppService.getArrearsAssetInfoByCondition(condition);
    }

    /**
     * 查询批量资产欠费信息
     * @param condition
     * @return
     * @throws ChargeBusinessException
     */
    @ApiOperation(value = "查询批量资产欠费信息")
    @PostMapping(value = "/listAssetsArrearsInfo/detail")
    public ChargeResponse<List<HouseOrderItemArrears>> listAssetsArrearsInfo(@Valid @RequestBody ListAssetsArrearsReq condition) throws ChargeBusinessException {
        return yueXinAppService.listAssetsArrearsInfo(condition);
    }

    private List<UploadFileRecordDTO> saveRefundImages(List<String> imagePathList) throws ChargeBusinessException {
        //1下载到本地
        List<MultipartFile> fileList = new ArrayList<>();
        imagePathList.forEach(item->{
            InputStream imageStream = null;
            try {
                String name = FilenameUtils.getName(item);
                if (name.contains("?")){
                    String[] split = name.split("\\?");
                    name = split[0];
                }
                URL url = new URL(item);
                imageStream = url.openStream();
                MultipartFile file = new MockMultipartFile(name, name, null, imageStream);
                fileList.add(file);
            }  catch (Exception e) {
                log.error("退款图片下载到本地失败");
                throw new RuntimeException(e);
            } finally {
                try {
                    if (imageStream != null){
                        imageStream.close();
                    }
                } catch (IOException e) {
                    log.error("退款图片下载到本地：流关闭失败");
                    throw new RuntimeException(e);
                }
            }
        });

        //2存储到本地oss
        List<UploadFileRecordDTO> uploadFileRecordDOS = new ArrayList<>();

        fileList.forEach(item->{
            String originalFilename = item.getOriginalFilename();
            String fileKey = DateUtils.get14StrCurrentTime().concat("-").concat(originalFilename);
            try {
                OssUtil.putOssFile(null, AliBucketEnum.COMMON, item.getInputStream(), fileKey, false);
            } catch (Exception e) {
                log.error("退款图片上传到oss失败");
                throw new RuntimeException(e);
            }
            UploadFileRecordDTO uploadFileRecordDO = new UploadFileRecordDTO();
            uploadFileRecordDO.setOriginalName(originalFilename);
            uploadFileRecordDO.setOssKey(fileKey);
            uploadFileRecordDOS.add(uploadFileRecordDO);
        });
        //3存储到本地数据库
        return AppInterfaceUtil.getResponseDataThrowException(uploadFileRecordClient.batchSaveFileRecord(uploadFileRecordDOS));
    }

}
