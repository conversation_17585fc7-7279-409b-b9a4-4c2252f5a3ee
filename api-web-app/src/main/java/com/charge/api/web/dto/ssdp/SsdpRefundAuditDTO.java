package com.charge.api.web.dto.ssdp;

import java.io.Serializable;

import lombok.Data;

/**
 * 退款审核结果
 * 
 * <AUTHOR>
 * @date 2023/2/27
 */
@Data
public class SsdpRefundAuditDTO implements Serializable {

    private static final long serialVersionUID = -294905699340803298L;

    /**
     * 退款记录id
     */
    private String refundRecordId;

    /**
     * 退款状态(1表示审核通过,2表示驳回)，详见SsdpRefundAuditStatusEnum
     */
    private String refundStatus;
}
