package com.charge.api.web.vo.joylife.response;

import com.charge.common.util.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 押金信息
 */
@Data
public class PredepositDepositAccountDTO {

    /**
     * 预收账户id
     */
    private Long id;

    /**
    * 项目id
    */
    private Long communityId;

    /**
    * 朝昔资产id
    */
    private String assetMsId;

    /**
     * 收费项id
     */
    private Long predepositItemId;

    /**
     * 资产流水id
     */
    private Long assetTransactionId;

    /**
     * 收费项名称（不拼接编码）
     */
    private String predepositItemName;

    /**
    * 缴费时间
    */
    @JsonFormat(pattern =  DateUtils.FORMAT_11,timezone = "GMT+8")
    private Date paymentTime;

    /**
     * 押金金额
     */
    private BigDecimal depositMoney;

    /**
     * 余额
     */
    private BigDecimal availableBalance;

    /**
     * 缴费人
     */
    private String employeeName;

    /**
     * 预收账户id
     */
    private Long predepositAccountId;

}
