package com.charge.api.web.controller.pos;

import com.charge.api.web.service.pos.ChargeBillService;
import com.charge.api.web.vo.pos.ReceiptVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/3/14
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(value = "收费2.0 pos收据相关接口")
public class ReceiptController {

    private final ChargeBillService chargeBillService;

    @ApiOperation(value = "查询批量账单收据详情", notes = "查询批量账单收据详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "orderId", value = "实收单id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @GetMapping(value = "/batchPay/getBatchReceiptInfo")
    public ChargeResponse<List<ReceiptVO>> getBatchReceiptInfo(@RequestParam String communityId, @RequestParam String orderId, @RequestParam String token) throws ChargeBusinessException {
        return new ChargeResponse<>(chargeBillService.getReceiptsByIncomeBill(Long.parseLong(orderId), Long.parseLong(communityId)));
    }


    @ApiOperation(value = "查询已支付订单收据信息(打印收据接口)", notes = "查询已支付订单收据信息")
    @RequestMapping(value = "/getReceiptInfo", method = {RequestMethod.GET})
    public ChargeResponse<ReceiptVO> getReceiptInfo(@RequestParam(value = "payId") String payId, String token) throws Exception {
        return new ChargeResponse<>(chargeBillService.getReceiptsByAssetTransactionId(Long.parseLong(payId)));
    }

    @ApiOperation(value = "更新批量账单收据信息", notes = "更新批量账单收据信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityId", value = "小区ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "orderId", value = "订单ID/实际上传实收单id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "printer", value = "打印人", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/batchPay/updateBatchReceiptInfo", method = {RequestMethod.POST})
    public ChargeResponse<Void> updateBatchReceiptInfo(String communityId, String orderId, String printer,String token) throws ChargeBusinessException {
        /**
         * 批量缴费时打印会先调updateReceiptInfo 再掉updateBatchReceiptInfo 因此目前设计下第二个可以不处理
         */
        return new ChargeResponse<>();
    }

    @ApiOperation(value = "打印收据回调接口", notes = "打印收据回调接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "收据id/实际使用资产流水id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "printer", value = "打印人", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/updateReceiptInfo", method = {RequestMethod.POST})
    public ChargeResponse<Void> updateReceiptInfo(String id, String printer, String token) throws ChargeBusinessException {
        return chargeBillService.updateReceiptInfo(id, printer);
    }
}


