package com.charge.api.web.vo.pos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * description
 *
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ItemAccountDetailDTOV1 {
    private Long predepositAccountId;
    private BigDecimal totalMoney;
    private BigDecimal freezeMoney;
    private BigDecimal availableMoney;
    /**
     * 需要适配1.0的pos协议需要，不能直接改为驼峰
     */
    private Long itemID;
    private String itemName;
}


