package com.charge.api.web.vo.joylife.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-05 18:32
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceivableBillInfo implements Serializable {

    private static final long serialVersionUID = -885371035925711498L;

    /**
     * 应收单id
     */
    private String billId;

    /**
     * 收费项id
     */
    private String itemId;

   /**
     * 所属年月
     */
    private String belongYears;

    /**
     * 欠费总金额
     */
    private BigDecimal itemArrearsAmount;

    /**
     * 违约金金额
     */
    private BigDecimal itemPenaltyAmount;

}
