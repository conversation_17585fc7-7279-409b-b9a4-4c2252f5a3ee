package com.charge.api.web.dto.joylife;

import com.charge.maindata.enums.CustomerTypeEnum;
import com.charge.maindata.pojo.dto.CustomerDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description
 * @date 2023/02/01
 */
@Data
public class AssetAdapter implements Serializable {
    /**
     * 资产id
     */
    private Long id;

    /**
     * 资产类型：1-房间,2-车位，详见AssetTypeEnum
     */
    private Integer type;

    /**
     * 项目id
     */
    private Long communityId;

    /**
     * 项目名称
     */
    private String communityName;

    /**
     * 楼栋id
     */
    private Long buildingId;

    /**
     * 楼栋编码
     */
    private String buildingCode;

    /**
     * 楼栋名称
     */
    private String buildingName;

    /**
     * 单元id
     */
    private Long unitId;

    /**
     * 单元名称
     */
    private String unitName;

    /**
     * 资产子类id
     */
    private Long subId;

    /**
     * 资产子类编码
     */
    private String subCode;

    /**
     * 资产子类名称
     */
    private String subName;

    /**
     * 资产子类号
     */
    private String subNum;

    /**
     * 资产名称
     */
    private String assetName;
    /**
     * 朝昔Id
     */
    private String msId;

    /**
     * 收费对象
     */
    private Integer chargeObject;

    /**
     * 客户信息
     */
    private List<CustomerDTO> listCustomer;

    /**
     * 资产使用状态
     */
    private Integer assetUseStatus;

    /**
     * 房屋类型（11-虚拟资产）
     */
    private Integer houseType;

    /**
     * 资产子类编码
     */
    private String assetCode;

    /**
     * 资产子类号
     */
    private String assetNum;

    /**
     * 收费对象id
     */
    private Long chargeObjectId;

    public String getOwnerName() {
        return getCustomerByType(CustomerTypeEnum.OWNER).stream().map(CustomerDTO::getCustomerName).collect(Collectors.joining(","));
    }

    public String getHouseHoldName(){
        return getCustomerByType(CustomerTypeEnum.HOUSE_HOLD).stream().map(CustomerDTO::getCustomerName).collect(Collectors.joining(","));

    }

    public List<CustomerDTO> getCustomerByType(CustomerTypeEnum customerType) {
        return listCustomer.stream().filter(a-> customerType.getCode().equals(a.getCustomerType())).collect(Collectors.toList());
    }

}
