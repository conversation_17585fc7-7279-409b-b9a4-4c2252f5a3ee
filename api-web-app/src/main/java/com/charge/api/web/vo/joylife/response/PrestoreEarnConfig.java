package com.charge.api.web.vo.joylife.response;


import com.charge.maindata.enums.AssetTypeEnum;
import com.charge.maindata.pojo.dto.AssetDTO;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.HouseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrestoreEarnConfig implements Serializable {

	Long communityId;
	String communityMsId;
	String houseMsId;
	Long houseId;

	public static List<PrestoreEarnConfig> from(Map<Long, List<CommunityDTO>> communityMap ,  List<AssetDTO> houseList) {
		List<PrestoreEarnConfig> result = new ArrayList<>();
		for (AssetDTO house : houseList) {
			String communityMs = null;
			Long communityId=null;
			String houseMsId=null;
			if (Objects.equals(AssetTypeEnum.HOUSE.getCode(), house.getType())) {
				communityId=house.getHouseDTO().getCommunityId();
				houseMsId=house.getHouseDTO().getMsId();
				List<CommunityDTO> communityList = communityMap.get(communityId);
				if (!CollectionUtils.isEmpty(communityList)) {
					communityMs = communityList.get(0).getMsId();
				}
			} else if(Objects.nonNull(house.getParkingSpaceDTO())){
				communityId=house.getParkingSpaceDTO().getCommunityId();
				List<CommunityDTO> communityList = communityMap.get(communityId);
				houseMsId=house.getParkingSpaceDTO().getMsId();
				if (!CollectionUtils.isEmpty(communityList)) {
					communityMs = communityList.get(0).getMsId();
				}
			}
			result.add(PrestoreEarnConfig.builder()
					.communityId(communityId)
					.houseId(house.getId())
					.houseMsId(houseMsId)
					.communityMsId(communityMs)
					.build());
		}
		return result;
	}
}
