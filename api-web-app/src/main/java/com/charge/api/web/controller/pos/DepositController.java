package com.charge.api.web.controller.pos;

import com.charge.api.web.service.pos.DepositService;
import com.charge.api.web.vo.ChargePageResponse;
import com.charge.api.web.vo.pos.DepositDetailVO;
import com.charge.api.web.vo.pos.DepositVO;
import com.charge.common.exception.ChargeBusinessException;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * pos 押金相关接口
 * <AUTHOR>
 * @date 2023/05/15 17:52
 */
@RestController
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DepositController {

    private final DepositService depositService;

    @ApiOperation(value = "获取押金列表", notes = "获取押金列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "houseUuid", value = "房屋uuid", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "depositStatus", value = "退款状态（1表示未退款，0表示已退款，默认1）", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区uuid", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "所要查询的页数,默认为1", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量，默认为10", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getDeposits", method = {RequestMethod.GET})
    public ChargePageResponse<List<DepositVO>> getDeposits(String communityUuid, Long houseUuid,
                                                           @RequestParam(value = "depositStatus", defaultValue = "1") String depositStatus,
                                                           @RequestParam(value = "pageNum", defaultValue = "1",required = false) Integer pageNum,
                                                           @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                           String token) throws ChargeBusinessException {
        return depositService.getDeposits(communityUuid, houseUuid, depositStatus, pageNum, pageSize);
    }

    @ApiOperation(value = "获取押金详情", notes = "获取押金详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "depositId", value = "押金缴费id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseId", value = "房屋id", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "itemId", value = "押金项id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "所要查询的页数,默认为1", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量，默认为10", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getDepositDetail", method = {RequestMethod.GET})
    public ChargePageResponse<DepositDetailVO> getDepositDetail(String depositId, String communityUuid, String houseId, String itemId, @RequestParam(value = "pageNum", defaultValue = "1") String pageNum,
                                                                @RequestParam(value = "pageSize", defaultValue = "10") String pageSize, String token) throws ChargeBusinessException {
        return depositService.getDepositDetail(depositId, communityUuid, houseId, pageNum, pageSize);
    }

    @ApiOperation(value = "押金列表搜索功能", notes = "押金列表搜索功能")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "communityUuid", value = "小区id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "houseId", value = "房屋id", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "content", value = "搜索内容(收据号或缴费人)", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "depositStatus", value = "退款状态（1表示未退款，0表示已退款，默认1）", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNum", value = "所要查询的页数,默认为1", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "每页数量，默认为10", required = false, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "token", value = "通讯密串", required = true, dataType = "String"),
    })
    @RequestMapping(value = "/getDepositSerach", method = {RequestMethod.GET})
    public ChargePageResponse<List<DepositVO>> getDepositSerach(String communityUuid, String houseId, String content,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                @RequestParam(value = "depositStatus", defaultValue = "1") String depositStatus,
                                                                String token) throws ChargeBusinessException {
        return depositService.getDepositSearch(communityUuid, houseId, content, depositStatus, pageNum, pageSize);
    }
}
