package com.charge.api.web.support;

import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.maindata.client.BuildingInfoClient;
import com.charge.maindata.pojo.dto.BuildingInfoConditionDTO;
import com.charge.maindata.pojo.dto.BuildingInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * BuildingSupport
 *
 * <AUTHOR>
 * @date 2025/1/9
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BuildingSupport {

    private final BuildingInfoClient buildingInfoClient;

    public List<BuildingInfoDTO> listByMsId(List<String> msId, Long communityId) throws ChargeBusinessException {
        ChargeResponse<List<BuildingInfoDTO>> listChargeResponse = buildingInfoClient.listByCondition(BuildingInfoConditionDTO
                .builder().communityId(communityId).msIdList(msId).build());
        return AppInterfaceUtil.getResponseDataThrowException(listChargeResponse);
    }

    public List<Long> listIdsByMsId(List<String> msId, Long communityId) throws ChargeBusinessException {
        return listByMsId(msId, communityId).stream().map(BuildingInfoDTO::getId).collect(Collectors.toList());
    }
}