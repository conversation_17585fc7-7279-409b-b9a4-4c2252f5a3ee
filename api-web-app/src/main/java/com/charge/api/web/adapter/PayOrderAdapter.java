package com.charge.api.web.adapter;

import com.alibaba.fastjson.JSON;
import com.charge.api.web.config.PrePayLockConfig;
import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.constants.YueConstants;
import com.charge.api.web.controller.joylife.bill.request.PayOrAdjustItemVO;
import com.charge.api.web.vo.CardPayItemInfo;
import com.charge.api.web.vo.joylife.AssetBillItem;
import com.charge.api.web.vo.joylife.BatchArrearsOrderList;
import com.charge.api.web.vo.joylife.PrestoreChargeOrderInfo;
import com.charge.api.web.vo.joylife.request.BatchPayDataRequest;
import com.charge.api.web.vo.joylife.request.PrestoreOrderRequest;
import com.charge.api.web.vo.joylife.request.YueCardPayRequestVO;
import com.charge.api.web.vo.joylife.response.MonthOrderItemArrears;
import com.charge.api.web.vo.joylife.response.OrderItemArrears;
import com.charge.bill.dto.income.AssetArrearsListDTO;
import com.charge.bill.dto.income.AssetReceivalbeBillListDTO;
import com.charge.bill.dto.income.IncomeBillDTO;
import com.charge.bill.dto.income.IncomeSubBillDTO;
import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentMethodEnum;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.DateUtils;
import com.charge.common.util.SpringContextUtil;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.StringUtil;
import com.charge.joylife.dto.*;
import com.charge.order.dto.OrderDTO;
import com.charge.order.dto.WorkOrderPayCmd;
import com.charge.pay.dto.pay.PayOrderRequestDTO;
import com.charge.pay.dto.pay.PayOrderSubmitResponseDTO;
import com.charge.pay.dto.pay.PaySubOrderRequestDTO;
import com.charge.pay.dto.pay.PaySubOrderResponseDTO;
import com.charge.pay.enums.ChannelPayTypeEnum;
import com.charge.pay.enums.PayChannelEnum;
import com.charge.pay.enums.TradeSceneEnum;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * [威富通预下单适配]
 *
 * <AUTHOR> wuChao
 * @version : [v1.0]
 */
@Slf4j
public class PayOrderAdapter {
    /**
     * 招行微信小程序
     */
    private static final String CMB_APPLET = "CMB_WX_PAY";
    /**
     * 招行微信app
     */
    private static final String CMB_APP = "CMB";
    /**
     * 招行适配参数
     */
    private final static String CMB_TXN_TIME = "txnTime";
    private final static String CMB_ENCRYPTED_CMB_ORDER_ID ="encryptedCmbOrderId";
    private final static String CMB_ENCRYPTED_TRADE_INFO ="encryptedTradeInfo";
    private final static String CMB_MINI_APP_ID ="cmbMiniAppId";
    private final static String CMB_ORDER_ID ="cmbOrderId";

    private final static String CMB_ALI_QRCODE ="qrCode";

    private final static String CMB_ALI_PAYINFO ="payInfo";
    /**
     * 招行小程序适配参数
     */
    private static final String CMB_PAY_DATA = "payData";



    /**
     * description: 入参适配
     * <AUTHOR>
     * date: 2023/7/5
     * param [pay, communityId, orderNum]
     * return com.charge.pay.dto.pay.PayOrderRequestDTO
     **/
    public static PayOrderRequestDTO requestParamConvert(BatchPayDataRequest pay, Long communityId, String orderNum) {
        PayOrderRequestDTO dto = new PayOrderRequestDTO();
        dto.setCommunityId((communityId));
        if (StringUtils.isBlank(orderNum)) {
            orderNum = IdGeneratorSupport.getIstance().nextId();
        }
        dto.setOutTradeNo(orderNum);
        // 下单 单位为分，入参接口 单位为元 需要转换
        dto.setTotalFee(new BigDecimal(pay.getActualPrice()).multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString());
        dto.setBody(PayRelatedConstants.GOODSNAME_FOR_PROPERTY);
        dto.setMchCreateIp(pay.getMchCreateIp());
        dto.setPaymentTerminal(pay.getPaymentSource());

        // 新增AliJsApi和AliApp
        fillTradeScene(dto, pay.getPaymentSource(), pay.getPaymentMethod(), pay.getPayNative());
        fillPaymentMethod(dto, pay.getPaymentMethod());
        dto.setSubAppId(pay.getSubAppid());
        dto.setSubOpenId(pay.getSubOpenid());
        dto.setPayMember(pay.getUserName());
        dto.setPayMemberId(pay.getUserId());
        dto.setBuyerLogonId(pay.getBuyerLogonId());
        dto.setPayItems(getPaySubItems(pay));
        dto.setReturnUrl(pay.getReturnUrl());
        fillPayTimeExpire(dto);
        log.info("预下单入参,{}", dto);
        return dto;
    }


    public static List<PaySubOrderRequestDTO> getPaySubItems(BatchPayDataRequest pay){
        // 如果存在部分积分抵扣,则以抵扣完后的费项下单
        if(CollectionUtil.isNotEmpty(pay.getActualList())){
            return fillDisPaysubItems(pay.getActualList());
        }
        return fillAllPaysubItems(pay.getArrearsOrderList());



    }

    private static List<PaySubOrderRequestDTO> fillDisPaysubItems(List<AssetArrearsListDTO> actualList) {
        List<PaySubOrderRequestDTO> payItems = new ArrayList<>();
        if(CollectionUtil.isEmpty(actualList)){
            return payItems;
        }

        List<AssetReceivalbeBillListDTO>  detailList = new ArrayList<>();
        for (AssetArrearsListDTO orderList:actualList) {
            List<AssetReceivalbeBillListDTO> billList = orderList.getDetailList();
            detailList.addAll(billList) ;
        }

        detailList.forEach(
                itemInfo->{
                    PaySubOrderRequestDTO orderRequestDTO = new PaySubOrderRequestDTO();
                    orderRequestDTO.setItemId(itemInfo.getItemId());
                    BigDecimal itemArrearsAmount = Objects.isNull(itemInfo.getItemArrearsAmount())?BigDecimal.ZERO:itemInfo.getItemArrearsAmount().setScale(2,BigDecimal.ROUND_HALF_UP);
                    BigDecimal itemPenaltyAmount = Objects.isNull(itemInfo.getItemPenaltyAmount())?BigDecimal.ZERO:itemInfo.getItemPenaltyAmount().setScale(2,BigDecimal.ROUND_HALF_UP);
                    orderRequestDTO.setMoney(itemArrearsAmount.add(itemPenaltyAmount));
                    payItems.add(orderRequestDTO);
                }
        );
        return payItems;
    }


    private static List<PaySubOrderRequestDTO> fillAllPaysubItems(List<BatchArrearsOrderList> arrearsOrderList) {

        List<OrderItemArrears> detailList = new ArrayList<>();
        for (BatchArrearsOrderList orderList : arrearsOrderList) {
            List<MonthOrderItemArrears> billList = orderList.getBillList();
            for (MonthOrderItemArrears monthOrderItems : billList) {
                detailList.addAll(monthOrderItems.getDetailList());
            }
        }
        List<PaySubOrderRequestDTO> payItems = new ArrayList<>();
        detailList.forEach(
                itemInfo -> {
                    PaySubOrderRequestDTO orderRequestDTO = new PaySubOrderRequestDTO();
                    orderRequestDTO.setItemId(Long.valueOf(itemInfo.getItemId()));
                    BigDecimal itemArrearsAmount = StringUtil.isEmpty(itemInfo.getItemArrearsAmount()) ? BigDecimal.ZERO : new BigDecimal(itemInfo.getItemArrearsAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal itemPenaltyAmount = StringUtil.isEmpty(itemInfo.getItemPenaltyAmount()) ? BigDecimal.ZERO : new BigDecimal(itemInfo.getItemPenaltyAmount()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    orderRequestDTO.setMoney(itemArrearsAmount.add(itemPenaltyAmount));
                    payItems.add(orderRequestDTO);
                }
        );
        return payItems;
    }
    /**
     * description: 出参适配
     * author: wuChao
     * date: 2023/7/5
     * param [responseDTO, paymentSource=支付来源]
     * return net.sf.json.JSONObject
     **/
    public static JSONObject responseParamConvert(PayOrderSubmitResponseDTO responseDTO, String paymentSource) {
        JSONObject jsonObject = new JSONObject();
        if (responseDTO != null) {
            //威富通适配
            if (PayChannelEnum.SWIFTPASS.getCode().equals(responseDTO.getPayChannel())) {
                Map<String, String> swiftPassMap = new HashMap<>();
                swiftPassMap.put("transactionId", "transaction_id");
                swiftPassMap.put("payInfo", "pay_info");
                jsonObject = JSONObject.fromObject(responseDTO);
                for (Map.Entry<String, String> entry : swiftPassMap.entrySet()) {
                    if (jsonObject.containsKey(entry.getKey())) {
                        jsonObject.put(entry.getValue(), jsonObject.get(entry.getKey()));
                    }
                }
                jsonObject.put("payType", PayChannelEnum.SWIFTPASS);
            }
            else {
                //招行适配
                jsonPayType(jsonObject,responseDTO);
                jsonObject.put("out_trade_no", responseDTO.getOutTradeNo());
                jsonObject.put("merId", responseDTO.getMerchantNumber());
                jsonObject.put("subItems", responseDTO.getSubItems());
                jsonObject.put("isMergeBill", responseDTO.getIsMergeBill());

            }
            jsonObject.put("orderNum", responseDTO.getOutTradeNo());
        }
        return jsonObject;
    }

    private static void jsonPayType( JSONObject jsonObject, PayOrderSubmitResponseDTO responseDTO) {
        if (Objects.equals(responseDTO.getTradeScene(), TradeSceneEnum.WX_APP.getCode())) {
            jsonObject.put("cmbPayInfo", responseDTO);
            jsonObject.put("payType", ChannelPayTypeEnum.CMB_APP.getCode());
        } else if(Objects.equals(responseDTO.getTradeScene(), TradeSceneEnum.WX_JSAPI.getCode())) {
            jsonObject.put("cmbWxPayInfo", responseDTO);
            jsonObject.put("payType", ChannelPayTypeEnum.CMB_WX_PAY.getCode());
        }else if(Objects.equals(responseDTO.getTradeScene(), TradeSceneEnum.ALI_APP.getCode())) {
            jsonObject.put("cmbPayInfo", responseDTO);
            jsonObject.put("payType", ChannelPayTypeEnum.CMB_ALI_APP.getCode());
        }else if(Objects.equals(responseDTO.getTradeScene(), TradeSceneEnum.ALI_JSAPI.getCode())) {
            jsonObject.put("cmbWxPayInfo", responseDTO);
            jsonObject.put("payType", ChannelPayTypeEnum.CMB_ALI_JSPAY.getCode());
        }
    }

    public static void buildCmb(PayInfoDTO payInfoDTO){
        if (payInfoDTO.getCmbPayInfo() != null) {
            CmbPayInfoDTO dto = buildCmbPayInfoDTO(payInfoDTO.getCmbPayInfo());
            payInfoDTO.setCmbPayInfo(dto);
        }
        if (payInfoDTO.getCmbWxPayInfo() != null) {
            CmbPayInfoDTO dto = buildCmbPayInfoDTO(payInfoDTO.getCmbWxPayInfo());
            payInfoDTO.setCmbWxPayInfo(dto);
        }
    }

    private static CmbPayInfoDTO buildCmbPayInfoDTO(Object cmbInfo) {
        JSONObject fromObject = JSONObject.fromObject(cmbInfo);
        JSONObject payInfo = JSONObject.fromObject(fromObject.get("payInfo"));
        CmbPayInfoDTO dto = new CmbPayInfoDTO();
        dto.setOutTradeNo(fromObject.getString("outTradeNo"));
        dto.setMerId(fromObject.getString("merchantNumber"));
        if (payInfo.containsKey(CMB_ORDER_ID)) {
            dto.setCmbOrderId(payInfo.getString(CMB_ORDER_ID));
        }
        if (payInfo.containsKey(CMB_TXN_TIME)) {
            dto.setTxnTime(payInfo.getString(CMB_TXN_TIME));
        }
        if (payInfo.containsKey(CMB_ENCRYPTED_CMB_ORDER_ID)) {
            dto.setEncryptedCmbOrderId(payInfo.getString(CMB_ENCRYPTED_CMB_ORDER_ID));
        }
        if (payInfo.containsKey(CMB_ENCRYPTED_TRADE_INFO)) {
            dto.setEncryptedTradeInfo(payInfo.getString(CMB_ENCRYPTED_TRADE_INFO));
        }
        if (payInfo.containsKey(CMB_MINI_APP_ID)) {
            dto.setCmbMiniAppId(payInfo.getString(CMB_MINI_APP_ID));
        }
        if (payInfo.containsKey(CMB_ALI_QRCODE)) {
            dto.setAliQrCode(payInfo.getString(CMB_ALI_QRCODE));
        }
        if (payInfo.containsKey(CMB_ALI_PAYINFO)) {
            dto.setAliPayInfo(payInfo.getString(CMB_ALI_PAYINFO));
        }
        if (payInfo.containsKey(CMB_PAY_DATA)) {
            JSONObject jsonObject = payInfo.getJSONObject(CMB_PAY_DATA);
            dto.setPayData(buildPayData(jsonObject));
        }
        return dto;
    }

    /**
     * description: 月卡下单适配
     * author: wuChao
     * date: 2023/7/11
     * param []
     * return com.charge.pay.dto.pay.PayOrderRequestDTO
     **/
    public static PayOrderRequestDTO requestYueCardPayParamConvert(YueCardPayRequestVO payVO, Long communityId, String orderNum,List<CardPayItemInfo> itemInfos) {
        PayOrderRequestDTO dto = new PayOrderRequestDTO();
        // 下单 单位为分，入参接口 单位为元 需要转换
        dto.setTotalFee(payVO.getAmount().multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString());
        dto.setPaymentTerminal(payVO.getPaymentSource());
        // 新增AliJsApi和AliApp
        fillTradeScene(dto, payVO.getPaymentSource(),payVO.getPaymentMethod(),payVO.getPayNative());
        fillPaymentMethod(dto, payVO.getPaymentMethod());
        dto.setMchCreateIp(payVO.getMchCreateIp());
        dto.setSubAppId(payVO.getSubAppid());
        dto.setSubOpenId(payVO.getSubOpenid());
        dto.setCommunityId(communityId);
        dto.setOutTradeNo(orderNum);
        dto.setBody(PayRelatedConstants.GOODSNAME_MONTH_PARKING);
        dto.setPayMember(payVO.getPayMember());
        dto.setPayMemberId(payVO.getPayMemberId());
        dto.setBuyerLogonId(payVO.getBuyerLogonId());
        dto.setPayItems(fillYueCardSubPayItem(itemInfos));
        dto.setReturnUrl(payVO.getReturnUrl());
        fillPayTimeExpire(dto);
        return dto;
    }

    private static List<PaySubOrderRequestDTO> fillYueCardSubPayItem(List<CardPayItemInfo> itemInfos) {
        List<PaySubOrderRequestDTO> payItems = new ArrayList<>();
        itemInfos.forEach(
                itemInfo->{
                    PaySubOrderRequestDTO orderRequestDTO = new PaySubOrderRequestDTO();
                    orderRequestDTO.setItemId(itemInfo.getItemId());
                    orderRequestDTO.setMoney(itemInfo.getPrice());
                    payItems.add(orderRequestDTO);
                }
        );
        return payItems;

    }

    public static PayOrderRequestDTO requestWorkOrderBuild(OrderDTO order, WorkOrderPayCmd workOrderPayCmd) {
        PayOrderRequestDTO dto = new PayOrderRequestDTO();
        dto.setCommunityId(order.getCommunityId());
        dto.setOutTradeNo(workOrderPayCmd.getOrderNum());
        dto.setTotalFee(order.getTotalPaidAmount().multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString());
        dto.setBody(com.charge.common.constant.PayRelatedConstants.GOODSNAME_WORD_ORDER);
        dto.setMchCreateIp(workOrderPayCmd.getMchCreateIp());
        dto.setPaymentTerminal(workOrderPayCmd.getPaymentSource());
        fillTradeScene(dto,workOrderPayCmd.getPaymentSource(),workOrderPayCmd.getPaymentMethod(),workOrderPayCmd.getPayNative());
        fillPaymentMethod(dto,workOrderPayCmd.getPaymentMethod());
        dto.setSubAppId(workOrderPayCmd.getSubAppid());
        dto.setSubOpenId(workOrderPayCmd.getSubOpenid());
        dto.setPayMember(workOrderPayCmd.getPayMember());
        dto.setPayMemberId(workOrderPayCmd.getPayMemberId());
        dto.setBuyerLogonId(workOrderPayCmd.getBuyerLogonId());
        dto.setReturnUrl(workOrderPayCmd.getReturnUrl());
        // 有偿工单失效时间为下单之后2天以内有效
        Date expireDate = DateUtils.addDays(order.getCreateAt(), 2);
//        Assert.isTrue(expireDate.getTime() > System.currentTimeMillis(), "有偿工单已经超出有效支付时间2天");
        String format = DateUtils.format(expireDate, DateUtils.FORMAT_4);
        dto.setTimeExpire(String.valueOf(format));
        dto.setPayItems(fillWorkOrderSubPayItem(order));
        return dto;
    }
    private static List<PaySubOrderRequestDTO> fillWorkOrderSubPayItem(OrderDTO order) {
        List<PaySubOrderRequestDTO> payItems = new ArrayList<>();
        PaySubOrderRequestDTO orderRequestDTO = new PaySubOrderRequestDTO();
        orderRequestDTO.setItemId(order.getChargeItemId());
        orderRequestDTO.setMoney(order.getTotalPaidAmount());
        payItems.add(orderRequestDTO);
        return payItems;

    }



    /**
     * description: 云交付-专项预存组合支付预下单 入参适配
     * author: wuChao
     * date: 2023/7/12
     * param [request, communityId, orderNum]
     * return com.charge.pay.dto.pay.PayOrderRequestDTO
     **/
    public static PayOrderRequestDTO requestDeliveryPayParamConvert(DeliveryPayDataRequest request, String communityId, String orderNum) {
        PayOrderRequestDTO dto = new PayOrderRequestDTO();
        dto.setCommunityId(Long.parseLong(communityId));
        dto.setOutTradeNo(orderNum);
        dto.setBody(PayRelatedConstants.GOODSNAME_FOR_PRESTORE);
        dto.setAttach(request.getMemo());
        dto.setPaymentTerminal( request.getPaymentSource() );
        fillTradeScene(dto, request.getPaymentSource(),request.getPaymentMethod(),null);
        fillPaymentMethod(dto, request.getPaymentMethod());
        dto.setPayMember(request.getPayMember());
        dto.setPayMemberId(request.getPayMemberId());
        // 下单 单位为分，入参接口 单位为元 需要转换
        dto.setTotalFee(new BigDecimal(request.getMoney()).multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString());
        dto.setMchCreateIp( request.getMchCreateIp() );
        dto.setSubAppId( request.getSubAppid() );
        dto.setSubOpenId( request.getSubOpenid() );
        dto.setBuyerLogonId(request.getBuyerLogonId());
        dto.setPayItems(fillDeliveryPaySubItems(request.getBillDetailVOS()));
        fillPayTimeExpire(dto);
        return dto;
    }

    private static List<PaySubOrderRequestDTO> fillDeliveryPaySubItems(List<DeliveryItemDetail> billDetailVOS) {
        List<PaySubOrderRequestDTO> payItems = new ArrayList<>();
        billDetailVOS.forEach(
                billDetail->{
                    PaySubOrderRequestDTO orderRequestDTO = new PaySubOrderRequestDTO();
                    orderRequestDTO.setItemId(Long.valueOf(billDetail.getItemId()));
                    orderRequestDTO.setMoney(billDetail.getPrice());
                    payItems.add(orderRequestDTO);
                }
        );
        return payItems;
    }

    public static void fillPaymentMethod(PayOrderRequestDTO dto, String paymentMethod){
        // 默认为微信
        dto.setPaymentMethod(String.valueOf(PaymentMethodEnum.WECHAT.getPaymentCode()));
        if (Objects.equals("3",paymentMethod)) {
            dto.setPaymentMethod(String.valueOf(PaymentMethodEnum.ALIPAY.getPaymentCode()));
        }
    }

    public static void fillTradeScene(PayOrderRequestDTO dto, String paymentSource, String paymentMethod,Boolean payNative) {
        // 默认是微信小程序
        dto.setTradeScene(TradeSceneEnum.WX_JSAPI.getCode());
        if(StringUtil.isEmpty(paymentMethod)){
            // 针对app 终端旧版本可能没有 tradeType
            if(Objects.equals(YueConstants.SOURCE_WECHAT_APPLET,paymentSource)){
                dto.setTradeScene(TradeSceneEnum.WX_JSAPI.getCode());
            }
            if(Objects.equals(YueConstants.SOURCE_YUEHOME_PAY,paymentSource)){
                dto.setTradeScene(TradeSceneEnum.WX_APP.getCode());
            }
        }else{
            matchTradeScene(dto, paymentSource, paymentMethod,payNative);
        }
    }

    private static void matchTradeScene(PayOrderRequestDTO dto, String paymentSource, String paymentMethod,Boolean payNative) {
        // 朝昔侧因为终端的原因历史版本微信支付 paymentMethod=4  与收费的枚举值对不上
        if (Objects.equals(YueConstants.SOURCE_WECHAT_APPLET, paymentSource) && Objects.equals("4", paymentMethod)) {
            if(Boolean.TRUE.equals(payNative)){
                dto.setTradeScene(TradeSceneEnum.WX_APP.getCode());
            }else {
                dto.setTradeScene(TradeSceneEnum.WX_JSAPI.getCode());
            }
        } else if (Objects.equals(YueConstants.SOURCE_WECHAT_APPLET, paymentSource) && Objects.equals("3", paymentMethod)) {
            if(Boolean.TRUE.equals(payNative)){
                dto.setTradeScene(TradeSceneEnum.ALI_APP.getCode());
            }else {
                dto.setTradeScene(TradeSceneEnum.ALI_JSAPI.getCode());
            }
        } else if (Objects.equals(YueConstants.SOURCE_YUEHOME_PAY, paymentSource) && Objects.equals("4", paymentMethod)) {
            dto.setTradeScene(TradeSceneEnum.WX_APP.getCode());
        } else if (Objects.equals(YueConstants.SOURCE_YUEHOME_PAY, paymentSource) && Objects.equals("3", paymentMethod)) {
            dto.setTradeScene(TradeSceneEnum.ALI_APP.getCode());
        }
    }

    /**
     * description: 月卡下单、云交付-专项预存组合支付预下单 入参适配 返回参数
     * author: wuChao
     * date: 2023/7/17
     * param [payReturnDTO, incomeId]
     * return com.charge.joylife.dto.PayInfoDTO
     **/
    public static PayInfoDTO responseYueCardPayOrDeliveryPayConvert(PayOrderSubmitResponseDTO payReturnDTO, Long incomeId) {
        String id = String.valueOf(incomeId);
        PayInfoDTO payInfoDTO = PayInfoDTO.builder().orderId(id).id(id).orderNum(payReturnDTO.getOutTradeNo()).build();
        if (Objects.equals(payReturnDTO.getPayChannel(), PayChannelEnum.CMB.getCode())) {
            CmbPayInfoDTO dto = buildCmbPayInfoDTO(payReturnDTO);
            fillChannelPayType(payReturnDTO.getTradeScene(), payInfoDTO, dto);
        } else {
            payInfoDTO.setPayInfo(JSON.toJSONString(payReturnDTO.getPayInfo()));
        }
        return payInfoDTO;
    }

    private static void fillChannelPayType(String tradeScene, PayInfoDTO payInfoDTO, CmbPayInfoDTO dto) {
        if (Objects.equals(tradeScene, TradeSceneEnum.WX_APP.getCode())) {
            payInfoDTO.setCmbPayInfo(dto);
            payInfoDTO.setPayType(ChannelPayTypeEnum.CMB_APP.getCode());
        } else if(Objects.equals(tradeScene, TradeSceneEnum.WX_JSAPI.getCode())) {
            payInfoDTO.setCmbWxPayInfo(dto);
            payInfoDTO.setPayType(ChannelPayTypeEnum.CMB_WX_PAY.getCode());
        }else if(Objects.equals(tradeScene, TradeSceneEnum.ALI_APP.getCode())) {
            payInfoDTO.setCmbPayInfo(dto);
            payInfoDTO.setPayType(ChannelPayTypeEnum.CMB_ALI_APP.getCode());
        }else if(Objects.equals(tradeScene, TradeSceneEnum.ALI_JSAPI.getCode())) {
            payInfoDTO.setCmbWxPayInfo(dto);
            payInfoDTO.setPayType(ChannelPayTypeEnum.CMB_ALI_JSPAY.getCode());
        }
    }

    private static void fillChannelPayType(String tradeScene, BatchPayInfoDTO batchPayInfoDTO, CmbPayInfoDTO dto) {
        if (Objects.equals(tradeScene, TradeSceneEnum.WX_APP.getCode())) {
            batchPayInfoDTO.setCmbPayInfo(dto);
            batchPayInfoDTO.setPayType(ChannelPayTypeEnum.CMB_APP.getCode());
        } else if(Objects.equals(tradeScene, TradeSceneEnum.WX_JSAPI.getCode())) {
            batchPayInfoDTO.setCmbWxPayInfo(dto);
            batchPayInfoDTO.setPayType(ChannelPayTypeEnum.CMB_WX_PAY.getCode());
        }else if(Objects.equals(tradeScene, TradeSceneEnum.ALI_APP.getCode())) {
            batchPayInfoDTO.setCmbPayInfo(dto);
            batchPayInfoDTO.setPayType(ChannelPayTypeEnum.CMB_ALI_APP.getCode());
        }else if(Objects.equals(tradeScene, TradeSceneEnum.ALI_JSAPI.getCode())) {
            batchPayInfoDTO.setCmbWxPayInfo(dto);
            batchPayInfoDTO.setPayType(ChannelPayTypeEnum.CMB_ALI_JSPAY.getCode());
        }
    }

    private static CmbPayInfoDTO buildCmbPayInfoDTO(PayOrderSubmitResponseDTO payInfoDTO) {
        JSONObject payInfo = JSONObject.fromObject(payInfoDTO.getPayInfo());
        CmbPayInfoDTO dto = new CmbPayInfoDTO();
        dto.setOutTradeNo(payInfoDTO.getOutTradeNo());
        dto.setMerId(payInfoDTO.getMerchantNumber());
        if (payInfo.containsKey(CMB_ORDER_ID)) {
            dto.setCmbOrderId(payInfo.getString(CMB_ORDER_ID));
        }
        if (payInfo.containsKey(CMB_TXN_TIME)) {
            dto.setTxnTime(payInfo.getString(CMB_TXN_TIME));
        }
        if (payInfo.containsKey(CMB_ENCRYPTED_CMB_ORDER_ID)) {
            dto.setEncryptedCmbOrderId(payInfo.getString(CMB_ENCRYPTED_CMB_ORDER_ID));
        }
        if (payInfo.containsKey(CMB_ENCRYPTED_TRADE_INFO)) {
            dto.setEncryptedTradeInfo(payInfo.getString(CMB_ENCRYPTED_TRADE_INFO));
        }
        if (payInfo.containsKey(CMB_MINI_APP_ID)) {
            dto.setCmbMiniAppId(payInfo.getString(CMB_MINI_APP_ID));
        }
        if (payInfo.containsKey(CMB_ALI_QRCODE)) {
            dto.setAliQrCode(payInfo.getString(CMB_ALI_QRCODE));
        }
        if (payInfo.containsKey(CMB_ALI_PAYINFO)) {
            dto.setAliPayInfo(payInfo.getString(CMB_ALI_PAYINFO));
        }
        if (payInfo.containsKey(CMB_PAY_DATA)) {
            JSONObject jsonObject = JSONObject.fromObject(payInfo.getString(CMB_PAY_DATA));
            dto.setPayData(buildPayData(jsonObject));
        }
        return dto;
    }

    private static Map<String, String> buildPayData(JSONObject jsonObject) {
        Map<String, String> resultMap = new HashMap<>(jsonObject.size());
        Iterator iterator = jsonObject.keys();
        String key;
        String value;
        while (iterator.hasNext()) {
            key = iterator.next().toString();
            value = jsonObject.getString(key);
            resultMap.put(key, value);
        }
        return resultMap;
    }

    /**
     * description: 入参适配
     * <AUTHOR>
     * date: 2023/7/5
     * param [pay, communityId, orderNum]
     * return com.charge.pay.dto.pay.PayOrderRequestDTO
     **/
    public static PayOrderRequestDTO requestParamConvert(PrestoreOrderRequest request, Long communityId, String orderNum) {
        PayOrderRequestDTO dto = new PayOrderRequestDTO();
        dto.setCommunityId(communityId);
        if (StringUtils.isBlank(orderNum)) {
            orderNum = IdGeneratorSupport.getIstance().nextId();
        }
        dto.setOutTradeNo(orderNum);
        // 下单 单位为分，入参接口 单位为元 需要转换
        dto.setTotalFee(new BigDecimal(request.getTotalPrice()).multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString());
        dto.setBody(com.charge.common.constant.PayRelatedConstants.GOODSNAME_FOR_PRESTORE);
        dto.setMchCreateIp(request.getMchCreateIp());
        dto.setPaymentTerminal(request.getPaymentSource());
        // 新增AliJsApi和AliApp
        fillTradeScene(dto, request.getPaymentSource(),request.getPaymentMethod(),request.getPayNative());
        fillPaymentMethod(dto, request.getPaymentMethod());
        dto.setPayItems(fillPayItems(request.getChargeOrderInfoList()));
        dto.setSubAppId(request.getSubAppid());
        dto.setSubOpenId(request.getSubOpenid());
        dto.setPayMember(request.getUserName());
        dto.setPayMemberId(request.getUserId());
        dto.setBuyerLogonId(request.getBuyerLogonId());
        dto.setReturnUrl(request.getReturnUrl());
        fillPayTimeExpire(dto);
        return dto;
    }

    public static List<PaySubOrderRequestDTO> fillPayItems(List<PrestoreChargeOrderInfo> chargeOrderInfoList) {
        List<PaySubOrderRequestDTO> payItems = new ArrayList<>();
        chargeOrderInfoList.forEach(
                prestoreChargeOrderInfo->{
                    PaySubOrderRequestDTO orderRequestDTO = new PaySubOrderRequestDTO();
                    orderRequestDTO.setItemId(Long.valueOf(prestoreChargeOrderInfo.getItemId()));
                    orderRequestDTO.setMoney(new BigDecimal(prestoreChargeOrderInfo.getMoney()));
                    payItems.add(orderRequestDTO);
                }
        );
      return payItems;
    }

    public static List<PaySubOrderRequestDTO> fillPayOrderItems(List<PayOrAdjustItemVO> itemVOList) {
        List<PaySubOrderRequestDTO> payItems = new ArrayList<>();
        itemVOList.forEach(
                payOrAdjustItemVO->{
                    PaySubOrderRequestDTO orderRequestDTO = new PaySubOrderRequestDTO();
                    orderRequestDTO.setItemId(payOrAdjustItemVO.getItemId());
                    orderRequestDTO.setMoney(new BigDecimal(payOrAdjustItemVO.getAmount()));
                    payItems.add(orderRequestDTO);
                }
        );
        return payItems;
    }

    public static PayInfoDTO responseParamConvert(PayOrderSubmitResponseDTO payReturnDTO, Long incomeId) {
        String id = String.valueOf(incomeId);
        PayInfoDTO payInfoDTO = PayInfoDTO.builder().orderId(id).id(id).orderNum(payReturnDTO.getOutTradeNo()).build();
        if (Objects.equals(payReturnDTO.getPayChannel(), PayChannelEnum.CMB.getCode())) {
            CmbPayInfoDTO dto = buildCmbPayInfoDTO(payReturnDTO);
            fillChannelPayType(payReturnDTO.getTradeScene(), payInfoDTO, dto);
        } else {
            payInfoDTO.setPayInfo(JSON.toJSONString(payReturnDTO.getPayInfo()));
            payInfoDTO.setPayType(PayChannelEnum.SWIFTPASS.getCode());
        }
        return payInfoDTO;
    }

    public static BatchPayInfoDTO responseParamConvertForBatch(PayOrderSubmitResponseDTO payReturnDTO, Long incomeId) {
        BatchPayInfoDTO batchPayInfoDTO = BatchPayInfoDTO.builder().id(incomeId).orderNum(payReturnDTO.getOutTradeNo()).build();
        if (Objects.equals(payReturnDTO.getPayChannel(), PayChannelEnum.CMB.getCode())) {
            CmbPayInfoDTO dto = buildCmbPayInfoDTO(payReturnDTO);
            fillChannelPayType(payReturnDTO.getTradeScene(), batchPayInfoDTO, dto);
        } else {
            batchPayInfoDTO.setPayInfo(JSON.toJSONString(payReturnDTO.getPayInfo()));
            batchPayInfoDTO.setPayType(PayChannelEnum.SWIFTPASS.getCode());
        }
        return batchPayInfoDTO;
    }

    public static PayInfoDTO responseWorkOrderBuild(PayOrderSubmitResponseDTO responseDTO) {
        PayInfoDTO payInfoDTO = PayInfoDTO.builder().orderNum(responseDTO.getOutTradeNo()).outTradeNo(responseDTO.getOutTradeNo())
                .transactionId(responseDTO.getTransactionId()).build();
        if (Objects.equals(responseDTO.getPayChannel(), PayChannelEnum.CMB.getCode())) {
            CmbPayInfoDTO dto = buildCmbPayInfoDTO(responseDTO);
            fillChannelPayType(responseDTO.getTradeScene(), payInfoDTO, dto);
        } else {
            payInfoDTO.setPayInfo(JSON.toJSONString(responseDTO.getPayInfo()));
            payInfoDTO.setPayType(PayChannelEnum.SWIFTPASS.getCode());
        }
        return payInfoDTO;
    }

    public static void fillPaymentMethod(IncomeBillDTO dto, String paymentMethod){
        // 默认为微信
        dto.setPaymentMethod(PaymentMethodEnum.WECHAT.getPaymentCode());
        dto.setPaymentChannel(PaymentChannelEnum.WECHAT_PAY.getPaymentChannel());
        if (Objects.equals("3",paymentMethod)) {
            dto.setPaymentMethod(PaymentMethodEnum.ALIPAY.getPaymentCode());
            dto.setPaymentChannel(PaymentChannelEnum.ALI_PAY.getPaymentChannel());
        }
    }

    public static List<IncomeSubBillDTO> fillSubBills(List<PaySubOrderResponseDTO> subItems) {
        List<IncomeSubBillDTO> subBillDTOS = new ArrayList<>();
        if(CollectionUtil.isEmpty(subItems)){
            return subBillDTOS;
        }
        subItems.forEach(paySubOrderResponseDTO->{
            IncomeSubBillDTO incomeSubBillDTO = new IncomeSubBillDTO();
            incomeSubBillDTO.setSubOrderNum(paySubOrderResponseDTO.getSubOutTradeNo());
            incomeSubBillDTO.setIncomeMoney(new BigDecimal(paySubOrderResponseDTO.getSubTotalFee() ).divide(new BigDecimal("100")));
            incomeSubBillDTO.setRewardAccountType(paySubOrderResponseDTO.getRewardAccountType());
            incomeSubBillDTO.setMerchantNum(paySubOrderResponseDTO.getSubMchId());
            incomeSubBillDTO.setItemId(paySubOrderResponseDTO.getItemId());
            subBillDTOS.add(incomeSubBillDTO);
        });
        return subBillDTOS;
    }

    public static void fillPayTimeExpire(PayOrderRequestDTO dto) {
        if (StringUtils.isNotBlank(dto.getTimeExpire())) {
            return;
        }
        long lockSecs = SpringContextUtil.getBean(PrePayLockConfig.class).getPayLockSecs();
        String format = DateUtils.format(DateUtils.addSeconds(DateUtils.getCurrentTimestamp(), (int) lockSecs)
                , DateUtils.FORMAT_4);
        dto.setTimeExpire(String.valueOf(format));
    }

    public static List<PaySubOrderRequestDTO> fillBatchAssetPayOrderItems(List<AssetBillItem> assetBillItems) {
        return assetBillItems.stream().map(
                payOrAdjustItemVO->{
                    PaySubOrderRequestDTO orderRequestDTO = new PaySubOrderRequestDTO();
                    orderRequestDTO.setItemId(payOrAdjustItemVO.getItemId());
                    orderRequestDTO.setMoney(payOrAdjustItemVO.getAmount());
                    return orderRequestDTO;
                }
        ).collect(Collectors.toList());
    }

}
