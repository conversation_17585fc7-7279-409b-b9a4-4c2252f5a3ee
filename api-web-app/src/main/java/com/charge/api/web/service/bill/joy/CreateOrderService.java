package com.charge.api.web.service.bill.joy;

import com.charge.api.web.adapter.PayOrderAdapter;
import com.charge.api.web.constants.PayRelatedConstants;
import com.charge.api.web.controller.joylife.bill.request.CreateOrderRequest;
import com.charge.api.web.controller.joylife.bill.request.PayOrAdjustItemVO;
import com.charge.api.web.dto.joylife.AssetAdapter;
import com.charge.api.web.service.bill.pos.FillCommonUtil;
import com.charge.api.web.support.AssetSupport;
import com.charge.api.web.support.BankCollectionCheckSupport;
import com.charge.api.web.support.CommunityParamSupport;
import com.charge.api.web.support.CommunitySupport;
import com.charge.api.web.vo.joylife.AssetBillItem;
import com.charge.api.web.vo.joylife.AssetBillReq;
import com.charge.api.web.vo.joylife.CreateBatchAssetBillReq;
import com.charge.bill.client.ReceivableBillClient;
import com.charge.bill.dto.BillAssetInfoDTO;
import com.charge.bill.dto.ReceivableBillDTO;
import com.charge.bill.dto.ReceivableConditionDTO;
import com.charge.bill.dto.domain.*;
import com.charge.bill.enums.PaymentChannelEnum;
import com.charge.bill.enums.PaymentMethodEnum;
import com.charge.bill.enums.PaymentTerminalEnum;
import com.charge.bill.enums.ReceivableBillPayStatusEnum;
import com.charge.bill.enums.domain.ClientSourceEnum;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.enums.common.ErrorInfoEnum;
import com.charge.common.exception.ChargeBusinessException;
import com.charge.common.support.IdGeneratorSupport;
import com.charge.common.util.AppInterfaceUtil;
import com.charge.common.util.DateUtils;
import com.charge.config.client.item.CommunityPreStoreItemClient;
import com.charge.config.dto.item.ItemInfo;
import com.charge.config.dto.item.condition.ZXSpecialPreStoreQueryConditionDTO;
import com.charge.core.util.CollectionUtil;
import com.charge.core.util.TraceContextUtil;
import com.charge.joylife.dto.BatchPayInfoDTO;
import com.charge.maindata.condition.AssetCondition;
import com.charge.maindata.pojo.dto.CommunityDTO;
import com.charge.maindata.pojo.dto.CustomerDTO;
import com.charge.pay.dto.pay.PayOrderRequestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class CreateOrderService {

    private final BankCollectionCheckSupport bankCollectionCheckSupport;
    private final AssetSupport assetSupport;
    private final CreateOrderFlowService createOrderFlowService;
    private final CommunitySupport communitySupport;

    private final CommunityParamSupport communityParamSupport;

    private final ReceivableBillClient receivableBillClient;

    public static final BigDecimal MAX_AMOUNT =new BigDecimal("********.99");

    private final CommunityPreStoreItemClient communityPreStoreItemClient;

    /**
     * 朝昔下单
     * @param request
     * @return
     * @throws ChargeBusinessException
     */
    public ChargeResponse<Object> createOrder(CreateOrderRequest request) throws ChargeBusinessException {
        // 参数校验
        checkParam(request);
        // 获取项目ID并注入上下文
        communityParamSupport.fillCommunityId(request);
        TraceContextUtil.setCommunityId(request.getCommunityId());
        // 资产信息
        AssetAdapter assetInfo = assetSupport.getAssetInfoByMdId(request.getAssetId(), request.getAssetType());
        if (Objects.isNull(assetInfo)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E8002);
        }
        // 校验资产是否在托收中
        if (bankCollectionCheckSupport.bankCollectingByAssetId(assetInfo.getId())) {
            throw new ChargeBusinessException(ErrorInfoEnum.E3042.getCode(), ErrorInfoEnum.E3042.getValue());
        }
        // 构建数据
        String orderNum = IdGeneratorSupport.getIstance().nextId();
        return createOrderFlowService.createOrder(buildAssetPayDTO(request, assetInfo, orderNum),buildPayOrderRequestDTO(request, orderNum));
    }

    private void checkParam(CreateOrderRequest request) throws ChargeBusinessException {
        // 收费项校验
        List<PayOrAdjustItemVO> itemVOS = CommonUtils.getAllBillItems(request);
        if (CollectionUtil.isEmpty(itemVOS)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "收费项不能为空");
        }
        for (PayOrAdjustItemVO itemVO:itemVOS) {
            if (BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).compareTo(new BigDecimal(itemVO.getAmount()).setScale(2, RoundingMode.HALF_UP)) > 0) {
                throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "收费项金额不符合要求，请重新输入");
            }
        }
        // 支付总金额校验
        if (StringUtils.isNotEmpty(request.getTotalPrice()) && request.getTotalPrice().length() > 11) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "金额超出数据库存储范围，请重新输入");
        }
        if (StringUtils.isEmpty(request.getTotalPrice()) || BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).compareTo(new BigDecimal(request.getTotalPrice()).setScale(2, RoundingMode.HALF_UP)) > 0) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "金额不符合要求，请重新输入");
        }
        // 支付总金额和收费项明细校验
        BigDecimal allMoney = itemVOS.stream().map(item -> new BigDecimal(item.getAmount()).setScale(2,
                RoundingMode.HALF_UP)).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (allMoney.compareTo(new BigDecimal(request.getTotalPrice()).setScale(2, RoundingMode.HALF_UP)) != 0) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "收费项金额明细不等于总和，请重新输入");
        }
    }

    /**
     * 构建下单支付DTO
     * @param request
     * @param orderNum
     * @return
     */
    private PayOrderRequestDTO buildPayOrderRequestDTO(CreateOrderRequest request, String orderNum) {
        PayOrderRequestDTO dto = new PayOrderRequestDTO();
        dto.setCommunityId(TraceContextUtil.getCommunityId());
        dto.setOutTradeNo(orderNum);
        // 下单 单位为分，入参接口 单位为元 需要转换
        dto.setTotalFee(new BigDecimal(request.getTotalPrice()).multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString());
        dto.setBody(CommonUtils.buildGoodsName(request));
        dto.setMchCreateIp(request.getMchCreateIp());
        dto.setPaymentTerminal(request.getPaymentSource());
        // 新增AliJsApi和AliApp
        PayOrderAdapter.fillTradeScene(dto, request.getPaymentSource(),request.getPaymentMethod(),request.getPayNative());
        PayOrderAdapter.fillPaymentMethod(dto, request.getPaymentMethod());
        dto.setPayItems(PayOrderAdapter.fillPayOrderItems(CommonUtils.getAllBillItems(request)));
        dto.setSubAppId(request.getSubAppid());
        dto.setSubOpenId(request.getSubOpenid());
        dto.setPayMember(request.getUserName());
        dto.setPayMemberId(request.getUserId());
        dto.setBuyerLogonId(request.getBuyerLogonId());
        dto.setReturnUrl(request.getReturnUrl());
        PayOrderAdapter.fillPayTimeExpire(dto);
        return dto;
    }

    /**
     * 构建创建订单DTO
     * @param request
     * @param assetAdapter
     * @param orderNum
     * @return
     * @throws ChargeBusinessException
     */
    private AssetPayDTO buildAssetPayDTO(CreateOrderRequest request, AssetAdapter assetAdapter, String orderNum) {
        AssetPayDTO assetPayDTO = new AssetPayDTO();
        assetPayDTO.setAssetPayBaseDTO(buildAssetPayBaseDTO(request, assetAdapter, orderNum));
        assetPayDTO.setAssetPaymentDetailDTO(buildAssetPaymentDetailDTO(assetAdapter, request));
        return assetPayDTO;
    }

    /**
     * 构建基础信息：OutTransactionNo在编排阶段进行属性注入
     * @param request
     * @param orderNum
     * @return
     */
    private AssetPayBaseDTO buildAssetPayBaseDTO(CreateOrderRequest request, AssetAdapter assetAdapter, String orderNum) {
        AssetPayBaseDTO assetOrderBaseDTO = new AssetPayBaseDTO();
        assetOrderBaseDTO.setCommunityId(assetAdapter.getCommunityId());
        assetOrderBaseDTO.setCommunityName(assetAdapter.getCommunityName());
        assetOrderBaseDTO.setOrderNum(orderNum);
        assetOrderBaseDTO.setGoodsName(CommonUtils.buildGoodsName(request));
        assetOrderBaseDTO.setActualPrice(new BigDecimal(request.getTotalPrice())); // 实收金额
        assetOrderBaseDTO.setPaymentTerminal(PaymentTerminalEnum.handleJoyLifeWechatApplet(request.getPaymentSource()));
        assetOrderBaseDTO.setPayMember(request.getUserName());
        assetOrderBaseDTO.setPayMemberId(request.getUserId());
        assetOrderBaseDTO.setPayMemberMobile(request.getPhone());
        assetOrderBaseDTO.setMemo(request.getMemo());
        assetOrderBaseDTO.setMsAssetOwnerId(request.getCustomId());
        CommonUtils.fillPaymentMethodInfo(request.getPaymentMethod(), assetOrderBaseDTO);
        if (Objects.equals(PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel(),request.getPaymentSource())) {
            assetOrderBaseDTO.setPaymentChannel(request.getPaymentSource());
        }
        assetOrderBaseDTO.setCollectorId(request.getCollectorId());
        assetOrderBaseDTO.setCollectorName(request.getCollectorName());
        assetOrderBaseDTO.setCreateUser(request.getCollectorName());
        assetOrderBaseDTO.setClientSourceEnum(ClientSourceEnum.JOY_PAY);
        return assetOrderBaseDTO;
    }

    /**
     * 构建缴费信息
     * @param assetAdapter
     * @param request
     * @return
     */
    private AssetPaymentDetailDTO buildAssetPaymentDetailDTO(AssetAdapter assetAdapter, CreateOrderRequest request) {
        AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
        // 资产信息
        assetPaymentDetailDTO.setBillAssetInfoDTO(buildBillAssetInfoDTO(assetAdapter, null));
        // 订单信息
        assetPaymentDetailDTO.setOrderItems(FillCommonUtil.buildPayItemDTOS(request.getOrderItems()));
        // 押金信息
        assetPaymentDetailDTO.setDepositItems(FillCommonUtil.buildPayItemDTOS(request.getDepositItems()));
        return assetPaymentDetailDTO;
    }
    private BillAssetInfoDTO buildBillAssetInfoDTO(AssetAdapter assetAdapter, Integer chargeObjectType) {
        BillAssetInfoDTO billAssetInfoDTO = new BillAssetInfoDTO();
        billAssetInfoDTO.setCommunityId(assetAdapter.getCommunityId());
        billAssetInfoDTO.setCommunityName(assetAdapter.getCommunityName());
        billAssetInfoDTO.setAssetId(assetAdapter.getId());
        billAssetInfoDTO.setAssetType(assetAdapter.getType());
        billAssetInfoDTO.setAssetName(assetAdapter.getSubName());
        billAssetInfoDTO.setAssetCode(assetAdapter.getSubCode());
        billAssetInfoDTO.setBuildingId(assetAdapter.getBuildingId());
        billAssetInfoDTO.setBuildingName(assetAdapter.getBuildingName());
        billAssetInfoDTO.setUnitId(assetAdapter.getUnitId());
        billAssetInfoDTO.setUnitName(assetAdapter.getUnitName());
        if (CollectionUtil.isNotEmpty(assetAdapter.getListCustomer())) {
            CustomerDTO customerDTO = assetAdapter.getListCustomer().get(0);
            billAssetInfoDTO.setOwnerId(customerDTO.getId());
            billAssetInfoDTO.setOwnerName(customerDTO.getCustomerName());
        }
        billAssetInfoDTO.setAssetUseStatus(assetAdapter.getAssetUseStatus());
        billAssetInfoDTO.setChargeObject(chargeObjectType);
        return billAssetInfoDTO;
    }

    public ChargeResponse<BatchPayInfoDTO> batchAssetCreateOrder(CreateBatchAssetBillReq request) throws ChargeBusinessException {
        CommunityDTO communityDTO = communitySupport.fillCommunityId(request);
        TraceContextUtil.setCommunityId(request.getCommunityId());
        Map<Long, AssetAdapter> assetAdapterMap = assetSupport.fillAssetIds(request.getAssetBillReqs(), request.getCommunityId(), getIsNeedCheckChargeObject(request));

        checkDateItems(request, assetAdapterMap);

        List<String> assetNamesNoChargeObject = new ArrayList<>();
        assetAdapterMap.forEach((assetId, assetAdapter) -> {
            if (Objects.isNull(assetAdapter.getChargeObject())) {
                assetNamesNoChargeObject.add(assetAdapter.getAssetName());
            }
        });
        if (CollectionUtils.isNotEmpty(assetNamesNoChargeObject)){
            throw new ChargeBusinessException(ErrorInfoEnum.E1018.getCode(),"资产:["+assetNamesNoChargeObject.stream().collect(Collectors.joining(","))+"]收费对象缺失,无法缴费!");
        }
        checkParam(request);

        //校验欠费
        List<ReceivableBillDTO> receivableBills = checkReceivableParam(request.getCommunityId(), request);
        Map<String, List<ReceivableBillDTO>> assetId2ReceivableMap = receivableBills.stream().collect(Collectors.groupingBy(e -> String.valueOf(e.getAssetId())));

        Boolean onlyPreStore = CollectionUtil.isEmpty(receivableBills);
        // 构建数据
        String orderNum = IdGeneratorSupport.getIstance().nextId();
        return createOrderFlowService.createBatchAssetOrder(buildBatchAssetPayDTO(request, communityDTO, assetAdapterMap, orderNum, assetId2ReceivableMap, onlyPreStore),
                buildBatchAssetPayOrderRequestDTO(request, orderNum, onlyPreStore));
    }

    private void checkDateItems(CreateBatchAssetBillReq request, Map<Long, AssetAdapter> assetAdapterMap) throws ChargeBusinessException {
        ZXSpecialPreStoreQueryConditionDTO condition = new ZXSpecialPreStoreQueryConditionDTO();
        condition.setCommunityId(request.getCommunityId());
        condition.setAssetIds(Lists.newArrayList(assetAdapterMap.keySet()));
        condition.setCheckDate(true);
        ChargeResponse<List<ItemInfo>> checkDateItemInfoResponse = communityPreStoreItemClient.listSpecialItemForZX(condition);
        List<ItemInfo> checkDateItemInfos = AppInterfaceUtil.getDataThrowException(checkDateItemInfoResponse);
        if (CollectionUtils.isEmpty(checkDateItemInfos)){
            throw new ChargeBusinessException(ErrorInfoEnum.E3020.getCode(),"资产:["+assetAdapterMap.values().stream().map(t->t.getAssetName()).collect(Collectors.toList()).stream().collect(Collectors.joining(","))+"]收费项未在生效时间，无法缴费!");
        }

        Map<Long, List<ItemInfo>> assetIdToItemInfoMap = checkDateItemInfos.stream()
                .collect(Collectors.groupingBy(
                        ItemInfo::getAssetId)
                );


        List<String> assetNamesCheckItemDateError = new ArrayList<>();
        request.getAssetBillReqs().forEach(item->{
            if (CollectionUtils.isEmpty(assetIdToItemInfoMap.get(item.getAssetId()))){
                assetNamesCheckItemDateError.add(assetAdapterMap.get(item.getAssetId()).getAssetName());
            }
            List<Long> checkDateItemIds = assetIdToItemInfoMap.get(item.getAssetId()).stream().map(t -> t.getItemId()).collect(Collectors.toList());
            List<Long> itemIds = item.getPrestores().stream().map(t -> Long.valueOf(t.getItemId())).collect(Collectors.toList());
            itemIds.removeAll(checkDateItemIds);
            if (CollectionUtils.isNotEmpty(itemIds)){
                assetNamesCheckItemDateError.add(assetAdapterMap.get(item.getAssetId()).getAssetName());
            }
        });
        if (CollectionUtils.isNotEmpty(assetNamesCheckItemDateError)){
            throw new ChargeBusinessException(ErrorInfoEnum.E3020.getCode(),"资产:["+assetNamesCheckItemDateError.stream().collect(Collectors.joining(","))+"]收费项未在生效时间，无法缴费!");
        }
    }

    private List<ReceivableBillDTO> checkReceivableParam(Long communityId, CreateBatchAssetBillReq request) throws ChargeBusinessException{
        List<Long> receivableBillIds = new ArrayList<>();
        request.getAssetBillReqs().forEach(item -> {
            if(CollectionUtil.isNotEmpty(item.getReceivables())){
                List<Long> ids = item.getReceivables().stream().map(AssetBillItem::getId).collect(Collectors.toList());
                receivableBillIds.addAll(ids);
            }
        });

        if(CollectionUtil.isEmpty(receivableBillIds)){
            return Collections.emptyList();
        }

        // 只拉当前月份之前的欠费
        List<ReceivableBillDTO> receivableBills =
                AppInterfaceUtil.getResponseDataThrowException(receivableBillClient.queryList(ReceivableConditionDTO.builder().ids(receivableBillIds).communityId(communityId).belongYearsEnd(DateUtils.format(new Date(), DateUtils.FORMAT_14)).build()));
        Assert.notEmpty(receivableBills, "不存在有效的欠费");
        for (ReceivableBillDTO receivableBill : receivableBills) {
            if (Objects.equals(receivableBill.getPayStatus(), ReceivableBillPayStatusEnum.COLLECTION.getCode())) {
                throw new ChargeBusinessException(ErrorInfoEnum.E3044);
            }
        }

        //校验账单数量一致性
        if(receivableBills.size() != receivableBillIds.size()){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),String.format("欠费账单数量与查询结果不匹配,传入欠费单数量:%s,查询欠费单数量:%s", receivableBillIds.size(), receivableBills.size()));
        }

        //校验欠费总金额
        BigDecimal totalArrears = receivableBills.stream().map(receivableBill -> receivableBill.getArrearsAmount().add(receivableBill.getPenaltyArrearsAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if(totalArrears.compareTo(request.getTotalPrice()) != 0){
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(),String.format("欠费总金额与缴欠费总金额不符,欠费:%s,缴费:%s", totalArrears, request.getTotalPoints()));
        }

        return receivableBills;
    }

    private void checkParam(CreateBatchAssetBillReq request) throws ChargeBusinessException {
        // 收费项校验
        List<AssetBillItem> billItems = CommonUtils.getAllBillItems(request);
        if (CollectionUtil.isEmpty(billItems)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "收费项不能为空");
        }

        // 支付总金额校验
        if ((request.getTotalPrice().compareTo(MAX_AMOUNT) > 0)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "金额超出数据库存储范围，请重新输入");
        }

        // 支付总金额和收费项明细校验
        BigDecimal allMoney = billItems.stream().map(item -> item.getAmount().setScale(2,
                RoundingMode.HALF_UP)).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (allMoney.compareTo(request.getTotalPrice().setScale(2, RoundingMode.HALF_UP)) != 0) {
            throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "收费项金额明细不等于总和，请重新输入");
        }
        if(request.getTotalPoints()!=null&&request.getTotalPoints()>0){
            Assert.hasText(request.getEquityAccount(),"积分账户不能为空");
        }
    }

    private CommunityDTO handleCommunityParam(CreateBatchAssetBillReq request) throws ChargeBusinessException {
        CommunityDTO communityDTO;
        if (org.springframework.util.StringUtils.hasText(request.getCommunityMsId())
                && Objects.isNull(request.getCommunityId())) {
            communityDTO = communitySupport.getCommunityByMsId(request.getCommunityMsId());
            if (communityDTO == null || communityDTO.getId() == null) {
                throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "当前项目不存在");
            }
            Long communityId = communityDTO.getId();
            request.setCommunityId(communityId);
        } else if (Objects.nonNull(request.getCommunityId())) {
            try {
                Long communityId = request.getCommunityId();
                communityDTO = communitySupport.getCommunityById(communityId);
                if (communityDTO == null) {
                    throw new ChargeBusinessException(ErrorInfoEnum.E1002.getCode(), "当前项目不存在");
                }
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("项目id不合法:" + request.getCommunityId());
            }
        } else {
            throw new IllegalArgumentException("项目id不能为空:" + request.getCommunityId());
        }
        return communityDTO;
    }

    private List<AssetAdapter> handleAssetParam(CreateBatchAssetBillReq request, Long communityId) throws ChargeBusinessException {
        //定义传参类型(1-朝昔资产msId,2-收费系统资产ID)
        Integer assetParamFlag = determineAssetParamFlag(request.getAssetBillReqs());
        AssetCondition assetCondition = buildAssetCondition(request.getAssetBillReqs(), communityId, assetParamFlag);

        List<AssetAdapter> assetAdapters = fetchAssetAdapters(assetCondition);
        validateAssetAdapters(assetAdapters, request.getAssetBillReqs(), assetParamFlag);

        if (assetParamFlag == 1) {
            mapMsIdToHouseId(assetAdapters, request.getAssetBillReqs());
        }

        checkIfAssetsAreBankCollecting(assetAdapters);
        return assetAdapters;
    }

    private Integer determineAssetParamFlag(List<AssetBillReq> assetBillReqs) throws IllegalArgumentException {
        Integer assetParamFlag = 2;
        for (AssetBillReq assetBillReq : assetBillReqs) {
            if (org.springframework.util.StringUtils.hasText(assetBillReq.getAssetMsId())) {
                if (Objects.isNull(assetBillReq.getAssetId())) {
                    Assert.notNull(assetBillReq.getAssetType(), "资产类型不能为空");
                }
                assetParamFlag = 1;
            } else if (Objects.isNull(assetBillReq.getAssetId())) {
                throw new IllegalArgumentException("资产id不能为空:" + assetBillReq.getAssetId());
            }
        }
        return assetParamFlag;
    }

    private AssetCondition buildAssetCondition(List<AssetBillReq> assetBillReqs, Long communityId, Integer assetParamFlag) {
        AssetCondition assetCondition = AssetCondition.builder().communityId(communityId).build();
        if (assetParamFlag == 1) {
            List<String> assetMsIds = assetBillReqs.stream().map(AssetBillReq::getAssetMsId).collect(Collectors.toList());
            List<Integer> assetTypes = assetBillReqs.stream().map(AssetBillReq::getAssetType).distinct().collect(Collectors.toList());

            if (assetTypes.size() > 1) {
                throw new IllegalArgumentException("资产类型只允许存在一个");
            }

            assetCondition.setMsIds(assetMsIds);
            assetCondition.setType(assetTypes.get(0));
        } else {
            List<Long> assetIds = assetBillReqs.stream().map(AssetBillReq::getAssetId).collect(Collectors.toList());
            assetCondition.setIds(assetIds);
        }
        return assetCondition;
    }

    private List<AssetAdapter> fetchAssetAdapters(AssetCondition assetCondition) throws ChargeBusinessException {
        List<AssetAdapter> assetAdapters = assetSupport.getAssetListByCondition(assetCondition);
        if (CollectionUtil.isEmpty(assetAdapters)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E8002);
        }
        return assetAdapters;
    }

    private void validateAssetAdapters(List<AssetAdapter> assetAdapters, List<AssetBillReq> assetBillReqs, Integer assetParamFlag) throws ChargeBusinessException {
        int expectedSize = (assetParamFlag == 1)
                ? (int) assetBillReqs.stream().map(AssetBillReq::getAssetMsId).distinct().count()
                : (int) assetBillReqs.stream().map(AssetBillReq::getAssetId).distinct().count();

        if (assetAdapters.size() != expectedSize) {
            throw new ChargeBusinessException(ErrorInfoEnum.E8002.getCode(), "部分资产信息查询结果为空");
        }
    }

    private void mapMsIdToHouseId(List<AssetAdapter> assetAdapters, List<AssetBillReq> assetBillReqs) {
        Map<String, Long> msId2IdMap = assetAdapters.stream()
                .collect(Collectors.toMap(AssetAdapter::getMsId, AssetAdapter::getId, (a, b) -> b));
        assetBillReqs.forEach(item -> item.setAssetId(msId2IdMap.get(item.getAssetMsId())));
    }

    private void checkIfAssetsAreBankCollecting(List<AssetAdapter> assetAdapters) throws ChargeBusinessException {
        List<Long> assetIdList = assetAdapters.stream().map(AssetAdapter::getId).collect(Collectors.toList());
        if (bankCollectionCheckSupport.bankCollectingByAssetIds(assetIdList)) {
            throw new ChargeBusinessException(ErrorInfoEnum.E3042.getCode(), ErrorInfoEnum.E3042.getValue());
        }
    }

    /**
     * 构建创建批量资产订单DTO
     * @param request
     * @param assetAdapterMap
     * @param orderNum
     * @return
     * @throws ChargeBusinessException
     */
    private AssetsPayDTO buildBatchAssetPayDTO(CreateBatchAssetBillReq request, CommunityDTO communityDTO, Map<Long, AssetAdapter> assetAdapterMap,
                                               String orderNum, Map<String, List<ReceivableBillDTO>> assetId2ReceivableMap, Boolean onlyPreStore) {
        AssetsPayDTO assetsPayDTO = new AssetsPayDTO();
        assetsPayDTO.setAssetPayBaseDTO(buildBatchAssetPayBaseDTO(request, communityDTO, orderNum, onlyPreStore));
        assetsPayDTO.setAssetPaymentDetailDTOS(buildBatchAssetPaymentDetailDTO(assetAdapterMap, request, assetsPayDTO.getAssetPayBaseDTO(), assetId2ReceivableMap));
        return assetsPayDTO;
    }

    /**
     * 构建基础信息：OutTransactionNo在编排阶段进行属性注入
     * @param request
     * @param orderNum
     * @return
     */
    private AssetPayBaseDTO buildBatchAssetPayBaseDTO(CreateBatchAssetBillReq request, CommunityDTO communityDTO, String orderNum, Boolean onlyPreStore) {
        AssetPayBaseDTO assetOrderBaseDTO = new AssetPayBaseDTO();
        assetOrderBaseDTO.setCommunityId(communityDTO.getId());
        assetOrderBaseDTO.setCommunityName(communityDTO.getName());
        assetOrderBaseDTO.setOrderNum(orderNum);
        assetOrderBaseDTO.setGoodsName(convertGoodsName(onlyPreStore));
        assetOrderBaseDTO.setPoints(Objects.isNull(request.getTotalPoints()) ? 0 : request.getTotalPoints());
        assetOrderBaseDTO.setEquityAccount(request.getEquityAccount());
        // 实收金额
        assetOrderBaseDTO.setActualPrice(request.getTotalPrice());
        assetOrderBaseDTO.setPaymentTerminal(PaymentTerminalEnum.handleJoyLifeWechatApplet(request.getPaymentSource()));
        assetOrderBaseDTO.setPayMember(request.getUserName());
        assetOrderBaseDTO.setPayMemberId(request.getUserId());
        assetOrderBaseDTO.setPayMemberMobile(request.getPhone());
        assetOrderBaseDTO.setMemo(request.getMemo());
        assetOrderBaseDTO.setMsAssetOwnerId(request.getCustomId());
        CommonUtils.fillPaymentMethodInfo(request.getPaymentMethod(), assetOrderBaseDTO);
        if (Objects.equals(PaymentChannelEnum.WECHAT_APPLET.getPaymentChannel(),request.getPaymentSource())) {
            assetOrderBaseDTO.setPaymentChannel(request.getPaymentSource());
        }
        assetOrderBaseDTO.setCreateUser(request.getUserName());
        assetOrderBaseDTO.setClientSourceEnum(convertClientSource(onlyPreStore));
        return assetOrderBaseDTO;
    }

    private String convertGoodsName(Boolean onlyPrestore){
        return onlyPrestore?PayRelatedConstants.GOODSNAME_FOR_PRESTORE:PayRelatedConstants.GOODSNAME_FOR_PROPERTY;
    }

    private ClientSourceEnum convertClientSource(Boolean onlyPreStore){
        return onlyPreStore?ClientSourceEnum.JOY_BATCH_PRESTORE_PAY:ClientSourceEnum.JOY_BATCH_NORMAL_PAY;
    }

    /**
     * 构建缴费信息
     * @param assetAdapterMap
     * @param request
     * @return
     */
    private List<AssetPaymentDetailDTO> buildBatchAssetPaymentDetailDTO(Map<Long, AssetAdapter> assetAdapterMap, CreateBatchAssetBillReq request,
                                                                        AssetPayBaseDTO assetPayBaseDTO, Map<String, List<ReceivableBillDTO>> assetId2ReceivableMap) {
        List<AssetPaymentDetailDTO> assetPaymentDetailDTOS = new ArrayList<>();
        for(AssetBillReq assetBillReq : request.getAssetBillReqs()) {
            AssetPaymentDetailDTO assetPaymentDetailDTO = new AssetPaymentDetailDTO();
            // 资产信息
            assetPaymentDetailDTO.setBillAssetInfoDTO(buildBillAssetInfoDTO(assetAdapterMap.get(assetBillReq.getAssetId()), assetBillReq.getChargeObjectType()));
            // 预存信息
            assetPaymentDetailDTO.setPrestoreItems(FillCommonUtil.buildBatchAssetPayItemDTOS(assetBillReq.getPrestores()));
            // 欠费信息
            assetPaymentDetailDTO.setReceivableBillDTOS(FillCommonUtil.buildPayRec(assetId2ReceivableMap.get(assetBillReq.getAssetMsId())));
            // 基础信息
            assetPaymentDetailDTO.setAssetPayBusinessDTO(buildAssetPayCommonDTO(assetBillReq, assetPayBaseDTO));
            assetPaymentDetailDTOS.add(assetPaymentDetailDTO);
        }
        return assetPaymentDetailDTOS;
    }

    private AssetPayBusinessDTO buildAssetPayCommonDTO(AssetBillReq assetBillReq, AssetPayBaseDTO assetPayBaseDTO) {
        AssetPayBusinessDTO assetPayBusinessDTO = new AssetPayBusinessDTO();
        assetPayBusinessDTO.setAssetTotalAmount(assetBillReq.getAmount());
        assetPayBusinessDTO.setPaymentMethod(assetPayBaseDTO.getPaymentMethod());
        String paymentChannel = Objects.equals(assetPayBaseDTO.getPaymentMethod(), PaymentMethodEnum.EQUITY.getPaymentCode())?"":assetPayBaseDTO.getPaymentChannel();
        assetPayBusinessDTO.setPaymentChannel(paymentChannel);
        return assetPayBusinessDTO;
    }

    /**
     * 构建批量资产下单支付DTO
     * @param request
     * @param orderNum
     * @return
     */
    private PayOrderRequestDTO buildBatchAssetPayOrderRequestDTO(CreateBatchAssetBillReq request, String orderNum, Boolean onlyPrestore) {
        PayOrderRequestDTO dto = new PayOrderRequestDTO();
        dto.setCommunityId(TraceContextUtil.getCommunityId());
        dto.setOutTradeNo(orderNum);
        // 下单 单位为分，入参接口 单位为元 需要转换
        dto.setTotalFee(request.getTotalPrice().multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString());
        dto.setBody(convertGoodsName(onlyPrestore));
        dto.setMchCreateIp(request.getMchCreateIp());
        dto.setPaymentTerminal(PaymentTerminalEnum.handleJoyLifeWechatApplet(request.getPaymentSource()));
        // 新增AliJsApi和AliApp
        PayOrderAdapter.fillTradeScene(dto, request.getPaymentSource(),request.getPaymentMethod(), request.getPayNative());
        PayOrderAdapter.fillPaymentMethod(dto, request.getPaymentMethod());
        dto.setPayItems(PayOrderAdapter.fillBatchAssetPayOrderItems(CommonUtils.getAllBillItems(request)));
        dto.setSubAppId(request.getSubAppid());
        dto.setSubOpenId(request.getSubOpenid());
        dto.setPayMember(request.getUserName());
        dto.setPayMemberId(request.getUserId());
        dto.setBuyerLogonId(request.getBuyerLogonId());
        dto.setReturnUrl(request.getReturnUrl());
        PayOrderAdapter.fillPayTimeExpire(dto);
        return dto;
    }

    /**
     * 根据请求参数，判断是否需要校验收费对象类型（缴预存才需要校验）
     * @param request
     * @return
     */
    private boolean getIsNeedCheckChargeObject(CreateBatchAssetBillReq request){
        AtomicBoolean result = new AtomicBoolean(false);
        if(CollectionUtil.isNotEmpty(request.getAssetBillReqs())){
            request.getAssetBillReqs().forEach(assetBill -> {
                if(CollectionUtil.isNotEmpty(assetBill.getPrestores())){
                    result.set(true);
                }
            });
        }
        return result.get();
    }

}
