package com.charge.api.web.vo.pos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OrderInstallmentVO
 * <p>
 * Description:
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderInstallmentWithItemsVO {

    /**
     * 进度单信息
     */
    private OrderInstallmentVO installment;
    /**
     * 子单列表
     */
    private List<EBusinessBaseOrderVO1> items;

}