package com.charge.api.web.util;

import java.math.BigDecimal;

/**
 * 小数过滤
 *
 * <AUTHOR>
 * @date 2018/3/2
 */
public class CancelZeroUtil {


    public static BigDecimal precisionDecimal(BigDecimal bigDecimal) {

        BigDecimal bigDecimalReturn = new BigDecimal("0");
        if (bigDecimal == null) {
            return bigDecimalReturn;
        }

        //等于0
        if (bigDecimal.compareTo(BigDecimal.ZERO) == 0) {
            return bigDecimal;
        }

        //整数或者位数全为0->浮点数转换失精
        bigDecimal = new BigDecimal(bigDecimal.toPlainString()).setScale(5, BigDecimal.ROUND_HALF_UP).stripTrailingZeros();
        if (new BigDecimal(bigDecimal.intValue()).equals(bigDecimal)) {
            return new BigDecimal(bigDecimal.toPlainString()).setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        //一个小数位
        String bigString = bigDecimal.toPlainString();
        if (bigString.matches("\\d+\\.\\d{1}$")) {
            return new BigDecimal(bigDecimal.toPlainString()).setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        //去除尾数0
        return new BigDecimal(bigDecimal.stripTrailingZeros().toPlainString());
    }
}
