package com.charge.api.web.vo.joylife.request;

import com.charge.api.web.vo.joylife.BatchArrearsOrderList;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@ApiModel(description = "积分抵扣金额计算请求实体", value = "积分抵扣金额计算请求实体")
@Data
public class MixPointsCalRequest implements Serializable {

    private static final long serialVersionUID = 2975728929297694877L;

    @ApiModelProperty(value = "小区ID（运营平台小区ID）", required = true)
    @NotNull(message = "小区Id参数缺失,请重试")
    private String communityId;

    @ApiModelProperty(value = "支付来源(微信小程序：WECHAT_APPLET，悦家APP：YUEHOME_PAY，润钱包：CRT_PAY)", required = true)
    @NotNull(message = "支付来源参数缺失,请重试")
    private String paymentSource;

    @ApiModelProperty(value = "支付人(当前登录用户姓名)", required = false)
    private String userName;

    @ApiModelProperty(value = "客户ID（当前登录用户唯一标识，记录用户缴费历史）", required = true)
    @NotNull(message = "用户Id参数缺失,请重试")
    private String userId;

    @ApiModelProperty(value = "缴费数据(使用查询欠费列表数据)，json数组字符串", required = true)
    @NotEmpty(message = "缴费信息参数缺失,请重试")
    private List<BatchArrearsOrderList> arrearsOrderList;

    @ApiModelProperty(value = "抵扣积分数量", required = true)
    @NotEmpty(message = "请传入抵扣积分数量")
    private String points;

    @ApiModelProperty(value = "积分账户pId", required = false)
    @NotEmpty(message = "积分账户pId缺失")
    private String equityAccount;

}
