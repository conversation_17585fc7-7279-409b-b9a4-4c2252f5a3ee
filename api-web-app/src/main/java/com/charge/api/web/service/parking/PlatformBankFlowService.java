package com.charge.api.web.service.parking;


import com.charge.api.web.dto.parking.GetPlatformBankFlowParam;
import com.charge.api.web.dto.parking.DeleteHistoryDataParam;
import com.charge.api.web.dto.parking.ResultResponse;
import com.charge.api.web.vo.parking.InvoiceCommunityConfigVO;
import com.charge.api.web.vo.parking.InvoiceCommunityItemTaxConfigVO;
import com.charge.common.dto.ChargeResponse;
import com.charge.common.exception.ChargeBusinessException;

/**
 * @ClassName: GetPlatformBankFlowService
 * @Author: wangle
 * @Description: 获取云平台银行收款业务流水
 * @Date: 2021/11/11 15:02:42
 * @Version: 1.0
 */
public interface PlatformBankFlowService {

    /**
     * 获取云平台银行收款业务流水
     * @return
     */
    ResultResponse getPlatformBankBusinessFlow(GetPlatformBankFlowParam getPlatformBankFlowParam) throws ChargeBusinessException;

    /**
     * 临停订单历史业务流水初始化（软删）
     * @param deleteHistoryDataParam
     * @return
     */
    ResultResponse historyDataDelete(DeleteHistoryDataParam deleteHistoryDataParam);

    /**
     * 查询项目的开票配置
     * @param communityCode
     */
    ChargeResponse<InvoiceCommunityConfigVO> getInvoiceCommunityConfig(String communityCode) throws ChargeBusinessException;

    /**
     *
     * @param communityCode
     * @param itemCode
     * @return
     */
    ChargeResponse<InvoiceCommunityItemTaxConfigVO> getInvoiceChargeItemConfig(String communityCode, String itemCode) throws ChargeBusinessException;
}
