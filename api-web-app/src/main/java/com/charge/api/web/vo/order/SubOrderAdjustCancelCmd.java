package com.charge.api.web.vo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 调整取消子订单
 *
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SubOrderAdjustCancelCmd extends BaseOrderCmdReq {

    /**
     * 子订单号列表
     */
    @NotEmpty(message = "子订单号列表不能为空")
    private List<String> subOrderNos;


}