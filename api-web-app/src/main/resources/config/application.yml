#定义服务前缀，本地调试时可用
application:
  name:
    prefix: 'local-dwj'
idempotent:
  enable: true
spring:
  http:
    encoding:
      force: true
  application:
    name: ${application.name.prefix}api-web
   #调用其他服务
      #pay 应用服务2
    pay:
      name: ${application.name.prefix}pay-app
    bill:
      name: ${application.name.prefix}charge-bill-app
    maindata:
      name: ${application.name.prefix}charge-maindata-app
    config:
      name: ${application.name.prefix}charge-config-app
    order:
      name: ${application.name.prefix}order-app
    user:
      name: ${application.name.prefix}user-app
    finance:
      name: ${application.name.prefix}finance-integration
    fee-calculate:
      name: ${application.name.prefix}fee-calculate-app
    reportcloud:
      name: ${application.name.prefix}reportcloud-app
    invoice:
      name: ${application.name.prefix}invoice-app
    apicloud:
      name: ${application.name.prefix}apicloud-app
    general:
      name: ${application.name.prefix}general-app
    leaf:
      name: ${application.name.prefix}leaf-app
redis:
  servers: r-wz93bdc31e22c914.redis.rds.aliyuncs.com:6379
  password: 'Hello1231020'
  maxIdle: 20
  maxTotal: 20
  retryTimes: 3
  publishRetry: 5
  defaultTimeOut: 5000
  maxWaitMillis: -1
  minIdle: 100
  testOnBorrow: false
  testOnCreate: false
  testOnReturn: false
  testWhileIdle: true
  timeBetweenEvictionRunsMillis: 2000
  cache.annotation.enable: true

ribbon:
  ReadTimeout: 30000
  eager-load:
    enabled: true
    #ribbon饿汉式加载应用服务
    clients: '${spring.application.pay.name},${spring.application.bill.name},${spring.application.maindata.name},${spring.application.user.name},${spring.application.finance.name}'

server:
  port: 8041
  servlet:
    encoding:
      #content-type字符集
      charset: UTF-8
      #Http请求和响应强制字符集为配置的值
      force: true
  tomcat:
    mbeanregistry:
      enabled: true
logging:
  config: classpath:log4j2.xml
  level:
    com:
      alibaba:
        nacos: warn

management:
  server:
    port: 18041
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: /ops
  metrics:
    percentiles:
      http.server.requests: 0.5,0.95,0.99
    sla:
      http.server.requests: 10ms,50ms,250ms,1s,5s,10s
feign:
  hystrix:
    enabled: false

thread:
  corePoolSize: 10
  maxPoolSize: 20
  queueCapacity: 200

charge:
  swagger2:
    scanPath: 'com.charge.api.web.controller'
    title: '收费系统-api-web相关服务'
    description: '收费系统-api-wb相关服务'
    version: '2.0'

#阿里云oss配置项
oss:
  accessKey: LTAI4FqhuTHcvoghscL21Qu4
  accessSecret: ******************************
  bucketEnv: dev

pay:
  ras:
    joyLiftPrivateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAK+jflNoE3hM+TmlupInDyl1t1hiZJnK0ibMAacqJK3ei10NF1RkxRjFBdwgvi2eo1S6yz/ItIDAds2YFKewFR/O0ErzY4oGWTRBJU2gVDCChZiEDkQ7TChWLEfXuvcgkV+4lLskRaA23BXfWDZR039TWq8WwjYtuSQNrCHGmJiVAgMBAAECgYAtMgTvsXhRHH2FOAAqW6KrKH9lXuxEJsys7xH90fpeQ0XZS+UU8llFZm17l3FYi9m7k9V5NEw+VUg830VxYP4OInYRG8ZKR0uRx+wvzLXpJznDaZa9w4SUD1Sy5HeijddATf2edsI2uiWu+887O42vYEL0p3ojE6OZjeEF35M6gQJBAPzkSjNwes/WaMdev2bd0Q6mVJQnDn0tdPwCDdM1qKPprwggUPPHN0bFUrjsZmuaqTudLsCmylN48Wnqg7y6tbsCQQCxzCGC34lrnSGlho9NxyoPwnGMaMxrBJjRh/I6R/5Ag4dRwuuUjUOWwxJOxFujDPODTXUBW95I55ZxUZc6jF3vAkARZTKlbytDeKAkdqP70245778JodYhTisp2opQ6PP8smpTyMVIv39R4ujZx4kUBcN2Uk2Q+ugX7c3NV1BQfxdZAkBMqXnhlkUIk64IUybDIJcwSGTxZ9aMZmUGI5n8K0KjypK0XFl7qhV0khP4xo3EljcBbIqA1X3Y7ZJethGA+zkdAkEAlfhzavFWAi77khqEFVvAXyKvBiJ8HbZEuNSyo4eNY04zRV6GBHswqztAdoh8q8rQx/KUq8AYsAGU4rhDbdpong==
    joyLiftPublicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCvo35TaBN4TPk5pbqSJw8pdbdYYmSZytImzAGnKiSt3otdDRdUZMUYxQXcIL4tnqNUuss/yLSAwHbNmBSnsBUfztBK82OKBlk0QSVNoFQwgoWYhA5EO0woVixH17r3IJFfuJS7JEWgNtwV31g2UdN/U1qvFsI2LbkkDawhxpiYlQIDAQAB
    chargePrivateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKGXJQq4tZaSuGoyxHYMdc0I2PMaZBUU3gKyMByLkLVD2EIXpD77QLOI/Bw8HK9zNeFeJ3QWsgQYrk8cuOmqkulD0K2e1rW1fqWnemxLuekYLl73tsJx3fwcbAlMZVQA8Ex4ULCmYQG5d2NxZhjun1EpuIiZj1t1Mf+TEehi4d2BAgMBAAECgYEAiI1HejX5DhNS0DuyleeVrPyrCVTl+oK10iVuTtEKKoP4vnFoFOkSFiXOlrWdsA52G8sLICkFqjyJIMWu5ZeFqUlLWgplzvQQGM12u9KJTovLp54GgG09lYLPsqC9PZiRHNHpsptZU2XgiXBFD4x/lsEHVL4lM7M4LoHgR9xkGVkCQQDwo7rweNWcAsZc8MCCns2i2Vvx2JzP46zZTVhel5V9zmMmljH7t3dDC9S/dxGg3vRW4trrpQS2aJAWmlwBZlOPAkEAq+et3Qa8c65rU03XamCrhCaT1hI4qO4MJHxrF1BV+NXDn4WTHuhf97EN6P5wyjNkDzjLOnmNUNtFJ1PFmn717wJAYT2YhiIeF5wbcmUrNd1cNYlmGTWyMGPNbkZOBnN0ylDDSk15R0GdJO+7+d7wtbRCwnWLh5u4asmYKDoaIcqqywJAJvD+o7ko65OxYFeb2s83S2Jpzu6lwy7ZtVIx+EAMmafi/roFj9TYGIYVPpiU+FkX65LUBtUOPCjPy47gjpUAJQJAfmiDUPQ/9IKfrQB5UqmjoiLhYiw1SEOTadzdffXmjyWF24yFZq26ntWsLcadsWkaZAulwlCy7KhFRZ3gihoESw==
    chargePublicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChlyUKuLWWkrhqMsR2DHXNCNjzGmQVFN4CsjAci5C1Q9hCF6Q++0CziPwcPByvczXhXid0FrIEGK5PHLjpqpLpQ9Ctnta1tX6lp3psS7npGC5e97bCcd38HGwJTGVUAPBMeFCwpmEBuXdjcWYY7p9RKbiImY9bdTH/kxHoYuHdgQIDAQAB
  lakala:
    apiUrl: https://test.wsmsd.cn/sit
    appid: OP00000003
    channelId: 2021052614391
    mchSerialNo: 00dfba8194c41b84cf
    createOrderApiPath: /api/v3/los/checkout_counter/mch_create_order
    queryOrderApiPath: /api/v3/los/checkout_counter/query_full_order
    merchantPrivateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDvDBZyHUDndAGxrIcsCV2njhNO3vCEZotTaWYSYwtDvkcAb1EjsBFabXZaKigpqFXk5XXNI3NIHP9M8XKzIgGvc65NpLAfRjVql8JiTvLyYd1gIUcOXMInabu+oX7dQSI1mS8XzqaoVRhDZQWhXcJW9bxMulgnzvk0Ggw07AjGF7si+hP/Va8SJmN7EJwfQq6TpSxR+WdIHpbWdhZ+NHwitnQwAJTLBFvfk28INM39G7XOsXdVLfsooFdglVTOHpNuRiQAj9gShCCNrpGsNQxDiJIxE43qRsNsRwigyo6DPJk/klgDJa417E2wgP8VrwiXparO4FMzOGK15quuoD7DAgMBAAECggEBANhmWOt1EAx3OBFf3f4/fEjylQgRSiqRqg8Ymw6KGuh4mE4Md6eW/B6geUOmZjVP7nIIR1wte28M0REWgn8nid8LGf+v1sB5DmIwgAf+8G/7qCwd8/VMg3aqgQtRp0ckb5OV2Mv0h2pbnltkWHR8LDIMwymyh5uCApbn/aTrCAZKNXcPOyAn9tM8Bu3FHk3Pf24Er3SN+bnGxgpzDrFjsDSHjDFT9UMIc2WdA3tuMv9X3DDn0bRCsHnsIw3WrwY6HQ8mumdbURk+2Ey3eRFfMYxyS96kOgBC2hqZOlDwVPAKTPtS4hoq+cQ0sRaJQ4T0UALJrBVHa+EESgRaTvrXqAECgYEA+WKmy9hcvp6IWZlk9Q1JZ+dgIVxrO65zylK2FnD1/vcTx2JMn73WKtQb6vdvTuk+Ruv9hY9PEsf7S8gHSTTmzHOUgo5x0F8yCxXFnfji2juoUnDdpkjtQK5KySDcpQb5kcCJWEVi9v+zObM0Zr1Nu5/NreE8EqUl3+7MtHOu1TMCgYEA9WM9P6m4frHPW7h4gs/GISA9LuOdtjLvAtgCK4cW2mhtGNAMttD8zOBQrRuafcbFAyU9de6nhGwetOhkW9YSV+xRNa7HWTeIRgXJuJBrluq5e1QGTIwZU/GujpNaR4Qiu0B8TodM/FME7htsyxjmCwEfT6SDYlkeMzTbMa9Q0DECgYBqsR/2+dvD2YMwAgZFKKgNAdoIq8dcwyfamUQ5mZ5EtGQL2yw48zibHh/LiIxgUD1Kjk/qQgNsX45NP4iOc0mCkrgomtRqdy+rumbPTNmQ0BEVJCBPscd+8pIgNiTvnWpMRvj7gMP0NDTzLI3wnnCRIq8WAtR2jZ0Ejt+ZHBziLQKBgQDibEe/zqNmhDuJrpXEXmO7fTv3YB/OVwEj5p1Z/LSho2nHU3Hn3r7lbLYEhUvwctCnLl2fzC7Wic1rsGOqOcWDS5NDrZpUQGGF+yE/JEOiZcPwgH+vcjaMtp0TAfRzuQEzNzV8YGwxB4mtC7E/ViIuVULHAk4ZGZI8PbFkDxjKgQKBgG8jEuLTI1tsP3kyaF3jAylnw7SkBc4gfe9knsYlw44YlrDSKr8AOp/zSgwvMYvqT+fygaJ3yf9uIBdrIilqCHKXccZ9uA/bT5JfIi6jbg3EoE9YhB0+1aGAS1O2dBvUiD8tJ+BjAT4OB0UDpmM6QsFLQgFyXgvDnzr/o+hQJelW
    lklCertificate: MIIEMTCCAxmgAwIBAgIGAXRTgcMnMA0GCSqGSIb3DQEBCwUAMHYxCzAJBgNVBAYTAkNOMRAwDgYDVQQIDAdCZWlKaW5nMRAwDgYDVQQHDAdCZWlKaW5nMRcwFQYDVQQKDA5MYWthbGEgQ28uLEx0ZDEqMCgGA1UEAwwhTGFrYWxhIE9yZ2FuaXphdGlvbiBWYWxpZGF0aW9uIENBMB4XDTIwMTAxMDA1MjQxNFoXDTMwMTAwODA1MjQxNFowZTELMAkGA1UEBhMCQ04xEDAOBgNVBAgMB0JlaUppbmcxEDAOBgNVBAcMB0JlaUppbmcxFzAVBgNVBAoMDkxha2FsYSBDby4sTHRkMRkwFwYDVQQDDBBBUElHVy5MQUtBTEEuQ09NMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt1zHL54HiI8d2sLJlwoQji3/ln0nsvfZ/XVpOjuB+1YR6/0LdxEDMC/hxI6iH2Rm5MjwWz3dmN/6BZeIgwGeTOWJUZFARo8UduKrlhC6gWMRpAiiGC8wA8stikc5gYB+UeFVZi/aJ0WN0cpPJYCvPBhxhMvhVDnd4hNohnR1L7k0ypuWg0YwGjC25FaNAEFBYP9EYUyCJjE//9Z7sMzHR9SJYCqqo6r9bOH9G6sWKuEp+osuAh+kJIxJMHfipw7w3tEcWG0hce9u/el4cYJtg8/PPMVoccKmeCzMvarr7jdKP4lenJbtwlgyfs+JgNu60KMUJH8RS72wC9NYuFz09wIDAQABo4HVMIHSMIGSBgNVHSMEgYowgYeAFCnH4DkZPR6CZxRn/kIqVsModJHpoWekZTBjMQswCQYDVQQGEwJDTjEQMA4GA1UECAwHQmVpSmluZzEQMA4GA1UEBwwHQmVpSmluZzEXMBUGA1UECgwOTGFrYWxhIENvLixMdGQxFzAVBgNVBAMMDkxha2FsYSBSb290IENBggYBaiUALIowHQYDVR0OBBYEFJ2Kx9YZfmWpkKFnC33C0r5DK3rFMAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQBZoeU0XyH9O0LGF9R+JyGwfU/O5amoB97VeM+5n9v2z8OCiIJ8eXVGKN9Ltl9QkpTEanYwK30KkpHcJP1xfVkhPi/cCMgfTWQ5eKYC7Zm16zk7n4CP6IIgZIqmTVGsIGKk8RzWseyWPB3lfqMDR52V1tdA1S8lJ7a2Xnpt5M2jkDXoArl3SVSwCb4DAmThYhak48M++fUJNYII9JBGRdRGbfJ2GSFdPXgesUL2CwlReQwbW4GZkYGOg9LKCNPK6XShlNdvgPv0CCR08KCYRwC3HZ0y1F0NjaKzYdGNPrvOq9lA495ONZCvzYDogmsu/kd6eqxTs/JwdaIYr4sCMg8Z
  unionpay:
    apiUrl: https://dhjt-uat.chinaums.com/entryService/billentryServlet
    queryOrderApiPath: https://dhjt-test.chinaums.com/queryService/billqueryServle54
    merchantPrivateKey: 11111111111111111111111111111111
#车辆临停收费项配置
parking:
  v2:
    itemCodes: A00105,A00106,A00107,A00045,A00046,A00047
  v1:
    itemCodes: B0183,B0112,B0120,B0227,B0113,B0230
    itemIds: 1b8ca25f-61d8-4658-a63a-8093c6df9362,d3348486-174a-458e-9254-6dc7f0913241,aff6ea1e-38a1-4d8f-b260-84c0b9493a72,da1f6fa0-3226-4890-ae16-90360cd3cde2,e9d18c67-ec28-4dc9-9d9e-1cc696b1ca78,3e5a6a52-c694-4ada-8ba5-feb79810fae9

gray:
  pay:
#    主灰度开关
    masterSwitch: true
#    是否灰度所有项目 true是 false否
    openAllCommunity: true
    uriGrayConstants:
#        POS缴费下单
      - uri: /addNewPayOrder
        communityIds:        #如果是配置All则表示全部开启，支持多个项目ID使用英文逗号分隔，没有配置表示无
#        POS小区临停收费下单
      - uri: /addCommunityTempChargeInfo
        communityIds:
#        pos小区多房间临时收费下单
      - uri: /addNewTempChargeInfo
        communityIds:
#        pos小区批量临时收费下单
      - uri: /addNewCommunityTempChargeInfo
        communityIds:
#        pos多房间缴费下单
      - uri: /batchPay/addBatchPayOrder
        communityIds:
#        pos预存充值
      - uri: /addNewDepositInfo
        communityIds:
#        pos房间交押金下单
      - uri: /addNewHouseDeposit
        communityIds:
#        pos小区交押金下单
      - uri: /addNewCommunityDeposit
        communityIds:
#        朝昔-生活缴费
      - uri: /new/chargeAPI/createBatchPay
        communityIds:
#        朝昔-月卡缴费
      - uri: /app/parking/createPayOrder
        communityIds:
#        朝昔-预存下单
      - uri: /new/weixin/addPrestoreInfo
        communityIds:
pointsEarnRule: '{"ratio":"2","points":"25000","factor":"12"}'
points-reward-rule:
  ratio: 1.5
  points: 250000
  factor: 1
pointsRate: "1"
#赠分记录查询（交易开始日期）
points.phone.dealStartTime: "2024-01-01 00:00:00"

transfer:
  pay-url: https://testcharge.crlandpm.com.cn/v2/biling/arrears-notice?id=


switch:
  useNewAssetOverview: true