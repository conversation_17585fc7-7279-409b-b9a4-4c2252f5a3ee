#定义服务前缀，本地调试时可用
application:
  name:
    prefix: ''

spring:
  http:
    encoding:
      force: true
  application:
    name: ${application.name.prefix}api-web
   #调用其他服务
      #pay 应用服务
    pay:
      name: ${application.name.prefix}pay-app
    bill:
      name: ${application.name.prefix}charge-bill-app
    maindata:
      name: ${application.name.prefix}charge-maindata-app
    config:
      name: ${application.name.prefix}charge-config-app
    order:
      name: ${application.name.prefix}order-app
    user:
      name: ${application.name.prefix}user-app
    finance:
      name: ${application.name.prefix}finance-integration
    fee-calculate:
      name: ${application.name.prefix}fee-calculate-app
    reportcloud:
      name: ${application.name.prefix}reportcloud-app
    invoice:
      name: ${application.name.prefix}invoice-app
    apicloud:
      name: ${application.name.prefix}apicloud-app
    general:
      name: ${application.name.prefix}general-app
    leaf:
      name: ${application.name.prefix}leaf-app
redis:
  servers: ${redis.servers}
  password: ${redis.password}
  maxIdle: 20
  maxTotal: 20
  minIdle: 100
  maxWaitMillis: -1
  timeBetweenEvictionRunsMillis: 2000

ribbon:
  ReadTimeout: 30000
  eager-load:
    enabled: true
    #ribbon饿汉式加载应用服务
    clients: '${spring.application.pay.name},${spring.application.bill.name},${spring.application.maindata.name},${spring.application.user.name},${spring.application.finance.name}'



server:
  port: 8041
  servlet:
    encoding:
      #content-type字符集
      charset: UTF-8
      #Http请求和响应强制字符集为配置的值
      force: true
  tomcat:
    mbeanregistry:
      enabled: true

logging:
  config: classpath:log4j2.xml

management:
  server:
    port: 18041
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: /ops

feign:
  hystrix:
    enabled: false


thread:
  corePoolSize: 10
  maxPoolSize: 20
  queueCapacity: 200

pay:
  ras:
    joyLiftPrivateKey: ${pay.ras.joyLiftPrivateKey}
    joyLiftPublicKey: ${pay.ras.joyLiftPublicKey}
    chargePrivateKey: ${pay.ras.chargePrivateKey}
    chargePublicKey: ${pay.ras.chargePublicKey}
  #车辆临停收费项配置
parking:
  v2:
    itemCodes: A00105,A00106,A00107,A00045,A00046,A00047
  v1:
    itemCodes: B0183,B0112,B0120,B0227,B0113,B0230
    itemIds: 1,2,3,4,5,6

pointsEarnRule: ${pointsEarnRule}
pointsRate: ${pointsRate}

#阿里云oss配置项
oss:
  accessKey: ${oss.accesskey.id}
  accessSecret: ${oss.accesskey.secret}
  bucketEnv:  test
idempotent:
  enable: true
#赠分记录查询（交易开始日期）
points.phone.dealStartTime: ${points.phone.dealStartTime}

transfer:
  pay-url: https://testcharge.crlandpm.com.cn/v2/biling/arrears-notice?id=