<?xml version="1.0" encoding="UTF-8"?>

<!-- status : 这个用于设置log4j2自身内部的信息输出,可以不设置,当设置成trace时,会看到log4j2内部各种详细输出 monitorInterval 
	: Log4j能够自动检测修改配置文件和重新配置本身, 设置间隔秒数。 -->
<Configuration status="WARN" packages="com.charge.core.log">

    <Properties>
        <!-- 配置日志文件输出目录 -->
        <Property name="LOG_HOME">/home/<USER>/</Property>
        <!-- 配置日志文件名称 -->
        <Property name="LOG_NAME">api-web</Property>
    </Properties>

    <Appenders>

        <!--这个输出控制台的配置 -->
        <console name="console" target="SYSTEM_OUT">
            <!-- 控制台只输出level及以上级别的信息(onMatch),其他的直接拒绝(onMismatch) -->
            <ThresholdFilter level="trace" onMatch="ACCEPT"
                             onMismatch="DENY"/>
            <!-- 输出日志的格式 -->
            <PatternLayout
                    pattern="[%x/%d{yyyy-MM-dd HH:mm:ss,SSS}][%traceId][%t][%-3p] %C{1}.%M(%L): %mm%n"/>
        </console>

        <!-- 设置日志格式并配置日志压缩格式(service.log.年份.gz) RandomAccessFile -->
        <RollingRandomAccessFile name="logfile" immediateFlush="false"
                                 fileName="${LOG_HOME}/${LOG_NAME}-${env:POD_IP:-noPod}.log"
                                 filePattern="${LOG_HOME}/${LOG_NAME}-${env:POD_IP:-noPod}.log.%d{yyyy-MM-dd}"
                                 append="true">
            <!-- %d{yyyy-MM-dd HH:mm:ss, SSS} : 日志生产时间 %p : 日志输出格式 %c : logger的名称
                                                             %m : 日志内容，即 logger.info("message") %n : 换行符 %C : Java类名 %L : 日志输出所在行数 %M
                                : 日志输出所在方法名 hostName : 本地机器名 hostAddress : 本地ip地址 -->
            <PatternLayout>
                <pattern>[%x %d{yyyy-MM-dd HH:mm:ss,SSS}][%traceId][%t][%-3p] %C{1}.%M(%L): %mm%n
                </pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true"
                                           interval="1"/>
                <!-- <SizeBasedTriggeringPolicy size="1 KB" /> -->
            </Policies>
        </RollingRandomAccessFile>


    </Appenders>

    <Loggers>
        <!-- 配置日志的根节点 -->
        <AsyncRoot level="info" includeLocation="true">
            <appender-ref ref="console"/>
            <appender-ref ref="logfile"/>
        </AsyncRoot>


    </Loggers>

</Configuration>