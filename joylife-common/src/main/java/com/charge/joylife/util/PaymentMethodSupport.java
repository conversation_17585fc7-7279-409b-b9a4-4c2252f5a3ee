package com.charge.joylife.util;

import com.charge.core.enums.LogCategoryEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * Author: yjw
 * Date: 2023/4/26 9:54
 */
@Slf4j
public class PaymentMethodSupport {

    /**
     * 收费2.0与收费1.0支付方式转换方法（值匹配，用于兼容1.0）
     * @param paymentMethod
     * @return
     */
    public static String convertTOPaymentMethodV1(Integer paymentMethod){
        log.info("{}|收费2.0支付方式paymentMethod={}", LogCategoryEnum.BUSSINESS, paymentMethod);
        String paymentMethodForV1 = "";
        switch(paymentMethod) {
            case 0:
                //银行刷卡支付--POS
                paymentMethodForV1 = "0";
                break;
            case 1:
                //线下转账或支票支付 -- 转账
                paymentMethodForV1 = "1";
                break;
            case 2:
                //支付宝支付
                paymentMethodForV1 = "3";
                break;
            case 3:
                //微信支付
                paymentMethodForV1 = "4";
                break;
            case 4:
                //银行托收
                paymentMethodForV1 = "7";
                break;
            case 5:
                //预存扣款
                paymentMethodForV1 = "5";
                break;
            case 6:
                //华润通积分支付
                paymentMethodForV1 = "10";
                break;
            case 7:
                //收入结转
                paymentMethodForV1 = "34";
                break;
            case 8:
                //云平台
                paymentMethodForV1 = "35";
                break;
            case 9:
                //预存直接红冲
                paymentMethodForV1 = "23";
                break;
            case 10:
                //预存调整
                paymentMethodForV1 = "27";
                break;
            case 11:
                //退款转预存
                paymentMethodForV1 = "26";
                break;
            case 12:
                //待结转扣款
                paymentMethodForV1 = "30";
                break;
            case 13:
                //待结转扣款直接红冲
                paymentMethodForV1 = "31";
                break;
            case 14:
                //红冲退款
                paymentMethodForV1 = "24";
                break;
            case 15:
                //红冲调整
                paymentMethodForV1 = "25";
                break;
            case 16:
                //政策性折扣
                paymentMethodForV1 = "28";
                break;
            case 17:
                //销售折让
                paymentMethodForV1 = "29";
                break;
            case 18:
                //往来冲销
                paymentMethodForV1 = "32";
            case 19:
                //银行退票
                paymentMethodForV1 = "12";
            case 20:
                //初始化
                paymentMethodForV1 = "99";
                break;
            case 21:
                //违约金减免
                paymentMethodForV1 = "8";
                break;
            case 22:
                //润钱包
                paymentMethodForV1 = "9";
                break;
            case 23:
                //现金
                paymentMethodForV1 = "36";
                break;
            case 24:
                //订单调整
                paymentMethodForV1 = "37";
                break;
            default:
                //未知
                paymentMethodForV1 = "100";
                break;
        }
        log.info("{}|转换为收费1.0支付方式paymentMethodForV1={}", LogCategoryEnum.BUSSINESS, paymentMethodForV1);
        return paymentMethodForV1;
    }
}
