package com.charge.joylife.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommunityPayEnableStatusResp implements Serializable {
    private static final long serialVersionUID = 8505311510494284992L;
    /**
     * 收费系统小区id
     */
    private String  communityId;
    /**
     * 朝昔小区msId
     */
    private String communityMsId;

    /**
     * 可否缴费：0 否   1 可 （判断是否有支付配置）
     */
    private Integer status;


    /**
     * 小区当前能否进行缴费:0 否   1 可（小区是否有批量任务）
     */
    private Integer payEnableStatus;

}
