package com.charge.joylife.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class AssetBillListQueryDTO implements Serializable {

    private static final long serialVersionUID = -7965469209261396035L;

    @NotBlank(message = "小区id不能为空")
    private String   communityMsId;
    /**
     * 房间msList
     */
     private List<AssetBillDetailQueryDTO> houseMsIdList;
    /**
     * 车位msList
     */
    private  List<AssetBillDetailQueryDTO>  parkingMsIdList;

    /**
     * 当前页
     */
    @NotBlank(message = "当前页不能为空")
    private Integer currentPage;

    /**
     * 页面大小
     */
    @NotBlank(message = "页面大小不能为空")
    private Integer pageSize;

    private String  payMemberId;

}
