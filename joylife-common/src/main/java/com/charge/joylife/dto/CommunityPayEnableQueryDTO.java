package com.charge.joylife.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommunityPayEnableQueryDTO implements Serializable {
    private static final long serialVersionUID = -6531849998538188892L;

    /**
     * 朝昔小区msId
     */
    @NotBlank(message = "小区msId缺失")
    private String communityMsId;

    /**
     * appId用于查询移动端APP商户配置
     */
    private String subAppid;

    /**
     * 资产msID
     */
    private List<AssetListReqDTO> assetList;

    /**
     * 收费资产id集合
     */
    private List<Long> assetIds;

}
