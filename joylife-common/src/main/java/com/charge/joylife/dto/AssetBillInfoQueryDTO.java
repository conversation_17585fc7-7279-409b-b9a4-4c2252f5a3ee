package com.charge.joylife.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
@Data
public class AssetBillInfoQueryDTO implements Serializable {

    private static final long serialVersionUID = -7965469209261396034L;
    @NotBlank(message = "小区id不能为空")
    private String   communityMsId;

    @NotBlank(message = "交易记录ID不能为空")
    private Long transactionId;
}
