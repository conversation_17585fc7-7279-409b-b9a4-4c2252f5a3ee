package com.charge.joylife.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ArrearsAssetInfo implements Serializable {

    private static final long serialVersionUID = -4363268924780369882L;
    /**
     * 收费系统资产id
     */
    private String assetId;

    /**
     * 朝昔资产msId
     */
    private String msId;

    /**
     * 资产类型：房间 1， 车位 2
     */
    private Integer assetType;

    /**
     * 收费对象类型（0-业主，1-开发商）
     */
    private Integer chargeObjectType;

}
