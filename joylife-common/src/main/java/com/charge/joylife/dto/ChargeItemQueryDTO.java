package com.charge.joylife.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/07 16:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChargeItemQueryDTO implements Serializable {

    private static final long serialVersionUID = -868008787129237541L;

    /**
     * 小区主数据ID
     */
    private String communityMsId;

    /**
     * 收费项id列表
     */
    private List<Long> itemIdList;

}
