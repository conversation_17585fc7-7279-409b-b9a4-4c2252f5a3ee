package com.charge.joylife.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/05 17:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssetStandardConfigRuleQueryDTO implements Serializable {

    private static final long serialVersionUID = -868008787129237541L;

    /**
     * 小区主数据ID
     */
    private String communityMsId;

    /**
     * 资产id列表
     */
    private List<Long> assetIdList;

    /**
     * 是否查询计费规则详情
     */
    private Boolean isQueryRuleDetail;

}
