package com.charge.joylife.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CommunityArrearsAssetsReq implements Serializable {
    private static final long serialVersionUID = 9074957289375012275L;

    /**
     * 小区主数据msID
     */
    @NotBlank(message = "项目id不能为空")
    private String communityMsId;

    /**
     * 主数据楼栋msId
     */
    private List<String> buildingMsIdList;

    /**
     * 资产类型：房间 1， 车位 2
     */
    private Integer assetType;
}
