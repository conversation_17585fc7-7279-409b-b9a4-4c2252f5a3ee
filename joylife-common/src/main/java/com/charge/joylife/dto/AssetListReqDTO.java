package com.charge.joylife.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AssetListReqDTO implements Serializable {
    private static final long serialVersionUID = -3738452801876638212L;

    /**
     * 房屋主数据ID
     */
    @NotEmpty(message = "资产id不能为空")
    List<String> houseMsIdList;

    /**
     * 资产类型：房间 1， 车位 2
     */
    @NotNull(message = "资产类型不能为空")
    private Integer assetType;
}
