package com.charge.joylife.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * [招行下单适配朝昔]
 *
 * <AUTHOR> wu<PERSON><PERSON>
 * @version : [v1.0]
 */
@Data
public class CmbPayInfoDTO implements Serializable {
    private static final long serialVersionUID = 8291145196788017635L;
    /**
     * 订单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    private String cmbOrderId;

    private String txnTime;

    private String encryptedCmbOrderId;

    private String encryptedTradeInfo;

    private String cmbMiniAppId;

    private String aliQrCode;

    private String aliPayInfo;
    /**
     * 商户id
     */
    private String merId;
    /**
     * 小程序支付信息
     */
    private Map<String, String> payData;
}
