package com.charge.joylife.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class JoylifeCommunityMerchantDTO  implements Serializable {
    private static final long serialVersionUID = -3738452801876638212L;

    /**
     * 商户号
     */
    private String merchantNumber;
    /**
     * appid
     */
    private String appId;

    private String signType;

    private String secondMerchantNumber;

    private String paymentSource;

    private String status;

    private String bankCardNumber;

    private String merchantName;

    private String remarks;
}
