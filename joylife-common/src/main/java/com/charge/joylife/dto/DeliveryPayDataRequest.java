package com.charge.joylife.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 云交付预存组合请求实体
 * Author: yjw
 * Date: 2023/1/10 16:26
 */

@Data
public class DeliveryPayDataRequest implements Serializable {

    @NotNull(message = "小区Id参数缺失,请重试")
    private String communityMsId;

    @NotNull(message = "标的物ID参数缺失,请重试")
    private String houseMsId;

    @NotNull(message = "标的物类型参数缺失,请重试")
    private String propertyType;

    @NotNull(message = "预存组合ID缺失,请重试")
    private Long prestoreFormId;

    @NotNull(message = "总金额参数缺失,请重试")
    private String money;

    @NotNull(message = "用户名称参数缺失,请重试")
    private String payMember;

    @NotNull(message = "用户Id参数缺失,请重试")
    private String payMemberId;

    private String systemType;

    @NotNull(message = "支付来源参数缺失,请重试")
    private String paymentSource;

    @NotEmpty(message = "预存费项数据参数缺失,请重试")
    private List<DeliveryItemDetail> billDetailVOS;

    @NotNull(message = "终端IP参数缺失,请重试")
    private String mchCreateIp;

    @NotNull(message = "APPId参数缺失,请重试")
    private String subAppid;

    private String subOpenid;

    private String needReceipt;

    private String equityAccount;

    private String phone;

    private String totalPoints;

    private String memo;

    @NotNull(message = "云交付缴费记录ID参数缺失,请重试")
    private Long recordId;

    private String customId;

    /**
     * 买家支付宝账号(支付宝时必填)
     */
    private String buyerLogonId;

    /**
     * 支付方式（支付宝：3,微信支付：4）
     */
    private String paymentMethod;



}
