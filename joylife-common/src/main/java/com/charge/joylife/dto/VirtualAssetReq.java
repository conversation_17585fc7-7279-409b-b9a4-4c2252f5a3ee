package com.charge.joylife.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VirtualAssetReq implements Serializable {

    private static final long serialVersionUID = -3321051896552579683L;

    /**
     * 朝昔项目id
     */
    @NotBlank(message = "项目id不能为空")
    private String communityMsId;

    /**
     * 资产id
     */
    private List<String> assetId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 资产名称
     */
    private String assetName;

}
