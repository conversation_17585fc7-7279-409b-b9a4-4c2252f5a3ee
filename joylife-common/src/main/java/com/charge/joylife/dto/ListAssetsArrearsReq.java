package com.charge.joylife.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ListAssetsArrearsReq implements Serializable {

    private static final long serialVersionUID = -868008787129237541L;
    /**
     * 小区主数据ID
     */
    @NotBlank(message = "项目id不能为空")
    String communityMsId;

    /**
     * 资产msID
     */
    private List<AssetListReqDTO> assetListReqDTOS;

    /**
     * 收费对象类型（0-业主，1-开发商）
     */
    private Integer chargeObjectType;

}
