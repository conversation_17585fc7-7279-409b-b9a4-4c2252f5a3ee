package com.charge.joylife.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 支付信息
 *
 * <AUTHOR>
 * @date 2023/7/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayInfoDTO implements Serializable {

    private static final long serialVersionUID = 2648444201790047881L;

    private String id;

    @JsonProperty("out_trade_no")
    private String outTradeNo;

    @JsonProperty("transaction_id")
    private String transactionId;

    @JsonProperty("pay_info")
    private String payInfo;

    private String orderNum;
    
    private String orderId;

    private String payType;
    /**
     * 招行微信小程序信息
     */
    private Object cmbWxPayInfo;
    /**
     * 招行微信app信息
     */
    private Object cmbPayInfo;

    /**
     * 万象星全额抵扣标识:true 全额抵扣、false需现金支付
     */
    private Boolean fullyDeductFlag;

}
