package com.charge.joylife.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VirtualAssetDTO implements Serializable {

    private static final long serialVersionUID = -465685910845962577L;

    /**
     * 收费系统资产id
     */
    private String assetId;

    /**
     * 朝昔资产msId
     */
    private String assetMsId;

    /**
     * 虚拟房屋名称
     */
    private String assetName;

    /**
     * 虚拟资产类型：虚拟房屋（项目） 1， 虚拟房屋 2
     */
    private Integer type;

    /**
     * 资产编码
     */
    private String assetCode;

    /**
     * 项目名称
     */
    private String communityName;

    /**
     * 楼栋名称
     */
    private String buildingName;

}
