package com.charge.joylife.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 批量资产下单返回体
 * @Author: yjw
 * @Date: 2024/6/20 14:13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchPayInfoDTO  implements Serializable {

    private static final long serialVersionUID = 2648444201790047881L;

    /**
     * 支付信息对象（不同支付场景有可能不一样）
     */
    private String payInfo;

    /**
     * 收费系统订单号
     */
    private String orderNum;

    /**
     * 收费系统实收单ID
     */
    private Long id;

    /**
     * 三方支付渠道,如CMB_WX_PAY、CMB、CMB_ALI_JSPAY、CMB_ALI_APP等等
     * @see com.charge.pay.enums.ChannelPayTypeEnum
     */
    private String payType;
    /**
     * 招行微信小程序信息
     */
    private CmbPayInfoDTO cmbWxPayInfo;

    /**
     * 招行微信app信息
     */
    private CmbPayInfoDTO cmbPayInfo;

}
