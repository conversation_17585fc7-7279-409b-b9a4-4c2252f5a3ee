package com.charge.joylife.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/12/16
 */
@Data
public class BillInfoVO implements Serializable {
    private static final long serialVersionUID = -7965469209261396035L;

    /**
     * 支付金额
     */
    private BigDecimal payPrice;
    /**
     * 违约金金额
     */
    private BigDecimal payPenaltyPrice;

    /**
     * 详情
     */
    private List<BillDataInfoVO> dataInfoList;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 折扣金额(1.0取的积分金额)
     */
    private BigDecimal discountPrice;
    /**
     * 积分金额
     */
    private BigDecimal pointsPrice;
    /**
     * 积分个数
     */
    private Integer points;
    /**
     * 积分交易类型
     * 1005 积分通兑  >>>> 积分抵扣
     * 1015 通兑撤销
     * 1001 积分通积  >>>> 积分赠送
     * 1016 通积撤销
     */
    private Integer mixcTransType;
    /**
    /**
     * 支付人
     */
    private String payMember;
    /**
     * 支付方法
     */
    private Integer paymentMethod;
    /**
     * 支付终端
     */
    private String paymentTerminal;
    /**
     * 支付渠道
     */
    private String paymentChannel;
    /**
     * 支付时间
     */
    private String payTime;
    /**
     * 交易流水号
     */
    private String transactionNo;
    /**
     * 订单号
     */
    private String orderNum;
}
