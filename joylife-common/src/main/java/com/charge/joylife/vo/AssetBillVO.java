package com.charge.joylife.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AssetBillVO  implements Serializable {
    private static final long serialVersionUID = -7965469209261396035L;
    /**
     * 资产实收账单id
     */
    private Long transactionId;

    /**
     * 收费项名称
     */
    private String itemName;

    /**
     * 本金金额
     */
    private BigDecimal price;

    /**
     * 违约金金额
     */
    private BigDecimal penaltyPrice;
    /**
     * 缴费时间
     */
    private String tradeTime;
    /**
     * 收费类型名称
     */
    private String billTypeName;

    /**
     * 资产信息
     */
    private String subjectInfo;

    /**
     * 资产信息
     */
    private Integer subjectType;

    /**
     * 朝昔小区id
     */
    private String communityMsId;

}
