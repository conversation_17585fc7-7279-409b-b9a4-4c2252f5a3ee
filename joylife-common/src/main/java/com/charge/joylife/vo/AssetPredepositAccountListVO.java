package com.charge.joylife.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 资产预收账户余额列表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssetPredepositAccountListVO implements Serializable {

    private static final long serialVersionUID = -8579094932744789378L;

    /**
     * 收费资产id
     */
    private Long assetId;

    /**
     * 朝昔资产id
     */
    private String assetMsId;

    /**
     * 总可用预存余额
     */
    private BigDecimal totalAvailableMoney;
    /**
     * 资产预收账户余额明细
     */
    private List<PrestoreDetailVO> detailList;

}
