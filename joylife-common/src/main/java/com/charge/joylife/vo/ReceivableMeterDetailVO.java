package com.charge.joylife.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 抄表详情
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReceivableMeterDetailVO implements Serializable {
    private static final long serialVersionUID = -8435127585442926220L;

    /**
     * 用量
     */
    private    String    usage;

    /**
     * 单位
     */
    private    String    unit;

    /**
     * 上次读数
     */
    private String lastNum;

    /**
     * 本次读数
     */
    private String currentNum;

    /**
     * 所属年月
     */
    private String belongYears;

    /**
     * 抄表所属区间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date meterDateBegin;
    /**
     * 抄表所属区间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date meterDateEnd;

    /**
     * 倍数
     */
    private Integer multiple;

    /**
     * 换表状态：0、未换表 1、已换表
     */
    private Integer changeStatus;
}
