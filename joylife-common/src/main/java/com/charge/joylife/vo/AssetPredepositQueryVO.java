package com.charge.joylife.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 资产预收账户余额列表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssetPredepositQueryVO implements Serializable {

    private static final long serialVersionUID = 7639279998918826458L;

    /**
     * 朝昔项目msid
     */
    @NotBlank(message = "项目id不能为空")
    private String communityMsId;

    /**
     * 收费项目id
     */
    private Long communityId;

    /**
     * 房间msid列表
     */
    private List<String> houseMsIdList;

    /**
     * 车位msid列表
     */
    private List<String> parkingMsIdList;

}
