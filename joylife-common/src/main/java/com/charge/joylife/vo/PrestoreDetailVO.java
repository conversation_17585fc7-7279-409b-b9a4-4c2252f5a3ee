package com.charge.joylife.vo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PrestoreDetailVO implements Serializable {
	/**
	 * 总金额
	 */
	private BigDecimal totalMoney;
	/**
	 * 冻结金额
	 */
	private BigDecimal freezeMoney;
	/**
	 * 可用余额
	 */
	private BigDecimal availableMoney;
	/**
	 * 费项id
	 */
	private Long itemId;
	/**
	 * 费项名称
	 */
	private String itemName;
}
